{"ast": null, "code": "// 权限检查工具函数\nimport { message } from 'antd';\n\n// 权限代码常量\nexport const Permissions = {\n  // 班级相关权限\n  VIEW_CLASS: 'view_class',\n  CREATE_CLASS: 'create_class',\n  EDIT_CLASS: 'edit_class',\n  DELETE_CLASS: 'delete_class',\n  MANAGE_CLASS: 'manage_class',\n  // 学生相关权限\n  VIEW_STUDENT: 'view_student',\n  CREATE_STUDENT: 'create_student',\n  EDIT_STUDENT: 'edit_student',\n  DELETE_STUDENT: 'delete_student',\n  MANAGE_STUDENT: 'manage_student',\n  // 教师相关权限\n  VIEW_TEACHER: 'view_teacher',\n  CREATE_TEACHER: 'create_teacher',\n  EDIT_TEACHER: 'edit_teacher',\n  DELETE_TEACHER: 'delete_teacher',\n  MANAGE_TEACHER: 'manage_teacher',\n  // 家长相关权限\n  VIEW_PARENT: 'view_parent',\n  CREATE_PARENT: 'create_parent',\n  EDIT_PARENT: 'edit_parent',\n  DELETE_PARENT: 'delete_parent',\n  MANAGE_PARENT: 'manage_parent'\n};\n\n// 权限检查函数\nexport const hasPermission = (userPermissions, requiredPermission) => {\n  if (!userPermissions || !requiredPermission) {\n    return false;\n  }\n  return userPermissions.includes(requiredPermission);\n};\n\n// 批量权限检查函数\nexport const hasAnyPermission = (userPermissions, requiredPermissions) => {\n  if (!userPermissions || !requiredPermissions || requiredPermissions.length === 0) {\n    return false;\n  }\n  return requiredPermissions.some(permission => hasPermission(userPermissions, permission));\n};\n\n// 检查是否有管理权限\nexport const hasManagePermission = (userPermissions, resourceType) => {\n  switch (resourceType) {\n    case 'class':\n      return hasAnyPermission(userPermissions, [Permissions.MANAGE_CLASS, Permissions.CREATE_CLASS, Permissions.EDIT_CLASS, Permissions.DELETE_CLASS]);\n    case 'student':\n      return hasAnyPermission(userPermissions, [Permissions.MANAGE_STUDENT, Permissions.CREATE_STUDENT, Permissions.EDIT_STUDENT, Permissions.DELETE_STUDENT]);\n    case 'teacher':\n      return hasAnyPermission(userPermissions, [Permissions.MANAGE_TEACHER, Permissions.CREATE_TEACHER, Permissions.EDIT_TEACHER, Permissions.DELETE_TEACHER]);\n    case 'parent':\n      return hasAnyPermission(userPermissions, [Permissions.MANAGE_PARENT, Permissions.CREATE_PARENT, Permissions.EDIT_PARENT, Permissions.DELETE_PARENT]);\n    default:\n      return false;\n  }\n};\n\n// 过滤菜单项（根据权限）\nexport const filterMenuItems = (menuItems, userPermissions) => {\n  return menuItems.filter(item => {\n    // 如果菜单项没有设置权限要求，默认显示\n    if (!item.permission) {\n      return true;\n    }\n    // 检查权限\n    return hasPermission(userPermissions, item.permission);\n  });\n};\n\n// 处理未授权操作\nexport const handleUnauthorized = (action = '访问该功能') => {\n  message.error(`您没有权限${action}，请联系管理员`);\n};\n\n// 检查并处理权限（用于 UI 操作）\nexport const checkAndHandlePermission = (userPermissions, permission, action) => {\n  if (!hasPermission(userPermissions, permission)) {\n    handleUnauthorized(action);\n    return false;\n  }\n  return true;\n};", "map": {"version": 3, "names": ["message", "Permissions", "VIEW_CLASS", "CREATE_CLASS", "EDIT_CLASS", "DELETE_CLASS", "MANAGE_CLASS", "VIEW_STUDENT", "CREATE_STUDENT", "EDIT_STUDENT", "DELETE_STUDENT", "MANAGE_STUDENT", "VIEW_TEACHER", "CREATE_TEACHER", "EDIT_TEACHER", "DELETE_TEACHER", "MANAGE_TEACHER", "VIEW_PARENT", "CREATE_PARENT", "EDIT_PARENT", "DELETE_PARENT", "MANAGE_PARENT", "hasPermission", "userPermissions", "requiredPermission", "includes", "hasAnyPermission", "requiredPermissions", "length", "some", "permission", "hasManagePermission", "resourceType", "filterMenuItems", "menuItems", "filter", "item", "handleUnauthorized", "action", "error", "checkAndHandlePermission"], "sources": ["D:/pythonproject/checkingsys/frontend/src/utils/permissionUtils.js"], "sourcesContent": ["// 权限检查工具函数\r\nimport { message } from 'antd';\r\n\r\n// 权限代码常量\r\nexport const Permissions = {\r\n    // 班级相关权限\r\n    VIEW_CLASS: 'view_class',\r\n    CREATE_CLASS: 'create_class',\r\n    EDIT_CLASS: 'edit_class',\r\n    DELETE_CLASS: 'delete_class',\r\n    MANAGE_CLASS: 'manage_class',\r\n    \r\n    // 学生相关权限\r\n    VIEW_STUDENT: 'view_student',\r\n    CREATE_STUDENT: 'create_student',\r\n    EDIT_STUDENT: 'edit_student',\r\n    DELETE_STUDENT: 'delete_student',\r\n    MANAGE_STUDENT: 'manage_student',\r\n    \r\n    // 教师相关权限\r\n    VIEW_TEACHER: 'view_teacher',\r\n    CREATE_TEACHER: 'create_teacher',\r\n    EDIT_TEACHER: 'edit_teacher',\r\n    DELETE_TEACHER: 'delete_teacher',\r\n    MANAGE_TEACHER: 'manage_teacher',\r\n    \r\n    // 家长相关权限\r\n    VIEW_PARENT: 'view_parent',\r\n    CREATE_PARENT: 'create_parent',\r\n    EDIT_PARENT: 'edit_parent',\r\n    DELETE_PARENT: 'delete_parent',\r\n    MANAGE_PARENT: 'manage_parent'\r\n};\r\n\r\n// 权限检查函数\r\nexport const hasPermission = (userPermissions, requiredPermission) => {\r\n    if (!userPermissions || !requiredPermission) {\r\n        return false;\r\n    }\r\n    return userPermissions.includes(requiredPermission);\r\n};\r\n\r\n// 批量权限检查函数\r\nexport const hasAnyPermission = (userPermissions, requiredPermissions) => {\r\n    if (!userPermissions || !requiredPermissions || requiredPermissions.length === 0) {\r\n        return false;\r\n    }\r\n    return requiredPermissions.some(permission => hasPermission(userPermissions, permission));\r\n};\r\n\r\n// 检查是否有管理权限\r\nexport const hasManagePermission = (userPermissions, resourceType) => {\r\n    switch (resourceType) {\r\n        case 'class':\r\n            return hasAnyPermission(userPermissions, [\r\n                Permissions.MANAGE_CLASS,\r\n                Permissions.CREATE_CLASS,\r\n                Permissions.EDIT_CLASS,\r\n                Permissions.DELETE_CLASS\r\n            ]);\r\n        case 'student':\r\n            return hasAnyPermission(userPermissions, [\r\n                Permissions.MANAGE_STUDENT,\r\n                Permissions.CREATE_STUDENT,\r\n                Permissions.EDIT_STUDENT,\r\n                Permissions.DELETE_STUDENT\r\n            ]);\r\n        case 'teacher':\r\n            return hasAnyPermission(userPermissions, [\r\n                Permissions.MANAGE_TEACHER,\r\n                Permissions.CREATE_TEACHER,\r\n                Permissions.EDIT_TEACHER,\r\n                Permissions.DELETE_TEACHER\r\n            ]);\r\n        case 'parent':\r\n            return hasAnyPermission(userPermissions, [\r\n                Permissions.MANAGE_PARENT,\r\n                Permissions.CREATE_PARENT,\r\n                Permissions.EDIT_PARENT,\r\n                Permissions.DELETE_PARENT\r\n            ]);\r\n        default:\r\n            return false;\r\n    }\r\n};\r\n\r\n// 过滤菜单项（根据权限）\r\nexport const filterMenuItems = (menuItems, userPermissions) => {\r\n    return menuItems.filter(item => {\r\n        // 如果菜单项没有设置权限要求，默认显示\r\n        if (!item.permission) {\r\n            return true;\r\n        }\r\n        // 检查权限\r\n        return hasPermission(userPermissions, item.permission);\r\n    });\r\n};\r\n\r\n// 处理未授权操作\r\nexport const handleUnauthorized = (action = '访问该功能') => {\r\n    message.error(`您没有权限${action}，请联系管理员`);\r\n};\r\n\r\n// 检查并处理权限（用于 UI 操作）\r\nexport const checkAndHandlePermission = (userPermissions, permission, action) => {\r\n    if (!hasPermission(userPermissions, permission)) {\r\n        handleUnauthorized(action);\r\n        return false;\r\n    }\r\n    return true;\r\n};\r\n"], "mappings": "AAAA;AACA,SAASA,OAAO,QAAQ,MAAM;;AAE9B;AACA,OAAO,MAAMC,WAAW,GAAG;EACvB;EACAC,UAAU,EAAE,YAAY;EACxBC,YAAY,EAAE,cAAc;EAC5BC,UAAU,EAAE,YAAY;EACxBC,YAAY,EAAE,cAAc;EAC5BC,YAAY,EAAE,cAAc;EAE5B;EACAC,YAAY,EAAE,cAAc;EAC5BC,cAAc,EAAE,gBAAgB;EAChCC,YAAY,EAAE,cAAc;EAC5BC,cAAc,EAAE,gBAAgB;EAChCC,cAAc,EAAE,gBAAgB;EAEhC;EACAC,YAAY,EAAE,cAAc;EAC5BC,cAAc,EAAE,gBAAgB;EAChCC,YAAY,EAAE,cAAc;EAC5BC,cAAc,EAAE,gBAAgB;EAChCC,cAAc,EAAE,gBAAgB;EAEhC;EACAC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE;AACnB,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAGA,CAACC,eAAe,EAAEC,kBAAkB,KAAK;EAClE,IAAI,CAACD,eAAe,IAAI,CAACC,kBAAkB,EAAE;IACzC,OAAO,KAAK;EAChB;EACA,OAAOD,eAAe,CAACE,QAAQ,CAACD,kBAAkB,CAAC;AACvD,CAAC;;AAED;AACA,OAAO,MAAME,gBAAgB,GAAGA,CAACH,eAAe,EAAEI,mBAAmB,KAAK;EACtE,IAAI,CAACJ,eAAe,IAAI,CAACI,mBAAmB,IAAIA,mBAAmB,CAACC,MAAM,KAAK,CAAC,EAAE;IAC9E,OAAO,KAAK;EAChB;EACA,OAAOD,mBAAmB,CAACE,IAAI,CAACC,UAAU,IAAIR,aAAa,CAACC,eAAe,EAAEO,UAAU,CAAC,CAAC;AAC7F,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAACR,eAAe,EAAES,YAAY,KAAK;EAClE,QAAQA,YAAY;IAChB,KAAK,OAAO;MACR,OAAON,gBAAgB,CAACH,eAAe,EAAE,CACrCtB,WAAW,CAACK,YAAY,EACxBL,WAAW,CAACE,YAAY,EACxBF,WAAW,CAACG,UAAU,EACtBH,WAAW,CAACI,YAAY,CAC3B,CAAC;IACN,KAAK,SAAS;MACV,OAAOqB,gBAAgB,CAACH,eAAe,EAAE,CACrCtB,WAAW,CAACU,cAAc,EAC1BV,WAAW,CAACO,cAAc,EAC1BP,WAAW,CAACQ,YAAY,EACxBR,WAAW,CAACS,cAAc,CAC7B,CAAC;IACN,KAAK,SAAS;MACV,OAAOgB,gBAAgB,CAACH,eAAe,EAAE,CACrCtB,WAAW,CAACe,cAAc,EAC1Bf,WAAW,CAACY,cAAc,EAC1BZ,WAAW,CAACa,YAAY,EACxBb,WAAW,CAACc,cAAc,CAC7B,CAAC;IACN,KAAK,QAAQ;MACT,OAAOW,gBAAgB,CAACH,eAAe,EAAE,CACrCtB,WAAW,CAACoB,aAAa,EACzBpB,WAAW,CAACiB,aAAa,EACzBjB,WAAW,CAACkB,WAAW,EACvBlB,WAAW,CAACmB,aAAa,CAC5B,CAAC;IACN;MACI,OAAO,KAAK;EACpB;AACJ,CAAC;;AAED;AACA,OAAO,MAAMa,eAAe,GAAGA,CAACC,SAAS,EAAEX,eAAe,KAAK;EAC3D,OAAOW,SAAS,CAACC,MAAM,CAACC,IAAI,IAAI;IAC5B;IACA,IAAI,CAACA,IAAI,CAACN,UAAU,EAAE;MAClB,OAAO,IAAI;IACf;IACA;IACA,OAAOR,aAAa,CAACC,eAAe,EAAEa,IAAI,CAACN,UAAU,CAAC;EAC1D,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMO,kBAAkB,GAAGA,CAACC,MAAM,GAAG,OAAO,KAAK;EACpDtC,OAAO,CAACuC,KAAK,CAAC,QAAQD,MAAM,SAAS,CAAC;AAC1C,CAAC;;AAED;AACA,OAAO,MAAME,wBAAwB,GAAGA,CAACjB,eAAe,EAAEO,UAAU,EAAEQ,MAAM,KAAK;EAC7E,IAAI,CAAChB,aAAa,CAACC,eAAe,EAAEO,UAAU,CAAC,EAAE;IAC7CO,kBAAkB,CAACC,MAAM,CAAC;IAC1B,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}