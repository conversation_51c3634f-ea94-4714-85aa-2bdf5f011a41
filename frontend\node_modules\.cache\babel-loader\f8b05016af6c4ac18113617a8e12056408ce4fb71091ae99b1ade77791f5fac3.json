{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { AggregationColor } from '../../color-picker/color';\nimport { isBright } from '../../color-picker/components/ColorPresets';\nimport { getLineHeight, mergeToken } from '../../theme/internal';\nimport { PresetColors } from '../../theme/interface';\nimport getAlphaColor from '../../theme/util/getAlphaColor';\nexport const prepareToken = token => {\n  const {\n    paddingInline,\n    onlyIconSize\n  } = token;\n  const buttonToken = mergeToken(token, {\n    buttonPaddingHorizontal: paddingInline,\n    buttonPaddingVertical: 0,\n    buttonIconOnlyFontSize: onlyIconSize\n  });\n  return buttonToken;\n};\nexport const prepareComponentToken = token => {\n  var _a, _b, _c, _d, _e, _f;\n  const contentFontSize = (_a = token.contentFontSize) !== null && _a !== void 0 ? _a : token.fontSize;\n  const contentFontSizeSM = (_b = token.contentFontSizeSM) !== null && _b !== void 0 ? _b : token.fontSize;\n  const contentFontSizeLG = (_c = token.contentFontSizeLG) !== null && _c !== void 0 ? _c : token.fontSizeLG;\n  const contentLineHeight = (_d = token.contentLineHeight) !== null && _d !== void 0 ? _d : getLineHeight(contentFontSize);\n  const contentLineHeightSM = (_e = token.contentLineHeightSM) !== null && _e !== void 0 ? _e : getLineHeight(contentFontSizeSM);\n  const contentLineHeightLG = (_f = token.contentLineHeightLG) !== null && _f !== void 0 ? _f : getLineHeight(contentFontSizeLG);\n  const solidTextColor = isBright(new AggregationColor(token.colorBgSolid), '#fff') ? '#000' : '#fff';\n  const shadowColorTokens = PresetColors.reduce((prev, colorKey) => Object.assign(Object.assign({}, prev), {\n    [`${colorKey}ShadowColor`]: `0 ${unit(token.controlOutlineWidth)} 0 ${getAlphaColor(token[`${colorKey}1`], token.colorBgContainer)}`\n  }), {});\n  return Object.assign(Object.assign({}, shadowColorTokens), {\n    fontWeight: 400,\n    defaultShadow: `0 ${token.controlOutlineWidth}px 0 ${token.controlTmpOutline}`,\n    primaryShadow: `0 ${token.controlOutlineWidth}px 0 ${token.controlOutline}`,\n    dangerShadow: `0 ${token.controlOutlineWidth}px 0 ${token.colorErrorOutline}`,\n    primaryColor: token.colorTextLightSolid,\n    dangerColor: token.colorTextLightSolid,\n    borderColorDisabled: token.colorBorder,\n    defaultGhostColor: token.colorBgContainer,\n    ghostBg: 'transparent',\n    defaultGhostBorderColor: token.colorBgContainer,\n    paddingInline: token.paddingContentHorizontal - token.lineWidth,\n    paddingInlineLG: token.paddingContentHorizontal - token.lineWidth,\n    paddingInlineSM: 8 - token.lineWidth,\n    onlyIconSize: 'inherit',\n    onlyIconSizeSM: 'inherit',\n    onlyIconSizeLG: 'inherit',\n    groupBorderColor: token.colorPrimaryHover,\n    linkHoverBg: 'transparent',\n    textTextColor: token.colorText,\n    textTextHoverColor: token.colorText,\n    textTextActiveColor: token.colorText,\n    textHoverBg: token.colorFillTertiary,\n    defaultColor: token.colorText,\n    defaultBg: token.colorBgContainer,\n    defaultBorderColor: token.colorBorder,\n    defaultBorderColorDisabled: token.colorBorder,\n    defaultHoverBg: token.colorBgContainer,\n    defaultHoverColor: token.colorPrimaryHover,\n    defaultHoverBorderColor: token.colorPrimaryHover,\n    defaultActiveBg: token.colorBgContainer,\n    defaultActiveColor: token.colorPrimaryActive,\n    defaultActiveBorderColor: token.colorPrimaryActive,\n    solidTextColor,\n    contentFontSize,\n    contentFontSizeSM,\n    contentFontSizeLG,\n    contentLineHeight,\n    contentLineHeightSM,\n    contentLineHeightLG,\n    paddingBlock: Math.max((token.controlHeight - contentFontSize * contentLineHeight) / 2 - token.lineWidth, 0),\n    paddingBlockSM: Math.max((token.controlHeightSM - contentFontSizeSM * contentLineHeightSM) / 2 - token.lineWidth, 0),\n    paddingBlockLG: Math.max((token.controlHeightLG - contentFontSizeLG * contentLineHeightLG) / 2 - token.lineWidth, 0)\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}