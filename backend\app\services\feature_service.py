"""
特性开关服务
用于控制家长端功能的启用和禁用
为第三阶段安全实施做准备
"""
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
import json
import logging

from ..models.system_settings import SystemSettings

logger = logging.getLogger(__name__)

class FeatureService:
    """特性开关服务"""
    
    # 默认特性配置
    DEFAULT_FEATURES = {
        "parent_basic": {
            "enabled": True,
            "description": "家长端基础功能",
            "features": {
                "bound_students": True,
                "homework_list": True,
                "student_profile": True
            }
        },
        "parent_enhanced": {
            "enabled": True,
            "description": "家长端增强功能",
            "features": {
                "homework_detail": True,
                "basic_statistics": True,
                "recent_performance": True
            }
        },
        "parent_advanced": {
            "enabled": False,
            "description": "家长端高级分析功能",
            "features": {
                "advanced_analysis": False,
                "learning_trends": False,
                "wrong_questions": False,
                "grade_trends": False,
                "ai_insights": False
            }
        }
    }
    
    def __init__(self, db: Session):
        self.db = db
    
    def is_feature_enabled(self, feature_name: str) -> bool:
        """检查特性是否启用"""
        try:
            # 从数据库获取特性配置
            features = self._get_features_config()
            
            # 解析特性路径 (如: parent_advanced.learning_trends)
            if '.' in feature_name:
                category, feature = feature_name.split('.', 1)
                return (
                    features.get(category, {}).get("enabled", False) and
                    features.get(category, {}).get("features", {}).get(feature, False)
                )
            else:
                # 检查类别是否启用
                return features.get(feature_name, {}).get("enabled", False)
                
        except Exception as e:
            logger.error(f"检查特性状态失败: {str(e)}")
            # 默认返回False，确保安全
            return False
    
    def get_enabled_features(self) -> Dict[str, Any]:
        """获取所有启用的特性"""
        try:
            features = self._get_features_config()
            enabled_features = {}
            
            for category, config in features.items():
                if config.get("enabled", False):
                    enabled_features[category] = {
                        "description": config.get("description", ""),
                        "features": {}
                    }
                    
                    # 获取启用的子特性
                    for feature, enabled in config.get("features", {}).items():
                        if enabled:
                            enabled_features[category]["features"][feature] = True
            
            return enabled_features
            
        except Exception as e:
            logger.error(f"获取启用特性失败: {str(e)}")
            return {}
    
    def enable_feature(self, feature_name: str) -> bool:
        """启用特性"""
        try:
            features = self._get_features_config()
            
            if '.' in feature_name:
                category, feature = feature_name.split('.', 1)
                if category not in features:
                    features[category] = self.DEFAULT_FEATURES.get(category, {})
                
                if "features" not in features[category]:
                    features[category]["features"] = {}
                
                features[category]["features"][feature] = True
            else:
                if feature_name not in features:
                    features[feature_name] = self.DEFAULT_FEATURES.get(feature_name, {})
                
                features[feature_name]["enabled"] = True
            
            self._save_features_config(features)
            logger.info(f"特性 {feature_name} 已启用")
            return True
            
        except Exception as e:
            logger.error(f"启用特性失败: {str(e)}")
            return False
    
    def disable_feature(self, feature_name: str) -> bool:
        """禁用特性"""
        try:
            features = self._get_features_config()
            
            if '.' in feature_name:
                category, feature = feature_name.split('.', 1)
                if category in features and "features" in features[category]:
                    features[category]["features"][feature] = False
            else:
                if feature_name in features:
                    features[feature_name]["enabled"] = False
            
            self._save_features_config(features)
            logger.info(f"特性 {feature_name} 已禁用")
            return True
            
        except Exception as e:
            logger.error(f"禁用特性失败: {str(e)}")
            return False
    
    def toggle_feature(self, feature_name: str) -> bool:
        """切换特性状态"""
        if self.is_feature_enabled(feature_name):
            return self.disable_feature(feature_name)
        else:
            return self.enable_feature(feature_name)
    
    def reset_features(self) -> bool:
        """重置特性配置为默认值"""
        try:
            self._save_features_config(self.DEFAULT_FEATURES)
            logger.info("特性配置已重置为默认值")
            return True
        except Exception as e:
            logger.error(f"重置特性配置失败: {str(e)}")
            return False
    
    def get_feature_status(self) -> Dict[str, Any]:
        """获取特性状态报告"""
        try:
            features = self._get_features_config()
            status = {
                "total_categories": len(features),
                "enabled_categories": 0,
                "categories": {}
            }
            
            for category, config in features.items():
                category_enabled = config.get("enabled", False)
                if category_enabled:
                    status["enabled_categories"] += 1
                
                feature_count = len(config.get("features", {}))
                enabled_feature_count = sum(
                    1 for enabled in config.get("features", {}).values() if enabled
                )
                
                status["categories"][category] = {
                    "enabled": category_enabled,
                    "description": config.get("description", ""),
                    "total_features": feature_count,
                    "enabled_features": enabled_feature_count,
                    "features": config.get("features", {})
                }
            
            return status
            
        except Exception as e:
            logger.error(f"获取特性状态失败: {str(e)}")
            return {}
    
    def _get_features_config(self) -> Dict[str, Any]:
        """从数据库获取特性配置"""
        try:
            # 尝试从系统设置表获取配置
            setting = self.db.query(SystemSettings).filter(
                SystemSettings.key == "parent_features"
            ).first()
            
            if setting and setting.value:
                try:
                    return json.loads(setting.value)
                except json.JSONDecodeError:
                    logger.warning("特性配置JSON解析失败，使用默认配置")
            
            # 如果没有配置，返回默认配置
            return self.DEFAULT_FEATURES.copy()
            
        except Exception as e:
            logger.error(f"获取特性配置失败: {str(e)}")
            return self.DEFAULT_FEATURES.copy()
    
    def _save_features_config(self, features: Dict[str, Any]) -> None:
        """保存特性配置到数据库"""
        try:
            # 查找现有设置
            setting = self.db.query(SystemSettings).filter(
                SystemSettings.key == "parent_features"
            ).first()
            
            features_json = json.dumps(features, ensure_ascii=False, indent=2)
            
            if setting:
                setting.value = features_json
            else:
                setting = SystemSettings(
                    key="parent_features",
                    value=features_json,
                    description="家长端特性开关配置"
                )
                self.db.add(setting)
            
            self.db.commit()
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"保存特性配置失败: {str(e)}")
            raise

# 装饰器：检查特性是否启用
def feature_required(feature_name: str):
    """特性检查装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 从参数中获取数据库会话
            db = None
            for arg in args:
                if isinstance(arg, Session):
                    db = arg
                    break
            
            # 从kwargs中查找数据库会话
            if not db:
                db = kwargs.get('db')
            
            if db:
                feature_service = FeatureService(db)
                if not feature_service.is_feature_enabled(feature_name):
                    from fastapi import HTTPException
                    raise HTTPException(
                        status_code=404, 
                        detail=f"功能 '{feature_name}' 未启用"
                    )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# 全局特性检查函数
def check_feature_enabled(feature_name: str, db: Session) -> bool:
    """检查特性是否启用"""
    feature_service = FeatureService(db)
    return feature_service.is_feature_enabled(feature_name)
