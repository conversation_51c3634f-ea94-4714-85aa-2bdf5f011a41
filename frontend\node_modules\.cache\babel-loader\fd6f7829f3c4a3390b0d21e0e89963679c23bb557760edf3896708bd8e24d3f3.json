{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nfunction getKey(data, index) {\n  var key = data.key;\n  var value;\n  if ('value' in data) {\n    value = data.value;\n  }\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  if (value !== undefined) {\n    return value;\n  }\n  return \"rc-index-key-\".concat(index);\n}\nexport function isValidCount(value) {\n  return typeof value !== 'undefined' && !Number.isNaN(value);\n}\nexport function fillFieldNames(fieldNames, childrenAsData) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    options = _ref.options,\n    groupLabel = _ref.groupLabel;\n  var mergedLabel = label || (childrenAsData ? 'children' : 'label');\n  return {\n    label: mergedLabel,\n    value: value || 'value',\n    options: options || 'options',\n    groupLabel: groupLabel || mergedLabel\n  };\n}\n\n/**\n * Flat options into flatten list.\n * We use `optionOnly` here is aim to avoid user use nested option group.\n * Here is simply set `key` to the index if not provided.\n */\nexport function flattenOptions(options) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    fieldNames = _ref2.fieldNames,\n    childrenAsData = _ref2.childrenAsData;\n  var flattenList = [];\n  var _fillFieldNames = fillFieldNames(fieldNames, false),\n    fieldLabel = _fillFieldNames.label,\n    fieldValue = _fillFieldNames.value,\n    fieldOptions = _fillFieldNames.options,\n    groupLabel = _fillFieldNames.groupLabel;\n  function dig(list, isGroupOption) {\n    if (!Array.isArray(list)) {\n      return;\n    }\n    list.forEach(function (data) {\n      if (isGroupOption || !(fieldOptions in data)) {\n        var value = data[fieldValue];\n\n        // Option\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          groupOption: isGroupOption,\n          data: data,\n          label: data[fieldLabel],\n          value: value\n        });\n      } else {\n        var grpLabel = data[groupLabel];\n        if (grpLabel === undefined && childrenAsData) {\n          grpLabel = data.label;\n        }\n\n        // Option Group\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          group: true,\n          data: data,\n          label: grpLabel\n        });\n        dig(data[fieldOptions], true);\n      }\n    });\n  }\n  dig(options, false);\n  return flattenList;\n}\n\n/**\n * Inject `props` into `option` for legacy usage\n */\nexport function injectPropsWithOption(option) {\n  var newOption = _objectSpread({}, option);\n  if (!('props' in newOption)) {\n    Object.defineProperty(newOption, 'props', {\n      get: function get() {\n        warning(false, 'Return type is option instead of Option instance. Please read value directly instead of reading from `props`.');\n        return newOption;\n      }\n    });\n  }\n  return newOption;\n}\nexport var getSeparatedContent = function getSeparatedContent(text, tokens, end) {\n  if (!tokens || !tokens.length) {\n    return null;\n  }\n  var match = false;\n  var separate = function separate(str, _ref3) {\n    var _ref4 = _toArray(_ref3),\n      token = _ref4[0],\n      restTokens = _ref4.slice(1);\n    if (!token) {\n      return [str];\n    }\n    var list = str.split(token);\n    match = match || list.length > 1;\n    return list.reduce(function (prevList, unitStr) {\n      return [].concat(_toConsumableArray(prevList), _toConsumableArray(separate(unitStr, restTokens)));\n    }, []).filter(Boolean);\n  };\n  var list = separate(text, tokens);\n  if (match) {\n    return typeof end !== 'undefined' ? list.slice(0, end) : list;\n  } else {\n    return null;\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}