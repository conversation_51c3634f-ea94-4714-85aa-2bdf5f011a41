"""
角色服务模块
提供统一的角色获取和管理功能
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import text
import logging

from ..models.user import User
from ..models.role import Role
from ..models.user_role import UserRole

logger = logging.getLogger(__name__)

class RoleService:
    """角色服务类"""
    
    @staticmethod
    def get_user_primary_role_name(user: User, db: Session) -> str:
        """
        获取用户的主要角色中文名称
        
        Args:
            user: 用户对象
            db: 数据库会话
            
        Returns:
            str: 角色的中文名称
        """
        try:
            # 1. 首先尝试从用户的primary_role_id获取
            if hasattr(user, 'primary_role_id') and user.primary_role_id:
                role = db.query(Role).filter(Role.id == user.primary_role_id).first()
                if role:
                    logger.debug(f"用户 {user.username} 从primary_role_id获取角色: {role.name}")
                    return role.name
            
            # 2. 从user_roles表中获取用户的角色（取第一个作为主要角色）
            user_role = db.query(UserRole).filter(UserRole.user_id == user.id).first()
            if user_role and user_role.role:
                logger.debug(f"用户 {user.username} 从user_roles获取角色: {user_role.role.name}")
                return user_role.role.name
            
            # 3. 如果用户表中有role字段且已经是中文名称，直接使用
            if user.role:
                # 检查是否已经是roles表中的中文名称
                role = db.query(Role).filter(Role.name == user.role).first()
                if role:
                    logger.debug(f"用户 {user.username} 从user.role获取角色: {user.role}")
                    return user.role
                
                # 如果是英文代码，尝试转换为中文
                role = db.query(Role).filter(Role.code == user.role).first()
                if role:
                    logger.debug(f"用户 {user.username} 从角色代码转换: {user.role} -> {role.name}")
                    return role.name
            
            # 4. 根据旧系统的is_admin和is_teacher字段推断角色
            if user.is_admin and user.is_teacher:
                role = db.query(Role).filter(Role.code == "super_admin").first()
                if role:
                    logger.debug(f"用户 {user.username} 推断为超级管理员")
                    return role.name
                return "超级管理员"  # 兜底
                
            elif user.is_admin:
                role = db.query(Role).filter(Role.code == "school_admin").first()
                if role:
                    logger.debug(f"用户 {user.username} 推断为学校管理员")
                    return role.name
                return "学校管理员"  # 兜底
                
            elif user.is_teacher:
                role = db.query(Role).filter(Role.code == "teacher").first()
                if role:
                    logger.debug(f"用户 {user.username} 推断为教师")
                    return role.name
                return "教师"  # 兜底
            
            else:
                # 默认为学生
                role = db.query(Role).filter(Role.code == "student").first()
                if role:
                    logger.debug(f"用户 {user.username} 默认为学生")
                    return role.name
                return "学生"  # 兜底
                
        except Exception as e:
            logger.error(f"获取用户 {user.username} 角色时出错: {str(e)}")
            # 出错时的兜底逻辑
            if user.is_admin and user.is_teacher:
                return "超级管理员"
            elif user.is_admin:
                return "学校管理员"
            elif user.is_teacher:
                return "教师"
            else:
                return "学生"
    
    @staticmethod
    def get_user_all_roles(user: User, db: Session) -> List[Dict[str, Any]]:
        """
        获取用户的所有角色信息
        
        Args:
            user: 用户对象
            db: 数据库会话
            
        Returns:
            List[Dict]: 角色信息列表
        """
        try:
            roles = []
            
            # 从user_roles表中获取所有角色
            user_roles = db.query(UserRole).filter(UserRole.user_id == user.id).all()
            
            for user_role in user_roles:
                if user_role.role:
                    role_info = {
                        "id": user_role.role.id,
                        "name": user_role.role.name,
                        "code": user_role.role.code,
                        "description": user_role.role.description,
                        "level": user_role.role.level,
                        "school_id": user_role.school_id,
                        "grade_id": user_role.grade_id,
                        "subject_id": user_role.subject_id,
                        "class_id": user_role.class_id
                    }
                    roles.append(role_info)
            
            # 如果没有找到角色，添加默认角色
            if not roles:
                primary_role_name = RoleService.get_user_primary_role_name(user, db)
                role = db.query(Role).filter(Role.name == primary_role_name).first()
                if role:
                    role_info = {
                        "id": role.id,
                        "name": role.name,
                        "code": role.code,
                        "description": role.description,
                        "level": role.level,
                        "school_id": user.school_id,
                        "grade_id": None,
                        "subject_id": None,
                        "class_id": user.class_id
                    }
                    roles.append(role_info)
            
            return roles
            
        except Exception as e:
            logger.error(f"获取用户 {user.username} 所有角色时出错: {str(e)}")
            return []
    
    @staticmethod
    def update_user_role_in_db(user: User, db: Session) -> bool:
        """
        更新用户表中的role字段为正确的中文名称
        
        Args:
            user: 用户对象
            db: 数据库会话
            
        Returns:
            bool: 是否更新成功
        """
        try:
            correct_role_name = RoleService.get_user_primary_role_name(user, db)
            
            if user.role != correct_role_name:
                logger.info(f"更新用户 {user.username} 角色: {user.role} -> {correct_role_name}")
                user.role = correct_role_name
                db.commit()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"更新用户 {user.username} 角色时出错: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def get_role_by_code(code: str, db: Session) -> Optional[Role]:
        """
        根据角色代码获取角色对象
        
        Args:
            code: 角色代码
            db: 数据库会话
            
        Returns:
            Optional[Role]: 角色对象
        """
        try:
            return db.query(Role).filter(Role.code == code).first()
        except Exception as e:
            logger.error(f"根据代码 {code} 获取角色时出错: {str(e)}")
            return None
    
    @staticmethod
    def get_role_by_name(name: str, db: Session) -> Optional[Role]:
        """
        根据角色名称获取角色对象
        
        Args:
            name: 角色名称
            db: 数据库会话
            
        Returns:
            Optional[Role]: 角色对象
        """
        try:
            return db.query(Role).filter(Role.name == name).first()
        except Exception as e:
            logger.error(f"根据名称 {name} 获取角色时出错: {str(e)}")
            return None
