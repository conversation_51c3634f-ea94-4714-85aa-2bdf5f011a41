"""
用户服务模块
提供统一的用户创建和管理功能，确保users和user_roles表的数据一致性
"""

from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import text
import logging
from datetime import datetime

from ..models.user import User, normalize_role
from ..models.role import Role
from ..models.user_role import UserRole
from ..schemas.user import UserCreate

logger = logging.getLogger(__name__)

class UserService:
    """用户服务类"""
    
    @staticmethod
    def create_user_with_role(
        user_data: UserCreate,
        db: Session,
        hashed_password: str,
        role_name: Optional[str] = None,
        additional_role_info: Optional[Dict[str, Any]] = None
    ) -> User:
        """
        创建用户并自动在user_roles表中创建对应记录

        Args:
            user_data: 用户创建数据
            db: 数据库会话
            hashed_password: 已哈希的密码
            role_name: 指定的角色名称（如果不提供，会根据is_teacher等字段推断）
            additional_role_info: 额外的角色信息（如grade_id, subject_id等）

        Returns:
            User: 创建的用户对象
        """
        try:
            # 1. 确定用户角色
            if not role_name:
                if getattr(user_data, 'is_admin', False) and getattr(user_data, 'is_teacher', False):
                    role_name = "超级管理员"
                elif getattr(user_data, 'is_admin', False):
                    role_name = "学校管理员"
                elif getattr(user_data, 'is_teacher', False):
                    role_name = "教师"
                else:
                    role_name = "学生"

            # 标准化角色名称
            role_name = normalize_role(role_name) or role_name

            # 2. 创建用户记录
            new_user = User(
                username=user_data.username,
                email=user_data.email,
                hashed_password=hashed_password,
                full_name=user_data.full_name,
                phone=getattr(user_data, 'phone', None),
                is_teacher=getattr(user_data, 'is_teacher', False),
                is_admin=getattr(user_data, 'is_admin', False),
                role=role_name,
                school_id=getattr(user_data, 'school_id', None),
                class_id=getattr(user_data, 'class_id', None),
                subject=getattr(user_data, 'subject', None)
            )

            db.add(new_user)
            db.flush()  # 获取用户ID

            logger.info(f"创建用户成功: {user_data.username} (ID: {new_user.id})")

            # 3. 在user_roles表中创建对应记录
            UserService.create_user_role_record(
                user_id=new_user.id,
                role_name=role_name,
                school_id=getattr(user_data, 'school_id', None),
                db=db,
                additional_info=additional_role_info
            )

            db.commit()
            db.refresh(new_user)

            return new_user

        except Exception as e:
            logger.error(f"创建用户失败: {str(e)}")
            db.rollback()
            raise
    
    @staticmethod
    def create_user_role_record(
        user_id: int,
        role_name: str,
        school_id: Optional[int],
        db: Session,
        additional_info: Optional[Dict[str, Any]] = None
    ) -> UserRole:
        """
        在user_roles表中创建用户角色记录
        
        Args:
            user_id: 用户ID
            role_name: 角色名称
            school_id: 学校ID
            db: 数据库会话
            additional_info: 额外信息（grade_id, subject_id, class_id等）
            
        Returns:
            UserRole: 创建的用户角色记录
        """
        try:
            # 查找角色ID
            role = db.query(Role).filter(
                (Role.name == role_name) | (Role.code == role_name)
            ).first()
            
            if not role:
                logger.warning(f"未找到角色: {role_name}，使用默认学生角色")
                role = db.query(Role).filter(Role.code == "student").first()
                if not role:
                    raise ValueError(f"无法找到角色: {role_name} 且默认学生角色也不存在")
            
            # 准备额外信息
            additional_info = additional_info or {}
            
            # 创建用户角色记录
            user_role = UserRole(
                user_id=user_id,
                role_id=role.id,
                school_id=school_id,
                grade_id=additional_info.get('grade_id'),
                subject_id=additional_info.get('subject_id'),
                class_id=additional_info.get('class_id'),
                created_at=datetime.now()
            )
            
            db.add(user_role)
            db.flush()
            
            logger.info(f"创建用户角色记录成功: 用户ID {user_id} -> 角色 {role.name} (ID: {role.id})")
            
            return user_role
            
        except Exception as e:
            logger.error(f"创建用户角色记录失败: {str(e)}")
            raise
    
    @staticmethod
    def sync_existing_user_to_roles(user_id: int, db: Session) -> bool:
        """
        将现有用户同步到user_roles表
        
        Args:
            user_id: 用户ID
            db: 数据库会话
            
        Returns:
            bool: 是否同步成功
        """
        try:
            # 检查用户是否已在user_roles表中
            existing_role = db.query(UserRole).filter(UserRole.user_id == user_id).first()
            if existing_role:
                logger.info(f"用户 {user_id} 已在user_roles表中，跳过同步")
                return True
            
            # 获取用户信息
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                logger.error(f"用户 {user_id} 不存在")
                return False
            
            # 确定角色名称
            role_name = user.role
            if not role_name:
                if user.is_admin and user.is_teacher:
                    role_name = "超级管理员"
                elif user.is_admin:
                    role_name = "学校管理员"
                elif user.is_teacher:
                    role_name = "教师"
                else:
                    role_name = "学生"
            
            # 创建用户角色记录
            UserService.create_user_role_record(
                user_id=user.id,
                role_name=role_name,
                school_id=user.school_id,
                db=db,
                additional_info={'class_id': user.class_id}
            )
            
            db.commit()
            logger.info(f"用户 {user.username} (ID: {user_id}) 同步到user_roles表成功")
            
            return True
            
        except Exception as e:
            logger.error(f"同步用户 {user_id} 到user_roles表失败: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def batch_sync_users_to_roles(db: Session) -> Dict[str, int]:
        """
        批量同步所有缺失的用户到user_roles表
        
        Args:
            db: 数据库会话
            
        Returns:
            Dict[str, int]: 同步结果统计
        """
        try:
            # 查找不在user_roles表中的用户
            missing_users = db.execute(text("""
                SELECT u.id, u.username, u.role, u.is_admin, u.is_teacher, u.school_id, u.class_id
                FROM users u
                LEFT JOIN user_roles ur ON u.id = ur.user_id
                WHERE ur.user_id IS NULL
                ORDER BY u.id
            """)).fetchall()
            
            total_users = len(missing_users)
            synced_count = 0
            failed_count = 0
            
            logger.info(f"开始批量同步 {total_users} 个用户到user_roles表")
            
            for user_row in missing_users:
                user_id, username, user_role, is_admin, is_teacher, school_id, class_id = user_row
                
                try:
                    # 确定角色名称
                    role_name = user_role
                    if not role_name:
                        if is_admin and is_teacher:
                            role_name = "超级管理员"
                        elif is_admin:
                            role_name = "学校管理员"
                        elif is_teacher:
                            role_name = "教师"
                        else:
                            role_name = "学生"
                    
                    # 创建用户角色记录
                    UserService.create_user_role_record(
                        user_id=user_id,
                        role_name=role_name,
                        school_id=school_id,
                        db=db,
                        additional_info={'class_id': class_id}
                    )
                    
                    synced_count += 1
                    logger.debug(f"同步用户 {username} (ID: {user_id}) 成功")
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"同步用户 {username} (ID: {user_id}) 失败: {str(e)}")
            
            db.commit()
            
            result = {
                'total': total_users,
                'synced': synced_count,
                'failed': failed_count
            }
            
            logger.info(f"批量同步完成: 总计 {total_users}, 成功 {synced_count}, 失败 {failed_count}")
            
            return result
            
        except Exception as e:
            logger.error(f"批量同步用户失败: {str(e)}")
            db.rollback()
            raise
    
    @staticmethod
    def update_user_role(
        user_id: int,
        new_role_name: str,
        db: Session,
        additional_info: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        更新用户角色
        
        Args:
            user_id: 用户ID
            new_role_name: 新角色名称
            db: 数据库会话
            additional_info: 额外信息
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 更新users表中的role字段
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                logger.error(f"用户 {user_id} 不存在")
                return False
            
            user.role = new_role_name
            
            # 更新user_roles表中的记录
            user_role = db.query(UserRole).filter(UserRole.user_id == user_id).first()
            
            # 查找新角色ID
            role = db.query(Role).filter(
                (Role.name == new_role_name) | (Role.code == new_role_name)
            ).first()
            
            if not role:
                logger.error(f"未找到角色: {new_role_name}")
                return False
            
            if user_role:
                # 更新现有记录
                user_role.role_id = role.id
                if additional_info:
                    user_role.grade_id = additional_info.get('grade_id', user_role.grade_id)
                    user_role.subject_id = additional_info.get('subject_id', user_role.subject_id)
                    user_role.class_id = additional_info.get('class_id', user_role.class_id)
            else:
                # 创建新记录
                UserService.create_user_role_record(
                    user_id=user_id,
                    role_name=new_role_name,
                    school_id=user.school_id,
                    db=db,
                    additional_info=additional_info
                )
            
            db.commit()
            logger.info(f"更新用户 {user.username} (ID: {user_id}) 角色为 {new_role_name} 成功")
            
            return True
            
        except Exception as e:
            logger.error(f"更新用户 {user_id} 角色失败: {str(e)}")
            db.rollback()
            return False
