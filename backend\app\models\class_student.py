from sqlalchemy import <PERSON>umn, <PERSON>teger, ForeignKey
from sqlalchemy.orm import relationship
from app.database import Base

class ClassStudent(Base):
    __tablename__ = "class_students"
    
    id = Column(Integer, primary_key=True, index=True)
    class_id = Column(Integer, ForeignKey("classes.id"))
    student_id = Column(Integer, ForeignKey("users.id"))
    
    # 关系
    student = relationship("User", back_populates="class_memberships")
    class_info = relationship("Class", back_populates="student_memberships")
