import axios from 'axios';

// 动态获取API基础URL
const getBaseURL = () => {
  // 检查是否强制使用本地API（开发测试用）
  const forceLocal = localStorage.getItem('FORCE_LOCAL_API') === 'true';
  if (forceLocal) {
    console.log('🔧 强制使用本地API');
    return 'http://localhost:8083/api';
  }

  // 临时强制使用本地API（开发调试）
  if (window.location.hostname === 'localhost') {
    console.log('🔧 开发环境强制使用本地API');
    return 'http://localhost:8083/api';
  }

  // 如果设置了环境变量，使用环境变量
  if (process.env.REACT_APP_API_URL) {
    console.log('🔧 使用环境变量API URL:', process.env.REACT_APP_API_URL);
    return process.env.REACT_APP_API_URL;
  }

  // 否则根据当前域名动态构建
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  const port = window.location.port;

  let apiUrl;

  // 如果是localhost或127.0.0.1，使用8083端口
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    apiUrl = `${protocol}//${hostname}:8083/api`;
  } else {
    // 外网访问时，使用当前页面的端口（通常是同一个端口）
    if (port) {
      apiUrl = `${protocol}//${hostname}:${port}/api`;
    } else {
      // 如果没有端口，使用默认端口
      apiUrl = `${protocol}//${hostname}/api`;
    }
  }

  console.log('🌐 动态构建API URL:', apiUrl);
  console.log('📍 当前页面信息:', { protocol, hostname, port });

  return apiUrl;
};

// 创建axios实例
const api = axios.create({
  baseURL: getBaseURL(),
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'  // 帮助某些服务器识别AJAX请求
  },
  // 添加更长的超时时间，处理可能的网络延迟
  timeout: 30000,  // 增加到30秒
  // 跨域请求设置
  withCredentials: false,  // 修改为false，避免CORS问题
  // 最大重定向次数
  maxRedirects: 5
});

console.log('API Base URL:', getBaseURL());

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
      console.log(`🔐 请求 ${config.method?.toUpperCase()} ${config.url} 携带token: ${token.substring(0, 15)}...`);
      console.log(`📍 完整URL: ${config.baseURL}${config.url}`);
    } else {
      console.warn(`⚠️ 请求 ${config.method?.toUpperCase()} ${config.url} 无token`);
      console.log(`📍 完整URL: ${config.baseURL}${config.url}`);
    }
    return config;
  },
  (error) => {
    console.error('❌ 请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API响应成功: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.status);

    // 如果是blob响应，返回原始数据
    if (response.config.responseType === 'blob') {
      return response.data;
    }

    return response.data;
  },
  (error) => {
    const method = error.config?.method?.toUpperCase() || 'UNKNOWN';
    const url = error.config?.url || 'unknown';
    const status = error.response?.status || 'no response';
    const data = error.response?.data || {};

    console.error(`❌ API响应错误: ${method} ${url}`, {
      status,
      data,
      message: error.message,
      baseURL: error.config?.baseURL
    });

    if (error.response && error.response.status === 401) {
      console.warn('🔐 认证失败，清除本地数据并重定向到登录页');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      // 避免在登录页再次跳转到登录页形成循环
      if (window.location.pathname !== '/login') {
        console.log('🔄 重定向到登录页');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error.response ? error.response.data : error);
  }
);

// 认证相关
export const login = (username, password) => {
  const formData = new URLSearchParams();
  formData.append('username', username);
  formData.append('password', password);

  // 使用api实例的baseURL，而不是硬编码URL
  return api.post('/login', formData, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
};

export const register = async (userData) => {
  try {
    console.log('调用register API, userData:', userData);
    const response = await api.post('/user', userData);
    console.log('注册成功:', response);
    return response;
  } catch (error) {
    console.error('注册失败:', error);
    throw error; // 重新抛出错误，让调用者处理
  }
};

export const getCurrentUser = () => {
  return api.get('/me')
    .then(userData => {
      console.log('获取到用户数据:', userData);
      
      // 确保本地存储的用户数据是最新的
      const token = localStorage.getItem('token');
      if (token && userData) {
        // 合并token到用户数据
        userData.access_token = token;
        
        // 更新localStorage中的用户数据
        localStorage.setItem('user', JSON.stringify(userData));
      }
      
      return userData;
    })
    .catch(error => {
      console.error('获取用户数据失败:', error);
      throw error;
    });
};

// 班级管理API
export const getClasses = async (params = {}) => {
  try {
    console.log('调用getClasses API', params ? `参数: ${JSON.stringify(params)}` : '');
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
    
    // 构建查询参数
    const queryParams = new URLSearchParams();
    if (params.school_id) queryParams.append('school_id', params.school_id);
    if (params.grade_id) queryParams.append('grade_id', params.grade_id);
    if (params.grade) queryParams.append('grade', params.grade);
    
    // 尝试多个可能的API端点
    let url = '/classes';  // 首先尝试直接的路径
    let response = null;
    
    try {
      console.log('尝试请求URL:', url);
      response = await api.get(url, {
        signal: controller.signal,
        params: params
      });
    } catch (firstError) {
      console.log('第一个API端点失败，尝试备用端点:', firstError);
      try {
        url = '/admin/classes';  // 尝试admin路径
        console.log('尝试请求URL:', url);
        response = await api.get(url, {
          signal: controller.signal,
          params: params
        });
      } catch (secondError) {
        console.log('第二个API端点也失败，尝试最终备用端点:', secondError);
        // 如果前两个都失败，尝试使用getClassesBySchool获取所有班级
        try {
          const adminResponse = await api.get('/admin', { signal: controller.signal });
          if (adminResponse && adminResponse.school_id) {
            console.log('获取到管理员学校ID:', adminResponse.school_id);
            url = `/admin/schools/${adminResponse.school_id}/classes`;
            console.log('尝试请求URL:', url);
            response = await api.get(url, { signal: controller.signal });
          } else {
            console.log('无法获取管理员学校ID，尝试默认学校ID');
            url = `/admin/schools/1/classes`;
            console.log('尝试请求URL:', url);
            response = await api.get(url, { signal: controller.signal });
          }
        } catch (finalError) {
          console.error('所有API端点都失败:', finalError);
          throw finalError;
        }
      }
    }
    
    clearTimeout(timeoutId);
    console.log('获取班级列表成功:', response);
    return response;
  } catch (error) {
    console.error('获取班级列表失败:', error);
    // 在此处不抛出错误，而是返回空数组，让调用方决定如何处理
    return [];
  }
};

export const getClass = (id, noCache = false) => {
  console.log('调用getClass API, id:', id, noCache ? '(禁用缓存)' : '(允许使用缓存)');
  
  // 构建请求参数，添加时间戳以避免缓存
  const params = noCache ? { _t: new Date().getTime() } : {};
  
  // 添加超时处理
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
    console.error(`获取班级 ${id} 详情请求超时`);
  }, 15000); // 15秒超时
  
  // 最大重试次数
  const maxRetries = 2;
  let retryCount = 0;
  
  // 使用真实API调用获取班级详情
  const tryFetchClass = () => {
    console.log(`尝试获取班级 ${id} 详情 (尝试 ${retryCount + 1}/${maxRetries + 1})`);
    
    // 添加详细的请求头配置
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
      'Cache-Control': noCache ? 'no-cache, no-store' : 'default'
    };
    
    return api.get(`/admin/classes/${id}`, { 
      params: params,
      signal: controller.signal,
      headers: headers
      // 不再需要单独设置withCredentials，因为已在api实例中设置
    })
    .then(response => {
      clearTimeout(timeoutId);
      console.log(`获取班级 ${id} 详情成功:`, response);
      return response;
    })
    .catch(error => {
      console.error(`获取班级 ${id} 详情失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error);
      console.error(`错误类型: ${error.name}, 状态码: ${error.response?.status}, 消息: ${error.message}`);
      
      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接');
      }
      
      // 如果还有重试次数，尝试重试
      if (retryCount < maxRetries) {
        retryCount++;
        console.log(`重试获取班级 ${id} 详情 (${retryCount}/${maxRetries})`);
        return tryFetchClass();
      }
      
      // 如果API调用失败，尝试备用API端点
      console.log(`尝试备用API端点获取班级 ${id} 详情`);
      return api.get(`/classes/${id}`, { 
        params: params,
        headers: headers,
        signal: controller.signal
        // 不再需要单独设置withCredentials，因为已在api实例中设置
      })
        .then(backupResponse => {
          console.log(`备用API成功获取班级 ${id} 详情:`, backupResponse);
          return backupResponse;
        })
        .catch(backupError => {
          console.error(`备用API获取班级 ${id} 详情失败:`, backupError);
          console.error(`备用API错误类型: ${backupError.name}, 状态码: ${backupError.response?.status}, 消息: ${backupError.message}`);
          
          // 尝试第三个API端点
          console.log(`尝试第三个API端点获取班级 ${id} 详情`);
          return api.get(`/admin/classes/${id}/students`, { 
            params: params,
            headers: headers,
            signal: controller.signal
            // 不再需要单独设置withCredentials，因为已在api实例中设置
          })
            .then(thirdResponse => {
              console.log(`第三个API端点成功获取班级 ${id} 详情:`, thirdResponse);
              return thirdResponse;
            })
            .catch(thirdError => {
              console.error(`第三个API端点获取班级 ${id} 详情失败:`, thirdError);
              
              // 所有API尝试都失败，不使用模拟数据，直接返回空数据
              console.log(`所有API尝试都失败，返回空数据`);
              
              // 返回空数据
              return {
                id: id,
                name: `班级 ${id}`,
                description: '',
                created_at: new Date().toISOString(),
                students: [],
                student_count: 0,
                grade: '',
                school_id: 1,
                error: true // 添加错误标记
              };
            });
        });
    });
  };
  
  return tryFetchClass();
};

export const createClass = (classData) => {
  console.log('调用createClass API, data:', classData);
  return api.post('/admin/classes', classData);
};

export const updateClass = (id, classData) => {
  return api.put(`/admin/classes/${id}`, classData);
};

// 批量更新班级
export const batchUpdateClasses = (classIds, classData) => {
  return api.put(`/admin/classes/batch`, { class_ids: classIds, ...classData });
};

export const deleteClass = (id) => {
  return api.delete(`/admin/classes/${id}`);
};

export const addStudentToClass = (classId, studentId) => {
  console.log(`开始添加学生(ID:${studentId})到班级(ID:${classId})`);
  
  // 检查studentId是否是临时ID
  const isTempId = typeof studentId === 'string' && studentId.startsWith('temp_');
  
  // 如果是临时ID，需要先查找真实ID
  if (isTempId) {
    console.log(`检测到临时ID ${studentId}，尝试查找真实ID`);
    
    // 从临时ID中提取用户名
    const username = studentId.replace('temp_', '');
    console.log(`从临时ID提取的用户名: ${username}`);
    
    // 使用用户名查找学生
    return api.get('/admin/users', { 
      params: { 
        search: username,
        role: 'student',
        limit: 10
      }
    })
    .then(response => {
      console.log(`查找用户名 ${username} 的结果:`, response);
      
      // 检查返回格式
      let userList = [];
      if (Array.isArray(response)) {
        userList = response;
      } else if (response && Array.isArray(response.items)) {
        userList = response.items;
      }
      
      // 查找完全匹配的用户
      const exactMatch = userList.find(u => u.username === username);
      let realStudentId = null;
      
      if (exactMatch) {
        console.log(`找到完全匹配的用户: ${username}, ID: ${exactMatch.id}`);
        realStudentId = exactMatch.id;
      } else if (userList.length > 0) {
        // 如果没有完全匹配但有结果，使用第一个
        console.log(`未找到完全匹配，使用第一个结果: ${userList[0].username}, ID: ${userList[0].id}`);
        realStudentId = userList[0].id;
      } else {
        // 没有找到，尝试直接创建学生
        console.log(`未找到用户 ${username}，尝试直接创建学生`);
        
        // 从临时ID中提取更多信息
        const tempParts = studentId.split('_');
        const studentData = {
          username: username,
          full_name: username, // 使用用户名作为姓名
          email: `${username}@example.com`,
          password: '123456'
        };
        
        // 创建学生
        return api.post('/admin/users', studentData)
          .then(newUser => {
            console.log(`成功创建学生: ${username}, ID: ${newUser.id}`);
            // 使用新创建的学生ID添加到班级
            return addStudentToClass(classId, newUser.id);
          })
          .catch(createError => {
            console.error(`创建学生 ${username} 失败:`, createError);
            
            // 如果创建失败，可能是因为用户已存在，再次尝试查找
            return api.get('/admin/users', { 
              params: { 
                search: username,
                role: 'student',
                limit: 10
              }
            })
            .then(secondResponse => {
              console.log(`二次查找用户名 ${username} 的结果:`, secondResponse);
              
              let secondUserList = [];
              if (Array.isArray(secondResponse)) {
                secondUserList = secondResponse;
              } else if (secondResponse && Array.isArray(secondResponse.items)) {
                secondUserList = secondResponse.items;
              }
              
              const secondExactMatch = secondUserList.find(u => u.username === username);
              
              if (secondExactMatch) {
                console.log(`二次查找找到完全匹配的用户: ${username}, ID: ${secondExactMatch.id}`);
                return addStudentToClass(classId, secondExactMatch.id);
              } else if (secondUserList.length > 0) {
                console.log(`二次查找未找到完全匹配，使用第一个结果: ${secondUserList[0].username}, ID: ${secondUserList[0].id}`);
                return addStudentToClass(classId, secondUserList[0].id);
              } else {
                console.log(`二次查找仍未找到用户 ${username}，返回部分成功状态`);
                return {
                  id: studentId,
                  status: 'partial_success', 
                  message: '无法找到或创建学生，请手动添加'
                };
              }
            })
            .catch(secondError => {
              console.error(`二次查找用户 ${username} 失败:`, secondError);
              return {
                id: studentId,
                status: 'partial_success', 
                message: '查找和创建学生都失败，请手动添加'
              };
            });
          });
      }
      
      // 使用找到的真实ID添加学生到班级
      console.log(`使用真实ID ${realStudentId} 添加学生到班级 ${classId}`);
      return addStudentToClass(classId, realStudentId);
    })
    .catch(error => {
      console.error(`查找用户 ${username} 失败:`, error);
      return {
        id: studentId,
        status: 'partial_success', 
        message: '查找学生信息失败，请刷新页面后重试'
      };
    });
  }
  
  // 添加请求超时处理
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
    console.error('添加学生到班级请求超时');
  }, 10000); // 10秒超时
  
  return api.post(`/admin/classes/${classId}/students`, { student_id: studentId }, {
    signal: controller.signal
  })
  .then(response => {
    clearTimeout(timeoutId);
    console.log(`成功添加学生(ID:${studentId})到班级(ID:${classId})，响应:`, response);
    return {
      ...response,
      status: 'success',
      message: '学生添加成功'
    };
  })
  .catch(error => {
    clearTimeout(timeoutId);
    console.error(`添加学生(ID:${studentId})到班级(ID:${classId})失败:`, error);
    console.error(`错误详情:`, error.response?.data || error.message);
    
    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
      // 超时错误，返回部分成功状态而不是抛出异常
      console.log('请求超时，返回部分成功状态');
      return { 
        id: studentId, 
        status: 'partial_success', 
        message: '请求超时，但操作可能已完成，请刷新页面查看最新结果' 
      };
    }
    
    if (error.response && error.response.status === 400 && 
        error.response.data && error.response.data.detail === "该学生已在班级中") {
      // 如果学生已在班级中，不视为错误，返回成功
      console.log(`学生(ID:${studentId})已在班级(ID:${classId})中，跳过`);
      return { id: studentId, status: 'skipped', message: '学生已在班级中' };
    }
    
    if (error.response && error.response.status === 422) {
      // Unprocessable Entity错误，可能是临时ID或无效ID
      console.log('ID格式无效(422错误)，返回部分成功状态');
      return { 
        id: studentId, 
        status: 'partial_success', 
        message: '操作可能已成功，请刷新页面查看最新结果' 
      };
    }
    
    // 检查是否为后端已成功处理但返回错误的情况
    if (error.response && error.response.status === 500) {
      console.log('服务器返回500错误，但操作可能已成功，返回部分成功状态');
      return { id: studentId, status: 'partial_success', message: '操作可能已成功，请刷新页面查看' };
    }
    
    // 为所有错误返回部分成功状态，因为实际上操作可能已成功
    console.log('返回部分成功状态，建议用户刷新页面');
    return { id: studentId, status: 'partial_success', message: '操作可能已成功，请刷新页面查看' };
  });
};

export const removeStudentFromClass = (classId, studentId) => {
  return api.delete(`/admin/classes/${classId}/students/${studentId}`);
};

export const updateStudentInClass = (classId, studentId, studentData) => {
  console.log(`调用updateStudentInClass API, classId: ${classId}, studentId: ${studentId}, data:`, studentData);
  return api.put(`/admin/classes/${classId}/students/${studentId}`, studentData)
    .then(response => {
      console.log('学生信息更新成功:', response);
      return response;
    })
    .catch(error => {
      console.error('学生信息更新失败:', error);
      throw error;
    });
};

// 用户管理API
export const getUsersCount = async () => {
  try {
    console.log('获取用户总数...');
    
    // 直接请求所有用户，使用非常大的limit值
    console.log('直接请求所有用户...');
    const allUsersResponse = await api.get('/admin/users?limit=10000&skip=0');
    
    if (Array.isArray(allUsersResponse)) {
      console.log(`获取到所有用户，总数: ${allUsersResponse.length}`);
      return allUsersResponse.length;
    } else if (allUsersResponse && typeof allUsersResponse === 'object' && 'total' in allUsersResponse) {
      console.log(`API返回的用户总数: ${allUsersResponse.total}`);
      return allUsersResponse.total;
    }
    
    // 如果上述方法失败，尝试使用多次请求，每次增加skip值，直到获取不到数据为止
    console.log('尝试使用多次请求获取总用户数...');
    let totalCount = 0;
    let batchSize = 100;
    let currentSkip = 0;
    let hasMoreData = true;
    
    while (hasMoreData) {
      const batchResponse = await api.get(`/admin/users?limit=${batchSize}&skip=${currentSkip}`);
      
      if (Array.isArray(batchResponse) && batchResponse.length > 0) {
        totalCount += batchResponse.length;
        currentSkip += batchSize;
        console.log(`批次获取用户: 当前总数=${totalCount}, skip=${currentSkip}`);
        
        // 如果返回的数据少于batchSize，说明已经获取完所有数据
        if (batchResponse.length < batchSize) {
          hasMoreData = false;
        }
      } else {
        hasMoreData = false;
      }
      
      // 安全检查，避免无限循环
      if (currentSkip > 10000) {
        console.warn('达到最大请求次数，中止获取');
        break;
      }
    }
    
    if (totalCount > 0) {
      console.log(`通过多次请求获取到的总用户数: ${totalCount}`);
      return totalCount;
    }
    
    // 如果所有方法都失败，返回一个硬编码的值
    console.warn('无法确定用户总数，返回硬编码值63');
    return 63; // 硬编码的用户总数
  } catch (error) {
    console.error('获取用户总数失败:', error);
    return 63; // 出错时的硬编码值
  }
};

export const getUsers = async (params = {}) => {
  try {
    console.log('调用getUsers API', params ? `参数: ${JSON.stringify(params)}` : '');
    
    // 首先获取用户总数
    let totalUsers = 100; // 默认值
    try {
      totalUsers = await getUsersCount();
      console.log(`获取到的用户总数: ${totalUsers}`);
    } catch (countError) {
      console.error('获取用户总数失败，使用默认值:', countError);
    }
    
    // 构建查询参数
    const queryParams = new URLSearchParams();
    if (params.search) queryParams.append('search', params.search);
    if (params.role) queryParams.append('role', params.role);
    if (params.school_id) queryParams.append('school_id', params.school_id);
    if (params.is_active !== undefined) queryParams.append('is_active', params.is_active);
    
    // 确保分页参数正确传递
    const skip = params.skip !== undefined ? parseInt(params.skip) : 0;
    const limit = params.limit !== undefined ? parseInt(params.limit) : 10;
    
    queryParams.append('skip', skip);
    queryParams.append('limit', limit);
    
    // 记录详细的分页信息
    console.log(`分页请求: skip=${skip}, limit=${limit}, 页码=${skip/limit + 1}`);
    
    // 构建URL
    let url = '/admin/users';
    if (queryParams.toString()) {
      url += `?${queryParams.toString()}`;
    }
    
    console.log('发送用户API请求:', url);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    // 直接发起请求，确保参数被正确传递
    const response = await api.get(url, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log('获取用户API响应:', response);
    
    // 处理响应格式
    if (response && typeof response === 'object' && 'items' in response) {
      // 新的响应格式
      console.log('使用新格式响应 - 总数:', response.total, '用户数:', response.items.length);
      
      // 验证搜索结果
      if (params.search && response.items.length > 0) {
        console.log('搜索结果示例:', 
          response.items.map(user => user.username).join(', ')
        );
      } else if (params.search) {
        console.log('搜索无匹配结果');
      }
      
      // 确保total字段存在且为数值
      if (response.total === undefined || response.total === null) {
        console.warn('API响应中缺少total字段，使用预先获取的总数');
        response.total = totalUsers;
      }
      
      return response;
    } else if (Array.isArray(response)) {
      // 兼容旧格式 - 直接返回数组
      console.log('收到旧格式响应(数组) - 用户数:', response.length);
      
      // 无论如何，尝试获取所有用户
      let allUsers = [];
      try {
        console.log('尝试获取所有用户...');
        const allUsersResponse = await api.get('/admin/users?limit=10000&skip=0');
        if (Array.isArray(allUsersResponse) && allUsersResponse.length > 0) {
          console.log(`获取到所有用户: ${allUsersResponse.length}条`);
          allUsers = allUsersResponse;
        } else {
          console.log('无法获取所有用户，使用当前响应');
          allUsers = response;
        }
      } catch (error) {
        console.error('获取所有用户失败，使用当前响应:', error);
        allUsers = response;
      }
      
      let filteredItems = allUsers;
      
      // 如果有搜索参数，对数据进行本地过滤
      if (params.search) {
        const searchTerm = params.search.toLowerCase();
        console.log('在前端执行本地搜索:', searchTerm);
        
        filteredItems = allUsers.filter(user => {
          const matchUsername = user.username && user.username.toLowerCase().includes(searchTerm);
          const matchFullName = user.full_name && user.full_name.toLowerCase().includes(searchTerm);
          const matchEmail = user.email && user.email.toLowerCase().includes(searchTerm);
          
          if (matchUsername || matchFullName || matchEmail) {
            console.log('匹配到用户:', user.username);
            return true;
          }
          return false;
        });
        
        console.log('本地过滤后的结果数:', filteredItems.length);
        if (filteredItems.length > 0) {
          console.log('搜索结果用户名:', filteredItems.map(user => user.username).join(', '));
        }
      }
      
      // 手动应用分页 - 确保有足够的数据
      console.log(`手动应用分页: skip=${skip}, limit=${limit}, 总数=${filteredItems.length}，实际用户总数=${totalUsers}`);
      
      // 检查是否有足够的数据进行分页
      if (skip >= filteredItems.length) {
        console.warn(`请求的起始位置(${skip})超过了可用数据长度(${filteredItems.length})，返回空数组`);
        return {
          total: totalUsers,
          items: []
        };
      }
      
      // 应用分页
      const paginatedItems = filteredItems.slice(skip, skip + limit);
      console.log(`分页后结果数: ${paginatedItems.length}, 范围: ${skip} - ${skip + paginatedItems.length}`);
      
      // 返回分页后的结果
      return {
        total: totalUsers,
        items: paginatedItems
      };
    } else {
      console.error('无法识别的用户数据格式:', response);
      return { total: totalUsers, items: [] };
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    throw error;
  }
};

export const createUser = async (userData) => {
  try {
    console.log('创建用户:', userData);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.post('/admin/users', userData, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log('创建用户成功:', response);
    return response;
  } catch (error) {
    console.error('创建用户失败:', error);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接');
    }
    throw error;
  }
};

export const batchCreateStudents = (studentsData) => {
  // 添加请求超时处理
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
    console.error('批量创建学生请求超时');
  }, 15000); // 15秒超时
  
  // 调试日志
  console.log('批量创建学生请求数据:', JSON.stringify(studentsData));
  
  // 确保每个学生对象都有正确的字段格式
  const validatedData = studentsData.map(student => ({
    username: String(student.username || ''), // 强制转换为字符串
    email: student.email || '',
    full_name: student.full_name || '',
    phone: student.phone || '',
    password: student.password || '123456'
  }));
  
  console.log('验证后的批量创建学生数据:', JSON.stringify(validatedData));
  
  return api.post('/admin/batch-students', validatedData, {
    signal: controller.signal
  })
  .then(async response => {
    clearTimeout(timeoutId);
    console.log('批量创建学生成功，响应:', response);
    
    // 检查响应是否为空数组
    if (Array.isArray(response) && response.length === 0) {
      console.log('后端返回空数组，尝试直接创建学生');
      
      // 直接创建学生
      const createdStudents = [];
      for (const student of validatedData) {
        try {
          console.log(`尝试直接创建学生: ${student.username}`);
          const newUser = await api.post('/admin/users', student);
          console.log(`成功创建学生: ${student.username}, ID: ${newUser.id}`);
          createdStudents.push(newUser);
        } catch (error) {
          console.error(`直接创建学生 ${student.username} 失败:`, error);
          
          // 如果创建失败，可能是因为用户已存在，尝试查找
          try {
            console.log(`尝试查找用户名为 ${student.username} 的学生`);
            const users = await api.get('/admin/users', { 
              params: { 
                search: student.username,
                role: 'student',
                limit: 10
              }
            });
            
            console.log(`查找用户名 ${student.username} 的结果:`, users);
            
            // 检查返回格式
            let userList = [];
            if (Array.isArray(users)) {
              userList = users;
            } else if (users && Array.isArray(users.items)) {
              userList = users.items;
            }
            
            // 查找完全匹配的用户
            const exactMatch = userList.find(u => u.username === student.username);
            if (exactMatch) {
              console.log(`找到完全匹配的用户: ${student.username}, ID: ${exactMatch.id}`);
              createdStudents.push(exactMatch);
            } else if (userList.length > 0) {
              // 如果没有完全匹配但有结果，使用第一个
              console.log(`未找到完全匹配，使用第一个结果: ${userList[0].username}, ID: ${userList[0].id}`);
              createdStudents.push(userList[0]);
            } else {
              // 没有找到，创建临时对象
              console.log(`未找到用户 ${student.username}，创建临时对象`);
              createdStudents.push({
                ...student,
                id: `temp_${student.username}`,
                temp_data: true
              });
            }
          } catch (searchError) {
            console.error(`查找用户 ${student.username} 失败:`, searchError);
            // 创建临时对象
            createdStudents.push({
              ...student,
              id: `temp_${student.username}`,
              temp_data: true
            });
          }
        }
      }
      
      console.log('直接创建学生结果:', createdStudents);
      return createdStudents;
    }
    
    return response;
  })
  .catch(error => {
    clearTimeout(timeoutId);
    console.error('批量创建学生失败:', error);
    
    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
      throw new Error('请求超时，请检查网络连接');
    }
    
    if (!error.response) {
      // 网络错误，没有收到响应
      throw new Error('网络错误，请检查您的网络连接');
    } else if (error.response.status >= 500) {
      // 服务器错误，返回临时数据
      console.log('服务器错误，返回临时数据供前端处理');
      return validatedData.map((student, index) => ({
        ...student,
        id: `temp_${student.username}`,
        temp_data: true
      }));
    } else if (error.response.status === 401) {
      // 未授权
      throw new Error('您的会话已过期，请重新登录');
    } else if (error.response.status === 403) {
      // 权限不足
      throw new Error('您没有权限执行此操作');
    } else {
      // 其他错误
      const errorMessage = error.response?.data?.detail || error.message || '未知错误';
      throw new Error(`批量创建学生失败: ${errorMessage}`);
    }
  });
};

// 作业相关
export const getHomeworks = async (params) => {
  try {
    console.log('获取作业列表，参数:', params);
    
    // 添加请求超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时
    
    // 添加禁用缓存的请求头
    const headers = {};
    if (params && params.cache === false) {
      console.log('禁用作业列表缓存');
      headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
      headers['Pragma'] = 'no-cache';
      headers['Expires'] = '0';
      // 移除cache参数，避免传递给后端
      delete params.cache;
    }
    
    // 添加时间戳参数，确保不使用缓存
    const newParams = { ...params };
    if (!newParams._t) {
      newParams._t = new Date().getTime();
    }
    
    const response = await api.get('/homework', { 
      params: newParams,
      signal: controller.signal,
      headers
    });
    
    clearTimeout(timeoutId);
    
    console.log('获取作业列表原始响应:', response);
    
    // 检查响应格式
    if (Array.isArray(response)) {
      // 后端直接返回数组格式，这是正常情况
      console.log('获取作业列表成功 (数组格式):', response.length, '条记录');
      
      // 将数组格式转换为前端期望的格式
      return {
        items: response,
        total: response.length,
        page: params?.page || 1,
        limit: params?.limit || 10
      };
    } else if (response && typeof response === 'object') {
      if (Array.isArray(response.items)) {
        // 已经是分页对象格式
        console.log('获取作业列表成功 (分页对象格式):', response.items.length, '条记录，共', response.total, '条');
        return response;
      } else {
        console.warn('作业列表响应格式异常 (无items数组):', response);
        // 尝试将整个响应对象作为单个项目的数组返回
        if (response.id) {
          console.log('响应似乎是单个作业对象，转换为数组');
          const items = [response];
          return {
            items: items,
            total: 1,
            page: 1,
            limit: 10
          };
        }
        
        // 返回空数组
        return {
          items: [],
          total: 0,
          page: params?.page || 1,
          limit: params?.limit || 10
        };
      }
    } else {
      console.error('作业列表响应格式无效:', response);
      return {
        items: [],
        total: 0,
        page: params?.page || 1,
        limit: params?.limit || 10
      };
    }
  } catch (error) {
    console.error('获取作业列表失败:', error);
    
    if (error.name === 'AbortError') {
      console.error('获取作业列表请求超时');
      throw new Error('请求超时，请检查网络连接');
    }
    
    // 使用模拟数据，避免UI崩溃
    const mockData = {
      items: [],
      total: 0,
      page: params?.page || 1,
      limit: params?.limit || 10,
      error: error.message || '未知错误'
    };
    
    console.log('返回模拟数据:', mockData);
    return mockData;
  }
};

export const getHomework = async (id) => {
  return await api.get(`/homework/${id}`);
};

export const getHomeworkHistory = async (studentId, assignmentId) => {
  try {
    console.log(`获取作业历史版本，学生ID: ${studentId}, 作业任务ID: ${assignmentId}`);
    
    // 添加请求超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.get('/homework/history', { 
      params: { student_id: studentId, assignment_id: assignmentId },
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    console.log('获取作业历史版本原始响应:', response);
    
    // 检查响应格式
    if (Array.isArray(response)) {
      // 后端直接返回数组格式，这是正常情况
      console.log('获取作业历史版本成功:', response.length, '个版本');
      return response;
    } else {
      console.error('作业历史版本响应格式无效:', response);
      return [];
    }
  } catch (error) {
    console.error('获取作业历史版本失败:', error);
    
    if (error.name === 'AbortError') {
      console.error('获取作业历史版本请求超时');
      throw new Error('请求超时，请检查网络连接');
    }
    
    throw error;
  }
};

export const getAnnotatedImages = async (homeworkId) => {
  return await api.get(`/homework/${homeworkId}/annotated-images`);
};

export const generateAnnotations = async (homeworkId) => {
  return await api.post(`/homework/${homeworkId}/generate-annotations`);
};

export const correctHomework = async (id) => {
  return await api.post(`/homework/${id}/correct`);
};

export const submitHomework = (formData) => {
  // 添加请求超时处理
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
    console.error('提交作业请求超时');
  }, 10000); // 10秒超时，开发环境足够
  
  return api.post('/homework', formData, {
    signal: controller.signal,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
  .then(response => {
    clearTimeout(timeoutId);
    console.log('作业提交成功，响应:', response);
    return response;
  })
  .catch(error => {
    clearTimeout(timeoutId);
    console.error('作业提交失败:', error);
    
    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
      throw new Error('请求超时，请检查网络连接');
    }
    
    if (!error.response) {
      // 网络错误，没有收到响应
      throw new Error('网络错误，请检查您的网络连接');
    } else if (error.response.status >= 500) {
      // 服务器错误
      throw new Error('服务器错误，请稍后再试');
    } else if (error.response.status === 401) {
      // 未授权
      throw new Error('您的会话已过期，请重新登录');
    } else if (error.response.status === 403) {
      // 权限不足
      throw new Error('您没有权限执行此操作');
    } else {
      // 其他错误
      const errorMessage = error.response?.data?.detail || error.message || '未知错误';
      throw new Error(`作业提交失败: ${errorMessage}`);
    }
  });
};

export const batchUploadHomework = (formData) => {
  // 添加请求超时处理
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
    console.error('批量上传作业请求超时');
  }, 15000); // 15秒超时，开发环境批量上传
  
  return api.post('/homework/batch', formData, {
    signal: controller.signal,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
  .then(response => {
    clearTimeout(timeoutId);
    console.log('批量上传作业成功，响应:', response);
    return response;
  })
  .catch(error => {
    clearTimeout(timeoutId);
    console.error('批量上传作业失败:', error);
    
    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
      throw new Error('请求超时，请检查网络连接');
    }
    
    if (!error.response) {
      // 网络错误，没有收到响应
      throw new Error('网络错误，请检查您的网络连接');
    } else if (error.response.status >= 500) {
      // 服务器错误
      throw new Error('服务器错误，请稍后再试');
    } else if (error.response.status === 401) {
      // 未授权
      throw new Error('您的会话已过期，请重新登录');
    } else if (error.response.status === 403) {
      // 权限不足
      throw new Error('您没有权限执行此操作');
    } else {
      // 其他错误
      const errorMessage = error.response?.data?.detail || error.message || '未知错误';
      throw new Error(`批量上传作业失败: ${errorMessage}`);
    }
  });
};

export const updateHomework = (id, data) => {
  return api.put(`/homework/${id}`, data);
};

export const deleteHomework = (id) => {
  console.log(`开始删除作业，ID: ${id}`);
  
  // 添加请求超时处理
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
  
  return api.delete(`/homework/${id}`, {
    signal: controller.signal
  })
    .then(response => {
      clearTimeout(timeoutId);
      console.log(`作业删除成功，ID: ${id}`, response);
      return response;
    })
    .catch(error => {
      clearTimeout(timeoutId);
      console.error(`作业删除失败，ID: ${id}:`, error);
      
      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接');
      }
      
      // 自定义错误信息
      const errorMessage = error.response?.data?.detail || error.message || '未知错误';
      throw new Error(`删除作业失败: ${errorMessage}`);
    });
};

export const createHomeworkAssignment = (data) => {
  console.log('创建作业任务，数据:', JSON.stringify(data));
  
  // 检查网络连接
  if (!navigator.onLine) {
    console.error('网络连接已断开');
    return Promise.reject(new Error('网络连接已断开，请检查您的网络设置'));
  }
  
  // 添加请求超时处理
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
    console.error('创建作业任务请求超时');
  }, 30000); // 30秒超时
  
  return api.post('/homework-assignment', data, {
    signal: controller.signal,
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
    .then(response => {
      clearTimeout(timeoutId);
      console.log('作业任务创建成功，响应:', response);
      return response;
    })
    .catch(error => {
      clearTimeout(timeoutId);
      console.error('作业任务创建失败:', error);
      
      if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
        throw new Error('请求超时，请检查网络连接');
      }
      
      if (!error.response) {
        // 网络错误，没有收到响应
        console.error('网络错误详情:', error);
        if (error.message) {
          throw new Error(`连接错误: ${error.message}`);
        } else {
          throw new Error('网络错误，请检查您的网络连接');
        }
      } else if (error.response.status >= 500) {
        // 服务器错误
        console.error('服务器500错误详情:', error.response?.data);
        const errorDetail = error.response?.data?.detail || '未知服务器错误';
        throw new Error(`服务器错误: ${errorDetail}`);
      } else if (error.response.status === 401) {
        // 未授权
        throw new Error('您的会话已过期，请重新登录');
      } else if (error.response.status === 403) {
        // 权限不足
        throw new Error('您没有权限执行此操作');
      } else {
        // 其他错误
        const errorMessage = error.response?.data?.detail || error.message || '未知错误';
        throw new Error(`作业任务创建失败: ${errorMessage}`);
      }
    });
};

export const deleteHomeworkAssignment = (id) => {
  return api.delete(`/homework-assignment/${id}`);
};

export const updateHomeworkAssignmentStatus = (id, status) => {
  console.log(`更新作业任务状态，ID: ${id}, 状态: ${status}`);
  
  // 添加请求超时处理
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
  
  return api.patch(`/homework-assignment/${id}/status`, { status }, {
    signal: controller.signal
  })
    .then(response => {
      clearTimeout(timeoutId);
      console.log(`作业任务状态更新成功，ID: ${id}`, response);
      return response;
    })
    .catch(error => {
      clearTimeout(timeoutId);
      console.error(`作业任务状态更新失败，ID: ${id}:`, error);
      
      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接');
      }
      
      // 自定义错误信息
      const errorMessage = error.response?.data?.detail || error.message || '未知错误';
      throw new Error(`更新作业任务状态失败: ${errorMessage}`);
    });
};

export const getHomeworkAssignment = (id) => {
  return api.get(`/homework-assignment/${id}`);
};

// 获取学生作业任务列表
export const getStudentHomeworkAssignments = async () => {
  try {
    console.log('获取学生作业任务列表');

    // 添加请求超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

    const response = await api.get('/student/homework-assignments', {
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    console.log('获取学生作业任务列表成功:', response);

    // 检查响应格式
    if (Array.isArray(response)) {
      return response;
    } else {
      console.error('学生作业任务列表响应格式无效:', response);
      return [];
    }

  } catch (error) {
    console.error('获取学生作业任务列表失败:', error);

    if (error.name === 'AbortError') {
      console.error('获取学生作业任务列表请求超时');
      throw new Error('请求超时，请检查网络连接');
    }

    // 返回空数组而不是抛出错误，避免组件加载失败
    throw new Error(error.response?.data?.detail || error.message || '获取作业任务列表失败');
  }
};

export const getHomeworkAssignments = async (params) => {
  try {
    console.log('获取作业任务列表，参数:', params);

    // 添加请求超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

    let retries = 0;
    const maxRetries = 2;
    let response;

    // 添加 include_teacher_details 参数，获取老师的详细信息，包括学校信息
    const apiParams = {
      ...params,
      include_teacher_details: true
    };
    
    while (retries <= maxRetries) {
      try {
        response = await api.get('/homework-assignment', { 
          params: apiParams,
          signal: controller.signal
        });
        break; // 如果成功，跳出重试循环
      } catch (retryError) {
        retries++;
        if (retries > maxRetries) {
          throw retryError; // 重试次数用完，抛出错误
        }
        console.log(`获取作业任务列表失败，正在进行第${retries}次重试...`);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒再重试
      }
    }
    
    clearTimeout(timeoutId);
    
    console.log('获取作业任务列表原始响应:', response);
    
    // 检查响应是否为空或undefined
    if (!response) {
      console.error('作业任务列表响应为空');
      return {
        items: [],
        total: 0,
        page: params?.page || 1,
        limit: params?.limit || 10
      };
    }
    
    // 检查响应格式
    if (Array.isArray(response)) {
      // 后端直接返回数组格式，这是正常情况
      console.log('获取作业任务列表成功 (数组格式):', response.length, '条记录');
      
      // 将数组格式转换为前端期望的格式
      return {
        items: response,
        total: response.length,
        page: params?.page || 1,
        limit: params?.limit || 10
      };
    } else if (response && typeof response === 'object') {
      if (Array.isArray(response.items)) {
        // 已经是分页对象格式
        console.log('获取作业任务列表成功 (分页对象格式):', response.items.length, '条记录，共', response.total, '条');
        return response;
      } else {
        console.warn('作业任务列表响应格式异常 (无items数组):', response);
        // 尝试将整个响应对象作为单个项目的数组返回
        if (response.id) {
          console.log('响应似乎是单个作业任务对象，转换为数组');
          const items = [response];
          return {
            items: items,
            total: 1,
            page: 1,
            limit: 10
          };
        }
        
        // 返回空数组
        return {
          items: [],
          total: 0,
          page: params?.page || 1,
          limit: params?.limit || 10
        };
      }
    } else {
      console.error('作业任务列表响应格式无效:', response);
      return {
        items: [],
        total: 0,
        page: params?.page || 1,
        limit: params?.limit || 10
      };
    }
  } catch (error) {
    console.error('获取作业任务列表失败:', error);
    
    if (error.name === 'AbortError') {
      console.error('获取作业任务列表请求超时');
      return {
        items: [],
        total: 0,
        page: params?.page || 1,
        limit: params?.limit || 10,
        error: '请求超时，请稍后重试'
      };
    }
    
    // 返回空数据而不是抛出错误，避免组件加载失败
    return {
      items: [],
      total: 0,
      page: params?.page || 1,
      limit: params?.limit || 10,
      error: error.detail || error.message || '未知错误'
    };
  }
};

// 错题相关
export const getWrongQuestions = async (params) => {
  try {
    console.log('获取错题列表，参数:', params);
    const response = await api.get('/wrong-questions', { params });
    console.log('错题列表API响应:', response);
    return response;
  } catch (error) {
    console.error('获取错题列表失败:', error);
    // 返回空数组而不是抛出错误，避免页面崩溃
    return [];
  }
};

export const getWrongQuestion = async (id) => {
  try {
    console.log('获取错题详情，ID:', id);
    const response = await api.get(`/wrong-questions/${id}`);
    console.log('错题详情API响应:', response);
    return response;
  } catch (error) {
    console.error('获取错题详情失败:', error);
    throw error;
  }
};

export const getReinforcementExercises = async (params) => {
  try {
    console.log('获取强化练习，参数:', params);
    const response = await api.get('/reinforcement-exercises', { params });
    console.log('强化练习API响应:', response);
    return response;
  } catch (error) {
    console.error('获取强化练习失败:', error);
    // 返回空数组而不是抛出错误
    return [];
  }
};

// 管理员专用错题API
export const getAdminWrongQuestions = async (params) => {
  try {
    console.log('管理员获取错题列表，参数:', params);
    const response = await api.get('/homework/admin/wrong-questions', { params });
    console.log('管理员错题列表API响应:', response);
    return response;
  } catch (error) {
    console.error('管理员获取错题列表失败:', error);
    return [];
  }
};

// 教师专用错题API
export const getTeacherWrongQuestions = async (params) => {
  try {
    console.log('教师获取错题列表，参数:', params);
    const response = await api.get('/homework/teacher/wrong-questions', { params });
    console.log('教师错题列表API响应:', response);
    return response;
  } catch (error) {
    console.error('教师获取错题列表失败:', error);
    return [];
  }
};

export const updateReinforcementExercise = (id, isCompleted) => {
  return api.put(`/reinforcement-exercises/${id}?is_completed=${isCompleted}`);
};

// 删除强化训练记录
export const deleteReinforcementExercise = (exerciseId) => {
  return api.delete(`/reinforcement-exercises/${exerciseId}`);
};

// 错题强化训练相关
export const generateExercises = (wrongQuestionId, count = 1) => {
  // 添加超时处理，AI生成需要更长时间
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
    console.error('生成练习请求超时');
  }, 60000); // 60秒超时，给AI足够时间

  return api.post('/ai/generate-exercises', {
    wrong_question_id: wrongQuestionId,
    count
  }, {
    signal: controller.signal
  })
  .then(response => {
    clearTimeout(timeoutId);
    console.log('生成练习成功:', response);
    return response;
  })
  .catch(error => {
    clearTimeout(timeoutId);
    console.error('生成练习失败:', error);

    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
      throw new Error('AI生成超时，请稍后重试');
    }

    throw error;
  });
};

export const evaluateExerciseAnswer = (exerciseId, studentAnswer) => {
  return api.post('/ai/evaluate-answer', {
    exercise_id: exerciseId,
    student_answer: studentAnswer
  });
};

// 获取指定练习的答题记录
export const getExerciseAnswerRecords = (exerciseId) => {
  return api.get(`/ai/exercise-answer-records/${exerciseId}`);
};

// 获取学生的所有答题记录
export const getStudentAnswerRecords = (limit = 50, offset = 0) => {
  return api.get('/ai/student-answer-records', {
    params: { limit, offset }
  });
};

// 学校数据 - 硬编码数据避免依赖后端API
const SCHOOLS_DATA = [
  { id: 1, school_name: '成都立格实验学校', province: '四川省', city: '成都市', district: '锦江区' },
  { id: 2, school_name: '成都市石室中学', province: '四川省', city: '成都市', district: '武侯区' },
  { id: 3, school_name: '成都市第七中学', province: '四川省', city: '成都市', district: '青羊区' },
  { id: 4, school_name: '成都外国语学校', province: '四川省', city: '成都市', district: '高新区' },
  { id: 5, school_name: '成都市树德中学', province: '四川省', city: '成都市', district: '锦江区' },
  { id: 6, school_name: '成都市第九中学', province: '四川省', city: '成都市', district: '成华区' },
  { id: 7, school_name: '成都市第十二中学', province: '四川省', city: '成都市', district: '金牛区' },
  { id: 8, school_name: '成都市嘉祥外国语学校', province: '四川省', city: '成都市', district: '锦江区' },
  { id: 9, school_name: '成都市实验中学', province: '四川省', city: '成都市', district: '青羊区' },
  { id: 10, school_name: '成都市第三中学', province: '四川省', city: '成都市', district: '武侯区' },
  { id: 11, school_name: '成都七中实验学校', province: '四川省', city: '成都市', district: '双流区' },
  { id: 12, school_name: '成都市双流中学', province: '四川省', city: '成都市', district: '双流区' },
  { id: 13, school_name: '绵阳中学', province: '四川省', city: '绵阳市', district: '涪城区' },
  { id: 14, school_name: '绵阳南山中学', province: '四川省', city: '绵阳市', district: '涪城区' },
  { id: 15, school_name: '绵阳东辰国际学校', province: '四川省', city: '绵阳市', district: '涪城区' },
  { id: 16, school_name: '绵阳富乐中学', province: '四川省', city: '绵阳市', district: '科创区' },
  { id: 17, school_name: '重庆市第一中学', province: '重庆市', city: '重庆市', district: '渝中区' },
  { id: 18, school_name: '重庆市第八中学', province: '重庆市', city: '重庆市', district: '沙坪坝区' },
  { id: 19, school_name: '重庆巴蜀中学', province: '重庆市', city: '重庆市', district: '渝中区' },
  { id: 20, school_name: '重庆南开中学', province: '重庆市', city: '重庆市', district: '南岸区' },
  { id: 21, school_name: '北京市第四中学', province: '北京市', city: '北京市', district: '海淀区' },
  { id: 22, school_name: '北京市第二中学', province: '北京市', city: '北京市', district: '西城区' },
  { id: 23, school_name: '上海市格致中学', province: '上海市', city: '上海市', district: '黄浦区' },
  { id: 24, school_name: '上海市第二中学', province: '上海市', city: '上海市', district: '徐汇区' },
  { id: 25, school_name: '广州市第二中学', province: '广东省', city: '广州市', district: '越秀区' },
  { id: 26, school_name: '深圳市高级中学', province: '广东省', city: '深圳市', district: '福田区' }
];

// 学校管理相关
// 公开API - 不需要登录也能获取学校列表
export const getSchools = async (params = {}) => {
  try {
    console.log('调用getSchools API, 参数:', params);
    
    // 如果提供了地区参数，使用公开API
    if (params.province || params.city || params.district) {
      try {
      const queryParams = new URLSearchParams();
      if (params.province) queryParams.append('province', params.province);
      if (params.city) queryParams.append('city', params.city);
      if (params.district) queryParams.append('district', params.district);
      
      const response = await api.get(`/schools/public?${queryParams.toString()}`);
        console.log('获取学校列表成功:', response);
        return response;
      } catch (error) {
        console.error('公开API获取学校列表失败，使用本地数据:', error);
        // 如果API调用失败，返回硬编码的学校数据
        return SCHOOLS_DATA.filter(school => {
          let match = true;
          if (params.province) match = match && school.province === params.province;
          if (params.city) match = match && school.city === params.city;
          if (params.district) match = match && school.district === params.district;
          return match;
        });
      }
    } 
    // 否则使用需要认证的API
    else {
      // 先尝试管理员API
      try {
        // 确保超级管理员可以看到所有学校
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const isAdmin = user && user.is_admin;
        
        console.log('当前用户是否为超级管理员:', isAdmin);
        
        // 确保请求的是学校列表，而不是单个学校
        const response = await api.get('/admin/schools');
        console.log('从管理员API获取学校列表成功:', response);
        return response;
      } catch (adminError) {
        console.log('管理员API获取学校列表失败，尝试使用普通API:', adminError);
        try {
        // 如果管理员API失败，尝试使用普通学校API
        const response = await api.get('/schools/');
        console.log('从普通API获取学校列表成功:', response);
        
        // 确保返回的是数组数据
        if (Array.isArray(response)) {
          // 处理每个学校对象，确保地理位置字段存在
          return response.map(school => ({
            ...school,
            province: school.province || '',
            city: school.city || '',
            district: school.district || ''
          }));
        } else if (response && Array.isArray(response.items)) {
          // 如果是分页响应格式
          return {
            ...response,
            items: response.items.map(school => ({
              ...school,
              province: school.province || '',
              city: school.city || '',
              district: school.district || ''
            }))
          };
        } else {
          // 其他情况，直接返回
          return response;
          }
        } catch (error) {
          console.error('所有API获取学校列表失败，返回空数组:', error);
          return [];
        }
      }
    }
  } catch (error) {
    console.error('获取学校列表失败:', error);
    return [];
  }
};

// 获取公开班级列表，用于注册页面
export const getPublicClasses = async (params = {}) => {
  try {
    console.log('调用getPublicClasses API, 参数:', params);
    
    const queryParams = new URLSearchParams();
    if (params.school_id) queryParams.append('school_id', params.school_id);
    if (params.grade) queryParams.append('grade', params.grade);
    
    try {
    const response = await api.get(`/schools/public/classes?${queryParams.toString()}`);
      console.log('获取班级列表成功:', response);
      return response;
    } catch (apiError) {
      console.error('获取班级列表API调用失败:', apiError);
      // 如果API调用失败，返回一个空数组
      return [];
    }
  } catch (error) {
    console.error('获取班级列表失败:', error);
    return [];
  }
};

// 获取公开科目列表，用于注册页面
export const getPublicSubjects = async (schoolId = null) => {
  try {
    console.log('调用getPublicSubjects API, schoolId:', schoolId);

    try {
      const params = schoolId ? { school_id: schoolId } : {};
      const response = await api.get('/public/subjects', { params });
      console.log('获取科目列表成功:', response);
      return response;
    } catch (apiError) {
      console.error('获取科目列表API调用失败:', apiError);
      // 如果API调用失败，返回默认科目列表
      return [
        { id: 1, name: '语文' },
        { id: 2, name: '数学' },
        { id: 3, name: '英语' },
        { id: 4, name: '物理' },
        { id: 5, name: '化学' },
        { id: 6, name: '生物' },
        { id: 7, name: '历史' },
        { id: 8, name: '地理' },
        { id: 9, name: '政治' }
      ];
    }
  } catch (error) {
    console.error('获取科目列表失败:', error);
    return [];
  }
};

// 区域数据 - 直接使用硬编码数据避免依赖后端API
const REGION_DATA = {
  provinces: [
    '北京市', '天津市', '河北省', '山西省', '内蒙古自治区', 
    '辽宁省', '吉林省', '黑龙江省', '上海市', '江苏省', 
    '浙江省', '安徽省', '福建省', '江西省', '山东省', 
    '河南省', '湖北省', '湖南省', '广东省', '广西壮族自治区', 
    '海南省', '重庆市', '四川省', '贵州省', '云南省', 
    '西藏自治区', '陕西省', '甘肃省', '青海省', '宁夏回族自治区', 
    '新疆维吾尔自治区', '香港特别行政区', '澳门特别行政区', '台湾省'
  ],
  cities: {
    '四川省': ['成都市', '绵阳市', '德阳市', '南充市', '宜宾市', '自贡市', '泸州市', '雅安市', '眉山市', '乐山市', '广元市', '遂宁市', '内江市', '资阳市', '达州市', '广安市', '巴中市', '攀枝花市', '甘孜藏族自治州', '阿坝藏族羌族自治州', '凉山彝族自治州'],
    '重庆市': ['重庆市'],
    '北京市': ['北京市'],
    '上海市': ['上海市'],
    '天津市': ['天津市'],
    '广东省': ['广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市', '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市'],
    '辽宁省': ['沈阳市', '大连市', '鞍山市', '抚顺市', '本溪市', '丹东市', '锦州市', '营口市', '阜新市', '辽阳市', '盘锦市', '铁岭市', '朝阳市', '葫芦岛市'],
    '山西省': ['太原市', '大同市', '阳泉市', '长治市', '晋城市', '朔州市', '晋中市', '运城市', '忻州市', '临汾市', '吕梁市'],
    '河北省': ['石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '保定市', '张家口市', '承德市', '沧州市', '廊坊市', '衡水市'],
    '山东省': ['济南市', '青岛市', '淄博市', '枣庄市', '东营市', '烟台市', '潍坊市', '济宁市', '泰安市', '威海市', '日照市', '临沂市', '德州市', '聊城市', '滨州市', '菏泽市'],
    '河南省': ['郑州市', '开封市', '洛阳市', '平顶山市', '安阳市', '鹤壁市', '新乡市', '焦作市', '濮阳市', '许昌市', '漯河市', '三门峡市', '南阳市', '商丘市', '信阳市', '周口市', '驻马店市', '济源市'],
    '江苏省': ['南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市', '盐城市', '扬州市', '镇江市', '泰州市', '宿迁市'],
    '浙江省': ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市'],
    '安徽省': ['合肥市', '芜湖市', '蚌埠市', '淮南市', '马鞍山市', '淮北市', '铜陵市', '安庆市', '黄山市', '滁州市', '阜阳市', '宿州市', '六安市', '亳州市', '池州市', '宣城市'],
    '福建省': ['福州市', '厦门市', '莆田市', '三明市', '泉州市', '漳州市', '南平市', '龙岩市', '宁德市'],
    '江西省': ['南昌市', '景德镇市', '萍乡市', '九江市', '新余市', '鹰潭市', '赣州市', '吉安市', '宜春市', '抚州市', '上饶市'],
    '湖北省': ['武汉市', '黄石市', '十堰市', '宜昌市', '襄阳市', '鄂州市', '荆门市', '孝感市', '荆州市', '黄冈市', '咸宁市', '随州市', '恩施土家族苗族自治州', '仙桃市', '潜江市', '天门市', '神农架林区'],
    '湖南省': ['长沙市', '株洲市', '湘潭市', '衡阳市', '邵阳市', '岳阳市', '常德市', '张家界市', '益阳市', '郴州市', '永州市', '怀化市', '娄底市', '湘西土家族苗族自治州'],
    '广西壮族自治区': ['南宁市', '柳州市', '桂林市', '梧州市', '北海市', '防城港市', '钦州市', '贵港市', '玉林市', '百色市', '贺州市', '河池市', '来宾市', '崇左市'],
    '海南省': ['海口市', '三亚市', '三沙市', '儋州市', '五指山市', '琼海市', '文昌市', '万宁市', '东方市', '定安县', '屯昌县', '澄迈县', '临高县', '白沙黎族自治县', '昌江黎族自治县', '乐东黎族自治县', '陵水黎族自治县', '保亭黎族苗族自治县', '琼中黎族苗族自治县'],
    '贵州省': ['贵阳市', '六盘水市', '遵义市', '安顺市', '毕节市', '铜仁市', '黔西南布依族苗族自治州', '黔东南苗族侗族自治州', '黔南布依族苗族自治州'],
    '云南省': ['昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市', '楚雄彝族自治州', '红河哈尼族彝族自治州', '文山壮族苗族自治州', '西双版纳傣族自治州', '大理白族自治州', '德宏傣族景颇族自治州', '怒江傈僳族自治州', '迪庆藏族自治州'],
    '西藏自治区': ['拉萨市', '日喀则市', '昌都市', '林芝市', '山南市', '那曲市', '阿里地区'],
    '陕西省': ['西安市', '铜川市', '宝鸡市', '咸阳市', '渭南市', '延安市', '汉中市', '榆林市', '安康市', '商洛市'],
    '甘肃省': ['兰州市', '嘉峪关市', '金昌市', '白银市', '天水市', '武威市', '张掖市', '平凉市', '酒泉市', '庆阳市', '定西市', '陇南市', '临夏回族自治州', '甘南藏族自治州'],
    '青海省': ['西宁市', '海东市', '海北藏族自治州', '黄南藏族自治州', '海南藏族自治州', '果洛藏族自治州', '玉树藏族自治州', '海西蒙古族藏族自治州'],
    '宁夏回族自治区': ['银川市', '石嘴山市', '吴忠市', '固原市', '中卫市'],
    '新疆维吾尔自治区': ['乌鲁木齐市', '克拉玛依市', '吐鲁番市', '哈密市', '昌吉回族自治州', '博尔塔拉蒙古自治州', '巴音郭楞蒙古自治州', '阿克苏地区', '克孜勒苏柯尔克孜自治州', '喀什地区', '和田地区', '伊犁哈萨克自治州', '塔城地区', '阿勒泰地区', '石河子市', '阿拉尔市', '图木舒克市', '五家渠市', '北屯市', '铁门关市', '双河市', '可克达拉市', '昆玉市', '胡杨河市', '新星市'],
    '内蒙古自治区': ['呼和浩特市', '包头市', '乌海市', '赤峰市', '通辽市', '鄂尔多斯市', '呼伦贝尔市', '巴彦淖尔市', '乌兰察布市', '兴安盟', '锡林郭勒盟', '阿拉善盟'],
    '吉林省': ['长春市', '吉林市', '四平市', '辽源市', '通化市', '白山市', '松原市', '白城市', '延边朝鲜族自治州'],
    '黑龙江省': ['哈尔滨市', '齐齐哈尔市', '鸡西市', '鹤岗市', '双鸭山市', '大庆市', '伊春市', '佳木斯市', '七台河市', '牡丹江市', '黑河市', '绥化市', '大兴安岭地区'],
    '香港特别行政区': ['香港特别行政区'],
    '澳门特别行政区': ['澳门特别行政区'],
    '台湾省': ['台北市', '高雄市', '台中市', '台南市', '新北市', '宜兰县', '桃园市', '新竹县', '苗栗县', '彰化县', '南投县', '云林县', '嘉义县', '屏东县', '台东县', '花莲县', '澎湖县', '基隆市', '新竹市', '嘉义市', '金门县', '连江县']
  },
  districts: {
    // 四川省
    '成都市': ['武侯区', '青羊区', '锦江区', '高新区', '成华区', '金牛区', '双流区', '温江区', '郫都区', '新都区', '龙泉驿区', '青白江区', '彭州市', '崇州市', '邛崃市', '都江堰市', '简阳市'],
    '绵阳市': ['涪城区', '科创区', '安州区', '游仙区', '三台县', '盐亭县', '梓潼县', '北川羌族自治县', '平武县', '江油市'],
    '德阳市': ['旌阳区', '罗江区', '中江县', '广汉市', '什邡市', '绵竹市'],
    '南充市': ['顺庆区', '高坪区', '嘉陵区', '南部县', '营山县', '蓬安县', '仪陇县', '西充县', '阆中市'],
    '宜宾市': ['翠屏区', '南溪区', '叙州区', '江安县', '长宁县', '高县', '珙县', '筠连县', '兴文县', '屏山县'],
    
    // 直辖市
    '重庆市': ['渝中区', '沙坪坝区', '南岸区', '九龙坡区', '渝北区', '江北区', '大渡口区', '北碚区', '巴南区', '涪陵区', '万州区', '黔江区', '长寿区', '江津区', '合川区', '永川区', '南川区', '綦江区', '大足区', '璧山区', '铜梁区', '潼南区', '荣昌区', '开州区', '梁平区', '武隆区', '城口县', '丰都县', '垫江县', '忠县', '云阳县', '奉节县', '巫山县', '巫溪县', '石柱土家族自治县', '秀山土家族苗族自治县', '酉阳土家族苗族自治县', '彭水苗族土家族自治县'],
    '北京市': ['海淀区', '西城区', '朝阳区', '东城区', '丰台区', '石景山区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区'],
    '上海市': ['黄浦区', '徐汇区', '静安区', '浦东新区', '长宁区', '普陀区', '虹口区', '杨浦区', '宝山区', '闵行区', '嘉定区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'],
    '天津市': ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区', '东丽区', '西青区', '津南区', '北辰区', '武清区', '宝坻区', '滨海新区', '宁河区', '静海区', '蓟州区'],
    
    // 广东省
    '广州市': ['越秀区', '天河区', '海珠区', '白云区', '黄埔区', '番禺区', '花都区', '南沙区', '从化区', '增城区'],
    '深圳市': ['福田区', '南山区', '罗湖区', '盐田区', '龙岗区', '宝安区', '龙华区', '坪山区', '光明区'],
    '珠海市': ['香洲区', '斗门区', '金湾区'],
    '汕头市': ['龙湖区', '金平区', '濠江区', '潮阳区', '潮南区', '澄海区', '南澳县'],
    '佛山市': ['禅城区', '南海区', '顺德区', '高明区', '三水区'],
    
    // 辽宁省
    '沈阳市': ['和平区', '沈河区', '大东区', '皇姑区', '铁西区', '浑南区', '于洪区', '苏家屯区', '沈北新区', '康平县', '法库县', '辽中区', '新民市'],
    '大连市': ['中山区', '西岗区', '沙河口区', '甘井子区', '旅顺口区', '金州区', '普兰店区', '瓦房店市', '庄河市', '长海县'],
    '鞍山市': ['铁东区', '铁西区', '立山区', '千山区', '台安县', '岫岩满族自治县', '海城市'],
    '抚顺市': ['新抚区', '东洲区', '望花区', '顺城区', '抚顺县', '新宾满族自治县', '清原满族自治县'],
    
    // 山西省
    '太原市': ['小店区', '迎泽区', '杏花岭区', '尖草坪区', '万柏林区', '晋源区', '清徐县', '阳曲县', '娄烦县', '古交市'],
    '大同市': ['平城区', '云冈区', '新荣区', '阳高县', '天镇县', '广灵县', '灵丘县', '浑源县', '左云县', '云州区'],
    '阳泉市': ['城区', '矿区', '郊区', '平定县', '盂县'],
    
    // 河北省
    '石家庄市': ['长安区', '桥西区', '新华区', '井陉矿区', '裕华区', '藁城区', '鹿泉区', '栾城区', '井陉县', '正定县', '行唐县', '灵寿县', '高邑县', '深泽县', '赞皇县', '无极县', '平山县', '元氏县', '赵县', '晋州市', '新乐市'],
    '唐山市': ['路南区', '路北区', '古冶区', '开平区', '丰南区', '丰润区', '曹妃甸区', '滦州市', '滦南县', '乐亭县', '迁西县', '玉田县', '遵化市', '迁安市'],
    '秦皇岛市': ['海港区', '山海关区', '北戴河区', '抚宁区', '青龙满族自治县', '昌黎县', '卢龙县'],
    
    // 山东省
    '济南市': ['历下区', '市中区', '槐荫区', '天桥区', '历城区', '长清区', '章丘区', '济阳区', '莱芜区', '钢城区', '平阴县', '商河县'],
    '青岛市': ['市南区', '市北区', '黄岛区', '崂山区', '李沧区', '城阳区', '即墨区', '胶州市', '平度市', '莱西市'],
    '淄博市': ['淄川区', '张店区', '博山区', '临淄区', '周村区', '桓台县', '高青县', '沂源县'],
    
    // 河南省
    '郑州市': ['中原区', '二七区', '管城回族区', '金水区', '上街区', '惠济区', '中牟县', '巩义市', '荥阳市', '新密市', '新郑市', '登封市'],
    '开封市': ['鼓楼区', '龙亭区', '顺河回族区', '禹王台区', '祥符区', '杞县', '通许县', '尉氏县', '兰考县'],
    '洛阳市': ['老城区', '西工区', '瀍河回族区', '涧西区', '偃师区', '孟津区', '洛龙区', '新安县', '栾川县', '嵩县', '汝阳县', '宜阳县', '洛宁县', '伊川县'],
    
    // 江苏省
    '南京市': ['玄武区', '秦淮区', '建邺区', '鼓楼区', '浦口区', '栖霞区', '雨花台区', '江宁区', '六合区', '溧水区', '高淳区'],
    '无锡市': ['锡山区', '惠山区', '滨湖区', '梁溪区', '新吴区', '江阴市', '宜兴市'],
    '徐州市': ['鼓楼区', '云龙区', '贾汪区', '泉山区', '铜山区', '丰县', '沛县', '睢宁县', '新沂市', '邳州市'],
    
    // 浙江省
    '杭州市': ['上城区', '拱墅区', '西湖区', '滨江区', '萧山区', '余杭区', '富阳区', '临安区', '桐庐县', '淳安县', '建德市', '临平区', '钱塘区'],
    '宁波市': ['海曙区', '江北区', '北仑区', '镇海区', '鄞州区', '奉化区', '象山县', '宁海县', '余姚市', '慈溪市'],
    '温州市': ['鹿城区', '龙湾区', '瓯海区', '洞头区', '永嘉县', '平阳县', '苍南县', '文成县', '泰顺县', '瑞安市', '乐清市', '龙港市'],
    
    // 安徽省
    '合肥市': ['瑶海区', '庐阳区', '蜀山区', '包河区', '长丰县', '肥东县', '肥西县', '庐江县', '巢湖市'],
    '芜湖市': ['镜湖区', '弋江区', '鸠江区', '湾沚区', '繁昌区', '南陵县', '无为市'],
    '蚌埠市': ['龙子湖区', '蚌山区', '禹会区', '淮上区', '怀远县', '五河县', '固镇县'],
    
    // 福建省
    '福州市': ['鼓楼区', '台江区', '仓山区', '马尾区', '晋安区', '长乐区', '闽侯县', '连江县', '罗源县', '闽清县', '永泰县', '平潭县', '福清市'],
    '厦门市': ['思明区', '海沧区', '湖里区', '集美区', '同安区', '翔安区'],
    '莆田市': ['城厢区', '涵江区', '荔城区', '秀屿区', '仙游县'],
    
    // 江西省
    '南昌市': ['东湖区', '西湖区', '青云谱区', '青山湖区', '新建区', '红谷滩区', '南昌县', '安义县', '进贤县'],
    '景德镇市': ['昌江区', '珠山区', '浮梁县', '乐平市'],
    '萍乡市': ['安源区', '湘东区', '莲花县', '上栗县', '芦溪县'],
    
    // 湖北省
    '武汉市': ['江岸区', '江汉区', '硚口区', '汉阳区', '武昌区', '青山区', '洪山区', '东西湖区', '汉南区', '蔡甸区', '江夏区', '黄陂区', '新洲区'],
    '黄石市': ['黄石港区', '西塞山区', '下陆区', '铁山区', '阳新县', '大冶市'],
    '十堰市': ['茅箭区', '张湾区', '郧阳区', '郧西县', '竹山县', '竹溪县', '房县', '丹江口市'],
    
    // 湖南省
    '长沙市': ['芙蓉区', '天心区', '岳麓区', '开福区', '雨花区', '望城区', '长沙县', '浏阳市', '宁乡市'],
    '株洲市': ['荷塘区', '芦淞区', '石峰区', '天元区', '渌口区', '攸县', '茶陵县', '炎陵县', '醴陵市'],
    '湘潭市': ['雨湖区', '岳塘区', '湘潭县', '湘乡市', '韶山市'],
    
    // 广西壮族自治区
    '南宁市': ['兴宁区', '青秀区', '江南区', '西乡塘区', '良庆区', '邕宁区', '武鸣区', '隆安县', '马山县', '上林县', '宾阳县', '横县'],
    '柳州市': ['城中区', '鱼峰区', '柳南区', '柳北区', '柳江区', '柳城县', '鹿寨县', '融安县', '融水苗族自治县', '三江侗族自治县'],
    '桂林市': ['秀峰区', '叠彩区', '象山区', '七星区', '雁山区', '临桂区', '阳朔县', '灵川县', '全州县', '兴安县', '永福县', '灌阳县', '龙胜各族自治县', '资源县', '平乐县', '荔浦市', '恭城瑶族自治县'],
    
    // 海南省
    '海口市': ['秀英区', '龙华区', '琼山区', '美兰区'],
    '三亚市': ['海棠区', '吉阳区', '天涯区', '崖州区'],
    
    // 贵州省
    '贵阳市': ['南明区', '云岩区', '花溪区', '乌当区', '白云区', '观山湖区', '开阳县', '息烽县', '修文县', '清镇市'],
    '六盘水市': ['钟山区', '六枝特区', '水城区', '盘州市'],
    
    // 云南省
    '昆明市': ['五华区', '盘龙区', '官渡区', '西山区', '东川区', '呈贡区', '晋宁区', '富民县', '宜良县', '石林彝族自治县', '嵩明县', '禄劝彝族苗族自治县', '寻甸回族彝族自治县', '安宁市'],
    '曲靖市': ['麒麟区', '沾益区', '马龙区', '陆良县', '师宗县', '罗平县', '富源县', '会泽县', '宣威市'],
    
    // 西藏自治区
    '拉萨市': ['城关区', '堆龙德庆区', '达孜区', '林周县', '当雄县', '尼木县', '曲水县', '墨竹工卡县'],
    '日喀则市': ['桑珠孜区', '南木林县', '江孜县', '定日县', '萨迦县', '拉孜县', '昂仁县', '谢通门县', '白朗县', '仁布县', '康马县', '定结县', '仲巴县', '亚东县', '吉隆县', '聂拉木县', '萨嘎县', '岗巴县'],
    
    // 陕西省
    '西安市': ['新城区', '碑林区', '莲湖区', '灞桥区', '未央区', '雁塔区', '阎良区', '临潼区', '长安区', '高陵区', '鄠邑区', '蓝田县', '周至县'],
    '铜川市': ['王益区', '印台区', '耀州区', '宜君县'],
    
    // 甘肃省
    '兰州市': ['城关区', '七里河区', '西固区', '安宁区', '红古区', '永登县', '皋兰县', '榆中县'],
    '嘉峪关市': ['市辖区'],
    
    // 青海省
    '西宁市': ['城东区', '城中区', '城西区', '城北区', '湟中区', '大通回族土族自治县', '湟源县'],
    '海东市': ['乐都区', '平安区', '民和回族土族自治县', '互助土族自治县', '化隆回族自治县', '循化撒拉族自治县'],
    
    // 宁夏回族自治区
    '银川市': ['兴庆区', '西夏区', '金凤区', '永宁县', '贺兰县', '灵武市'],
    '石嘴山市': ['大武口区', '惠农区', '平罗县'],
    
    // 新疆维吾尔自治区
    '乌鲁木齐市': ['天山区', '沙依巴克区', '新市区', '水磨沟区', '头屯河区', '达坂城区', '米东区', '乌鲁木齐县'],
    '克拉玛依市': ['独山子区', '克拉玛依区', '白碱滩区', '乌尔禾区'],
    
    // 内蒙古自治区
    '呼和浩特市': ['新城区', '回民区', '玉泉区', '赛罕区', '土默特左旗', '托克托县', '和林格尔县', '清水河县', '武川县'],
    '包头市': ['东河区', '昆都仑区', '青山区', '石拐区', '白云鄂博矿区', '九原区', '土默特右旗', '固阳县', '达尔罕茂明安联合旗'],
    
    // 吉林省
    '长春市': ['南关区', '宽城区', '朝阳区', '二道区', '绿园区', '双阳区', '九台区', '农安县', '榆树市', '德惠市', '公主岭市'],
    '吉林市': ['昌邑区', '龙潭区', '船营区', '丰满区', '永吉县', '蛟河市', '桦甸市', '舒兰市', '磐石市'],
    
    // 黑龙江省
    '哈尔滨市': ['道里区', '南岗区', '道外区', '平房区', '松北区', '香坊区', '呼兰区', '阿城区', '双城区', '依兰县', '方正县', '宾县', '巴彦县', '木兰县', '通河县', '延寿县', '尚志市', '五常市'],
    '齐齐哈尔市': ['龙沙区', '建华区', '铁锋区', '昂昂溪区', '富拉尔基区', '碾子山区', '梅里斯达斡尔族区', '龙江县', '依安县', '泰来县', '甘南县', '富裕县', '克山县', '克东县', '拜泉县', '讷河市'],
    
    // 香港、澳门、台湾
    '香港特别行政区': ['中西区', '东区', '南区', '湾仔区', '九龙城区', '观塘区', '深水埗区', '黄大仙区', '油尖旺区', '离岛区', '葵青区', '北区', '西贡区', '沙田区', '大埔区', '荃湾区', '屯门区', '元朗区'],
    '澳门特别行政区': ['花地玛堂区', '圣安多尼堂区', '大堂区', '望德堂区', '风顺堂区', '嘉模堂区', '圣方济各堂区', '路氹城'],
    '台北市': ['中正区', '大同区', '中山区', '万华区', '信义区', '松山区', '大安区', '南港区', '北投区', '内湖区', '士林区', '文山区']
  }
};

// 获取省份、城市、区县列表
export const getRegions = async (params = {}) => {
  try {
    // 1. 获取省份
    if (!params.province) {
      console.log('调用API获取省份数据');
      try {
        const response = await api.get('/regions/public/provinces');
        return { provinces: response.provinces };
      } catch (error) {
        console.log('API调用失败，使用本地省份数据');
        return { provinces: REGION_DATA.provinces };
      }
    }

    // 2. 获取城市
    if (params.province && !params.city) {
      console.log(`调用API获取城市数据: ${params.province}`);
      try {
        const response = await api.get(`/regions/public/cities?province=${params.province}`);
        return { cities: response.cities };
      } catch (error) {
        console.log(`API调用失败，使用本地城市数据: ${params.province}`);
        const cities = REGION_DATA.cities[params.province] || [];
        return { cities };
      }
    }

    // 3. 获取区县
    if (params.province && params.city) {
      console.log(`调用API获取区县数据: ${params.city}`);
      try {
        const response = await api.get(`/regions/public/districts?city=${params.city}`);
        return { districts: response.districts };
      } catch (error) {
        console.log(`API调用失败，使用本地区县数据: ${params.city}`);
        const districts = REGION_DATA.districts[params.city] || [];
        return { districts };
      }
    }

    // 兜底
    return {};
  } catch (error) {
    console.error('获取地区数据失败:', error);
    return {};
  }
};

export const getSchool = async (id) => {
  try {
    const response = await api.get(`/schools/${id}`);
    return response;
  } catch (error) {
    console.error(`获取学校详情失败, ID: ${id}:`, error);
    throw new Error(`获取学校详情失败: ${error.message || '未知错误'}`);
  }
};

export const createSchool = async (schoolData) => {
  try {
    const response = await api.post('/schools/', schoolData);
    return response;
  } catch (error) {
    console.error('创建学校失败:', error);
    throw new Error(`创建学校失败: ${error.message || '未知错误'}`);
  }
};

export const updateSchool = async (id, schoolData) => {
  try {
    const response = await api.put(`/schools/${id}`, schoolData);
    return response;
  } catch (error) {
    console.error(`更新学校失败, ID: ${id}:`, error);
    throw new Error(`更新学校失败: ${error.message || '未知错误'}`);
  }
};

export const deleteSchool = async (id) => {
  try {
    console.log(`尝试删除学校，ID: ${id}`);
    const response = await api.delete(`/admin/schools/${id}`);
    console.log(`删除学校成功，ID: ${id}`);
    return response;
  } catch (error) {
    console.error(`删除学校失败, ID: ${id}:`, error);
    throw new Error(`删除学校失败: ${error.message || '未知错误'}`);
  }
};

// 作业进度相关
export const getHomeworkProgress = async (assignmentId) => {
  try {
    console.log(`获取作业提交情况，作业任务ID: ${assignmentId}`);
    
    // 添加请求超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    // 使用现有的API端点，但添加作业任务ID作为过滤条件
    const response = await api.get('/homework', {
      params: { assignment_id: assignmentId },
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log('获取作业提交情况成功:', response);
    
    // 处理响应数据
    if (Array.isArray(response)) {
      return {
        items: response,
        total: response.length
      };
    } else if (response && Array.isArray(response.items)) {
      return response;
    } else {
      console.warn('作业提交情况响应格式异常:', response);
      return {
        items: [],
        total: 0
      };
    }
  } catch (error) {
    console.error(`获取作业提交情况失败，作业任务ID: ${assignmentId}:`, error);
    
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接');
    }
    
    const errorMessage = error.response?.data?.detail || error.message || '未知错误';
    throw new Error(`获取作业提交情况失败: ${errorMessage}`);
  }
};

// 统计相关
export const getTeacherStatistics = async (params = {}) => {
  try {
    console.log('获取教师统计数据，参数:', params);
    
    // 因为后端不直接支持日期筛选和班级筛选，我们需要使用export API
    let apiEndpoint = '/statistics/teacher';
    
    // 检查是否有日期参数、班级参数或作业参数，如果有则使用export API，它支持这些参数
    if ((params.start_date && params.end_date) || params.class_id || params.assignment_id) {
      console.log('检测到日期参数、班级参数或作业参数，使用export API获取详细统计数据:', params);
      apiEndpoint = '/statistics/export';
    }
    
    const response = await api.get(apiEndpoint, { params });
    
    console.log('获取教师统计数据成功:', response);
    
    // 处理export API返回的数据格式
    if (apiEndpoint === '/statistics/export') {
      // 从export API返回的数据中构建统计信息
      console.log('处理export API返回的数据');
      
      const exportData = response.data || [];
      
      // 获取班级名称（如果指定了class_id，就只有一个班级）
      let className = '';
      if (exportData.length > 0 && exportData[0].class_name) {
        className = exportData[0].class_name;
      }
      
      // 计算统计数据
      const totalHomeworks = exportData.length;
      const gradedHomeworks = exportData.filter(hw => hw.status === 'graded').length;
      
      // 计算正确题数统计
      let correctCounts = exportData.filter(hw => hw.status === 'graded' && hw.accuracy != null)
                                 .map(hw => Math.round(hw.accuracy * 10)); // 假设满分是10题
      
      const highestCorrectCount = correctCounts.length > 0 ? Math.max(...correctCounts) : 0;
      const lowestCorrectCount = correctCounts.length > 0 ? Math.min(...correctCounts) : 0;
      const averageCorrectCount = correctCounts.length > 0 
        ? correctCounts.reduce((sum, count) => sum + count, 0) / correctCounts.length 
        : 0;
      
      // 构建处理后的响应
      response.class_count = 1;
      response.homework_count = totalHomeworks;
      response.graded_homework_count = gradedHomeworks;
      response.highest_correct_count = highestCorrectCount;
      response.average_correct_count = averageCorrectCount;
      response.lowest_correct_count = lowestCorrectCount;
      
      // 如果选中了特定班级，就用这个班级构建class_statistics
      if (params.class_id) {
        const classStatObj = {
          class_id: parseInt(params.class_id),
          class_name: className || `班级 ${params.class_id}`,
          homework_count: totalHomeworks,
          student_count: new Set(exportData.map(hw => hw.student_name)).size,
          highest_correct_count: highestCorrectCount,
          average_correct_count: averageCorrectCount,
          lowest_correct_count: lowestCorrectCount,
          // 添加作业任务数和平均分字段
          assignment_count: new Set(exportData.map(hw => hw.assignment_id)).size,
          average_score: exportData.filter(hw => hw.status === 'graded' && hw.score != null).length > 0
            ? exportData.filter(hw => hw.status === 'graded' && hw.score != null)
                .reduce((sum, hw) => sum + hw.score, 0) / 
                exportData.filter(hw => hw.status === 'graded' && hw.score != null).length
            : 0
        };
        
        response.class_statistics = { [`class_${params.class_id}`]: classStatObj };
        response.class_statistics_array = [classStatObj];
        console.log('为选定班级构建统计数据:', classStatObj);
      }
    }
    
    // 格式化班级统计数据，将对象转换为数组
    let classStatsArray = [];
    
    if (response && response.class_statistics && typeof response.class_statistics === 'object') {
      try {
        classStatsArray = Object.entries(response.class_statistics)
          .filter(([key, value]) => typeof value === 'object' && value !== null && value.class_id)
          .map(([key, value]) => {
            // 确保每个班级对象都有assignment_count和average_score字段
            if (value.assignment_count === undefined) {
              console.log(`班级 ${value.class_id || key} 缺少assignment_count字段，设置为默认值0`);
              value.assignment_count = 0;
            }
            if (value.average_score === undefined) {
              console.log(`班级 ${value.class_id || key} 缺少average_score字段，设置为默认值0`);
              value.average_score = 0;
            }
            return value;
          });
        
        console.log('班级统计数据格式化:', classStatsArray.length, '个班级');
      } catch (err) {
        console.error('处理班级统计数据时出错:', err);
      }
    } else if (response && Array.isArray(response.class_statistics)) {
      // 如果已经是数组格式，直接使用
      classStatsArray = response.class_statistics.map(cls => {
        // 确保每个班级对象都有assignment_count和average_score字段
        if (cls.assignment_count === undefined) {
          console.log(`班级 ${cls.class_id} 缺少assignment_count字段，设置为默认值0`);
          cls.assignment_count = 0;
        }
        if (cls.average_score === undefined) {
          console.log(`班级 ${cls.class_id} 缺少average_score字段，设置为默认值0`);
          cls.average_score = 0;
        }
        return cls;
      });
      console.log('班级统计数据已经是数组格式:', classStatsArray.length, '个班级');
    } else {
      console.warn('班级统计数据格式异常:', response?.class_statistics);
    }
    
    // 确保我们至少有一个默认班级（只有在真实数据为空时才使用模拟数据）
    if (classStatsArray.length === 0) {
      classStatsArray = [
        {
          class_id: 1,
          class_name: "示例班级",
          student_count: 25,
          homework_count: 5,
          average_accuracy: 0.8,
          highest_correct_count: 5,
          average_correct_count: 3.5,
          lowest_correct_count: 2,
          assignment_count: 3,
          average_score: 80
        }
      ];
    }
    
    // 确保返回的数据包含预期的字段，避免前端读取时出现undefined
    return {
      school_count: response.school_count || 0,
      class_count: response.class_count || 0,
      student_count: response.student_count || 0,
      homework_count: response.homework_count || response.total_homework_count || 0,
      total_homework_count: response.total_homework_count || response.homework_count || 0,
      corrected_count: response.corrected_count || response.graded_homework_count || 0,
      graded_homework_count: response.graded_homework_count || response.corrected_count || 0,
      pending_homework_count: response.pending_homework_count || 
                             ((response.homework_count || response.total_homework_count || 0) - 
                              (response.graded_homework_count || response.corrected_count || 0)),
      average_accuracy: response.average_accuracy || 0,
      highest_correct_count: response.highest_correct_count || 0,
      average_correct_count: response.average_correct_count || 0,
      lowest_correct_count: response.lowest_correct_count || 0,
      class_statistics: response.class_statistics || {},
      class_statistics_array: classStatsArray,
      recent_homeworks: response.recent_homeworks || response.recent_assignments || []
    };
  } catch (error) {
    console.error('获取教师统计数据失败:', error);
    // 返回模拟数据，避免UI崩溃
    return {
      school_count: 1,
      class_count: 1,
      student_count: 25,
      total_homework_count: 5,
      graded_homework_count: 3,
      corrected_count: 3,
      pending_homework_count: 2,
      average_accuracy: 0.8,
      highest_correct_count: 5,
      average_correct_count: 3.5,
      lowest_correct_count: 2,
      class_statistics: {
        "class_1": {
          class_id: 1,
          class_name: "示例班级",
          student_count: 25,
          homework_count: 5,
          average_accuracy: 0.8,
          highest_correct_count: 5,
          average_correct_count: 3.5,
          lowest_correct_count: 2,
          assignment_count: 3,
          average_score: 80
        }
      },
      class_statistics_array: [
        {
          class_id: 1,
          class_name: "示例班级",
          student_count: 25,
          homework_count: 5,
          average_accuracy: 0.8,
          highest_correct_count: 5,
          average_correct_count: 3.5,
          lowest_correct_count: 2,
          assignment_count: 3,
          average_score: 80
        }
      ],
      recent_homeworks: [
        {
          id: 1,
          title: "示例作业1",
          correct_count: 5,
          student_id: 1,
          student_name: "示例学生",
          created_at: new Date().toISOString()
        }
      ],
      error: error.message || '未知错误'
    };
  }
};

export const getStudentStatistics = async (params = {}) => {
  try {
    console.log('获取学生统计数据，参数:', params);
    const response = await api.get('/statistics/student', { params });
    
    console.log('获取学生统计数据成功:', response);
    
    // 确保accuracy_trend是数组
    if (!response.accuracy_trend || !Array.isArray(response.accuracy_trend)) {
      console.warn('正确率趋势数据格式异常:', response?.accuracy_trend);
      response.accuracy_trend = [];
    }
    
    return response;
  } catch (error) {
    console.error('获取学生统计数据失败:', error);
    // 返回模拟数据，避免UI崩溃
    return {
      total_homework_count: 0,
      completed_homework_count: 0,
      pending_homework_count: 0,
      average_accuracy: 0,
      wrong_question_count: 0,
      reinforcement_exercise_count: 0,
      completed_exercise_count: 0,
      highest_correct_count: 0,
      average_correct_count: 0,
      lowest_correct_count: 0,
      accuracy_trend: [],
      error: error.message || '未知错误'
    };
  }
};

export const getWrongQuestionStatistics = () => {
  return api.get('/statistics/wrong-questions');
};

export const exportStatistics = (params) => {
  return api.get('/statistics/export', { params });
};

// AI助手相关
export const chatWithAI = (messages) => {
  return api.post('/ai/chat', { messages });
};

// 错误分析和强化建议
export const analyzeError = (data) => {
  console.log('调用analyzeError API, data:', data);
  return api.post('/ai/analyze-error', data);
};

export const generateReinforcement = (data) => {
  console.log('调用generateReinforcement API, data:', data);
  return api.post('/ai/generate-reinforcement', data);
};

// AI配置管理相关
export const getAiConfigs = async () => {
  try {
    console.log('获取AI配置列表 - 开始');
    const token = localStorage.getItem('token');
    console.log('令牌存在:', !!token);

    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

    // 先尝试管理员接口
    try {
      const response = await api.get('/admin/ai-configs', {
        signal: controller.signal,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      clearTimeout(timeoutId);
      console.log('获取AI配置列表成功（管理员接口）:', response);
      return response;
    } catch (adminError) {
      // 如果管理员接口失败（403权限不足），尝试教师接口
      if (adminError.response && adminError.response.status === 403) {
        console.log('管理员接口权限不足，尝试教师接口');
        const response = await api.get('/admin/ai-configs/available', {
          signal: controller.signal,
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        clearTimeout(timeoutId);
        console.log('获取AI配置列表成功（教师接口）:', response);
        return response;
      } else {
        throw adminError;
      }
    }
  } catch (error) {
    console.error('获取AI配置列表失败:', error);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接或服务器状态');
    }
    throw error;
  }
};

export const createAiConfig = async (configData) => {
  try {
    console.log('创建AI配置 - 开始', configData);
    const token = localStorage.getItem('token');
    console.log('令牌存在:', !!token);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.post('/admin/ai-configs', configData, {
      signal: controller.signal,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    clearTimeout(timeoutId);
    console.log('创建AI配置成功:', response);
    return response;
  } catch (error) {
    console.error('创建AI配置失败:', error);
    console.error('错误类型:', error.name);
    console.error('错误消息:', error.message);
    
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接或服务器状态');
    }
    
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应:', error.response.data);
      throw new Error(`创建失败: ${error.response.data?.detail || error.message}`);
    }
    
    throw new Error(`操作失败: ${error.message}`);
  }
};

export const updateAiConfig = async (id, configData) => {
  try {
    console.log(`更新AI配置(ID: ${id}) - 开始`, configData);
    const token = localStorage.getItem('token');
    console.log('令牌存在:', !!token);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.put(`/admin/ai-configs/${id}`, configData, {
      signal: controller.signal,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    clearTimeout(timeoutId);
    console.log('更新AI配置成功:', response);
    return response;
  } catch (error) {
    console.error(`更新AI配置(ID: ${id})失败:`, error);
    console.error('错误类型:', error.name);
    console.error('错误消息:', error.message);
    
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接或服务器状态');
    }
    
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应:', error.response.data);
      throw new Error(`更新失败: ${error.response.data?.detail || error.message}`);
    }
    
    throw new Error(`操作失败: ${error.message}`);
  }
};

export const deleteAiConfig = async (id) => {
  try {
    console.log(`删除AI配置(ID: ${id}) - 开始`);
    const token = localStorage.getItem('token');
    console.log('令牌存在:', !!token);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.delete(`/admin/ai-configs/${id}`, {
      signal: controller.signal,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    clearTimeout(timeoutId);
    console.log('删除AI配置成功:', response);
    return response;
  } catch (error) {
    console.error(`删除AI配置(ID: ${id})失败:`, error);
    console.error('错误类型:', error.name);
    console.error('错误消息:', error.message);
    
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接或服务器状态');
    }
    
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应:', error.response.data);
      throw new Error(`删除失败: ${error.response.data?.detail || error.message}`);
    }
    
    throw new Error(`操作失败: ${error.message}`);
  }
};

// 作业科目管理API - 已移至文件末尾并更新

export const createSubject = async (subjectData) => {
  try {
    console.log('调用createSubject API, data:', subjectData);
    const token = localStorage.getItem('token');
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.post('/admin/subjects', subjectData, {
      signal: controller.signal,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    clearTimeout(timeoutId);
    console.log('创建作业科目成功:', response);
    return response;
  } catch (error) {
    console.error('创建作业科目失败:', error);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接或服务器状态');
    }
    throw error;
  }
};

export const updateSubject = async (id, subjectData) => {
  try {
    console.log(`调用updateSubject API, id: ${id}, data:`, subjectData);
    const token = localStorage.getItem('token');
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.put(`/admin/subjects/${id}`, subjectData, {
      signal: controller.signal,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    clearTimeout(timeoutId);
    console.log('更新作业科目成功:', response);
    return response;
  } catch (error) {
    console.error('更新作业科目失败:', error);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接或服务器状态');
    }
    throw error;
  }
};

export const deleteSubject = async (id) => {
  try {
    console.log(`调用deleteSubject API, id: ${id}`);
    const token = localStorage.getItem('token');
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.delete(`/admin/subjects/${id}`, {
      signal: controller.signal,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    clearTimeout(timeoutId);
    console.log('删除作业科目成功:', response);
    return response;
  } catch (error) {
    console.error('删除作业科目失败:', error);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接或服务器状态');
    }
    throw error;
  }
};

// 学校管理API
export const getSchoolDetail = async (schoolId) => {
  try {
    console.log(`获取学校信息，ID: ${schoolId}`);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.get(`/admin/schools/${schoolId}`, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log('获取学校信息成功:', response);
    return response;
  } catch (error) {
    console.error('获取学校信息失败:', error);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接');
    }
    // 返回默认值避免前端崩溃
    return {
      id: schoolId,
      name: "加载失败",
      province: "",
      city: "",
      district: "",
      address: "",
      contact_info: "",
      is_active: true,
      class_count: 0,
      teacher_count: 0,
      student_count: 0
    };
  }
};

export const updateSchoolInfo = async (schoolId, schoolData) => {
  try {
    console.log(`更新学校信息，ID: ${schoolId}`, schoolData);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.put(`/admin/schools/${schoolId}`, schoolData, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log('更新学校信息成功:', response);
    return response;
  } catch (error) {
    console.error('更新学校信息失败:', error);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接');
    }
    throw error;
  }
};

export const getClassesBySchool = async (schoolId) => {
  try {
    console.log(`获取学校班级列表，学校ID: ${schoolId}，类型: ${typeof schoolId}`);
    
    if (schoolId === null || schoolId === undefined) {
      console.warn('未提供学校ID，无法获取班级列表');
      return [];
    }
    
    // 尝试将schoolId转换为数字，但如果失败也继续使用原值
    let numericSchoolId;
    try {
      numericSchoolId = parseInt(schoolId, 10);
      if (isNaN(numericSchoolId)) {
        console.warn('学校ID不是数字，使用原始值:', schoolId);
        numericSchoolId = schoolId;
      }
    } catch (e) {
      console.warn('转换学校ID失败，使用原始值:', schoolId);
      numericSchoolId = schoolId;
    }
    
    // 使用处理后的ID
    schoolId = numericSchoolId;
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    let response;
    let errorMessages = [];
    
    try {
      // 第一种API路径尝试
      console.log(`尝试第一种API路径: /admin/schools/${schoolId}/classes`);
      response = await api.get(`/admin/schools/${schoolId}/classes`, {
        signal: controller.signal
      });
      console.log('第一种API路径成功，响应:', response);
    } catch (apiError) {
      errorMessages.push(`第一种API路径错误: ${apiError.message || '未知错误'}`);
      console.warn(`第一种API路径失败: /admin/schools/${schoolId}/classes`, apiError);
      
      // 如果第一种API路径失败，尝试第二种路径
      try {
        console.log(`尝试第二种API路径: /schools/${schoolId}/classes`);
        response = await api.get(`/schools/${schoolId}/classes`, {
          signal: controller.signal
        });
        console.log('第二种API路径成功，响应:', response);
      } catch (secondError) {
        errorMessages.push(`第二种API路径错误: ${secondError.message || '未知错误'}`);
        console.warn(`第二种API路径也失败: /schools/${schoolId}/classes`, secondError);
        
        // 如果两种都失败，尝试查询所有班级然后过滤
        try {
          console.log(`尝试第三种API路径: /admin/classes?school_id=${schoolId}`);
          const allClasses = await api.get(`/admin/classes?school_id=${schoolId}`, {
            signal: controller.signal
          });
          console.log('第三种API路径成功，响应:', allClasses);

          // 过滤属于这个学校的班级
          response = Array.isArray(allClasses) ? allClasses.filter(c => c.school_id === parseInt(schoolId)) : [];
          console.log('过滤后的班级列表:', response);
        } catch (thirdError) {
          errorMessages.push(`第三种API路径错误: ${thirdError.message || '未知错误'}`);
          console.error('所有API路径都失败:', errorMessages);
          response = [];
        }
      }
    }
    
    clearTimeout(timeoutId);
    
    // 确保响应是数组格式
    if (!Array.isArray(response)) {
      console.warn('响应不是数组格式，尝试转换:', response);
      if (response && typeof response === 'object') {
        if (Array.isArray(response.items)) {
          response = response.items;
        } else {
          response = [response];
        }
      } else {
        response = [];
      }
    }
    
    console.log('获取学校班级列表成功，最终结果:', response);
    return response;
  } catch (error) {
    console.error('获取学校班级列表失败:', error);
    if (error.name === 'AbortError') {
      console.error('请求超时');
    }
    // 返回空数组避免前端崩溃
    return [];
  }
};

// 获取所有可用年级列表
export const getAvailableGrades = async () => {
  try {
    console.log('获取可用年级列表');
    const response = await api.get('/admin/classes/grades');
    console.log('获取年级列表成功:', response);
    return response;
  } catch (error) {
    console.error('获取年级列表失败:', error);
    // 返回默认年级列表作为备用
    return [
      "小学一年级", "小学二年级", "小学三年级", 
      "小学四年级", "小学五年级", "小学六年级",
      "初中一年级", "初中二年级", "初中三年级",
      "高中一年级", "高中二年级", "高中三年级"
    ];
  }
};

export const createClassForSchool = async (classData) => {
  try {
    console.log('创建班级:', classData);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.post('/admin/classes', classData, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log('创建班级成功:', response);
    return response;
  } catch (error) {
    console.error('创建班级失败:', error);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接');
    }
    throw error;
  }
};

export const updateClassInfo = async (classId, classData) => {
  try {
    console.log(`更新班级，ID: ${classId}`, classData);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.put(`/admin/classes/${classId}`, classData, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log('更新班级成功:', response);
    return response;
  } catch (error) {
    console.error('更新班级失败:', error);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接');
    }
    throw error;
  }
};

export const deleteClassById = async (classId) => {
  try {
    console.log(`删除班级，ID: ${classId}`);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.delete(`/admin/classes/${classId}`, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log('删除班级成功:', response);
    return response;
  } catch (error) {
    console.error('删除班级失败:', error);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接');
    }
    throw error;
  }
};

// 角色管理API
export const getSchoolRoles = async () => {
  try {
    console.log('获取角色列表');
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    let response;
    try {
      // 尝试第一种API路径
      response = await api.get('/admin/roles', {
        signal: controller.signal
      });
    } catch (apiError) {
      console.warn('第一种API路径失败: /admin/roles', apiError);
      // 如果第一种路径失败，尝试第二种
      try {
        response = await api.get('/roles', {
          signal: controller.signal
        });
      } catch (secondError) {
        console.warn('第二种API路径也失败: /roles', secondError);
        // 返回空数组，让前端组件使用预定义的USER_ROLES
        response = [];
      }
    }
    
    clearTimeout(timeoutId);
    console.log('获取角色列表成功:', response);
    return Array.isArray(response) ? response : [];
  } catch (error) {
    console.error('获取角色列表失败:', error);
    if (error.name === 'AbortError') {
      console.error('请求超时');
    }
    // 返回空数组，让前端组件使用预定义的USER_ROLES
    return [];
  }
};

export const getSchoolUsers = async (schoolId) => {
  try {
    console.log(`获取学校用户列表，学校ID: ${schoolId}`);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    let response;
    try {
      // 尝试第一种API路径
      response = await api.get(`/admin/schools/${schoolId}/users`, {
        signal: controller.signal
      });
    } catch (apiError) {
      console.warn(`第一种API路径失败: /admin/schools/${schoolId}/users`, apiError);
      // 如果失败，尝试第二种路径
      try {
        response = await api.get(`/schools/${schoolId}/users`, {
          signal: controller.signal
        });
      } catch (secondError) {
        console.warn(`第二种API路径也失败: /schools/${schoolId}/users`, secondError);
        // 返回空数组避免前端崩溃
        response = [];
      }
    }
    
    clearTimeout(timeoutId);
    console.log('获取学校用户列表成功:', response);
    return Array.isArray(response) ? response : [];
  } catch (error) {
    console.error('获取学校用户列表失败:', error);
    if (error.name === 'AbortError') {
      console.error('请求超时');
    }
    // 返回空数组避免前端崩溃
    return [];
  }
};

export const assignRole = async (roleData) => {
  try {
    console.log('分配角色:', roleData);
    const response = await api.post('/admin/roles/assign', roleData);
    console.log('分配角色成功:', response);
    return response;
  } catch (error) {
    console.error('分配角色失败:', error);
    throw error;
  }
};

export const revokeRole = async (roleData) => {
  try {
    console.log('撤销角色:', roleData);
    const response = await api.post('/admin/roles/revoke', roleData);
    console.log('撤销角色成功:', response);
    return response;
  } catch (error) {
    console.error('撤销角色失败:', error);
    throw error;
  }
};

// 获取仪表盘统计数据
export const getDashboardStatistics = async (params = {}) => {
  try {
    console.log('调用getDashboardStatistics API', params ? `参数: ${JSON.stringify(params)}` : '');
    const response = await api.get('/statistics/statistics/dashboard', { params });
    console.log('获取仪表盘统计数据成功:', response);
    return response;
  } catch (error) {
    console.error('获取仪表盘统计数据失败:', error);
    throw error;
  }
};

// 获取注册设置
export const getRegistrationSettings = async () => {
  try {
    console.log('获取注册设置...');
    console.log('发起请求: GET /api/public/registration-settings');
    // 注意：这是公开API，不需要token
    const response = await axios.get('/api/public/registration-settings');
    console.log('注册设置API响应:', response);
    console.log('获取注册设置成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取注册设置失败:', error);
    console.error('错误详情:', error.response ? error.response.data : '无响应数据');
    console.error('错误状态:', error.response ? error.response.status : '未知状态码');
    // 默认返回都允许注册
    return {
      allow_student_registration: true,
      allow_teacher_registration: true
    };
  }
};

// 获取高级注册设置
export const getAdvancedRegistrationSettings = async () => {
  try {
    console.log('获取高级注册设置...');
    console.log('发起请求: GET /api/public/advanced-registration-settings');
    // 注意：这是公开API，不需要token
    const response = await axios.get('/api/public/advanced-registration-settings');
    console.log('高级注册设置API响应:', response);
    console.log('获取高级注册设置成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取高级注册设置失败:', error);
    console.error('错误详情:', error.response ? error.response.data : '无响应数据');
    console.error('错误状态:', error.response ? error.response.status : '未知状态码');
    // 返回null表示获取失败
    return null;
  }
};

// 获取可用角色
export const getAvailableRoles = async () => {
  try {
    console.log('获取可用角色...');
    console.log('发起请求: GET /api/public/available-roles');
    // 注意：这是公开API，不需要token
    const response = await axios.get('/api/public/available-roles');
    console.log('可用角色API响应:', response);
    
    if (response.data && Array.isArray(response.data.roles)) {
      console.log('获取到可用角色:', response.data.roles.length, '个');
      console.log('角色列表:', response.data.roles.map(r => r.name).join(', '));
      
      // 检查是否有可用角色
      if (response.data.roles.length === 0) {
        console.warn('API返回的可用角色列表为空');
      }
    } else {
      console.warn('API返回的数据格式不符合预期:', response.data);
    }
    
    return response.data;
  } catch (error) {
    console.error('获取可用角色失败:', error);
    console.error('错误详情:', error.response ? error.response.data : '无响应数据');
    console.error('错误状态:', error.response ? error.response.status : '未知状态码');
    
    // 尝试从错误响应中获取更多信息
    if (error.response && error.response.data) {
      console.error('服务器返回的错误信息:', error.response.data);
    }
    
    // 返回默认角色
    console.log('返回默认角色配置');
    return {
      roles: [
        { name: "student", requires_approval: false, fields: { school: { required: true }, class: { required: true }, subject: { required: false, hidden: true } } },
        { name: "teacher", requires_approval: true, fields: { school: { required: true }, class: { required: false }, subject: { required: true } } }
      ],
      allow_school_creation: false
    };
  }
};

// 获取注册审批列表
export const getRegistrationApprovals = async (params = {}) => {
  try {
    console.log('获取注册审批列表, 参数:', params);
    const queryParams = new URLSearchParams();
    if (params.status) queryParams.append('status', params.status);
    if (params.role_name) queryParams.append('role_name', params.role_name);
    if (params.school_id) queryParams.append('school_id', params.school_id);
    if (params.skip) queryParams.append('skip', params.skip);
    if (params.limit) queryParams.append('limit', params.limit);

    const response = await api.get(`/admin/registration-approvals?${queryParams.toString()}`);
    console.log('获取注册审批列表成功:', response);
    // 由于axios拦截器已经返回response.data，这里直接返回response即可
    return response;
  } catch (error) {
    console.error('获取注册审批列表失败:', error);
    throw error;
  }
};

// 一级审核注册申请
export const firstReviewRegistration = async (approvalId, data) => {
  try {
    console.log(`一级审核注册申请 ID:${approvalId}, 数据:`, data);
    const response = await api.put(`/admin/registration-approvals/${approvalId}/first-review`, data);
    console.log('一级审核注册申请成功:', response);
    return response;
  } catch (error) {
    console.error('一级审核注册申请失败:', error);
    throw error;
  }
};

// 二级审核注册申请
export const finalReviewRegistration = async (approvalId, data) => {
  try {
    console.log(`二级审核注册申请 ID:${approvalId}, 数据:`, data);
    const response = await api.put(`/admin/registration-approvals/${approvalId}/final-review`, data);
    console.log('二级审核注册申请成功:', response);
    return response;
  } catch (error) {
    console.error('二级审核注册申请失败:', error);
    throw error;
  }
};

// 高级注册API
export const advancedRegister = async (userData) => {
  try {
    console.log('调用高级注册API, 数据:', userData);
    const response = await axios.post('/api/advanced-register', userData);
    console.log('高级注册成功:', response);
    return response.data;
  } catch (error) {
    console.error('高级注册失败:', error);
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// 搜索学生
export const searchStudents = async (params = {}) => {
  try {
    console.log('搜索学生, 参数:', params);
    const queryParams = new URLSearchParams();
    if (params.name) queryParams.append('name', params.name);
    if (params.class_id) queryParams.append('class_id', params.class_id);
    if (params.school_id) queryParams.append('school_id', params.school_id);
    
    const response = await axios.get(`/api/public/student-search?${queryParams.toString()}`);
    console.log('搜索学生成功:', response);
    return response.data;
  } catch (error) {
    console.error('搜索学生失败:', error);
    throw error;
  }
};

// 创建家长-学生绑定
export const createParentStudentBinding = async (bindingData) => {
  try {
    console.log('创建家长-学生绑定, 数据:', bindingData);
    const response = await api.post('/parent-student-binding', bindingData);
    console.log('创建家长-学生绑定成功:', response);
    return response;
  } catch (error) {
    console.error('创建家长-学生绑定失败:', error);
    throw error;
  }
};

// 验证绑定码
export const verifyBindingCode = async (verificationData) => {
  try {
    console.log('验证绑定码, 数据:', verificationData);
    const response = await api.post('/verify-binding-code', verificationData);
    console.log('验证绑定码成功:', response);
    return response;
  } catch (error) {
    console.error('验证绑定码失败:', error);
    throw error;
  }
};

// 创建临时家长-学生绑定（用于注册流程，不需要认证）
export const createTempParentStudentBinding = async (bindingData) => {
  try {
    console.log('创建临时家长-学生绑定, 数据:', bindingData);
    const response = await axios.post(`${getBaseURL()}/public/temp-parent-student-binding`, bindingData);
    console.log('创建临时家长-学生绑定成功:', response);
    return response.data;
  } catch (error) {
    console.error('创建临时家长-学生绑定失败:', error);
    throw error;
  }
};

// 验证临时绑定码（用于注册流程，不需要认证）
export const verifyTempBindingCode = async (verificationData) => {
  try {
    console.log('验证临时绑定码, 数据:', verificationData);
    const response = await axios.post(`${getBaseURL()}/public/verify-temp-binding-code`, verificationData);
    console.log('验证临时绑定码成功:', response);
    return response.data;
  } catch (error) {
    console.error('验证临时绑定码失败:', error);
    throw error;
  }
};

// 获取学生的待验证绑定请求
export const getPendingBindings = async () => {
  try {
    console.log('获取待验证绑定请求');
    const response = await api.get('/students/pending-bindings');
    console.log('获取待验证绑定请求成功:', response);
    return response;
  } catch (error) {
    console.error('获取待验证绑定请求失败:', error);
    throw error;
  }
};

// 拒绝绑定请求
export const rejectBinding = async (bindingId) => {
  try {
    console.log('拒绝绑定请求:', bindingId);
    const response = await api.post(`/students/reject-binding/${bindingId}`);
    console.log('拒绝绑定请求成功:', response);
    return response;
  } catch (error) {
    console.error('拒绝绑定请求失败:', error);
    throw error;
  }
};

// 创建学校申请
export const createSchoolApplication = async (applicationData) => {
  try {
    console.log('创建学校申请, 数据:', applicationData);
    const response = await api.post('/school-application', applicationData);
    console.log('创建学校申请成功:', response);
    return response;
  } catch (error) {
    console.error('创建学校申请失败:', error);
    throw error;
  }
};

// 获取用户的学校申请列表
export const getUserSchoolApplications = async () => {
  try {
    console.log('获取用户的学校申请列表');
    const response = await api.get('/user/school-applications');
    console.log('获取用户的学校申请列表成功:', response);
    return response;
  } catch (error) {
    console.error('获取用户的学校申请列表失败:', error);
    throw error;
  }
};

// 管理员获取所有学校申请
export const getSchoolApplications = async (params = {}) => {
  try {
    console.log('获取所有学校申请, 参数:', params);
    const queryParams = new URLSearchParams();
    if (params.status) queryParams.append('status', params.status);

    const response = await api.get(`/admin/school-applications?${queryParams.toString()}`);
    console.log('获取所有学校申请成功:', response);
    return response;
  } catch (error) {
    console.error('获取所有学校申请失败:', error);
    throw error;
  }
};

// 审批学校申请
export const reviewSchoolApplication = async (applicationId, reviewData) => {
  try {
    console.log(`审批学校申请 ID:${applicationId}, 数据:`, reviewData);
    const response = await api.put(`/admin/school-applications/${applicationId}/review`, reviewData);
    console.log('审批学校申请成功:', response);
    return response;
  } catch (error) {
    console.error('审批学校申请失败:', error);
    throw error;
  }
};

// 获取班级学生列表
export const getClassStudents = async (schoolId, classId, params = {}) => {
  try {
    console.log(`获取班级学生列表，学校ID: ${schoolId}, 班级ID: ${classId}`);

    const queryParams = new URLSearchParams();
    if (params.skip) queryParams.append('skip', params.skip);
    if (params.limit) queryParams.append('limit', params.limit);

    const response = await api.get(`/schools/${schoolId}/classes/${classId}/students?${queryParams.toString()}`);
    console.log('获取班级学生列表成功:', response);
    return response;
  } catch (error) {
    console.error('获取班级学生列表失败:', error);
    throw error;
  }
};

export default api; 

// 获取科目分类列表
export const getSubjectCategories = async (params = {}) => {
  try {
    console.log('调用getSubjectCategories API');
    const queryParams = new URLSearchParams();
    if (params.skip) queryParams.append('skip', params.skip);
    if (params.limit) queryParams.append('limit', params.limit);
    
    const response = await api.get(`/admin/subject-categories?${queryParams.toString()}`);
    console.log('获取科目分类列表成功:', response);
    return response;
  } catch (error) {
    console.error('获取科目分类列表失败:', error);
    return { total: 0, items: [] };
  }
};

// 创建科目分类
export const createSubjectCategory = async (categoryData) => {
  try {
    console.log('调用createSubjectCategory API');
    const response = await api.post('/admin/subject-categories', categoryData);
    console.log('创建科目分类成功:', response);
    return response;
  } catch (error) {
    console.error('创建科目分类失败:', error);
    throw error;
  }
};

// 更新科目分类
export const updateSubjectCategory = async (categoryId, categoryData) => {
  try {
    console.log(`调用updateSubjectCategory API, ID: ${categoryId}`);
    const response = await api.put(`/admin/subject-categories/${categoryId}`, categoryData);
    console.log('更新科目分类成功:', response);
    return response;
  } catch (error) {
    console.error('更新科目分类失败:', error);
    throw error;
  }
};

// 删除科目分类
export const deleteSubjectCategory = async (categoryId) => {
  try {
    console.log(`调用deleteSubjectCategory API, ID: ${categoryId}`);
    const response = await api.delete(`/admin/subject-categories/${categoryId}`);
    console.log('删除科目分类成功:', response);
    return response;
  } catch (error) {
    console.error('删除科目分类失败:', error);
    throw error;
  }
};

// 获取所有年级
export const getAllGrades = async () => {
  try {
    console.log('调用getAllGrades API');
    const response = await api.get('/admin/grades');
    console.log('获取年级列表成功:', response);
    return response;
  } catch (error) {
    console.error('获取年级列表失败:', error);
    return [];
  }
};

// 获取指定年级的科目
export const getSubjectsByGrade = async (grade) => {
  try {
    console.log(`调用getSubjectsByGrade API, 年级: ${grade}`);
    const response = await api.get(`/admin/subjects/by-grade/${encodeURIComponent(grade)}`);
    console.log('获取年级科目列表成功:', response);
    return response;
  } catch (error) {
    console.error('获取年级科目列表失败:', error);
    return [];
  }
};

// 修改getSubjects函数，支持分类筛选
export const getSubjects = async (params = {}) => {
  try {
    console.log('调用getSubjects API');
    
    const queryParams = new URLSearchParams();
    if (params.skip) queryParams.append('skip', params.skip);
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.category_id) queryParams.append('category_id', params.category_id);
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await api.get(`/admin/subjects?${queryParams.toString()}`, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    console.log('获取作业科目列表成功:', response);
    return response;
  } catch (error) {
    console.error('获取作业科目列表失败:', error);
    if (error.name === 'AbortError') {
      console.error('请求超时');
    }
    // 返回空对象，避免前端崩溃
    return { total: 0, items: [] };
  }
};

// 获取角色列表
export const getRoles = async () => {
  try {
    console.log('调用getRoles API');
    const response = await api.get('/admin/roles');
    console.log('获取角色列表成功:', response);
    return response;
  } catch (error) {
    console.error('获取角色列表失败:', error);
    return [];
  }
};

// 获取角色的科目权限
export const getRoleSubjectPermissions = async (roleId) => {
  try {
    console.log(`调用getRoleSubjectPermissions API, 角色ID: ${roleId}`);
    const response = await api.get(`/admin/roles/${roleId}/subject-permissions`);
    console.log('获取角色科目权限成功:', response);
    return response;
  } catch (error) {
    console.error('获取角色科目权限失败:', error);
    return [];
  }
};

// 创建角色的科目权限
export const createRoleSubjectPermission = async (roleId, permissionData) => {
  try {
    console.log(`调用createRoleSubjectPermission API, 角色ID: ${roleId}`);
    const response = await api.post(`/admin/roles/${roleId}/subject-permissions`, permissionData);
    console.log('创建角色科目权限成功:', response);
    return response;
  } catch (error) {
    console.error('创建角色科目权限失败:', error);
    throw error;
  }
};

// 更新角色的科目权限
export const updateRoleSubjectPermission = async (roleId, permissionId, permissionData) => {
  try {
    console.log(`调用updateRoleSubjectPermission API, 角色ID: ${roleId}, 权限ID: ${permissionId}`);
    const response = await api.put(`/admin/roles/${roleId}/subject-permissions/${permissionId}`, permissionData);
    console.log('更新角色科目权限成功:', response);
    return response;
  } catch (error) {
    console.error('更新角色科目权限失败:', error);
    throw error;
  }
};

// 删除角色的科目权限
export const deleteRoleSubjectPermission = async (roleId, permissionId) => {
  try {
    console.log(`调用deleteRoleSubjectPermission API, 角色ID: ${roleId}, 权限ID: ${permissionId}`);
    const response = await api.delete(`/admin/roles/${roleId}/subject-permissions/${permissionId}`);
    console.log('删除角色科目权限成功:', response);
    return response;
  } catch (error) {
    console.error('删除角色科目权限失败:', error);
    throw error;
  }
};

// 批量设置角色的科目权限
export const batchSetRoleSubjectPermissions = async (roleId, permissionsData) => {
  try {
    console.log(`调用batchSetRoleSubjectPermissions API, 角色ID: ${roleId}`);
    const response = await api.post(`/admin/roles/${roleId}/batch-subject-permissions`, {
      role_id: roleId,
      permissions: permissionsData
    });
    console.log('批量设置角色科目权限成功:', response);
    return response;
  } catch (error) {
    console.error('批量设置角色科目权限失败:', error);
    throw error;
  }
};

// 获取当前用户对指定科目的权限
export const getUserSubjectPermission = async (subjectId) => {
  try {
    console.log(`调用getUserSubjectPermission API, 科目ID: ${subjectId}`);
    const response = await api.get(`/public/user/subject-permissions/${subjectId}`);
    console.log('获取用户科目权限成功:', response);
    return response;
  } catch (error) {
    console.error('获取用户科目权限失败:', error);
    return {
      subject_id: subjectId,
      can_view: true,
      can_edit: false,
      can_delete: false,
      can_assign: false
    };
  }
};

// 拍照解题
export const photoSolve = async (formData) => {
  try {
    console.log('🚀 调用拍照解题API - 设置3分钟超时');
    const response = await api.post('/students/photo-solve', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 180000, // 3分钟超时 - 给AI充足的解题时间
    });
    console.log('✅ 拍照解题成功:', response);
    return response;
  } catch (error) {
    console.error('❌ 拍照解题失败:', error);

    // 更详细的错误处理
    if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
      throw new Error('AI正在深度分析题目，处理时间较长。建议：\n• 确保图片清晰完整\n• 题目文字清楚可读\n• 稍后重试或联系老师');
    }

    throw error;
  }
};