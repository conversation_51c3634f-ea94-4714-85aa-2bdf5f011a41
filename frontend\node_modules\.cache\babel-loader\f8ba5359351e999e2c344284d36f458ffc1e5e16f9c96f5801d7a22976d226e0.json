{"ast": null, "code": "\"use client\";\n\nimport React from 'react';\nimport { generateColor } from '../util';\nconst ColorClear = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const handleClick = () => {\n    if (onChange && value && !value.cleared) {\n      const hsba = value.toHsb();\n      hsba.a = 0;\n      const genColor = generateColor(hsba);\n      genColor.cleared = true;\n      onChange(genColor);\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-clear`,\n    onClick: handleClick\n  });\n};\nexport default ColorClear;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}