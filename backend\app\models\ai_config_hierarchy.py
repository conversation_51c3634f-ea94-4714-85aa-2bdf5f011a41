from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base
import enum

class AIConfigScope(enum.Enum):
    """AI配置作用域枚举"""
    SYSTEM = "system"           # 系统级（超级管理员）
    SCHOOL = "school"           # 学校级（学校管理员）
    GRADE = "grade"             # 年级级（年级组长）
    SUBJECT = "subject"         # 学科级（教研组长）
    CLASS = "class"             # 班级级（班主任）
    TEACHER = "teacher"         # 教师级（个人）

class AIUsageType(enum.Enum):
    """AI使用类型枚举"""
    HOMEWORK_GRADING = "homework_grading"      # 作业批改
    ERROR_ANALYSIS = "error_analysis"          # 错题分析
    REINFORCEMENT_EXERCISE = "reinforcement_exercise"  # 强化练习生成
    AI_ASSISTANT = "ai_assistant"              # AI助手问答
    HOMEWORK_COMMENT = "homework_comment"      # 作业点评

class AIModelConfigHierarchy(Base):
    """分层级AI模型配置表"""
    __tablename__ = "ai_model_config_hierarchy"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 基本配置信息
    provider = Column(String(50), nullable=False)  # 提供商名称
    model_name = Column(String(100), nullable=False)  # 模型名称
    model_id = Column(String(100), nullable=True)  # 模型ID
    usage_type = Column(Enum(AIUsageType), nullable=False)  # 使用类型
    api_key = Column(String(255), nullable=True)  # API密钥
    api_endpoint = Column(String(255), nullable=True)  # API端点URL
    
    # 层级管理字段
    scope = Column(Enum(AIConfigScope), nullable=False)  # 配置作用域
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # 配置拥有者
    school_id = Column(Integer, ForeignKey("schools.id"), nullable=True)  # 所属学校
    grade_id = Column(Integer, nullable=True)  # 所属年级
    subject_id = Column(Integer, ForeignKey("subjects.id"), nullable=True)  # 所属学科
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=True)  # 所属班级
    
    # 继承和权限控制
    parent_config_id = Column(Integer, ForeignKey("ai_model_config_hierarchy.id"), nullable=True)  # 父级配置
    inherit_from_parent = Column(Boolean, default=True)  # 是否继承父级配置
    allow_override = Column(Boolean, default=True)  # 是否允许下级覆盖
    is_enabled = Column(Boolean, default=True)  # 是否启用
    is_forced = Column(Boolean, default=False)  # 是否强制使用（不允许下级覆盖）
    
    # 状态和时间
    is_active = Column(Boolean, default=True)  # 是否可用
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 配置描述和备注
    description = Column(Text, nullable=True)  # 配置描述
    notes = Column(Text, nullable=True)  # 备注信息
    
    # 关系
    owner = relationship("User", back_populates="ai_configs")
    school = relationship("School")
    subject = relationship("Subject")
    class_ = relationship("Class")
    parent_config = relationship("AIModelConfigHierarchy", remote_side=[id])
    child_configs = relationship("AIModelConfigHierarchy", back_populates="parent_config")

class UserAIConfigPreference(Base):
    """用户AI配置偏好表"""
    __tablename__ = "user_ai_config_preferences"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    usage_type = Column(Enum(AIUsageType), nullable=False)
    
    # 配置选择
    preferred_config_id = Column(Integer, ForeignKey("ai_model_config_hierarchy.id"), nullable=True)
    use_inherited = Column(Boolean, default=True)  # 是否使用继承的配置
    
    # 权限控制
    can_change_config = Column(Boolean, default=True)  # 是否可以更改配置
    is_forced_config = Column(Boolean, default=False)  # 是否被强制使用某配置
    forced_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # 强制配置的管理员
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", foreign_keys=[user_id])
    preferred_config = relationship("AIModelConfigHierarchy")
    forced_by_user = relationship("User", foreign_keys=[forced_by_user_id])

class AIConfigPermission(Base):
    """AI配置权限控制表"""
    __tablename__ = "ai_config_permissions"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 权限主体
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    role_code = Column(String(50), nullable=False)  # 角色代码
    
    # 权限范围
    scope = Column(Enum(AIConfigScope), nullable=False)
    school_id = Column(Integer, ForeignKey("schools.id"), nullable=True)
    grade_id = Column(Integer, nullable=True)
    subject_id = Column(Integer, ForeignKey("subjects.id"), nullable=True)
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=True)
    
    # 权限类型
    can_create_config = Column(Boolean, default=False)  # 可以创建配置
    can_edit_config = Column(Boolean, default=False)   # 可以编辑配置
    can_delete_config = Column(Boolean, default=False) # 可以删除配置
    can_force_config = Column(Boolean, default=False)  # 可以强制下级使用配置
    can_disable_config = Column(Boolean, default=False) # 可以禁用下级配置
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User")
    school = relationship("School")
    subject = relationship("Subject")
    class_ = relationship("Class")
