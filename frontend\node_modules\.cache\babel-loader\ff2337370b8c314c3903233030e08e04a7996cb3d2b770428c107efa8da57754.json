{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileJpgOutlinedSvg from \"@ant-design/icons-svg/es/asn/FileJpgOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileJpgOutlined = function FileJpgOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileJpgOutlinedSvg\n  }));\n};\n\n/**![file-jpg](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3NC42IDMwMS44TDU5Ni44IDIxLjNjLTQuNS00LjUtOS40LTguMy0xNC43LTExLjUtMS40LS44LTIuOC0xLjYtNC4zLTIuMy0uOS0uNS0xLjktLjktMi44LTEuMy05LTQtMTguOS02LjItMjktNi4ySDIwMWMtMzkuOCAwLTczIDMyLjItNzMgNzJ2ODgwYzAgMzkuOCAzMy4yIDcyIDczIDcyaDYyM2MzOS44IDAgNzEtMzIuMiA3MS03MlYzNTIuNWMwLTE5LTctMzcuMi0yMC40LTUwLjd6TTU4MyAxMTAuNEw3ODMuOCAzMTJINTgzVjExMC40ek04MjMgOTUySDIwMFY3MmgzMTF2MjQwYzAgMzkuOCAzMy4yIDcyIDczIDcyaDIzOXY1Njh6TTM1MCA2OTYuNWMwIDI0LjItNy41IDMxLjQtMjEuOSAzMS40LTkgMC0xOC40LTUuOC0yNC44LTE4LjVMMjcyLjkgNzMyYzEzLjQgMjIuOSAzMi4zIDM0LjIgNjEuMyAzNC4yIDQxLjYgMCA2MC44LTI5LjkgNjAuOC02Ni4yVjU3N2gtNDV2MTE5LjV6TTUwMS4zIDU3N0g0Mzd2MTg2aDQ0di02MmgyMS42YzM5LjEgMCA3My4xLTE5LjYgNzMuMS02My42IDAtNDUuOC0zMy41LTYwLjQtNzQuNC02MC40em0tLjggODlINDgxdi01M2gxOC4yYzIxLjUgMCAzMy40IDYuMiAzMy40IDI0LjkgMCAxOC4xLTEwLjUgMjguMS0zMi4xIDI4LjF6bTE4Mi41LTl2MzZoMzB2MzAuMWMtNCAyLjktMTEgNC43LTE3LjcgNC43LTM0LjMgMC01MC43LTIxLjQtNTAuNy01OC4yIDAtMzYuMSAxOS43LTU3LjQgNDcuMS01Ny40IDE1LjMgMCAyNSA2LjIgMzQgMTQuNGwyMy43LTI4LjNjLTEyLjctMTIuOC0zMi4xLTI0LjItNTkuMi0yNC4yLTQ5LjYgMC05MS4xIDM1LjMtOTEuMSA5NyAwIDYyLjcgNDAgOTUuMSA5MS41IDk1LjEgMjUuOSAwIDQ5LjItMTAuMiA2MS41LTIyLjZWNjU3SDY4M3oiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileJpgOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileJpgOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}