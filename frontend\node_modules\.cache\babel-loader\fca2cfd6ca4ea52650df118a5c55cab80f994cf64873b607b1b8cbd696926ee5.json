{"ast": null, "code": "import * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useFrameState(defaultValue) {\n  const [value, setValue] = React.useState(defaultValue);\n  const frameRef = React.useRef(null);\n  const batchRef = React.useRef([]);\n  const destroyRef = React.useRef(false);\n  React.useEffect(() => {\n    destroyRef.current = false;\n    return () => {\n      destroyRef.current = true;\n      raf.cancel(frameRef.current);\n      frameRef.current = null;\n    };\n  }, []);\n  function setFrameValue(updater) {\n    if (destroyRef.current) {\n      return;\n    }\n    if (frameRef.current === null) {\n      batchRef.current = [];\n      frameRef.current = raf(() => {\n        frameRef.current = null;\n        setValue(prevValue => {\n          let current = prevValue;\n          batchRef.current.forEach(func => {\n            current = func(current);\n          });\n          return current;\n        });\n      });\n    }\n    batchRef.current.push(updater);\n  }\n  return [value, setFrameValue];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}