{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genFocusOutline } from '../../style';\nconst accessibilityFocus = token => Object.assign({}, genFocusOutline(token));\nconst getThemeStyle = (token, themeSuffix) => {\n  const {\n    componentCls,\n    itemColor,\n    itemSelectedColor,\n    subMenuItemSelectedColor,\n    groupTitleColor,\n    itemBg,\n    subMenuItemBg,\n    itemSelectedBg,\n    activeBarHeight,\n    activeBarWidth,\n    activeBarBorderWidth,\n    motionDurationSlow,\n    motionEaseInOut,\n    motionEaseOut,\n    itemPaddingInline,\n    motionDurationMid,\n    itemHoverColor,\n    lineType,\n    colorSplit,\n    // Disabled\n    itemDisabledColor,\n    // Danger\n    dangerItemColor,\n    dangerItemHoverColor,\n    dangerItemSelectedColor,\n    dangerItemActiveBg,\n    dangerItemSelectedBg,\n    // Bg\n    popupBg,\n    itemHoverBg,\n    itemActiveBg,\n    menuSubMenuBg,\n    // Horizontal\n    horizontalItemSelectedColor,\n    horizontalItemSelectedBg,\n    horizontalItemBorderRadius,\n    horizontalItemHoverBg\n  } = token;\n  return {\n    [`${componentCls}-${themeSuffix}, ${componentCls}-${themeSuffix} > ${componentCls}`]: {\n      color: itemColor,\n      background: itemBg,\n      [`&${componentCls}-root:focus-visible`]: Object.assign({}, accessibilityFocus(token)),\n      // ======================== Item ========================\n      [`${componentCls}-item`]: {\n        '&-group-title, &-extra': {\n          color: groupTitleColor\n        }\n      },\n      [`${componentCls}-submenu-selected > ${componentCls}-submenu-title`]: {\n        color: subMenuItemSelectedColor\n      },\n      [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n        color: itemColor,\n        [`&:not(${componentCls}-item-disabled):focus-visible`]: Object.assign({}, accessibilityFocus(token))\n      },\n      // Disabled\n      [`${componentCls}-item-disabled, ${componentCls}-submenu-disabled`]: {\n        color: `${itemDisabledColor} !important`\n      },\n      // Hover\n      [`${componentCls}-item:not(${componentCls}-item-selected):not(${componentCls}-submenu-selected)`]: {\n        [`&:hover, > ${componentCls}-submenu-title:hover`]: {\n          color: itemHoverColor\n        }\n      },\n      [`&:not(${componentCls}-horizontal)`]: {\n        [`${componentCls}-item:not(${componentCls}-item-selected)`]: {\n          '&:hover': {\n            backgroundColor: itemHoverBg\n          },\n          '&:active': {\n            backgroundColor: itemActiveBg\n          }\n        },\n        [`${componentCls}-submenu-title`]: {\n          '&:hover': {\n            backgroundColor: itemHoverBg\n          },\n          '&:active': {\n            backgroundColor: itemActiveBg\n          }\n        }\n      },\n      // Danger - only Item has\n      [`${componentCls}-item-danger`]: {\n        color: dangerItemColor,\n        [`&${componentCls}-item:hover`]: {\n          [`&:not(${componentCls}-item-selected):not(${componentCls}-submenu-selected)`]: {\n            color: dangerItemHoverColor\n          }\n        },\n        [`&${componentCls}-item:active`]: {\n          background: dangerItemActiveBg\n        }\n      },\n      [`${componentCls}-item a`]: {\n        '&, &:hover': {\n          color: 'inherit'\n        }\n      },\n      [`${componentCls}-item-selected`]: {\n        color: itemSelectedColor,\n        // Danger\n        [`&${componentCls}-item-danger`]: {\n          color: dangerItemSelectedColor\n        },\n        'a, a:hover': {\n          color: 'inherit'\n        }\n      },\n      [`& ${componentCls}-item-selected`]: {\n        backgroundColor: itemSelectedBg,\n        // Danger\n        [`&${componentCls}-item-danger`]: {\n          backgroundColor: dangerItemSelectedBg\n        }\n      },\n      [`&${componentCls}-submenu > ${componentCls}`]: {\n        backgroundColor: menuSubMenuBg\n      },\n      // ===== 设置浮层的颜色 =======\n      // ！dark 模式会被popupBg 会被rest 为 darkPopupBg\n      [`&${componentCls}-popup > ${componentCls}`]: {\n        backgroundColor: popupBg\n      },\n      [`&${componentCls}-submenu-popup > ${componentCls}`]: {\n        backgroundColor: popupBg\n      },\n      // ===== 设置浮层的颜色 end =======\n      // ====================== Horizontal ======================\n      [`&${componentCls}-horizontal`]: Object.assign(Object.assign({}, themeSuffix === 'dark' ? {\n        borderBottom: 0\n      } : {}), {\n        [`> ${componentCls}-item, > ${componentCls}-submenu`]: {\n          top: activeBarBorderWidth,\n          marginTop: token.calc(activeBarBorderWidth).mul(-1).equal(),\n          marginBottom: 0,\n          borderRadius: horizontalItemBorderRadius,\n          '&::after': {\n            position: 'absolute',\n            insetInline: itemPaddingInline,\n            bottom: 0,\n            borderBottom: `${unit(activeBarHeight)} solid transparent`,\n            transition: `border-color ${motionDurationSlow} ${motionEaseInOut}`,\n            content: '\"\"'\n          },\n          '&:hover, &-active, &-open': {\n            background: horizontalItemHoverBg,\n            '&::after': {\n              borderBottomWidth: activeBarHeight,\n              borderBottomColor: horizontalItemSelectedColor\n            }\n          },\n          '&-selected': {\n            color: horizontalItemSelectedColor,\n            backgroundColor: horizontalItemSelectedBg,\n            '&:hover': {\n              backgroundColor: horizontalItemSelectedBg\n            },\n            '&::after': {\n              borderBottomWidth: activeBarHeight,\n              borderBottomColor: horizontalItemSelectedColor\n            }\n          }\n        }\n      }),\n      // ================== Inline & Vertical ===================\n      //\n      [`&${componentCls}-root`]: {\n        [`&${componentCls}-inline, &${componentCls}-vertical`]: {\n          borderInlineEnd: `${unit(activeBarBorderWidth)} ${lineType} ${colorSplit}`\n        }\n      },\n      // ======================== Inline ========================\n      [`&${componentCls}-inline`]: {\n        // Sub\n        [`${componentCls}-sub${componentCls}-inline`]: {\n          background: subMenuItemBg\n        },\n        [`${componentCls}-item`]: {\n          position: 'relative',\n          '&::after': {\n            position: 'absolute',\n            insetBlock: 0,\n            insetInlineEnd: 0,\n            borderInlineEnd: `${unit(activeBarWidth)} solid ${itemSelectedColor}`,\n            transform: 'scaleY(0.0001)',\n            opacity: 0,\n            transition: [`transform ${motionDurationMid} ${motionEaseOut}`, `opacity ${motionDurationMid} ${motionEaseOut}`].join(','),\n            content: '\"\"'\n          },\n          // Danger\n          [`&${componentCls}-item-danger`]: {\n            '&::after': {\n              borderInlineEndColor: dangerItemSelectedColor\n            }\n          }\n        },\n        [`${componentCls}-selected, ${componentCls}-item-selected`]: {\n          '&::after': {\n            transform: 'scaleY(1)',\n            opacity: 1,\n            transition: [`transform ${motionDurationMid} ${motionEaseInOut}`, `opacity ${motionDurationMid} ${motionEaseInOut}`].join(',')\n          }\n        }\n      }\n    }\n  };\n};\nexport default getThemeStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}