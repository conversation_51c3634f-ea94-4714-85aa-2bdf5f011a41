from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form
from sqlalchemy.orm import Session
from ..database import get_db
from ..models.registration import ParentStudentVerification
from ..models.user import User
from ..models.ai_config import AIModelConfig
from ..schemas import registration as registration_schema
from .auth import get_current_user
import logging
import base64
import json
import os
import shutil
import requests
from tempfile import NamedTemporaryFile
from PIL import Image as PILImage
import io

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/pending-bindings", response_model=List[registration_schema.ParentStudentVerification])
async def get_pending_bindings(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取当前学生的待验证绑定请求
    """
    # 验证当前用户是否为学生
    if current_user.role != "student":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有学生用户可以访问此接口"
        )
    
    # 查询待验证的绑定请求
    bindings = db.query(ParentStudentVerification).filter(
        ParentStudentVerification.student_id == current_user.id,
        ParentStudentVerification.status == "pending"
    ).all()
    
    # 获取家长信息
    result = []
    for binding in bindings:
        parent = db.query(User).filter(User.id == binding.parent_id).first()
        parent_name = parent.full_name if parent else None
        
        result.append({
            "id": binding.id,
            "parent_id": binding.parent_id,
            "parent_name": parent_name,
            "student_id": binding.student_id,
            "relationship": binding.relationship_type,
            "is_primary": binding.is_primary,
            "status": binding.status,
            "code": binding.verification_code,
            "created_at": binding.created_at
        })
    
    return result

@router.post("/reject-binding/{binding_id}", response_model=registration_schema.ParentStudentVerification)
async def reject_binding(
    binding_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    拒绝家长绑定请求
    """
    # 验证当前用户是否为学生
    if current_user.role != "student":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有学生用户可以访问此接口"
        )
    
    # 查询绑定请求
    binding = db.query(ParentStudentVerification).filter(
        ParentStudentVerification.id == binding_id,
        ParentStudentVerification.student_id == current_user.id,
        ParentStudentVerification.status == "pending"
    ).first()
    
    if not binding:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="绑定请求不存在或不属于当前用户"
        )
    
    # 更新状态为拒绝
    binding.status = "rejected"
    db.commit()
    
    # 获取家长信息
    parent = db.query(User).filter(User.id == binding.parent_id).first()
    parent_name = parent.full_name if parent else None
    
    return {
        "id": binding.id,
        "parent_id": binding.parent_id,
        "parent_name": parent_name,
        "student_id": binding.student_id,
        "relationship": binding.relationship_type,
        "is_primary": binding.is_primary,
        "status": binding.status,
        "code": binding.verification_code,
        "created_at": binding.created_at
    }


@router.post("/photo-solve")
async def photo_solve_question(
    image: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    学生拍照解题功能
    """
    import logging
    logger = logging.getLogger(__name__)

    logger.info(f"🚀 开始拍照解题处理，用户: {current_user.username}")

    # 验证当前用户是否为学生
    # 支持中文和英文角色名称
    student_roles = ["student", "学生"]
    if current_user.role not in student_roles:
        logger.warning(f"❌ 非学生用户尝试使用拍照解题: {current_user.username}, 角色: {current_user.role}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只允许学生用户使用拍照解题功能"
        )

    # 获取专门的拍照解题AI配置
    logger.info("🔍 查找拍照解题专用AI配置...")
    photo_solve_config = db.query(AIModelConfig).filter(
        AIModelConfig.usage_type == "photo_solve",
        AIModelConfig.is_active == True
    ).first()

    if not photo_solve_config:
        logger.warning("⚠️ 未找到拍照解题专用配置，尝试使用火山引擎通用配置...")
        # 备用方案：使用火山引擎通用配置
        photo_solve_config = db.query(AIModelConfig).filter(
            AIModelConfig.provider == "volcano",
            AIModelConfig.is_active == True
        ).first()

        if not photo_solve_config:
            logger.error("❌ 未找到任何可用的AI配置")
            raise HTTPException(status_code=404, detail="AI服务配置不可用")

    logger.info(f"✅ 使用AI配置: ID={photo_solve_config.id}, {photo_solve_config.model_name}")
    logger.info(f"🎯 配置用途: {photo_solve_config.usage_type}")
    logger.info(f"📡 API端点: {photo_solve_config.api_endpoint}")
    logger.info(f"🔑 API密钥: {photo_solve_config.api_key[:10]}..." if photo_solve_config.api_key else "🔑 API密钥: 未设置")

    # 处理图片
    try:
        logger.info(f"📸 开始处理图片，文件名: {image.filename}, 大小: {image.size} bytes")

        # 读取上传的图片
        image_data = await image.read()
        logger.info(f"📖 图片数据读取完成，大小: {len(image_data)} bytes")

        # 使用PIL打开图片
        pil_image = PILImage.open(io.BytesIO(image_data))
        logger.info(f"🖼️ 图片打开成功，尺寸: {pil_image.size}, 模式: {pil_image.mode}")

        # 转换为RGB模式（确保兼容性）
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
            logger.info("🔄 图片已转换为RGB模式")

        # 调整图片大小（如果太大的话）
        max_size = (1024, 1024)
        if pil_image.size[0] > max_size[0] or pil_image.size[1] > max_size[1]:
            original_size = pil_image.size
            pil_image.thumbnail(max_size, PILImage.Resampling.LANCZOS)
            logger.info(f"📏 图片尺寸已调整: {original_size} -> {pil_image.size}")

        # 保存为JPEG格式
        with NamedTemporaryFile(delete=False, suffix=".jpg") as temp_file:
            pil_image.save(temp_file, format='JPEG', quality=85)
            temp_path = temp_file.name
            logger.info(f"💾 图片已保存到临时文件: {temp_path}")

    except Exception as e:
        logger.error(f"❌ 图片处理失败: {str(e)}")
        logger.error(f"❌ 异常类型: {type(e).__name__}")
        import traceback
        logger.error(f"❌ 异常堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"图片处理失败: {str(e)}")

    try:
        # 读取转换后的图片转为base64
        with open(temp_path, "rb") as img_file:
            base64_image = base64.b64encode(img_file.read()).decode('utf-8')

        # 解题提示词
        solve_prompt = """你是一位专业的数学和学科解题助手，具有强大的OCR图像识别能力。请完成以下任务:

1. 识别图片中的题目内容
2. 提供详细的解题步骤和答案
3. 给出相关知识点解释

**重要**: 你必须以下面的JSON格式返回结果，不要添加任何额外文字、标记或注释:
```json
{
  "question": "题目内容",
  "answer": "最终答案",
  "steps": [
    "解题步骤1",
    "解题步骤2",
    "解题步骤3"
  ],
  "knowledge_points": [
    "相关知识点1",
    "相关知识点2"
  ],
  "difficulty": "简单/中等/困难",
  "subject": "数学/语文/英语/物理/化学/生物/历史/地理/政治"
}
```

请严格按照上述JSON格式输出，确保:
1. 所有字符串使用双引号而不是单引号
2. 不要在JSON前后添加任何额外文字
3. 确保输出是有效的JSON格式
"""

        # 调用拍照解题专用AI配置
        api_key = photo_solve_config.api_key
        api_endpoint = photo_solve_config.api_endpoint
        model_id = photo_solve_config.model_id
        url = api_endpoint or "https://api.volcengineapi.com/v1/chat/completions"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        payload = {
            "model": model_id,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": solve_prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 4096,
            "temperature": 0.1
        }

        # 发送API请求
        logger.info(f"🚀 发送API请求到: {url}")
        logger.info(f"📋 请求头: {headers}")
        logger.info(f"📦 请求负载大小: {len(str(payload))} 字符")

        try:
            response = requests.post(url, headers=headers, json=payload, verify=False, timeout=150)
            logger.info(f"📡 API响应状态码: {response.status_code}")

        except requests.exceptions.Timeout as e:
            logger.error(f"⏰ API请求超时: {str(e)}")
            raise HTTPException(status_code=500, detail="AI服务请求超时，请稍后重试")
        except requests.exceptions.ConnectionError as e:
            logger.error(f"🔌 API连接错误: {str(e)}")
            raise HTTPException(status_code=500, detail="无法连接到AI服务，请检查网络连接")
        except Exception as e:
            logger.error(f"❌ API请求异常: {str(e)}")
            raise HTTPException(status_code=500, detail=f"API请求失败: {str(e)}")

        if response.status_code != 200:
            logger.error(f"❌ API返回错误状态码: {response.status_code}")
            logger.error(f"❌ 响应内容: {response.text}")

            # 尝试解析错误响应
            try:
                error_data = response.json()
                logger.error(f"❌ 错误数据: {error_data}")
                if "error" in error_data:
                    error_msg = error_data["error"].get("message", "未知错误")
                    raise HTTPException(status_code=500, detail=f"AI解题失败: {error_msg}")
            except json.JSONDecodeError:
                logger.error("❌ 无法解析错误响应为JSON")
                pass

            raise HTTPException(status_code=500, detail=f"AI服务调用失败，状态码: {response.status_code}")

        # 解析响应
        logger.info("📋 开始解析API响应...")
        try:
            response_data = response.json()
            logger.info(f"✅ 响应JSON解析成功")
            logger.info(f"📊 响应数据键: {list(response_data.keys())}")
        except json.JSONDecodeError as e:
            logger.error(f"❌ 响应JSON解析失败: {str(e)}")
            logger.error(f"❌ 原始响应: {response.text[:500]}...")
            raise HTTPException(status_code=500, detail="AI服务响应格式错误")

        if "choices" in response_data and len(response_data["choices"]) > 0:
            ai_content = response_data["choices"][0]["message"]["content"]
            logger.info(f"🤖 AI响应内容长度: {len(ai_content)} 字符")
            logger.info(f"🤖 AI响应内容预览: {ai_content[:200]}...")

            # 提取并解析JSON
            try:
                json_start = ai_content.find("{")
                json_end = ai_content.rfind("}") + 1
                logger.info(f"🔍 JSON位置: start={json_start}, end={json_end}")

                if json_start >= 0 and json_end > json_start:
                    json_str = ai_content[json_start:json_end]
                    logger.info(f"📝 提取的JSON字符串: {json_str[:300]}...")

                    solve_result = json.loads(json_str)
                    logger.info(f"✅ JSON解析成功，包含键: {list(solve_result.keys())}")

                    # 添加用户信息
                    solve_result["user_id"] = current_user.id
                    solve_result["username"] = current_user.username

                    logger.info(f"🎉 拍照解题处理完成，用户: {current_user.username}")
                    return solve_result
                else:
                    logger.error(f"❌ 未找到有效的JSON格式，json_start={json_start}, json_end={json_end}")
                    logger.error(f"❌ AI完整响应: {ai_content}")
                    raise HTTPException(status_code=500, detail="AI响应格式错误")

            except json.JSONDecodeError as e:
                logger.error(f"❌ JSON解析失败: {str(e)}")
                logger.error(f"❌ 尝试解析的JSON: {json_str}")
                raise HTTPException(status_code=500, detail=f"解析AI响应失败: {str(e)}")
        else:
            logger.error(f"❌ 响应格式不符合预期，缺少choices字段")
            logger.error(f"❌ 完整响应数据: {response_data}")
            raise HTTPException(status_code=500, detail="AI响应格式不符合预期")

    except HTTPException as he:
        logger.error(f"❌ HTTP异常: {he.detail}")
        raise
    except Exception as e:
        logger.error(f"❌ 未预期的异常: {str(e)}")
        logger.error(f"❌ 异常类型: {type(e).__name__}")
        import traceback
        logger.error(f"❌ 异常堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"解题处理失败: {str(e)}")

    finally:
        # 清理临时文件
        try:
            if 'temp_path' in locals() and os.path.exists(temp_path):
                os.remove(temp_path)
                logger.info(f"🗑️ 临时文件已清理: {temp_path}")
        except Exception as cleanup_error:
            logger.warning(f"⚠️ 清理临时文件失败: {str(cleanup_error)}")