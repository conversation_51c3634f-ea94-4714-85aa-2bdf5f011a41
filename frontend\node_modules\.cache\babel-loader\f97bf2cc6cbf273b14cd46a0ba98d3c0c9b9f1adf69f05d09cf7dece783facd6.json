{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ExperimentFilledSvg from \"@ant-design/icons-svg/es/asn/ExperimentFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ExperimentFilled = function ExperimentFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ExperimentFilledSvg\n  }));\n};\n\n/**![experiment](data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ExperimentFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ExperimentFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}