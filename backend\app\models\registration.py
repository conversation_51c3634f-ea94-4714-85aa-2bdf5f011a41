from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
import secrets
import json
from ..database import Base

class UserRegistrationStatus(Base):
    """用户注册状态表，用于跟踪需要审核的用户注册申请"""
    __tablename__ = "user_registration_status"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    role_id = Column(Integer, ForeignKey("roles.id"), nullable=True)
    role_name = Column(String(50))  # 冗余存储角色名称，便于查询
    status = Column(String(20), default="pending")  # pending, approved, rejected, needs_verification
    first_reviewer_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    final_reviewer_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    rejection_reason = Column(Text, nullable=True)
    additional_info = Column(Text, nullable=True)  # 存储JSON格式的额外信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", foreign_keys=[user_id], back_populates="registration_status")
    role = relationship("Role", foreign_keys=[role_id])
    first_reviewer = relationship("User", foreign_keys=[first_reviewer_id], back_populates="first_reviewed_registrations")
    final_reviewer = relationship("User", foreign_keys=[final_reviewer_id], back_populates="final_reviewed_registrations")
    
    def __repr__(self):
        return f"<UserRegistrationStatus(user_id={self.user_id}, role_name='{self.role_name}', status='{self.status}')>"
    
    def get_additional_info(self):
        """获取额外信息的JSON解析结果"""
        if self.additional_info:
            try:
                return json.loads(self.additional_info)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_additional_info(self, info_dict):
        """设置额外信息"""
        if info_dict is not None:
            self.additional_info = json.dumps(info_dict, ensure_ascii=False)
        else:
            self.additional_info = None


class ParentStudentVerification(Base):
    """家长学生绑定验证表，用于验证家长与学生的关系"""
    __tablename__ = "parent_student_verifications"
    
    id = Column(Integer, primary_key=True, index=True)
    parent_id = Column(Integer, ForeignKey("users.id"))
    student_id = Column(Integer, ForeignKey("users.id"))
    verification_code = Column(String(10))
    relationship_type = Column(String(20))  # 父亲、母亲、祖父母等
    is_primary = Column(Boolean, default=False)  # 是否主要监护人
    status = Column(String(20), default="pending")  # pending, verified, rejected
    expires_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    parent = relationship("User", foreign_keys=[parent_id], back_populates="student_verifications")
    student = relationship("User", foreign_keys=[student_id], back_populates="parent_verifications")
    
    def __repr__(self):
        return f"<ParentStudentVerification(parent_id={self.parent_id}, student_id={self.student_id}, status='{self.status}')>"
    
    @classmethod
    def generate_verification(cls, parent_id, student_id, relationship_type, is_primary=False, expiry_minutes=30):
        """生成新的验证记录"""
        code = secrets.token_hex(3).upper()  # 生成6位大写字母+数字的验证码
        expires_at = datetime.utcnow() + timedelta(minutes=expiry_minutes)
        
        return cls(
            parent_id=parent_id,
            student_id=student_id,
            verification_code=code,
            relationship_type=relationship_type,
            is_primary=is_primary,
            expires_at=expires_at
        )
    
    def is_expired(self):
        """检查验证码是否已过期"""
        return datetime.utcnow() > self.expires_at
    
    def verify(self, code):
        """验证验证码是否正确"""
        if self.is_expired():
            return False, "验证码已过期"
        
        if self.verification_code != code:
            return False, "验证码不正确"
        
        self.status = "verified"
        self.updated_at = datetime.utcnow()
        return True, "验证成功"


class SchoolApplication(Base):
    """学校申请表，用于用户申请添加新学校"""
    __tablename__ = "school_applications"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    province = Column(String(50))
    city = Column(String(50))
    district = Column(String(50))
    address = Column(String(255))
    contact_name = Column(String(50))
    contact_phone = Column(String(20))
    contact_email = Column(String(100))
    description = Column(Text, nullable=True)
    applicant_id = Column(Integer, ForeignKey("users.id"))
    status = Column(String(20), default="pending")  # pending, approved, rejected
    reviewer_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    rejection_reason = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    applicant = relationship("User", foreign_keys=[applicant_id], back_populates="school_applications")
    reviewer = relationship("User", foreign_keys=[reviewer_id], back_populates="reviewed_school_applications")
    
    def __repr__(self):
        return f"<SchoolApplication(name='{self.name}', applicant_id={self.applicant_id}, status='{self.status}')>" 