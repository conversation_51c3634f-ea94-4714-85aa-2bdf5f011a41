#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地区数据初始化脚本
用于初始化省份、城市、区县的基础数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.database import engine, get_db
from app.models.region import Province, City, District

def init_basic_regions():
    """初始化基础地区数据"""
    db = Session(bind=engine)
    
    try:
        # 检查是否已有数据
        existing_provinces = db.query(Province).count()
        if existing_provinces > 0:
            print(f"数据库中已有 {existing_provinces} 个省份，跳过初始化")
            return
        
        # 基础省份数据
        provinces_data = [
            {"name": "北京市", "code": "110000"},
            {"name": "天津市", "code": "120000"},
            {"name": "河北省", "code": "130000"},
            {"name": "山西省", "code": "140000"},
            {"name": "内蒙古自治区", "code": "150000"},
            {"name": "辽宁省", "code": "210000"},
            {"name": "吉林省", "code": "220000"},
            {"name": "黑龙江省", "code": "230000"},
            {"name": "上海市", "code": "310000"},
            {"name": "江苏省", "code": "320000"},
            {"name": "浙江省", "code": "330000"},
            {"name": "安徽省", "code": "340000"},
            {"name": "福建省", "code": "350000"},
            {"name": "江西省", "code": "360000"},
            {"name": "山东省", "code": "370000"},
            {"name": "河南省", "code": "410000"},
            {"name": "湖北省", "code": "420000"},
            {"name": "湖南省", "code": "430000"},
            {"name": "广东省", "code": "440000"},
            {"name": "广西壮族自治区", "code": "450000"},
            {"name": "海南省", "code": "460000"},
            {"name": "重庆市", "code": "500000"},
            {"name": "四川省", "code": "510000"},
            {"name": "贵州省", "code": "520000"},
            {"name": "云南省", "code": "530000"},
            {"name": "西藏自治区", "code": "540000"},
            {"name": "陕西省", "code": "610000"},
            {"name": "甘肃省", "code": "620000"},
            {"name": "青海省", "code": "630000"},
            {"name": "宁夏回族自治区", "code": "640000"},
            {"name": "新疆维吾尔自治区", "code": "650000"},
            {"name": "台湾省", "code": "710000"},
            {"name": "香港特别行政区", "code": "810000"},
            {"name": "澳门特别行政区", "code": "820000"},
        ]
        
        # 创建省份
        province_objects = {}
        for province_data in provinces_data:
            province = Province(**province_data)
            db.add(province)
            db.flush()  # 获取ID
            province_objects[province_data["name"]] = province
            print(f"创建省份: {province.name}")
        
        # 示例城市数据（部分主要城市）
        cities_data = [
            # 北京市
            {"name": "北京市", "code": "110100", "province": "北京市"},
            
            # 上海市
            {"name": "上海市", "code": "310100", "province": "上海市"},
            
            # 广东省
            {"name": "广州市", "code": "440100", "province": "广东省"},
            {"name": "深圳市", "code": "440300", "province": "广东省"},
            {"name": "珠海市", "code": "440400", "province": "广东省"},
            {"name": "汕头市", "code": "440500", "province": "广东省"},
            {"name": "佛山市", "code": "440600", "province": "广东省"},
            {"name": "韶关市", "code": "440200", "province": "广东省"},
            {"name": "湛江市", "code": "440800", "province": "广东省"},
            {"name": "肇庆市", "code": "441200", "province": "广东省"},
            {"name": "江门市", "code": "440700", "province": "广东省"},
            {"name": "茂名市", "code": "440900", "province": "广东省"},
            {"name": "惠州市", "code": "441300", "province": "广东省"},
            {"name": "梅州市", "code": "441400", "province": "广东省"},
            {"name": "汕尾市", "code": "441500", "province": "广东省"},
            {"name": "河源市", "code": "441600", "province": "广东省"},
            {"name": "阳江市", "code": "441700", "province": "广东省"},
            {"name": "清远市", "code": "441800", "province": "广东省"},
            {"name": "东莞市", "code": "441900", "province": "广东省"},
            {"name": "中山市", "code": "442000", "province": "广东省"},
            {"name": "潮州市", "code": "445100", "province": "广东省"},
            {"name": "揭阳市", "code": "445200", "province": "广东省"},
            {"name": "云浮市", "code": "445300", "province": "广东省"},
            
            # 四川省
            {"name": "成都市", "code": "510100", "province": "四川省"},
            {"name": "自贡市", "code": "510300", "province": "四川省"},
            {"name": "攀枝花市", "code": "510400", "province": "四川省"},
            {"name": "泸州市", "code": "510500", "province": "四川省"},
            {"name": "德阳市", "code": "510600", "province": "四川省"},
            {"name": "绵阳市", "code": "510700", "province": "四川省"},
            {"name": "广元市", "code": "510800", "province": "四川省"},
            {"name": "遂宁市", "code": "510900", "province": "四川省"},
            {"name": "内江市", "code": "511000", "province": "四川省"},
            {"name": "乐山市", "code": "511100", "province": "四川省"},
            {"name": "南充市", "code": "511300", "province": "四川省"},
            {"name": "眉山市", "code": "511400", "province": "四川省"},
            {"name": "宜宾市", "code": "511500", "province": "四川省"},
            {"name": "广安市", "code": "511600", "province": "四川省"},
            {"name": "达州市", "code": "511700", "province": "四川省"},
            {"name": "雅安市", "code": "511800", "province": "四川省"},
            {"name": "巴中市", "code": "511900", "province": "四川省"},
            {"name": "资阳市", "code": "512000", "province": "四川省"},
            
            # 江苏省
            {"name": "南京市", "code": "320100", "province": "江苏省"},
            {"name": "无锡市", "code": "320200", "province": "江苏省"},
            {"name": "徐州市", "code": "320300", "province": "江苏省"},
            {"name": "常州市", "code": "320400", "province": "江苏省"},
            {"name": "苏州市", "code": "320500", "province": "江苏省"},
            {"name": "南通市", "code": "320600", "province": "江苏省"},
            {"name": "连云港市", "code": "320700", "province": "江苏省"},
            {"name": "淮安市", "code": "320800", "province": "江苏省"},
            {"name": "盐城市", "code": "320900", "province": "江苏省"},
            {"name": "扬州市", "code": "321000", "province": "江苏省"},
            {"name": "镇江市", "code": "321100", "province": "江苏省"},
            {"name": "泰州市", "code": "321200", "province": "江苏省"},
            {"name": "宿迁市", "code": "321300", "province": "江苏省"},
        ]
        
        # 创建城市
        city_objects = {}
        for city_data in cities_data:
            province_name = city_data.pop("province")
            if province_name in province_objects:
                city = City(
                    **city_data,
                    province_id=province_objects[province_name].id
                )
                db.add(city)
                db.flush()  # 获取ID
                city_objects[f"{province_name}-{city_data['name']}"] = city
                print(f"创建城市: {province_name} - {city.name}")
        
        # 示例区县数据（部分主要区县）
        districts_data = [
            # 北京市区县
            {"name": "东城区", "code": "110101", "city": "北京市-北京市"},
            {"name": "西城区", "code": "110102", "city": "北京市-北京市"},
            {"name": "朝阳区", "code": "110105", "city": "北京市-北京市"},
            {"name": "丰台区", "code": "110106", "city": "北京市-北京市"},
            {"name": "石景山区", "code": "110107", "city": "北京市-北京市"},
            {"name": "海淀区", "code": "110108", "city": "北京市-北京市"},
            
            # 上海市区县
            {"name": "黄浦区", "code": "310101", "city": "上海市-上海市"},
            {"name": "徐汇区", "code": "310104", "city": "上海市-上海市"},
            {"name": "长宁区", "code": "310105", "city": "上海市-上海市"},
            {"name": "静安区", "code": "310106", "city": "上海市-上海市"},
            {"name": "普陀区", "code": "310107", "city": "上海市-上海市"},
            {"name": "虹口区", "code": "310109", "city": "上海市-上海市"},
            
            # 广州市区县
            {"name": "荔湾区", "code": "440103", "city": "广东省-广州市"},
            {"name": "越秀区", "code": "440104", "city": "广东省-广州市"},
            {"name": "海珠区", "code": "440105", "city": "广东省-广州市"},
            {"name": "天河区", "code": "440106", "city": "广东省-广州市"},
            {"name": "白云区", "code": "440111", "city": "广东省-广州市"},
            {"name": "黄埔区", "code": "440112", "city": "广东省-广州市"},
            
            # 深圳市区县
            {"name": "罗湖区", "code": "440303", "city": "广东省-深圳市"},
            {"name": "福田区", "code": "440304", "city": "广东省-深圳市"},
            {"name": "南山区", "code": "440305", "city": "广东省-深圳市"},
            {"name": "宝安区", "code": "440306", "city": "广东省-深圳市"},
            {"name": "龙岗区", "code": "440307", "city": "广东省-深圳市"},
            {"name": "盐田区", "code": "440308", "city": "广东省-深圳市"},
            
            # 成都市区县
            {"name": "锦江区", "code": "510104", "city": "四川省-成都市"},
            {"name": "青羊区", "code": "510105", "city": "四川省-成都市"},
            {"name": "金牛区", "code": "510106", "city": "四川省-成都市"},
            {"name": "武侯区", "code": "510107", "city": "四川省-成都市"},
            {"name": "成华区", "code": "510108", "city": "四川省-成都市"},
            {"name": "龙泉驿区", "code": "510112", "city": "四川省-成都市"},
        ]
        
        # 创建区县
        for district_data in districts_data:
            city_key = district_data.pop("city")
            if city_key in city_objects:
                district = District(
                    **district_data,
                    city_id=city_objects[city_key].id
                )
                db.add(district)
                print(f"创建区县: {city_key} - {district.name}")
        
        # 提交所有更改
        db.commit()
        print("\n地区数据初始化完成！")
        print(f"共创建 {len(provinces_data)} 个省份")
        print(f"共创建 {len(cities_data)} 个城市")
        print(f"共创建 {len(districts_data)} 个区县")
        
    except Exception as e:
        print(f"初始化失败: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    print("开始初始化地区数据...")
    init_basic_regions()
