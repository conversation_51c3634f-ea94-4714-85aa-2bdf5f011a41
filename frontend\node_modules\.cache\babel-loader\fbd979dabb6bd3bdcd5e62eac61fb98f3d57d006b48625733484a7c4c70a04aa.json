{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genNoticeStyle, prepareComponentToken, prepareNotificationToken } from '.';\nimport { genSubStyleComponent } from '../../theme/internal';\nexport default genSubStyleComponent(['Notification', 'PurePanel'], token => {\n  const noticeCls = `${token.componentCls}-notice`;\n  const notificationToken = prepareNotificationToken(token);\n  return {\n    [`${noticeCls}-pure-panel`]: Object.assign(Object.assign({}, genNoticeStyle(notificationToken)), {\n      width: notificationToken.width,\n      maxWidth: `calc(100vw - ${unit(token.calc(notificationToken.notificationMarginEdge).mul(2).equal())})`,\n      margin: 0\n    })\n  };\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}