{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport FileTwoTone from \"@ant-design/icons/es/icons/FileTwoTone\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport PaperClipOutlined from \"@ant-design/icons/es/icons/PaperClipOutlined\";\nimport PictureTwoTone from \"@ant-design/icons/es/icons/PictureTwoTone\";\nimport classNames from 'classnames';\nimport CSSMotion, { CSSMotionList } from 'rc-motion';\nimport omit from \"rc-util/es/omit\";\nimport useForceUpdate from '../../_util/hooks/useForceUpdate';\nimport initCollapseMotion from '../../_util/motion';\nimport { cloneElement } from '../../_util/reactNode';\nimport Button from '../../button';\nimport { ConfigContext } from '../../config-provider';\nimport { isImageUrl, previewImage } from '../utils';\nimport ListItem from './ListItem';\nconst InternalUploadList = (props, ref) => {\n  const {\n    listType = 'text',\n    previewFile = previewImage,\n    onPreview,\n    onDownload,\n    onRemove,\n    locale,\n    iconRender,\n    isImageUrl: isImgUrl = isImageUrl,\n    prefixCls: customizePrefixCls,\n    items = [],\n    showPreviewIcon = true,\n    showRemoveIcon = true,\n    showDownloadIcon = false,\n    removeIcon,\n    previewIcon,\n    downloadIcon,\n    extra,\n    progress = {\n      size: [-1, 2],\n      showInfo: false\n    },\n    appendAction,\n    appendActionVisible = true,\n    itemRender,\n    disabled\n  } = props;\n  const forceUpdate = useForceUpdate();\n  const [motionAppear, setMotionAppear] = React.useState(false);\n  const isPictureCardOrCirle = ['picture-card', 'picture-circle'].includes(listType);\n  // ============================= Effect =============================\n  React.useEffect(() => {\n    if (!listType.startsWith('picture')) {\n      return;\n    }\n    (items || []).forEach(file => {\n      if (!(file.originFileObj instanceof File || file.originFileObj instanceof Blob) || file.thumbUrl !== undefined) {\n        return;\n      }\n      file.thumbUrl = '';\n      previewFile === null || previewFile === void 0 ? void 0 : previewFile(file.originFileObj).then(previewDataUrl => {\n        // Need append '' to avoid dead loop\n        file.thumbUrl = previewDataUrl || '';\n        forceUpdate();\n      });\n    });\n  }, [listType, items, previewFile]);\n  React.useEffect(() => {\n    setMotionAppear(true);\n  }, []);\n  // ============================= Events =============================\n  const onInternalPreview = (file, e) => {\n    if (!onPreview) {\n      return;\n    }\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    return onPreview(file);\n  };\n  const onInternalDownload = file => {\n    if (typeof onDownload === 'function') {\n      onDownload(file);\n    } else if (file.url) {\n      window.open(file.url);\n    }\n  };\n  const onInternalClose = file => {\n    onRemove === null || onRemove === void 0 ? void 0 : onRemove(file);\n  };\n  const internalIconRender = file => {\n    if (iconRender) {\n      return iconRender(file, listType);\n    }\n    const isLoading = file.status === 'uploading';\n    if (listType.startsWith('picture')) {\n      const loadingIcon = listType === 'picture' ? /*#__PURE__*/React.createElement(LoadingOutlined, null) : locale.uploading;\n      const fileIcon = (isImgUrl === null || isImgUrl === void 0 ? void 0 : isImgUrl(file)) ? /*#__PURE__*/React.createElement(PictureTwoTone, null) : /*#__PURE__*/React.createElement(FileTwoTone, null);\n      return isLoading ? loadingIcon : fileIcon;\n    }\n    return isLoading ? /*#__PURE__*/React.createElement(LoadingOutlined, null) : /*#__PURE__*/React.createElement(PaperClipOutlined, null);\n  };\n  const actionIconRender = (customIcon, callback, prefixCls, title, acceptUploadDisabled) => {\n    const btnProps = {\n      type: 'text',\n      size: 'small',\n      title,\n      onClick: e => {\n        var _a, _b;\n        callback();\n        if (/*#__PURE__*/React.isValidElement(customIcon)) {\n          (_b = (_a = customIcon.props).onClick) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n        }\n      },\n      className: `${prefixCls}-list-item-action`,\n      disabled: acceptUploadDisabled ? disabled : false\n    };\n    return /*#__PURE__*/React.isValidElement(customIcon) ? (/*#__PURE__*/React.createElement(Button, Object.assign({}, btnProps, {\n      icon: cloneElement(customIcon, Object.assign(Object.assign({}, customIcon.props), {\n        onClick: () => {}\n      }))\n    }))) : (/*#__PURE__*/React.createElement(Button, Object.assign({}, btnProps), /*#__PURE__*/React.createElement(\"span\", null, customIcon)));\n  };\n  // ============================== Ref ===============================\n  // Test needs\n  React.useImperativeHandle(ref, () => ({\n    handlePreview: onInternalPreview,\n    handleDownload: onInternalDownload\n  }));\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  // ============================= Render =============================\n  const prefixCls = getPrefixCls('upload', customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const listClassNames = classNames(`${prefixCls}-list`, `${prefixCls}-list-${listType}`);\n  const listItemMotion = React.useMemo(() => omit(initCollapseMotion(rootPrefixCls), ['onAppearEnd', 'onEnterEnd', 'onLeaveEnd']), [rootPrefixCls]);\n  const motionConfig = Object.assign(Object.assign({}, isPictureCardOrCirle ? {} : listItemMotion), {\n    motionDeadline: 2000,\n    motionName: `${prefixCls}-${isPictureCardOrCirle ? 'animate-inline' : 'animate'}`,\n    keys: _toConsumableArray(items.map(file => ({\n      key: file.uid,\n      file\n    }))),\n    motionAppear\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: listClassNames\n  }, /*#__PURE__*/React.createElement(CSSMotionList, Object.assign({}, motionConfig, {\n    component: false\n  }), ({\n    key,\n    file,\n    className: motionClassName,\n    style: motionStyle\n  }) => (/*#__PURE__*/React.createElement(ListItem, {\n    key: key,\n    locale: locale,\n    prefixCls: prefixCls,\n    className: motionClassName,\n    style: motionStyle,\n    file: file,\n    items: items,\n    progress: progress,\n    listType: listType,\n    isImgUrl: isImgUrl,\n    showPreviewIcon: showPreviewIcon,\n    showRemoveIcon: showRemoveIcon,\n    showDownloadIcon: showDownloadIcon,\n    removeIcon: removeIcon,\n    previewIcon: previewIcon,\n    downloadIcon: downloadIcon,\n    extra: extra,\n    iconRender: internalIconRender,\n    actionIconRender: actionIconRender,\n    itemRender: itemRender,\n    onPreview: onInternalPreview,\n    onDownload: onInternalDownload,\n    onClose: onInternalClose\n  }))), appendAction && (/*#__PURE__*/React.createElement(CSSMotion, Object.assign({}, motionConfig, {\n    visible: appendActionVisible,\n    forceRender: true\n  }), ({\n    className: motionClassName,\n    style: motionStyle\n  }) => cloneElement(appendAction, oriProps => ({\n    className: classNames(oriProps.className, motionClassName),\n    style: Object.assign(Object.assign(Object.assign({}, motionStyle), {\n      // prevent the element has hover css pseudo-class that may cause animation to end prematurely.\n      pointerEvents: motionClassName ? 'none' : undefined\n    }), oriProps.style)\n  })))));\n};\nconst UploadList = /*#__PURE__*/React.forwardRef(InternalUploadList);\nif (process.env.NODE_ENV !== 'production') {\n  UploadList.displayName = 'UploadList';\n}\nexport default UploadList;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}