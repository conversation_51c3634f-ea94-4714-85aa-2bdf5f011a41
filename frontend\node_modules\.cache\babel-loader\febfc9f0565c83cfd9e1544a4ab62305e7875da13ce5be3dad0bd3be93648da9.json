{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ChromeFilledSvg from \"@ant-design/icons-svg/es/asn/ChromeFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ChromeFilled = function ChromeFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ChromeFilledSvg\n  }));\n};\n\n/**![chrome](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM3MS44IDUxMmMwIDc3LjUgNjIuNyAxNDAuMiAxNDAuMiAxNDAuMlM2NTIuMiA1ODkuNSA2NTIuMiA1MTIgNTg5LjUgMzcxLjggNTEyIDM3MS44IDM3MS44IDQzNC40IDM3MS44IDUxMnpNOTAwIDM2Mi40bC0yMzQuMyAxMi4xYzYzLjYgNzQuMyA2NC42IDE4MS41IDExLjEgMjYzLjdsLTE4OCAyODkuMmM3OCA0LjIgMTU4LjQtMTIuOSAyMzEuMi01NS4yIDE4MC0xMDQgMjUzLTMyMi4xIDE4MC01MDkuOHpNMzIwLjMgNTkxLjlMMTYzLjggMjg0LjFBNDE1LjM1IDQxNS4zNSAwIDAwOTYgNTEyYzAgMjA4IDE1Mi4zIDM4MC4zIDM1MS40IDQxMC44bDEwNi45LTIwOS40Yy05Ni42IDE4LjItMTg5LjktMzQuOC0yMzQtMTIxLjV6bTIxOC41LTI4NS41bDM0NC40IDE4LjFDODQ4IDI1NC43IDc5Mi42IDE5NCA3MTkuOCAxNTEuNyA2NTMuOSAxMTMuNiA1ODEuNSA5NS41IDUxMC41IDk2Yy0xMjIuNS41LTI0Mi4yIDU1LjItMzIyLjEgMTU0LjVsMTI4LjIgMTk2LjljMzItOTEuOSAxMjQuOC0xNDYuNyAyMjIuMi0xNDF6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ChromeFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ChromeFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}