#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量更新地区代码脚本
为provinces、cities、districts表添加标准的行政区划代码
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from app.models.region import Province, City, District

# 直接创建数据库连接，使用正确的数据库路径
DATABASE_URL = "sqlite:///backend/smart_edu.db"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})

def update_province_codes():
    """更新省份代码"""
    db = Session(bind=engine)
    
    try:
        # 标准省份代码映射
        province_codes = {
            "北京市": "110000",
            "天津市": "120000", 
            "河北省": "130000",
            "山西省": "140000",
            "内蒙古自治区": "150000",
            "辽宁省": "210000",
            "吉林省": "220000",
            "黑龙江省": "230000",
            "上海市": "310000",
            "江苏省": "320000",
            "浙江省": "330000",
            "安徽省": "340000",
            "福建省": "350000",
            "江西省": "360000",
            "山东省": "370000",
            "河南省": "410000",
            "湖北省": "420000",
            "湖南省": "430000",
            "广东省": "440000",
            "广西壮族自治区": "450000",
            "海南省": "460000",
            "重庆市": "500000",
            "四川省": "510000",
            "贵州省": "520000",
            "云南省": "530000",
            "西藏自治区": "540000",
            "陕西省": "610000",
            "甘肃省": "620000",
            "青海省": "630000",
            "宁夏回族自治区": "640000",
            "新疆维吾尔自治区": "650000",
            "台湾省": "710000",
            "香港特别行政区": "810000",
            "澳门特别行政区": "820000"
        }
        
        provinces = db.query(Province).all()
        updated_count = 0
        
        for province in provinces:
            if province.name in province_codes:
                province.code = province_codes[province.name]
                updated_count += 1
                print(f"更新省份: {province.name} -> {province.code}")
            else:
                print(f"警告: 未找到省份 {province.name} 的代码")
        
        db.commit()
        print(f"\n省份代码更新完成，共更新 {updated_count} 个省份")
        
    except Exception as e:
        print(f"更新省份代码失败: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def update_city_codes():
    """更新城市代码"""
    db = Session(bind=engine)
    
    try:
        # 主要城市代码映射（部分示例）
        city_codes = {
            # 北京市
            "北京市": "110100",
            
            # 天津市
            "天津市": "120100",
            
            # 上海市
            "上海市": "310100",
            
            # 重庆市
            "重庆市": "500100",
            
            # 四川省主要城市
            "成都市": "510100",
            "自贡市": "510300",
            "攀枝花市": "510400",
            "泸州市": "510500",
            "德阳市": "510600",
            "绵阳市": "510700",
            "广元市": "510800",
            "遂宁市": "510900",
            "内江市": "511000",
            "乐山市": "511100",
            "南充市": "511300",
            "眉山市": "511400",
            "宜宾市": "511500",
            "广安市": "511600",
            "达州市": "511700",
            "雅安市": "511800",
            "巴中市": "511900",
            "资阳市": "512000",
            "阿坝藏族羌族自治州": "513200",
            "甘孜藏族自治州": "513300",
            "凉山彝族自治州": "513400",
            
            # 广东省主要城市
            "广州市": "440100",
            "韶关市": "440200",
            "深圳市": "440300",
            "珠海市": "440400",
            "汕头市": "440500",
            "佛山市": "440600",
            "江门市": "440700",
            "湛江市": "440800",
            "茂名市": "440900",
            "肇庆市": "441200",
            "惠州市": "441300",
            "梅州市": "441400",
            "汕尾市": "441500",
            "河源市": "441600",
            "阳江市": "441700",
            "清远市": "441800",
            "东莞市": "441900",
            "中山市": "442000",
            "潮州市": "445100",
            "揭阳市": "445200",
            "云浮市": "445300",
            
            # 江苏省主要城市
            "南京市": "320100",
            "无锡市": "320200",
            "徐州市": "320300",
            "常州市": "320400",
            "苏州市": "320500",
            "南通市": "320600",
            "连云港市": "320700",
            "淮安市": "320800",
            "盐城市": "320900",
            "扬州市": "321000",
            "镇江市": "321100",
            "泰州市": "321200",
            "宿迁市": "321300",
        }
        
        cities = db.query(City).all()
        updated_count = 0
        
        for city in cities:
            if city.name in city_codes:
                city.code = city_codes[city.name]
                updated_count += 1
                print(f"更新城市: {city.name} -> {city.code}")
        
        db.commit()
        print(f"\n城市代码更新完成，共更新 {updated_count} 个城市")
        
    except Exception as e:
        print(f"更新城市代码失败: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def update_district_codes():
    """更新区县代码"""
    db = Session(bind=engine)
    
    try:
        # 主要区县代码映射（部分示例）
        district_codes = {
            # 北京市区县
            "东城区": "110101",
            "西城区": "110102",
            "朝阳区": "110105",
            "丰台区": "110106",
            "石景山区": "110107",
            "海淀区": "110108",
            "门头沟区": "110109",
            "房山区": "110111",
            "通州区": "110112",
            "顺义区": "110113",
            "昌平区": "110114",
            "大兴区": "110115",
            "怀柔区": "110116",
            "平谷区": "110117",
            "密云区": "110118",
            "延庆区": "110119",
            
            # 上海市区县
            "黄浦区": "310101",
            "徐汇区": "310104",
            "长宁区": "310105",
            "静安区": "310106",
            "普陀区": "310107",
            "虹口区": "310109",
            "杨浦区": "310110",
            "闵行区": "310112",
            "宝山区": "310113",
            "嘉定区": "310114",
            "浦东新区": "310115",
            "金山区": "310116",
            "松江区": "310117",
            "青浦区": "310118",
            "奉贤区": "310120",
            "崇明区": "310151",
            
            # 成都市区县
            "锦江区": "510104",
            "青羊区": "510105",
            "金牛区": "510106",
            "武侯区": "510107",
            "成华区": "510108",
            "龙泉驿区": "510112",
            "青白江区": "510113",
            "新都区": "510114",
            "温江区": "510115",
            "双流区": "510116",
            "郫都区": "510117",
            "新津区": "510118",
            "金堂县": "510121",
            "大邑县": "510129",
            "蒲江县": "510131",
            "都江堰市": "510181",
            "彭州市": "510182",
            "崇州市": "510183",
            "邛崃市": "510184",
            "简阳市": "510185",
            
            # 广州市区县
            "荔湾区": "440103",
            "越秀区": "440104",
            "海珠区": "440105",
            "天河区": "440106",
            "白云区": "440111",
            "黄埔区": "440112",
            "番禺区": "440113",
            "花都区": "440114",
            "南沙区": "440115",
            "从化区": "440117",
            "增城区": "440118",
            
            # 深圳市区县
            "罗湖区": "440303",
            "福田区": "440304",
            "南山区": "440305",
            "宝安区": "440306",
            "龙岗区": "440307",
            "盐田区": "440308",
            "龙华区": "440309",
            "坪山区": "440310",
            "光明区": "440311",
        }
        
        districts = db.query(District).all()
        updated_count = 0
        
        for district in districts:
            if district.name in district_codes:
                district.code = district_codes[district.name]
                updated_count += 1
                print(f"更新区县: {district.name} -> {district.code}")
        
        db.commit()
        print(f"\n区县代码更新完成，共更新 {updated_count} 个区县")
        
    except Exception as e:
        print(f"更新区县代码失败: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """主函数"""
    print("开始批量更新地区代码...")
    
    try:
        # 更新省份代码
        print("\n=== 更新省份代码 ===")
        update_province_codes()
        
        # 更新城市代码
        print("\n=== 更新城市代码 ===")
        update_city_codes()
        
        # 更新区县代码
        print("\n=== 更新区县代码 ===")
        update_district_codes()
        
        print("\n✅ 所有地区代码更新完成！")
        
    except Exception as e:
        print(f"\n❌ 更新失败: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
