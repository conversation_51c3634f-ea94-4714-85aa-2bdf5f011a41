from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, DateTime, Text, Float
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

# Import AIModelConfig directly to fix the relationship error
from .ai_config import AIModelConfig
from .subject import Subject
from .school import School

class HomeworkAssignment(Base):
    __tablename__ = "homework_assignments"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True)
    description = Column(Text, nullable=True)
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=True)
    teacher_id = Column(Integer, ForeignKey("users.id"))
    school_id = Column(Integer, ForeignKey("schools.id"), nullable=True)  # 新增学校ID字段
    subject_id = Column(Integer, ForeignKey("subjects.id"), nullable=True)  # 新增科目ID字段
    due_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    correction_mode = Column(String, default="auto")
    pattern_provider = Column(String, default="volcano")  # 新增字段，用于存储模型提供商：volcano、ollama、qianwen
    ai_config_id = Column(Integer, ForeignKey("ai_model_configs.id"), nullable=True)  # 关联的AI配置ID
    auto_correct_description = Column(Text, nullable=True)
    reference_description = Column(Text, nullable=True)
    
    # 虚拟属性，不存储在数据库中
    _class_name = None
    _teacher_name = None
    _status = "active"
    
    @property
    def class_name(self):
        return self._class_name
    
    @class_name.setter
    def class_name(self, value):
        self._class_name = value
    
    @property
    def teacher_name(self):
        return self._teacher_name
    
    @teacher_name.setter
    def teacher_name(self, value):
        self._teacher_name = value
    
    @property
    def status(self):
        return self._status
    
    @status.setter
    def status(self, value):
        self._status = value
    
    # 关系
    class_ = relationship("Class", back_populates="homework_assignments")
    teacher = relationship("User", back_populates="assigned_homeworks")
    homeworks = relationship("Homework", back_populates="assignment")
    ai_config = relationship(AIModelConfig, foreign_keys=[ai_config_id])
    school = relationship("School")  # 新增学校关系
    subject = relationship("Subject")  # 新增科目关系

class Homework(Base):
    __tablename__ = "homeworks"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey("users.id"))
    assignment_id = Column(Integer, ForeignKey("homework_assignments.id"), nullable=True)
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=True)
    school_id = Column(Integer, ForeignKey("schools.id"), nullable=True)  # 学校ID字段
    subject_id = Column(Integer, ForeignKey("subjects.id"), nullable=True)  # 科目ID字段
    title = Column(String, index=True)
    description = Column(Text, nullable=True)
    status = Column(String, default="submitted")  # submitted, grading, graded
    score = Column(Float, nullable=True)
    accuracy = Column(Float, nullable=True)
    homework_comment = Column(Text, nullable=True)  # 作业点评字段
    created_at = Column(DateTime, default=datetime.utcnow)
    graded_at = Column(DateTime, nullable=True)
    
    # 关系
    student = relationship("User", back_populates="submitted_homeworks")
    assignment = relationship("HomeworkAssignment", back_populates="homeworks")
    class_ = relationship("Class", foreign_keys=[class_id])
    school = relationship("School", foreign_keys=[school_id])  # 学校关系
    subject = relationship("Subject", foreign_keys=[subject_id])  # 科目关系
    images = relationship("HomeworkImage", back_populates="homework")
    corrections = relationship("HomeworkCorrection", back_populates="homework")
    wrong_questions = relationship("WrongQuestion", back_populates="homework")

class HomeworkImage(Base):
    __tablename__ = "homework_images"
    
    id = Column(Integer, primary_key=True, index=True)
    homework_id = Column(Integer, ForeignKey("homeworks.id"))
    image_path = Column(String)
    page_number = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    homework = relationship("Homework", back_populates="images")

class HomeworkAnnotatedImage(Base):
    __tablename__ = "homework_annotated_images"
    
    id = Column(Integer, primary_key=True, index=True)
    homework_id = Column(Integer, ForeignKey("homeworks.id"))
    original_image_id = Column(Integer, ForeignKey("homework_images.id"))
    image_path = Column(String)
    page_number = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    homework = relationship("Homework", backref="annotated_images")
    original_image = relationship("HomeworkImage")

class HomeworkCorrection(Base):
    __tablename__ = "homework_corrections"
    
    id = Column(Integer, primary_key=True, index=True)
    homework_id = Column(Integer, ForeignKey("homeworks.id"))
    page_number = Column(Integer)
    correction_data = Column(Text)  # JSON格式存储批改数据
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    homework = relationship("Homework", back_populates="corrections")

class WrongQuestion(Base):
    __tablename__ = "wrong_questions"
    
    id = Column(Integer, primary_key=True, index=True)
    homework_id = Column(Integer, ForeignKey("homeworks.id"))
    student_id = Column(Integer, ForeignKey("users.id"))
    question_type = Column(String)
    question_content = Column(Text)
    correct_answer = Column(Text)
    wrong_answer = Column(Text, nullable=True)
    analysis = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    homework = relationship("Homework", back_populates="wrong_questions")
    student = relationship("User")
    reinforcement_exercises = relationship("ReinforcementExercise", back_populates="wrong_question")

class ReinforcementExercise(Base):
    __tablename__ = "reinforcement_exercises"
    
    id = Column(Integer, primary_key=True, index=True)
    wrong_question_id = Column(Integer, ForeignKey("wrong_questions.id"))
    student_id = Column(Integer, ForeignKey("users.id"))
    exercise_content = Column(Text)
    answer = Column(Text)
    analysis = Column(Text)
    is_completed = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    
    # 关系
    wrong_question = relationship("WrongQuestion", back_populates="reinforcement_exercises")
    student = relationship("User")

# 新增：作业分析相关表
class HomeworkAnalysis(Base):
    """作业分析表 - 存储班级作业的整体分析数据"""
    __tablename__ = "homework_analysis"

    id = Column(Integer, primary_key=True, index=True)
    assignment_id = Column(Integer, ForeignKey("homework_assignments.id"), nullable=False)
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=False)
    school_id = Column(Integer, ForeignKey("schools.id"), nullable=False)

    # 统计数据
    total_students = Column(Integer, default=0)  # 班级总人数
    submitted_count = Column(Integer, default=0)  # 已提交人数
    average_score = Column(Float, default=0.0)  # 平均分
    max_score = Column(Float, default=0.0)  # 最高分
    min_score = Column(Float, default=0.0)  # 最低分

    # 分数段分布
    excellent_count = Column(Integer, default=0)  # 优秀人数 (90-100%)
    good_count = Column(Integer, default=0)  # 良好人数 (80-90%)
    pass_count = Column(Integer, default=0)  # 合格人数 (60-80%)
    fail_count = Column(Integer, default=0)  # 待合格人数 (0-60%)

    # 分析结果JSON
    analysis_data = Column(Text, nullable=True)  # 存储详细分析数据

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    assignment = relationship("HomeworkAssignment")
    class_ = relationship("Class", foreign_keys=[class_id])
    school = relationship("School", foreign_keys=[school_id])

class StudentPerformanceAnalysis(Base):
    """学生表现分析表 - 存储每个学生的详细分析"""
    __tablename__ = "student_performance_analysis"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    assignment_id = Column(Integer, ForeignKey("homework_assignments.id"), nullable=False)
    homework_id = Column(Integer, ForeignKey("homeworks.id"), nullable=True)

    # 基础信息
    total_score = Column(Float, default=0.0)
    accuracy_rate = Column(Float, default=0.0)
    class_rank = Column(Integer, nullable=True)
    submit_status = Column(String, default="not_submitted")  # submitted, not_submitted, late

    # 详细分析数据JSON
    performance_data = Column(Text, nullable=True)  # 答题详情、错题分析等
    suggestions = Column(Text, nullable=True)  # 个性化建议

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    student = relationship("User")
    assignment = relationship("HomeworkAssignment")
    homework = relationship("Homework")

class QuestionAnalysis(Base):
    """题目分析表 - 存储每道题的分析数据"""
    __tablename__ = "question_analysis"

    id = Column(Integer, primary_key=True, index=True)
    assignment_id = Column(Integer, ForeignKey("homework_assignments.id"), nullable=False)
    question_number = Column(Integer, nullable=False)

    # 题目信息
    question_type = Column(String, nullable=True)  # 题目类型
    question_content = Column(Text, nullable=True)  # 题目内容
    correct_answer = Column(Text, nullable=True)  # 正确答案

    # 统计数据
    total_answers = Column(Integer, default=0)  # 总答题人数
    correct_count = Column(Integer, default=0)  # 正确人数
    accuracy_rate = Column(Float, default=0.0)  # 正确率
    difficulty_level = Column(String, default="medium")  # easy, medium, hard

    # 选项分布（JSON格式）
    option_distribution = Column(Text, nullable=True)  # 各选项选择分布

    # 分析结果
    analysis_result = Column(Text, nullable=True)  # 题目分析结果
    teaching_suggestions = Column(Text, nullable=True)  # 教学建议

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    assignment = relationship("HomeworkAssignment")

class ExerciseAnswerRecord(Base):
    """学生答题记录表 - 存储学生的答题记录和AI评估结果"""
    __tablename__ = "exercise_answer_records"

    id = Column(Integer, primary_key=True, index=True)
    exercise_id = Column(Integer, ForeignKey("reinforcement_exercises.id"), nullable=False)
    student_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # 答题信息
    student_answer = Column(Text, nullable=False)  # 学生答案

    # AI评估结果
    is_correct = Column(Boolean, nullable=False)  # AI判断是否正确
    ai_explanation = Column(Text, nullable=True)  # AI评估解释
    confidence_score = Column(Float, nullable=True)  # AI评估置信度

    # 时间记录
    submitted_at = Column(DateTime, default=datetime.utcnow)  # 提交时间

    # 关系
    exercise = relationship("ReinforcementExercise")
    student = relationship("User")

class ExportTemplate(Base):
    """导出模板表 - 存储各种导出模板配置"""
    __tablename__ = "export_templates"

    id = Column(Integer, primary_key=True, index=True)
    template_name = Column(String(100), nullable=False)
    template_type = Column(String(20), nullable=False)  # pdf, excel, word
    template_category = Column(String(50), nullable=False)  # student_report, class_statistics, parent_report

    # 模板配置JSON
    template_config = Column(Text, nullable=True)

    # 模板状态
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Comment(Base):
    """评论表 - 存储用户对作业的评论"""
    __tablename__ = "comments"

    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    homework_id = Column(Integer, ForeignKey("homeworks.id"), nullable=False)
    parent_id = Column(Integer, ForeignKey("comments.id"), nullable=True)  # 用于回复功能
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False)

    # 关系
    user = relationship("User", back_populates="comments")
    homework = relationship("Homework")
    replies = relationship("Comment", backref="parent", remote_side=[id])