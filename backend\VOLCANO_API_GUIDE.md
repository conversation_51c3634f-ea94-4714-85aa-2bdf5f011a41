# 火山引擎API配置指南

## 概述

智教云端智能辅导平台支持接入火山引擎API，以提供更强大的AI助手功能。本指南将帮助您正确配置火山引擎API。

## 配置步骤

### 1. 获取火山引擎API密钥

1. 注册并登录[火山引擎开放平台](https://www.volcengine.com/)
2. 创建应用并开通AI服务
3. 获取API密钥(Access Key)和模型ID

### 2. 配置系统

#### 方法一：使用配置脚本

1. 在backend目录下运行配置脚本：
   ```bash
   python configure_ai.py
   ```

2. 选择"添加新配置"选项
3. 选择提供商为"volcano"
4. 输入您的API密钥、API端点URL和模型ID
5. 确认激活此配置

#### 方法二：直接编辑数据库

在数据库中添加配置记录，确保以下字段正确设置：
- provider: "volcengine" 或 "volcano"
- model_name: 您选择的模型ID，如"glm-4"
- api_key: 您的火山引擎API密钥
- api_endpoint: API端点URL，通常为 "https://api.volc.wastai.com/v1/chat/completions"
- is_active: 设为true以激活此配置

## 常见问题排查

如果AI助手无法正常回复，请检查以下几点：

1. **日志检查**：查看后端日志，寻找API调用相关的错误信息
2. **配置验证**：确认数据库中的配置信息正确，特别是API密钥和模型ID
3. **提供商名称**：确保provider字段为"volcano"或"volcengine"
4. **网络连接**：确保服务器能够访问火山引擎API
5. **API额度**：检查您的火山引擎账户是否有足够的API调用额度

## 支持的模型

火山引擎支持多种大语言模型，包括但不限于：
- glm-4
- glm-3-turbo
- Doubao-1.5-vision-pro

请根据您的需求和账户权限选择合适的模型。

## 其他说明

1. 火山引擎API调用是付费服务，请关注您的账户余额
2. 不同模型的API格式可能略有差异，请参考火山引擎官方文档
3. 如遇到问题，请联系系统管理员或参考火山引擎官方支持 