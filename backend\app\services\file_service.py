#!/usr/bin/env python3
"""
文件服务 - 整合文件管理、权限控制和元数据管理
"""

import os
import mimetypes
from datetime import datetime
from typing import Optional, List, Dict, Any, BinaryIO
from sqlalchemy.orm import Session
from fastapi import UploadFile, HTTPException

from .file_manager import file_manager
from .file_permission import FilePermissionService
from ..models.file_metadata import FileMetadata, FileType, FileAccessLog
from ..models.user import User

class FileService:
    """文件服务 - 统一的文件操作接口"""
    
    @staticmethod
    def upload_file(
        db: Session,
        user: User,
        file: UploadFile,
        file_type: str,
        school_id: int,
        class_id: Optional[int] = None,
        subject: Optional[str] = None,
        homework_id: Optional[int] = None,
        assignment_id: Optional[int] = None
    ) -> FileMetadata:
        """
        上传文件
        
        Args:
            db: 数据库会话
            user: 当前用户
            file: 上传的文件
            file_type: 文件类型
            school_id: 学校ID
            class_id: 班级ID
            subject: 科目
            homework_id: 作业ID
            assignment_id: 作业任务ID
            
        Returns:
            文件元数据对象
        """
        # 验证文件类型和大小
        FileService._validate_file(file, file_type)
        
        # 生成文件路径信息
        file_extension = os.path.splitext(file.filename)[1] if file.filename else '.jpg'
        file_info = file_manager.generate_file_path(
            file_type=file_type,
            school_id=school_id,
            class_id=class_id,
            subject=subject,
            user_id=user.id,
            homework_id=homework_id,
            file_extension=file_extension
        )
        
        # 读取文件内容
        file_content = file.file.read()
        
        # 保存文件
        if not file_manager.save_file(file_content, file_info):
            raise HTTPException(status_code=500, detail="文件保存失败")
        
        # 创建文件元数据记录
        file_metadata = FileMetadata(
            file_path=file_info["url_path"],
            file_name=file_info["filename"],
            original_name=file.filename,
            file_type=FileType(file_type),
            file_size=len(file_content),
            mime_type=file.content_type or mimetypes.guess_type(file.filename)[0],
            school_id=school_id,
            class_id=class_id,
            subject_id=FileService._get_subject_id(db, subject) if subject else None,
            user_id=user.id,
            homework_id=homework_id,
            assignment_id=assignment_id,
            created_by=user.id
        )
        
        db.add(file_metadata)
        db.commit()
        db.refresh(file_metadata)
        
        # 记录访问日志
        FileService._log_file_access(db, file_metadata.id, user.id, "upload")
        
        return file_metadata
    
    @staticmethod
    def get_file(db: Session, user: User, file_id: int) -> Optional[FileMetadata]:
        """
        获取文件信息
        
        Args:
            db: 数据库会话
            user: 当前用户
            file_id: 文件ID
            
        Returns:
            文件元数据对象或None
        """
        file_metadata = db.query(FileMetadata).filter(
            FileMetadata.id == file_id,
            FileMetadata.is_active == 1
        ).first()
        
        if not file_metadata:
            return None
        
        # 检查权限
        if not FilePermissionService.can_access_file(user, file_metadata, "view"):
            return None
        
        # 记录访问日志
        FileService._log_file_access(db, file_id, user.id, "view")
        
        return file_metadata
    
    @staticmethod
    def delete_file(db: Session, user: User, file_id: int) -> bool:
        """
        删除文件
        
        Args:
            db: 数据库会话
            user: 当前用户
            file_id: 文件ID
            
        Returns:
            是否删除成功
        """
        file_metadata = db.query(FileMetadata).filter(
            FileMetadata.id == file_id,
            FileMetadata.is_active == 1
        ).first()
        
        if not file_metadata:
            return False
        
        # 检查权限
        if not FilePermissionService.can_access_file(user, file_metadata, "delete"):
            return False
        
        # 软删除（标记为不活跃）
        file_metadata.is_active = 0
        db.commit()
        
        # 记录访问日志
        FileService._log_file_access(db, file_id, user.id, "delete")
        
        return True
    
    @staticmethod
    def get_user_files(
        db: Session,
        user: User,
        file_type: Optional[str] = None,
        homework_id: Optional[int] = None,
        assignment_id: Optional[int] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[FileMetadata]:
        """
        获取用户可访问的文件列表
        
        Args:
            db: 数据库会话
            user: 当前用户
            file_type: 文件类型过滤
            homework_id: 作业ID过滤
            assignment_id: 作业任务ID过滤
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            文件列表
        """
        query = db.query(FileMetadata).filter(FileMetadata.is_active == 1)
        
        # 添加过滤条件
        if file_type:
            query = query.filter(FileMetadata.file_type == FileType(file_type))
        if homework_id:
            query = query.filter(FileMetadata.homework_id == homework_id)
        if assignment_id:
            query = query.filter(FileMetadata.assignment_id == assignment_id)
        
        # 根据用户权限过滤
        if not user.is_superuser:
            if user.is_teacher:
                query = query.filter(FileMetadata.school_id == user.school_id)
            else:
                query = query.filter(FileMetadata.user_id == user.id)
        
        files = query.order_by(FileMetadata.upload_time.desc()).offset(offset).limit(limit).all()
        
        # 进一步权限过滤
        return FilePermissionService.filter_accessible_files(user, files)
    
    @staticmethod
    def _validate_file(file: UploadFile, file_type: str):
        """验证文件"""
        # 文件大小限制（10MB）
        max_size = 10 * 1024 * 1024
        
        # 允许的文件类型
        allowed_types = {
            "homework": [".jpg", ".jpeg", ".png", ".pdf"],
            "assignment": [".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx"],
            "annotation": [".jpg", ".jpeg", ".png"],
            "profile": [".jpg", ".jpeg", ".png"]
        }
        
        if file.filename:
            file_extension = os.path.splitext(file.filename)[1].lower()
            if file_type in allowed_types and file_extension not in allowed_types[file_type]:
                raise HTTPException(
                    status_code=400, 
                    detail=f"不支持的文件类型: {file_extension}"
                )
        
        # 检查文件大小（需要先读取内容）
        file.file.seek(0, 2)  # 移动到文件末尾
        file_size = file.file.tell()
        file.file.seek(0)  # 重置到开头
        
        if file_size > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"文件大小超过限制: {file_size / 1024 / 1024:.1f}MB > 10MB"
            )
    
    @staticmethod
    def _get_subject_id(db: Session, subject_name: str) -> Optional[int]:
        """根据科目名称获取科目ID"""
        # 这里需要根据实际的科目表结构来实现
        # 暂时返回None
        return None
    
    @staticmethod
    def _log_file_access(db: Session, file_id: int, user_id: int, access_type: str):
        """记录文件访问日志"""
        try:
            access_log = FileAccessLog(
                file_id=file_id,
                user_id=user_id,
                access_type=access_type
            )
            db.add(access_log)
            db.commit()
        except Exception as e:
            print(f"记录文件访问日志失败: {e}")
    
    @staticmethod
    def get_file_stats(db: Session, user: User) -> Dict[str, Any]:
        """
        获取文件统计信息
        
        Args:
            db: 数据库会话
            user: 当前用户
            
        Returns:
            统计信息字典
        """
        query = db.query(FileMetadata).filter(FileMetadata.is_active == 1)
        
        # 根据用户权限过滤
        if not user.is_superuser:
            if user.is_teacher:
                query = query.filter(FileMetadata.school_id == user.school_id)
            else:
                query = query.filter(FileMetadata.user_id == user.id)
        
        # 统计各类型文件数量
        stats = {}
        for file_type in FileType:
            count = query.filter(FileMetadata.file_type == file_type).count()
            stats[file_type.value] = count
        
        # 统计总文件大小
        total_size = db.query(db.func.sum(FileMetadata.file_size)).filter(
            FileMetadata.is_active == 1
        ).scalar() or 0
        
        if not user.is_superuser:
            if user.is_teacher:
                total_size = db.query(db.func.sum(FileMetadata.file_size)).filter(
                    FileMetadata.is_active == 1,
                    FileMetadata.school_id == user.school_id
                ).scalar() or 0
            else:
                total_size = db.query(db.func.sum(FileMetadata.file_size)).filter(
                    FileMetadata.is_active == 1,
                    FileMetadata.user_id == user.id
                ).scalar() or 0
        
        stats["total_size"] = total_size
        stats["total_files"] = sum(stats[ft.value] for ft in FileType)
        
        return stats
