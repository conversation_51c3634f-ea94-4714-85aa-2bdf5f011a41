import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Spin,
  message,
  Avatar,
  List,
  Badge,
  Statistic,
  Progress,
  Empty,
  Button,
  Space,
  Divider,
  Tag
} from 'antd';
import {
  UserOutlined,
  BookOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  Bar<PERSON><PERSON>Outlined,
  EyeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import api from '../utils/api';

const { Title, Text, Paragraph } = Typography;

const ParentDashboard = ({ user }) => {
  const navigate = useNavigate();

  // 添加样式
  const selectedStudentStyle = {
    border: '2px solid #1890ff',
    boxShadow: '0 2px 8px rgba(24, 144, 255, 0.2)'
  };
  const [loading, setLoading] = useState(true);
  const [boundStudents, setBoundStudents] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [recentHomework, setRecentHomework] = useState({});
  const [studentHomework, setStudentHomework] = useState([]);
  const [studentStats, setStudentStats] = useState(null);

  // 获取绑定学生列表
  const fetchBoundStudents = async () => {
    try {
      const response = await api.get('/parent/bound-students');

      // 检查响应格式
      if (response && typeof response === 'object' && response.success) {
        setBoundStudents(response.data);
        // 默认选择第一个学生
        if (response.data && response.data.length > 0) {
          setSelectedStudent(response.data[0]);
        }
        // 获取每个学生的最近作业
        for (const student of response.data) {
          await fetchRecentHomework(student.student_id);
        }
      } else if (Array.isArray(response)) {
        setBoundStudents(response);
        // 默认选择第一个学生
        if (response.length > 0) {
          setSelectedStudent(response[0]);
        }
        // 获取每个学生的最近作业
        for (const student of response) {
          await fetchRecentHomework(student.student_id);
        }
      } else {
        console.log('❌ 未知响应格式:', response);
      }
    } catch (error) {
      console.error('获取绑定学生失败:', error);
      message.error('获取绑定学生信息失败');
    }
  };

  // 获取学生最近作业（按科目分组，每科最近2次）
  const fetchRecentHomework = async (studentId) => {
    try {
      const response = await api.get(`/parent/student/${studentId}/homework`, {
        params: { limit: 20 } // 获取更多作业以便按科目分组
      });

      if (response && response.success && response.data && response.data.homework) {
        const homeworkList = response.data.homework;

        // 按科目分组
        const homeworkBySubject = {};
        homeworkList.forEach(hw => {
          const subject = hw.subject_name || '未知科目';
          if (!homeworkBySubject[subject]) {
            homeworkBySubject[subject] = [];
          }
          homeworkBySubject[subject].push(hw);
        });

        // 每科只保留最近2次作业
        const recentBySubject = {};
        Object.keys(homeworkBySubject).forEach(subject => {
          recentBySubject[subject] = homeworkBySubject[subject]
            .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
            .slice(0, 2);
        });

        setRecentHomework(prev => ({
          ...prev,
          [studentId]: recentBySubject
        }));
      }
    } catch (error) {
      console.error('获取最近作业失败:', error);
    }
  };

  // 获取学生作业列表
  const fetchStudentHomework = async (studentId) => {
    if (!studentId) return;
    
    try {
      const response = await api.get(`/parent/student/${studentId}/homework?limit=10`);
      if (response.data.success) {
        setStudentHomework(response.data.data.homeworks || []);
      }
    } catch (error) {
      console.error('获取学生作业失败:', error);
      message.error('获取学生作业信息失败');
    }
  };

  // 获取学生统计信息
  const fetchStudentStats = async (studentId) => {
    if (!studentId) return;
    
    try {
      const response = await api.get(`/parent/student/${studentId}/statistics`);
      if (response.data.success) {
        setStudentStats(response.data.data);
      }
    } catch (error) {
      console.error('获取学生统计失败:', error);
      // 不显示错误消息，因为这是增强功能
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchBoundStudents();
      setLoading(false);
    };
    
    loadData();
  }, []);

  useEffect(() => {
    if (selectedStudent) {
      fetchStudentHomework(selectedStudent.student_id);
      fetchStudentStats(selectedStudent.student_id);
    }
  }, [selectedStudent]);

  // 查看作业详情
  const handleViewHomework = (student) => {
    // 导航到作业详情页面，而不是打开新窗口
    navigate(`/parent/student/${student.student_id}/homework`, {
      state: {
        studentName: student.student_name,
        studentId: student.student_id
      }
    });
  };

  // 查看学习报告
  const handleViewReport = (student) => {
    // 导航到学习报告页面，而不是打开新窗口
    navigate(`/parent/student/${student.student_id}/report`, {
      state: {
        studentName: student.student_name,
        studentId: student.student_id
      }
    });
  };

  // 渲染学生选择卡片
  const renderStudentCards = () => {
    if (boundStudents.length === 0) {
      return (
        <Card>
          <Empty
            description="暂无绑定学生"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Text type="secondary">
              请联系学校管理员为您绑定学生账户
            </Text>
          </Empty>
        </Card>
      );
    }

    return (
      <Row className="student-cards-row" gutter={[16, 16]}>
        {boundStudents.map((student) => {
          const studentRecentHomework = recentHomework[student.student_id] || {};
          const totalRecentHomework = Object.values(studentRecentHomework).flat().length;
          const subjectCount = Object.keys(studentRecentHomework).length;

          return (
            <Col xs={24} sm={12} md={8} lg={6} key={student.student_id}>
              <Card
                className="student-card"
                hoverable
                onClick={() => setSelectedStudent(student)}
                style={{
                  ...(selectedStudent?.student_id === student.student_id ? selectedStudentStyle : {}),
                  borderRadius: '12px',
                  overflow: 'hidden'
                }}
                bodyStyle={{ padding: '20px' }}
              >
                <div className="student-info-header" style={{ textAlign: 'center', marginBottom: 16 }}>
                  <Avatar
                    size={64}
                    icon={<UserOutlined />}
                    style={{
                      backgroundColor: selectedStudent?.student_id === student.student_id ? '#1890ff' : '#87d068',
                      marginBottom: 8
                    }}
                  />
                  <div>
                    <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
                      {student.student_name}
                    </Title>
                    <Text type="secondary" style={{ fontSize: '13px' }}>
                      {student.class_name}
                    </Text>
                  </div>
                </div>

                <Divider style={{ margin: '12px 0' }} />

                <Row className="student-stats-row" gutter={8} style={{ textAlign: 'center' }}>
                  <Col span={12}>
                    <Statistic
                      title="科目数"
                      value={subjectCount}
                      valueStyle={{ fontSize: '16px', color: '#1890ff' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="最近作业"
                      value={totalRecentHomework}
                      valueStyle={{ fontSize: '16px', color: '#52c41a' }}
                    />
                  </Col>
                </Row>

                <div className="student-card-buttons" style={{ marginTop: 16 }}>
                  <Space size="small" style={{ width: '100%', justifyContent: 'center' }}>
                    <Button
                      type="primary"
                      size="small"
                      icon={<BookOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewHomework(student);
                      }}
                    >
                      作业详情
                    </Button>
                    <Button
                      size="small"
                      icon={<BarChartOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewReport(student);
                      }}
                    >
                      学习报告
                    </Button>
                  </Space>
                </div>

                {selectedStudent?.student_id === student.student_id && (
                  <div style={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    background: '#1890ff',
                    color: 'white',
                    borderRadius: '50%',
                    width: 20,
                    height: 20,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '12px'
                  }}>
                    ✓
                  </div>
                )}
              </Card>
            </Col>
          );
        })}
      </Row>
    );
  };

  // 渲染统计信息
  const renderStatistics = () => {
    if (!selectedStudent || !studentStats) {
      return null;
    }

    const stats = studentStats.homework_stats;
    
    return (
      <Card title="学习统计" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col xs={12} sm={6}>
            <Statistic
              title="总作业数"
              value={stats.total_homework}
              prefix={<BookOutlined />}
            />
          </Col>
          <Col xs={12} sm={6}>
            <Statistic
              title="已提交"
              value={stats.submitted_homework}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
          <Col xs={12} sm={6}>
            <Statistic
              title="完成率"
              value={stats.completion_rate}
              suffix="%"
              prefix={<TrophyOutlined />}
            />
          </Col>
          <Col xs={12} sm={6}>
            <Statistic
              title="平均分"
              value={stats.avg_score}
              precision={1}
              prefix={<BarChartOutlined />}
            />
          </Col>
        </Row>
        
        <Divider />
        
        <div>
          <Text strong>完成进度</Text>
          <Progress 
            percent={stats.completion_rate} 
            status={stats.completion_rate >= 80 ? 'success' : stats.completion_rate >= 60 ? 'normal' : 'exception'}
            style={{ marginTop: 8 }}
          />
        </div>
      </Card>
    );
  };

  // 渲染最近作业
  const renderRecentHomework = () => {
    if (!selectedStudent) {
      return null;
    }

    const studentRecentHomework = recentHomework[selectedStudent.student_id] || {};
    const subjects = Object.keys(studentRecentHomework);

    return (
      <Card
        className="recent-homework-card"
        title={
          <Space>
            <BookOutlined style={{ color: '#1890ff' }} />
            最近作业
          </Space>
        }
        style={{ marginTop: 16 }}
        extra={
          <Button
            type="primary"
            icon={<EyeOutlined />}
            onClick={() => handleViewHomework(selectedStudent)}
          >
            查看全部作业
          </Button>
        }
      >
        {subjects.length === 0 ? (
          <Empty description="暂无作业记录" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        ) : (
          <Row className="recent-homework-subjects" gutter={[16, 16]}>
            {subjects.map(subject => {
              const subjectHomework = studentRecentHomework[subject];
              return (
                <Col xs={24} sm={12} lg={8} key={subject}>
                  <Card
                    size="small"
                    title={
                      <Space>
                        <Tag color="blue">{subject}</Tag>
                        <Text type="secondary">最近{subjectHomework.length}次</Text>
                      </Space>
                    }
                    style={{ height: '100%' }}
                  >
                    <List
                      size="small"
                      dataSource={subjectHomework}
                      renderItem={(homework) => (
                        <List.Item style={{ padding: '8px 0' }}>
                          <div style={{ width: '100%' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 }}>
                              <Text strong className="homework-item-title" style={{ fontSize: '13px' }}>
                                {homework.assignment_title || homework.title}
                              </Text>
                              <Badge
                                status={
                                  homework.status === 'graded' ? 'success' :
                                  homework.status === 'submitted' ? 'processing' : 'warning'
                                }
                                text={
                                  homework.status === 'graded' ? '已批改' :
                                  homework.status === 'submitted' ? '已提交' : '未完成'
                                }
                              />
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Text type="secondary" className="homework-item-meta" style={{ fontSize: '12px' }}>
                                {new Date(homework.created_at).toLocaleDateString()}
                              </Text>
                              {homework.score !== null && homework.score !== undefined ? (
                                <Text
                                  style={{
                                    fontSize: '12px',
                                    color: homework.score >= 80 ? '#52c41a' : homework.score >= 60 ? '#faad14' : '#ff4d4f',
                                    fontWeight: 'bold'
                                  }}
                                >
                                  {homework.score}分
                                </Text>
                              ) : null}
                            </div>
                          </div>
                        </List.Item>
                      )}
                    />
                  </Card>
                </Col>
              );
            })}
          </Row>
        )}
      </Card>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>正在加载家长端数据...</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="parent-page" style={{ padding: '24px' }}>
      {/* 欢迎信息 */}
      <Card
        style={{
          marginBottom: 24,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          border: 'none',
          borderRadius: '12px'
        }}
        bodyStyle={{ padding: '32px' }}
      >
        <Row align="middle" gutter={16}>
          <Col>
            <div style={{ fontSize: '48px' }}>👨‍👩‍👧‍👦</div>
          </Col>
          <Col flex="auto">
            <Title level={2} style={{ color: 'white', margin: 0 }}>
              欢迎回来，{user?.full_name || user?.username}！
            </Title>
            <Paragraph style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0', fontSize: '16px' }}>
              智慧云端家长端为您提供孩子的学习情况查看和分析服务
            </Paragraph>
          </Col>
          <Col>
            <Space direction="vertical" style={{ textAlign: 'center' }}>
              <Text style={{ color: 'white', fontSize: '14px' }}>今日</Text>
              <Text style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>
                {new Date().toLocaleDateString()}
              </Text>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 学生选择 */}
      <Card
        title={
          <Space>
            <UserOutlined style={{ color: '#1890ff' }} />
            我的孩子
            <Badge count={boundStudents.length} style={{ backgroundColor: '#52c41a' }} />
          </Space>
        }
        style={{ marginBottom: 24 }}
        bodyStyle={{ padding: '24px' }}
      >
        {renderStudentCards()}
      </Card>

      {/* 选中学生的详细信息 */}
      {selectedStudent && (
        <>
          {renderStatistics()}
          {renderRecentHomework()}
        </>
      )}
    </div>
  );
};

export default ParentDashboard;
