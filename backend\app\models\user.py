from datetime import datetime
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app.database import Base

# 角色映射 - 统一使用中文角色
ROLE_MAPPING = {
    "student": "学生",
    "teacher": "教师",
    "parent": "家长",
    "admin": "管理员",
    "school_admin": "学校管理员",
    "super_admin": "超级管理员"
}

def normalize_role(role):
    """标准化角色名称为中文"""
    if not role:
        return None
    return ROLE_MAPPING.get(role, role)

class User(Base):
    """用户表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), index=True)  # 移除unique=True，允许多学校系统用户名重复
    email = Column(String(100), unique=True, index=True, nullable=True)
    hashed_password = Column(String(100))
    full_name = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    is_teacher = Column(Boolean, default=False)
    subject = Column(String(50), nullable=True)  # 教师教授的学科
    role = Column(String(50), nullable=True)  # 用户角色
    school_id = Column(Integer, ForeignKey("schools.id"), nullable=True)
    school_name = Column(String, nullable=True)  # 学校名称
    primary_role_id = Column(Integer, nullable=True)  # 主要角色ID
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    avatar_url = Column(String(255), nullable=True)
    
    # 关系
    school = relationship("School", back_populates="users")
    class_ = relationship("Class", foreign_keys=[class_id])
    submitted_homeworks = relationship("Homework", back_populates="student")
    comments = relationship("Comment", back_populates="user")
    assigned_homeworks = relationship("HomeworkAssignment", back_populates="teacher")
    classes_teaching = relationship("ClassTeacher", back_populates="teacher")
    roles = relationship("UserRole", back_populates="user", cascade="all, delete-orphan")
    
    # 新增关系
    registration_status = relationship("UserRegistrationStatus", foreign_keys="UserRegistrationStatus.user_id", back_populates="user", uselist=False)
    first_reviewed_registrations = relationship("UserRegistrationStatus", foreign_keys="UserRegistrationStatus.first_reviewer_id", back_populates="first_reviewer")
    final_reviewed_registrations = relationship("UserRegistrationStatus", foreign_keys="UserRegistrationStatus.final_reviewer_id", back_populates="final_reviewer")
    student_verifications = relationship("ParentStudentVerification", foreign_keys="ParentStudentVerification.student_id", back_populates="student")
    parent_verifications = relationship("ParentStudentVerification", foreign_keys="ParentStudentVerification.parent_id", back_populates="parent")
    school_applications = relationship("SchoolApplication", foreign_keys="SchoolApplication.applicant_id", back_populates="applicant")
    reviewed_school_applications = relationship("SchoolApplication", foreign_keys="SchoolApplication.reviewer_id", back_populates="reviewer")
    
    # 家长-学生关系
    children = relationship("ParentStudent", foreign_keys="ParentStudent.parent_id")
    parents = relationship("ParentStudent", foreign_keys="ParentStudent.student_id")
    
    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}', role='{self.role}')>"

class Class(Base):
    __tablename__ = "classes"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String, nullable=True)
    school_id = Column(Integer, ForeignKey("schools.id"), nullable=True)
    grade = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    teachers = relationship("ClassTeacher", back_populates="class_")
    students = relationship("ClassStudent", back_populates="class_")
    homework_assignments = relationship("HomeworkAssignment", back_populates="class_")
    school = relationship("School", back_populates="classes")

class ClassTeacher(Base):
    __tablename__ = "class_teachers"
    
    id = Column(Integer, primary_key=True, index=True)
    class_id = Column(Integer, ForeignKey("classes.id"))
    teacher_id = Column(Integer, ForeignKey("users.id"))
    
    # 关系
    class_ = relationship("Class", back_populates="teachers")
    teacher = relationship("User", back_populates="classes_teaching")

class ClassStudent(Base):
    __tablename__ = "class_students"
    
    id = Column(Integer, primary_key=True, index=True)
    class_id = Column(Integer, ForeignKey("classes.id"))
    student_id = Column(Integer, ForeignKey("users.id"))
    
    # 关系
    class_ = relationship("Class", back_populates="students")
    student = relationship("User") 

# 家长学生关联表
class ParentStudent(Base):
    """家长学生关联表"""
    __tablename__ = "parent_students"

    id = Column(Integer, primary_key=True, index=True)
    parent_id = Column(Integer, ForeignKey("users.id"))
    student_id = Column(Integer, ForeignKey("users.id"))
    relationship = Column(String(20))  # 父亲、母亲、祖父母等
    is_primary = Column(Boolean, default=False)  # 是否主要监护人
    created_at = Column(DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<ParentStudent(parent_id={self.parent_id}, student_id={self.student_id}, relationship='{self.relationship}')>"