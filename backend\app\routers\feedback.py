from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from ..database import get_db
from ..models.user import User
from ..models.homework import WrongQuestion, ReinforcementExercise
from ..schemas import homework as homework_schema
from ..routers.auth import get_current_user

router = APIRouter()

@router.get("/wrong-questions", response_model=List[homework_schema.WrongQuestion])
async def get_wrong_questions(
    homework_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取错题列表"""
    # 构建基本查询
    query = db.query(WrongQuestion)
    
    # 学生只能查看自己的错题
    if not current_user.is_teacher:
        query = query.filter(WrongQuestion.student_id == current_user.id)
    
    # 按作业ID过滤
    if homework_id:
        query = query.filter(WrongQuestion.homework_id == homework_id)
    else:
        # 如果是教师，需要限制查询范围
        if current_user.is_teacher:
            from ..models.homework import Homework, HomeworkAssignment
            query = query.join(Homework, WrongQuestion.homework_id == Homework.id)\
                         .join(HomeworkAssignment, Homework.assignment_id == HomeworkAssignment.id)\
                         .filter(HomeworkAssignment.teacher_id == current_user.id)
    
    # 执行查询
    wrong_questions = query.order_by(WrongQuestion.created_at.desc()).all()
    
    return wrong_questions

@router.get("/wrong-questions/{question_id}", response_model=homework_schema.WrongQuestion)
async def get_wrong_question(
    question_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取单个错题详情"""
    # 查询错题
    question = db.query(WrongQuestion).filter(WrongQuestion.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="错题不存在")
    
    # 验证权限
    if not current_user.is_teacher and question.student_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此错题")
    
    return question

@router.get("/reinforcement-exercises", response_model=List[homework_schema.ReinforcementExercise])
async def get_reinforcement_exercises(
    wrong_question_id: Optional[int] = None,
    is_completed: Optional[bool] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取强化训练列表"""
    # 构建基本查询
    query = db.query(ReinforcementExercise)
    
    # 学生只能查看自己的强化训练
    if not current_user.is_teacher:
        query = query.filter(ReinforcementExercise.student_id == current_user.id)
    
    # 应用过滤器
    if wrong_question_id:
        query = query.filter(ReinforcementExercise.wrong_question_id == wrong_question_id)
    
    if is_completed is not None:
        query = query.filter(ReinforcementExercise.is_completed == is_completed)
    
    # 执行查询
    exercises = query.order_by(ReinforcementExercise.created_at.desc()).all()
    
    return exercises

@router.put("/reinforcement-exercises/{exercise_id}", response_model=homework_schema.ReinforcementExercise)
async def update_reinforcement_exercise(
    exercise_id: int,
    is_completed: bool,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新强化训练完成状态"""
    # 查询练习
    exercise = db.query(ReinforcementExercise).filter(ReinforcementExercise.id == exercise_id).first()
    if not exercise:
        raise HTTPException(status_code=404, detail="强化训练不存在")
    
    # 验证权限
    if exercise.student_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限更新此强化训练")
    
    # 更新完成状态
    exercise.is_completed = is_completed
    if is_completed:
        exercise.completed_at = datetime.utcnow()
    else:
        exercise.completed_at = None
    
    db.commit()
    db.refresh(exercise)
    
    return exercise 