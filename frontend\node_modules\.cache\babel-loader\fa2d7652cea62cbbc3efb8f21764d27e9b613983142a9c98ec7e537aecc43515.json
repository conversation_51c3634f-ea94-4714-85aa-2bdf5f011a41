{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genQRCodeStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    lineType,\n    colorSplit\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: token.paddingSM,\n      backgroundColor: token.colorWhite,\n      borderRadius: token.borderRadiusLG,\n      border: `${unit(lineWidth)} ${lineType} ${colorSplit}`,\n      position: 'relative',\n      overflow: 'hidden',\n      [`& > ${componentCls}-mask`]: {\n        position: 'absolute',\n        insetBlockStart: 0,\n        insetInlineStart: 0,\n        zIndex: 10,\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        width: '100%',\n        height: '100%',\n        color: token.colorText,\n        lineHeight: token.lineHeight,\n        background: token.QRCodeMaskBackgroundColor,\n        textAlign: 'center',\n        [`& > ${componentCls}-expired, & > ${componentCls}-scanned`]: {\n          color: token.QRCodeTextColor\n        }\n      },\n      '> canvas': {\n        alignSelf: 'stretch',\n        flex: 'auto',\n        minWidth: 0\n      },\n      '&-icon': {\n        marginBlockEnd: token.marginXS,\n        fontSize: token.controlHeight\n      }\n    }),\n    [`${componentCls}-borderless`]: {\n      borderColor: 'transparent',\n      padding: 0,\n      borderRadius: 0\n    }\n  };\n};\nexport const prepareComponentToken = token => ({\n  QRCodeMaskBackgroundColor: new FastColor(token.colorBgContainer).setA(0.96).toRgbString()\n});\nexport default genStyleHooks('QRCode', token => {\n  const mergedToken = mergeToken(token, {\n    QRCodeTextColor: token.colorText\n  });\n  return genQRCodeStyle(mergedToken);\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}