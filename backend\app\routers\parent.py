"""
家长端专用API路由
独立实现，避免与现有系统耦合
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc
from datetime import datetime, timedelta

from ..database import get_db
from ..models.user import User, ParentStudent
from ..models.homework import Homework, HomeworkAssignment, HomeworkCorrection
from ..models.user import Class, ClassStudent
from ..models.subject import Subject
from ..routers.auth import get_current_user
from ..services.parent_service import ParentService
from ..services.feature_service import FeatureService, feature_required, check_feature_enabled
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/parent", tags=["家长端"])

def get_current_parent_user(current_user: User = Depends(get_current_user)):
    """验证当前用户是否为家长"""
    # 支持中文和英文角色名称
    if current_user.role not in ["parent", "家长"]:
        raise HTTPException(status_code=403, detail="仅限家长用户访问")
    return current_user

def verify_student_access(parent_id: int, student_id: int, db: Session):
    """验证家长是否有权限访问指定学生的数据"""
    parent_student = db.query(ParentStudent).filter(
        ParentStudent.parent_id == parent_id,
        ParentStudent.student_id == student_id
    ).first()
    
    if not parent_student:
        raise HTTPException(status_code=403, detail="无权限访问该学生数据")
    
    return parent_student

# ==================== 第一阶段：基础功能 ====================

@router.get("/bound-students")
async def get_bound_students(
    current_user: User = Depends(get_current_parent_user),
    db: Session = Depends(get_db)
):
    """获取家长绑定的学生列表"""
    try:
        # 检查基础功能是否启用
        if not check_feature_enabled("parent_basic.bound_students", db):
            raise HTTPException(status_code=404, detail="功能未启用")

        service = ParentService(db)
        students = await service.get_bound_students(current_user.id)
        return {
            "success": True,
            "data": students
        }
    except Exception as e:
        logger.error(f"获取绑定学生列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取绑定学生列表失败: {str(e)}")

@router.get("/student/{student_id}/homework")
async def get_student_homework(
    student_id: int,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None, description="作业状态筛选"),
    current_user: User = Depends(get_current_parent_user),
    db: Session = Depends(get_db)
):
    """获取指定学生的作业列表"""
    try:
        # 验证权限
        verify_student_access(current_user.id, student_id, db)
        
        service = ParentService(db)
        homework_list = await service.get_student_homework(
            student_id, page, limit, status
        )
        return {
            "success": True,
            "data": homework_list
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学生作业列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取学生作业列表失败: {str(e)}")

@router.get("/student/{student_id}/profile")
async def get_student_profile(
    student_id: int,
    current_user: User = Depends(get_current_parent_user),
    db: Session = Depends(get_db)
):
    """获取学生基本信息"""
    try:
        # 验证权限
        verify_student_access(current_user.id, student_id, db)
        
        service = ParentService(db)
        profile = await service.get_student_profile(student_id)
        return {
            "success": True,
            "data": profile
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学生信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取学生信息失败: {str(e)}")

# ==================== 第二阶段：增强功能 ====================

@router.get("/homework/{homework_id}/detail")
async def get_homework_detail(
    homework_id: int,
    current_user: User = Depends(get_current_parent_user),
    db: Session = Depends(get_db)
):
    """获取作业详细信息"""
    try:
        # 获取作业信息并验证权限
        homework = db.query(Homework).filter(Homework.id == homework_id).first()
        if not homework:
            raise HTTPException(status_code=404, detail="作业不存在")
        
        # 验证家长是否有权限查看该学生的作业
        verify_student_access(current_user.id, homework.student_id, db)
        
        service = ParentService(db)
        detail = await service.get_homework_detail(homework_id)
        return {
            "success": True,
            "data": detail
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取作业详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取作业详情失败: {str(e)}")

@router.get("/student/{student_id}/statistics")
async def get_student_statistics(
    student_id: int,
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    current_user: User = Depends(get_current_parent_user),
    db: Session = Depends(get_db)
):
    """获取学生基础统计信息"""
    try:
        # 验证权限
        verify_student_access(current_user.id, student_id, db)
        
        service = ParentService(db)
        statistics = await service.get_student_statistics(student_id, days)
        return {
            "success": True,
            "data": statistics
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学生统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取学生统计信息失败: {str(e)}")

@router.get("/student/{student_id}/recent-performance")
async def get_recent_performance(
    student_id: int,
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_parent_user),
    db: Session = Depends(get_db)
):
    """获取学生最近表现"""
    try:
        # 验证权限
        verify_student_access(current_user.id, student_id, db)
        
        service = ParentService(db)
        performance = await service.get_recent_performance(student_id, limit)
        return {
            "success": True,
            "data": performance
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学生最近表现失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取学生最近表现失败: {str(e)}")

# ==================== 工具函数 ====================

@router.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "parent-api"
    }

@router.get("/features")
async def get_available_features(
    current_user: User = Depends(get_current_parent_user),
    db: Session = Depends(get_db)
):
    """获取当前可用的功能列表"""
    try:
        feature_service = FeatureService(db)
        enabled_features = feature_service.get_enabled_features()

        return {
            "success": True,
            "data": enabled_features
        }
    except Exception as e:
        logger.error(f"获取功能列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取功能列表失败: {str(e)}")
