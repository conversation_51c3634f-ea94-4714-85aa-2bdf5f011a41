from pydantic import BaseModel, Field, validator
from typing import List, Optional
from datetime import datetime
import re

# 基础学生信息schema
class StudentBase(BaseModel):
    id: int
    username: str
    full_name: Optional[str] = None
    email: str
    # 移除phone字段，因为User模型中没有这个属性

# 班级基础信息schema
class ClassBase(BaseModel):
    name: str
    description: Optional[str] = None
    grade: Optional[str] = None
    school_id: Optional[int] = None

# 创建班级请求schema
class ClassCreate(ClassBase):
    pass

# 更新班级请求schema
class ClassUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    grade: Optional[str] = None
    school_id: Optional[int] = None

# 添加学生到班级请求schema
class AddStudentToClass(BaseModel):
    student_id: int

# 创建新学生并添加到班级请求schema
class CreateStudentForClass(BaseModel):
    username: str
    full_name: str
    email: str
    password: Optional[str] = None

    # 自定义邮箱验证器，与UserBase保持一致
    @validator('email')
    def validate_email(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('邮箱不能为空')

        # 使用简单的正则表达式验证邮箱格式
        email_pattern = re.compile(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$')
        if not email_pattern.match(v):
            raise ValueError('邮箱格式不正确')

        return v

# 班级响应schema
class Class(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    created_at: datetime
    student_count: int = 0
    grade: Optional[str] = None
    school_id: Optional[int] = None

    model_config = {
        "from_attributes": True
    }

# 班级详细信息模型，包含学生列表
class ClassDetail(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    created_at: datetime
    student_count: int
    students: List[StudentBase]
    grade: Optional[str] = None
    school_id: Optional[int] = None
    
    model_config = {
        "from_attributes": True
    }

# 带学生列表的班级详情schema
class ClassWithStudents(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    created_at: datetime
    student_count: int
    students: List[StudentBase]
    
    class Config:
        from_attributes = True 