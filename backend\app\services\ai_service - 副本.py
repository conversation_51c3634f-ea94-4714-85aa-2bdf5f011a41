import httpx
import json
import os
import re
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any
import logging
from dotenv import load_dotenv

from ..models.homework import Homework, HomeworkImage, HomeworkCorrection, WrongQuestion, HomeworkAnnotatedImage
from ..models.user import User
from ..models.ai_config import AIModelConfig, AIUsageType
import traceback
from datetime import datetime, timezone, timedelta
import requests
import httpx
import time
import asyncio
import uuid
import base64
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont


from typing import Dict, List, Any, Optional, Tuple
from ..database import SessionLocal
import random
import textwrap

# 设置北京时间时区（UTC+8）
BEIJING_TIMEZONE = timezone(timedelta(hours=8))

# 获取北京时间的函数
def get_beijing_time():
    """获取北京时间（UTC+8）"""
    return datetime.now(BEIJING_TIMEZONE)
# 修正导入路径
# from ..models.admin import AiConfig

# 定义上传目录
UPLOAD_DIR = "backend/uploads"

# 配置日志 - 输出到控制台和文件
import sys
logging.basicConfig(
    level=logging.DEBUG,  # 使用DEBUG级别，记录更多信息
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),  # 输出到控制台
        logging.FileHandler("debug_ai.log", mode='a')  # 输出到文件，追加模式
    ]
)

# 确保日志立即刷新
for handler in logging.root.handlers:
    handler.flush()
logger = logging.getLogger(__name__)

def get_ai_config_by_usage(db: Session, usage_type: str) -> AIModelConfig:
    """根据用途获取AI配置"""
    # 如果传入的是枚举，转换为字符串
    if hasattr(usage_type, 'value'):
        usage_type = usage_type.value

    config = db.query(AIModelConfig).filter(
        AIModelConfig.usage_type == usage_type,
        AIModelConfig.is_active == True
    ).first()

    if not config:
        # 如果没有找到指定用途的配置，尝试使用作业批改的配置作为后备
        config = db.query(AIModelConfig).filter(
            AIModelConfig.usage_type == "homework_grading",
            AIModelConfig.is_active == True
        ).first()

        if not config:
            raise Exception(f"未找到可用的AI配置，用途: {usage_type}")

    return config


# 确保日志信息被输出
logger.info("AI服务初始化")
logger.info(f"当前北京时间: {get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')}")

# 加载环境变量
load_dotenv()

# 检查Ollama模型是否存在
async def check_ollama_model(model_name: str, api_url: str = "http://localhost:11434/api/tags") -> bool:
    """检查指定的Ollama模型是否存在
    
    参数:
        model_name: 模型名称
        api_url: Ollama API URL
    
    返回:
        bool: 模型是否存在
    """
    try:
        # 根据实际的安装模型列表（从控制台输出获取）
        installed_models = [
            "Model-7.6B-F16.gguf", 
            "llava:7b", 
            "Qwen2.5-VL-7B-Instruct-BF16.gguf", 
            "qwen3:14b", 
            "deepseek-r1:8b", 
            "gemma3:4b", 
            "bge-m3:567m", 
            "llama3.2-vision:11b", 
            "qwen2.5vl:7b", 
            "gemma3:12b", 
            "qwen3:32b"
        ]
        
        # 直接检查模型名称是否在已安装列表中
        if model_name in installed_models:
            logger.info(f"找到已安装的Ollama模型: {model_name}")
            return True
            
        # 如果需要，尝试通过API验证（可能会比较慢）
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(api_url, timeout=2.0)  # 减少超时时间加快速度
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    # 检查模型是否在列表中
                    for model in models:
                        if model.get("name") == model_name:
                            logger.info(f"API确认已安装模型: {model_name}")
                            return True
                    logger.warning(f"API未找到模型: {model_name}")
                    return False
        except Exception as api_error:
            logger.warning(f"通过API检查模型失败: {str(api_error)}")
            # API检查失败时，继续使用本地验证结果
            
        logger.warning(f"未找到模型: {model_name}")
        return False
    except Exception as e:
        logger.error(f"检查Ollama模型时出错: {str(e)}")
        return False

# 异步函数确定可用的Ollama模型
async def find_available_ollama_model() -> str:
    """尝试找到一个可用的Ollama模型
    
    返回:
        str: 可用的模型名称，如果都不可用则返回默认模型
    """
    # 根据实际安装的模型列表
    model_candidates = ["deepseek-r1:8b", "gemma3:12b", "llama3.2-vision:11b", "qwen2.5vl:7b", "qwen3:14b", "gemma3:4b"]
    
    for model in model_candidates:
        logger.info(f"检查Ollama模型是否可用: {model}")
        if await check_ollama_model(model):
            logger.info(f"使用可用的Ollama模型: {model}")
            return model
    
    # 如果没有找到可用的模型，使用默认值
    default_model = "deepseek-r1:8b"  # 使用已知可用的模型
    logger.warning(f"未找到指定的Ollama模型，使用默认值: {default_model}")
    return default_model

# API密钥和URL
VOLCANO_ENGINE_API_KEY = os.getenv("VOLCANO_ENGINE_API_KEY", "")
VOLCANO_ENGINE_URL = os.getenv("VOLCANO_ENGINE_URL", "")
VOLCANO_ENGINE_MODEL_ID = os.getenv("VOLCANO_ENGINE_MODEL_ID", "")
OLLAMA_URL = os.getenv("OLLAMA_URL", "http://localhost:11434/api/generate")

# 使用环境变量中的值，如果没有，则使用默认的模型
OLLAMA_MODEL = os.getenv("OLLAMA_MODEL", "deepseek-r1:8b")  # 默认使用deepseek-r1:8b

# 不在导入时运行异步函数，改为同步初始化
def init_ollama_model():
    """初始化Ollama模型配置"""
    global OLLAMA_MODEL
    # 如果环境变量已经指定了模型，则使用环境变量中的模型
    if os.getenv("OLLAMA_MODEL"):
        logger.info(f"使用环境变量指定的Ollama模型: {OLLAMA_MODEL}")
        return
    
    # 使用默认模型列表中的第一个可用模型
    model_candidates = ["deepseek-r1:8b", "gemma3:12b", "llama3.2-vision:11b", "qwen2.5vl:7b", "qwen3:14b", "gemma3:4b"]
    logger.info(f"没有环境变量指定模型，将使用默认值: {OLLAMA_MODEL}")
    # 此处不进行实际检查，避免事件循环冲突，使用预设的默认值

# 初始化模型配置
init_ollama_model()

QIANWEN_API_KEY = os.getenv("QIANWEN_API_KEY", "")
QIANWEN_URL = os.getenv("QIANWEN_URL", "")
QIANWEN_MODEL = os.getenv("QIANWEN_MODEL", "qwen-turbo")
# 默认使用ollama作为提供商，因为它是本地可用的
AI_PROVIDER = os.getenv("AI_PROVIDER", "ollama")  # 默认使用ollama，因为它是本地的

async def get_active_ai_config(db: Session = None, pattern_provider: str = None, ai_config_id: int = None):
    """
    获取当前激活的AI配置
    
    参数:
    db: 数据库会话
    pattern_provider: 模型提供商名称
    ai_config_id: 指定的AI配置ID
    
    返回:
    dict: 激活的AI配置字典
    """
    try:
        # 记录日志
        logger.info(f"获取AI配置: provider={pattern_provider}, config_id={ai_config_id}")
        
        # 如果没有提供数据库会话，创建一个新的会话
        if db is None:
            logger.info("未提供数据库会话，创建新会话")
            db = SessionLocal()
            local_db = True
        else:
            local_db = False
            
        # 如果提供了特定的AI配置ID，直接获取该配置
        if ai_config_id:
            logger.info(f"使用指定的AI配置ID: {ai_config_id}")
            config = db.query(AIModelConfig).filter(AIModelConfig.id == ai_config_id).first()
            if config:
                logger.info(f"找到指定的AI配置: {config.model_name}")
                return {
                    "provider": config.provider,
                    "model_name": config.model_name,
                    "model_id": config.model_id,
                    "api_key": config.api_key,
                    "api_endpoint": config.api_endpoint,
                    "is_active": config.is_active
                }
            else:
                logger.warning(f"未找到ID为 {ai_config_id} 的AI配置")
        
        # 如果提供了模型提供商，获取该提供商的配置
        if pattern_provider:
            try:
                provider_config = db.query(AIModelConfig).filter(
                    AIModelConfig.is_active == True,
                    AIModelConfig.provider == pattern_provider,
                ).first()
                
                if provider_config:
                    logger.info(f"从数据库获取到{pattern_provider}提供商的配置: {provider_config.model_name}")
                    return {
                        "provider": provider_config.provider,
                        "model_name": provider_config.model_name,
                        "model_id": provider_config.model_id,
                        "api_key": provider_config.api_key,
                        "api_endpoint": provider_config.api_endpoint,
                        "is_active": provider_config.is_active
                    }
                else:
                    logger.warning(f"未找到{pattern_provider}提供商的激活配置")
            except Exception as e:
                logger.error(f"查询{pattern_provider}提供商配置时出错: {str(e)}")
                
            # 如果数据库中没有找到配置，使用默认配置
            if pattern_provider == "volcano":
                logger.info("使用默认火山引擎配置")
                # 对于火山引擎，确保使用支持图片处理的模型ID
                volcano_model_id = os.environ.get("VOLCANO_ENGINE_MODEL_ID", "")
                if not volcano_model_id or "vision" not in volcano_model_id.lower():
                    # 如果环境变量中的模型ID不支持图片处理，使用已知支持图片的模型ID
                    volcano_model_id = "Doubao-1.5-vision-pro"
                    logger.info(f"环境变量中的模型ID不支持图片处理，使用默认视觉模型: {volcano_model_id}")
                
                return {
                    "provider": "volcano",
                    "model_name": "eb-turbo-appbuilder",
                    "model_id": volcano_model_id,
                    "api_key": os.environ.get("VOLCANO_API_KEY", ""),
                    "api_endpoint": VOLCANO_ENGINE_URL,
                    "is_active": True
                }
            elif pattern_provider == "ollama":
                logger.info("使用默认Ollama配置")
                return {
                    "provider": "ollama",
                    "model_name": "gemma3:4b",
                    "model_id": OLLAMA_MODEL,
                    "api_key": "",
                    "api_endpoint": "http://localhost:11434/api/generate",
                    "is_active": True
                }
            elif pattern_provider == "qianwen":
                logger.info("使用默认千问配置")
                return {
                    "provider": "qianwen",
                    "model_name": "qwen-max",
                    "model_id": QIANWEN_MODEL,
                    "api_key": os.environ.get("QIANWEN_API_KEY", ""),
                    "api_endpoint": QIANWEN_URL,
                    "is_active": True
                }
    except Exception as e:
        logger.error(f"获取AI配置出错: {str(e)}")
        logger.error(traceback.format_exc())
    
    # 如果以上方法都失败，使用环境变量
    logger.info("使用环境变量中的默认AI配置")
    
    # 确保使用支持图片处理的模型ID
    volcano_model_id = os.environ.get("VOLCANO_ENGINE_MODEL_ID", "")
    if not volcano_model_id or "vision" not in volcano_model_id.lower():
        # 如果环境变量中的模型ID不支持图片处理，使用已知支持图片的模型ID
        volcano_model_id = "Doubao-1.5-vision-pro"
        logger.info(f"环境变量中的模型ID不支持图片处理，使用默认视觉模型: {volcano_model_id}")
    
    return {
        "provider": AI_PROVIDER,
        "api_key": VOLCANO_ENGINE_API_KEY,
        "api_endpoint": VOLCANO_ENGINE_URL,
        "model_name": "默认模型",
        "model_id": volcano_model_id
    }

async def call_volcano_engine_api(prompt: str, db: Session = None) -> Optional[str]:
    """调用火山引擎API"""
    # 获取配置（优先从数据库获取）
    config = await get_active_ai_config(db)
    
    api_key = config["api_key"]
    api_endpoint = config["api_endpoint"]
    model_id = config["model_id"]  # 使用model_id
    
    if not api_key or not api_endpoint:
        logger.error("未配置火山引擎API密钥或URL")
        return None
    
    if not model_id:
        logger.error("未配置火山引擎模型ID")
        return None
    
    try:
        logger.info(f"调用火山引擎API，模型ID: {model_id}, 端点: {api_endpoint}")
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 根据不同的火山引擎模型调整payload格式
        if "glm" in model_id.lower():
            # GLM系列模型
            payload = {
                "model": model_id,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.7,
                "top_p": 0.9,
                "stream": False
            }
            logger.info("使用GLM系列模型格式")
        elif "doubao" in model_id.lower():
            # 豆包系列模型
            payload = {
                "model": model_id,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "parameters": {
                    "temperature": 0.7,
                    "top_p": 0.9
                },
                "stream": False
            }
            logger.info("使用豆包系列模型格式")
        else:
            # 默认格式
            payload = {
                "model": model_id,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.7,
                "top_p": 0.9,
                "stream": False
            }
            logger.info("使用默认模型格式")
        
        logger.info(f"请求负载: {json.dumps(payload, ensure_ascii=False)}")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                api_endpoint,
                headers=headers,
                json=payload,
                timeout=30.0
            )
            response.raise_for_status()
            result = response.json()
            logger.info("火山引擎API调用成功")
            logger.info(f"响应结果: {json.dumps(result, ensure_ascii=False)[:200]}...")  # 只记录前200个字符
            
            # 尝试不同的响应格式
            if "choices" in result and len(result["choices"]) > 0:
                if "message" in result["choices"][0]:
                    return result["choices"][0]["message"]["content"]
                elif "text" in result["choices"][0]:
                    return result["choices"][0]["text"]
            
            # 豆包模型可能有不同的响应格式
            if "data" in result:
                if "choices" in result["data"] and len(result["data"]["choices"]) > 0:
                    return result["data"]["choices"][0].get("message", {}).get("content", "")
            
            # 如果无法解析结果，返回原始JSON
            return json.dumps(result, ensure_ascii=False)
            
    except Exception as e:
        logger.error(f"火山引擎API调用失败: {str(e)}")
        logger.error(traceback.format_exc())
        return None

async def call_volcano_engine_vision_api(prompt: str, image_data: str, db: Session = None, ai_config_id: int = None) -> Optional[str]:
    """调用火山引擎支持图片的API
    
    参数:
        prompt: 提示词
        image_data: 图片的base64编码数据
        db: 数据库会话
        ai_config_id: AI配置ID
    """
    # 获取配置（优先从数据库获取）
    config = await get_active_ai_config(db, ai_config_id=ai_config_id)
    
    api_key = config["api_key"]
    api_endpoint = config["api_endpoint"]
    model_id = config["model_id"]  # 使用model_id
    
    # 记录配置信息用于调试
    logger.info(f"火山引擎视觉API配置信息: 模型ID={model_id}, API端点={api_endpoint}, API密钥长度={len(api_key) if api_key else 0}")
    
    # 对于ID为3的特殊配置，确保API密钥和端点正确设置
    if ai_config_id == 3:
        if not api_key or api_key.strip() == "":
            api_key = "sk-2f8e3e6e9b9b4f3e9f9b9b9b9b9b9b9b"  # 默认API密钥
            logger.info("使用默认API密钥")
        
        if not api_endpoint or api_endpoint.strip() == "":
            api_endpoint = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"  # 默认API端点
            logger.info("使用默认API端点")
    
    if not api_key or not api_endpoint:
        logger.error("未配置火山引擎API密钥或URL")
        return None
    
    if not model_id:
        logger.error("未配置火山引擎模型ID")
        return None
    
    try:
        logger.info(f"调用火山引擎视觉API，模型ID: {model_id}, 端点: {api_endpoint}")
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 构建包含图片的消息
        # 尝试检测图片类型
        import base64
        try:
            # 检查base64编码的前几个字节来确定图片类型
            img_bytes = base64.b64decode(image_data[:20])
            img_type = "jpeg"  # 默认类型
            
            # 简单的图片格式检测
            if img_bytes.startswith(b'\xff\xd8'):
                img_type = "jpeg"
            elif img_bytes.startswith(b'\x89PNG'):
                img_type = "png"
            elif img_bytes.startswith(b'GIF'):
                img_type = "gif"
            
            logger.info(f"检测到图片类型: {img_type}")
        except Exception as e:
            logger.error(f"检测图片类型失败: {str(e)}")
            img_type = "jpeg"  # 默认使用jpeg
        
        # 构建图片URL
        image_url = f"data:image/{img_type};base64,{image_data}"
        logger.info(f"图片URL前缀: data:image/{img_type};base64,...")
        
        message = {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": image_url
                    }
                }
            ]
        }
        
        # 根据不同的火山引擎模型调整payload格式
        if "doubao" in model_id.lower() and "vision" in model_id.lower():
            # 豆包视觉模型
            payload = {
                "model": model_id,
                "messages": [message],
                "parameters": {
                    "temperature": 0.7,
                    "top_p": 0.9
                },
                "stream": False
            }
            logger.info("使用豆包视觉模型格式")
        else:
            # 默认格式
            payload = {
                "model": model_id,
                "messages": [message],
                "temperature": 0.7,
                "top_p": 0.9,
                "stream": False
            }
            logger.info("使用默认视觉模型格式")
        
        logger.info("发送带图片的请求到火山引擎API")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                api_endpoint,
                headers=headers,
                json=payload,
                timeout=180.0  # 增加超时时间到180秒，以适应大图片处理
            )
            response.raise_for_status()
            result = response.json()
            logger.info("火山引擎视觉API调用成功")
            logger.info(f"响应结果: {json.dumps(result, ensure_ascii=False)[:200]}...")  # 只记录前200个字符
            
            # 尝试不同的响应格式
            if "choices" in result and len(result["choices"]) > 0:
                if "message" in result["choices"][0]:
                    return result["choices"][0]["message"]["content"]
                elif "text" in result["choices"][0]:
                    return result["choices"][0]["text"]
            
            # 豆包模型可能有不同的响应格式
            if "data" in result:
                if "choices" in result["data"] and len(result["data"]["choices"]) > 0:
                    return result["data"]["choices"][0].get("message", {}).get("content", "")
            
            # 如果无法解析结果，返回原始JSON
            return json.dumps(result, ensure_ascii=False)
            
    except Exception as e:
        logger.error(f"火山引擎视觉API调用失败: {str(e)}")
        logger.error(traceback.format_exc())
        return None

async def call_volcano_engine_vision_api_alt(prompt: str, image_data: str, db: Session = None, ai_config_id: int = None) -> Optional[str]:
    """使用替代格式调用火山引擎支持图片的API
    
    参数:
        prompt: 提示词
        image_data: 图片的base64编码数据
        db: 数据库会话
        ai_config_id: AI配置ID
    """
    # 获取配置（优先从数据库获取）
    config = await get_active_ai_config(db, ai_config_id=ai_config_id)
    
    api_key = config["api_key"]
    api_endpoint = config["api_endpoint"]
    model_id = config["model_id"]  # 使用model_id
    
    # 记录配置信息用于调试
    logger.info(f"火山引擎视觉API(替代格式)配置信息: 模型ID={model_id}, API端点={api_endpoint}, API密钥长度={len(api_key) if api_key else 0}")
    
    # 对于ID为3的特殊配置，确保API密钥和端点正确设置
    if ai_config_id == 3:
        if not api_key or api_key.strip() == "":
            api_key = "sk-2f8e3e6e9b9b4f3e9f9b9b9b9b9b9b9b"  # 默认API密钥
            logger.info("使用默认API密钥(替代格式)")
        
        if not api_endpoint or api_endpoint.strip() == "":
            api_endpoint = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"  # 默认API端点
            logger.info("使用默认API端点(替代格式)")
    
    if not api_key or not api_endpoint:
        logger.error("未配置火山引擎API密钥或URL")
        return None
    
    if not model_id:
        logger.error("未配置火山引擎模型ID")
        return None
    
    try:
        logger.info(f"使用替代格式调用火山引擎视觉API，模型ID: {model_id}, 端点: {api_endpoint}")
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 使用不同的消息格式
        # 方式1：使用单一消息，但图片URL直接包含在文本中
        message1 = {
            "role": "user",
            "content": f"{prompt}\n\n[图片内容：data:image/jpeg;base64,{image_data[:20]}...]"
        }
        
        # 方式2：使用system消息设置角色和任务
        messages2 = [
            {
                "role": "system",
                "content": "你是一位专业教师，负责批改学生作业。请分析图片中的作业内容，并按照JSON格式返回批改结果。"
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_data}"
                        }
                    }
                ]
            }
        ]
        
        # 尝试不同的请求格式
        payloads = []
        
        # 格式1：标准OpenAI格式
        payloads.append({
            "model": model_id,
            "messages": messages2,
            "temperature": 0.3,  # 降低温度，使输出更确定性
            "response_format": {"type": "json_object"}  # 明确要求JSON格式
        })
        
        # 格式2：火山引擎特定格式
        payloads.append({
            "model": model_id,
            "messages": messages2,
            "parameters": {
                "temperature": 0.3,
                "top_p": 0.95,
                "response_format": {"type": "json_object"}
            }
        })
        
        # 格式3：简化格式
        payloads.append({
            "model": model_id,
            "prompt": f"请分析以下学生作业图片并进行批改，以JSON格式返回结果：\n\n[图片数据已上传]",
            "messages": messages2,
            "temperature": 0.3
        })
        
        # 尝试每种格式
        for i, payload in enumerate(payloads):
            try:
                logger.info(f"尝试请求格式 {i+1}")
                
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        api_endpoint,
                        headers=headers,
                        json=payload,
                        timeout=180.0  # 增加超时时间到180秒，以适应大图片处理
                    )
                    response.raise_for_status()
                    result = response.json()
                    logger.info(f"格式 {i+1} 请求成功")
                    
                    # 尝试解析响应
                    content = None
                    
                    # 尝试不同的响应格式
                    if "choices" in result and len(result["choices"]) > 0:
                        if "message" in result["choices"][0]:
                            content = result["choices"][0]["message"].get("content")
                        elif "text" in result["choices"][0]:
                            content = result["choices"][0]["text"]
                    
                    # 豆包模型可能有不同的响应格式
                    if content is None and "data" in result:
                        if "choices" in result["data"] and len(result["data"]["choices"]) > 0:
                            content = result["data"]["choices"][0].get("message", {}).get("content")
                    
                    if content:
                        logger.info(f"成功获取内容: {content[:100]}...")
                        return content
                    
                    logger.warning(f"无法从响应中提取内容: {json.dumps(result, ensure_ascii=False)[:200]}...")
                    
            except Exception as e:
                logger.error(f"格式 {i+1} 请求失败: {str(e)}")
                continue
        
        logger.error("所有替代格式都失败了")
        return None
        
    except Exception as e:
        logger.error(f"火山引擎视觉API替代调用失败: {str(e)}")
        logger.error(traceback.format_exc())
        return None


async def call_ollama_api(prompt: str, image_data: str = None, model_id: str = None, api_endpoint: str = None) -> Optional[str]:
    """调用本地Ollama API"""
    try:
        # 获取模型ID和API端点，优先使用传入的参数，其次使用环境变量中的默认值
        model_id = model_id or OLLAMA_MODEL
        api_url = api_endpoint or OLLAMA_URL

        # 记录请求详情，帮助调试
        logger.info(f"Ollama API请求URL: {api_url}")
        logger.info(f"Ollama API使用模型: {model_id}")

        # 首先检查Ollama服务是否可用
        try:
            async with httpx.AsyncClient() as client:
                # 检查Ollama服务状态
                tags_url = f"{api_url.rstrip('/')}/api/tags"
                health_response = await client.get(tags_url, timeout=5.0)
                if health_response.status_code != 200:
                    logger.error(f"Ollama服务不可用，状态码: {health_response.status_code}")
                    raise Exception(f"Ollama服务不可用: {health_response.status_code}")

                # 检查模型是否存在
                models_data = health_response.json()
                available_models = [model.get("name", "") for model in models_data.get("models", [])]
                logger.info(f"可用的Ollama模型: {available_models}")

                if model_id not in available_models:
                    logger.warning(f"指定的模型 {model_id} 不在可用列表中，尝试使用第一个可用模型")
                    if available_models:
                        model_id = available_models[0]
                        logger.info(f"使用可用模型: {model_id}")
                    else:
                        raise Exception("没有可用的Ollama模型")

        except Exception as health_error:
            logger.error(f"检查Ollama服务状态失败: {str(health_error)}")
            # 如果健康检查失败，直接返回错误响应而不是继续尝试
            error_result = {
                "error": f"Ollama服务不可用: {str(health_error)}",
                "questions": [
                    {
                        "question_number": "1",
                        "question_type": "服务不可用",
                        "question_content": "Ollama AI服务当前不可用",
                        "student_answer": "无法处理",
                        "is_correct": False,
                        "correct_answer": "请检查Ollama服务",
                        "analysis": f"Ollama服务检查失败: {str(health_error)}。请确保Ollama服务正在运行并且模型已正确安装。",
                        "reinforcement": "请联系管理员检查AI服务配置"
                    }
                ],
                "summary": {
                    "total_questions": 1,
                    "correct_count": 0,
                    "accuracy": 0.0
                }
            }
            return json.dumps(error_result, ensure_ascii=False)

        # 添加详细的批改日志
        with open("ollama_correction.log", "a", encoding="utf-8") as f:
            f.write(f"\n\n--- 新的请求 {get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')} ---\n")
            f.write(f"使用模型: {model_id}\n")
            f.write(f"API端点: {api_url}\n")
            f.write(f"提示词前200字符: {prompt[:200]}...\n")
            if image_data:
                f.write(f"包含图片数据: 是 (长度: {len(image_data)} 字符)\n")
        
        # 检查是否是批改任务还是普通对话
        is_correction_task = any(keyword in prompt.lower() for keyword in ["批改", "学生作业", "题目", "json", "questions", "summary"])

        if is_correction_task and image_data:
            # 批改任务的提示词
            json_prompt = f"""你是一位批改作业的老师。请分析图片中的作业内容并给出批改结果。

请使用以下JSON格式返回结果，不要有任何其他文本：

{{
    "questions": [
        {{
            "question_number": "1",
            "question_type": "题目类型",
            "question_content": "题目内容",
            "student_answer": "学生答案",
            "is_correct": true或false,
            "correct_answer": "正确答案",
            "analysis": "分析",
            "reinforcement": "强化训练建议"
        }}
    ],
    "summary": {{
        "total_questions": 1,
        "correct_count": 0,
        "accuracy": 0.0
    }}
}}

注意：
1. 只输出JSON格式，不要有任何前缀或后缀
2. 不要使用markdown代码块
3. 不要添加解释或说明

问题：{prompt}"""
        else:
            # 普通对话的提示词
            json_prompt = f"""你是智教云端平台的AI助手，专门为教师和学生提供教育相关的帮助和解答。请用简洁、专业、友好的语言回答用户的问题。

用户问题：{prompt}

请直接回答用户的问题，不要使用JSON格式，用自然语言回复即可。"""
        
        # 基本payload
        if is_correction_task and image_data:
            # 批改任务的配置
            payload = {
                "model": model_id,
                "prompt": json_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.01,  # 使用极低的温度以获得确定性的输出
                    "top_p": 0.99,
                    "top_k": 1,          # 限制词汇选择，使输出更确定
                    "format": "json",    # 请求JSON格式输出
                    "num_ctx": 8192,     # 增加上下文窗口大小
                    "seed": 42,          # 固定种子值以获得一致的结果
                    "num_predict": 4096  # 限制生成长度，避免过长输出
                }
            }
        else:
            # 普通对话的配置
            payload = {
                "model": model_id,
                "prompt": json_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,   # 对话使用较高的温度，更自然
                    "top_p": 0.9,
                    "num_ctx": 4096,      # 对话使用较小的上下文窗口
                    "num_predict": 1024   # 对话限制较短的回复长度
                }
            }

        # 如果提供了图像数据，始终尝试发送图像
        if image_data:
            try:
                logger.info(f"尝试处理图像数据并发送到模型: {model_id}")
                # 直接添加图像到payload，不检查模型类型
                payload["images"] = [image_data]
                
                # 检查图像数据是否有效
                if len(image_data) < 100:
                    logger.error(f"图像数据可能无效，长度过短: {len(image_data)} 字符")
                else:
                    logger.info(f"图像数据长度: {len(image_data)} 字符")
            except Exception as e:
                logger.error(f"处理图像数据失败: {str(e)}")
                logger.error(traceback.format_exc())

        # 记录请求参数
        logger.info(f"Ollama API请求Payload: {json.dumps(payload)}")
        # 记录完整请求详情（用于调试）
        logger.debug(f"发送的请求详情: {json.dumps(payload, ensure_ascii=False, indent=2)}")

        # 构建正确的API端点
        generate_url = f"{api_url.rstrip('/')}/api/generate"
        logger.info(f"Ollama API生成端点: {generate_url}")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                generate_url,
                json=payload,
                timeout=90.0  # 增加超时时间以处理大图片
            )
            # 记录响应状态
            logger.info(f"Ollama API响应状态码: {response.status_code}")
            logger.info(f"Ollama API响应内容: {response.text[:200]}...")
            # 记录完整响应（用于调试）
            logger.debug(f"收到的完整响应: {response.text}")
            
            # 记录响应到专用日志文件
            with open("ollama_correction.log", "a", encoding="utf-8") as f:
                f.write(f"响应状态码: {response.status_code}\n")
                f.write(f"响应内容前300字符: {response.text[:300]}...\n")
                f.write(f"响应时间: {get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            response.raise_for_status()
            result = response.json()
            # 记录解析后的JSON响应（用于调试）
            logger.debug(f"解析后的JSON响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

            # 尝试多种方式提取响应内容
            response_text = None
            
            # 方式1: 标准response字段
            if "response" in result:
                response_text = result["response"]
                logger.info("从标准response字段获取响应")
            # 方式2: 检查其他可能的响应格式
            elif "message" in result:
                response_text = result.get("message", {}).get("content", "")
                logger.info("从message.content字段获取响应")
            elif "choices" in result and len(result["choices"]) > 0:
                if "message" in result["choices"][0]:
                    response_text = result["choices"][0]["message"].get("content", "")
                    logger.info("从choices[0].message.content字段获取响应")
                elif "text" in result["choices"][0]:
                    response_text = result["choices"][0]["text"]
                    logger.info("从choices[0].text字段获取响应")
            
            # 如果找不到任何有效响应，记录错误并返回错误信息
            if not response_text:
                error_msg = f"无法从Ollama响应中提取有效内容: {json.dumps(result, ensure_ascii=False)}"
                logger.error(error_msg)
                
                # 记录错误到专用日志
                with open("ollama_correction.log", "a", encoding="utf-8") as f:
                    f.write(f"错误: {error_msg}\n")
                    f.write(f"原始响应: {json.dumps(result, ensure_ascii=False)[:500]}...\n")
                
                # 创建错误响应并返回
                error_result = {
                    "error": "无法从Ollama响应中提取内容",
                    "questions": [
                        {
                            "question_number": "1",
                            "question_type": "自动批改失败",
                            "question_content": "API返回的响应格式无效",
                            "student_answer": "无法处理",
                            "is_correct": False,
                            "correct_answer": "请手动批改",
                            "analysis": "无法从AI响应中提取有效内容，请检查ollama_correction.log获取详细信息",
                            "reinforcement": "请稍后再试或联系管理员"
                        }
                    ],
                    "summary": {
                        "total_questions": 1,
                        "correct_count": 0,
                        "accuracy": 0.0
                    }
                }
                return json.dumps(error_result, ensure_ascii=False)

            # 清理和处理响应格式
            clean_response = response_text.strip()
            logger.debug(f"提取的响应文本: {clean_response[:200]}...")

            # 如果是普通对话，直接返回文本响应
            if not is_correction_task or not image_data:
                logger.info("普通对话响应，直接返回文本")
                return clean_response

            # 以下是批改任务的JSON处理逻辑
            # 检查是否包含Markdown格式的JSON代码块
            if "```json" in clean_response:
                import re
                logger.debug("检测到JSON代码块，尝试提取")
                json_match = re.search(r'```json\s*(.*?)\s*```', clean_response, re.DOTALL)
                if json_match:
                    clean_response = json_match.group(1).strip()
                    logger.debug("成功从JSON代码块提取内容")
            elif "```" in clean_response:
                import re
                logger.debug("检测到代码块，尝试提取")
                json_match = re.search(r'```\s*(.*?)\s*```', clean_response, re.DOTALL)
                if json_match:
                    clean_response = json_match.group(1).strip()
                    logger.debug("成功从代码块提取内容")

            # 尝试解析JSON
            try:
                # 首先尝试查找JSON开始和结束位置
                json_start = clean_response.find('{')
                json_end = clean_response.rfind('}') + 1
                
                if json_start >= 0 and json_end > json_start:
                    # 提取可能的JSON部分
                    potential_json = clean_response[json_start:json_end]
                    logger.info(f"从响应中提取的潜在JSON: {potential_json[:100]}...")
                    
                    try:
                        # 尝试解析提取的JSON
                        json_obj = json.loads(potential_json)
                        logger.info("成功从响应中提取并解析JSON")
                        
                        # 验证JSON结构是否符合预期格式
                        if "questions" not in json_obj or "summary" not in json_obj:
                            logger.warning("JSON结构不完整，添加缺失的字段")
                            if "questions" not in json_obj:
                                json_obj["questions"] = []
                            if "summary" not in json_obj:
                                json_obj["summary"] = {
                                    "total_questions": len(json_obj.get("questions", [])),
                                    "correct_count": 0,
                                    "accuracy": 0.0
                                }
                        
                        # 将解析后的JSON重新序列化为字符串
                        return json.dumps(json_obj, ensure_ascii=False)
                    except json.JSONDecodeError as e:
                        logger.warning(f"提取的JSON解析失败: {str(e)}")
                
                # 如果上述方法失败，尝试直接解析整个响应
                if clean_response.startswith("{") and clean_response.endswith("}"):
                    try:
                        # 检查是否包含JSON对象
                        json_obj = json.loads(clean_response)
                        logger.info("成功解析完整的JSON对象")
                        
                        # 验证JSON结构是否符合预期格式
                        if "questions" not in json_obj or "summary" not in json_obj:
                            logger.warning("JSON结构不完整，添加缺失的字段")
                            if "questions" not in json_obj:
                                json_obj["questions"] = []
                            if "summary" not in json_obj:
                                json_obj["summary"] = {
                                    "total_questions": len(json_obj.get("questions", [])),
                                    "correct_count": 0,
                                    "accuracy": 0.0
                                }
                        
                        # 将解析后的JSON重新序列化为字符串
                        return json.dumps(json_obj, ensure_ascii=False)
                    except json.JSONDecodeError:
                        logger.warning("完整响应不是有效的JSON格式")
                
                # 如果以上方法都失败，检查是否是普通对话
                if not is_correction_task or not image_data:
                    logger.info("普通对话响应，直接返回文本内容")
                    return clean_response

                # 对于批改任务，创建一个模拟的批改结果
                logger.warning("批改任务响应文本不是有效的JSON格式，创建模拟批改结果")

                # 记录原始响应到错误日志
                with open("ollama_correction_error.log", "a", encoding="utf-8") as f:
                    f.write(f"\n--- 无法解析的响应 {get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')} ---\n")
                    f.write(f"原始响应:\n{clean_response}\n")
                    f.write("------------------------------\n")

                # 创建一个基本的批改结果结构
                formatted_result = {
                    "questions": [
                        {
                            "question_number": "1",
                            "question_type": "文本响应",
                            "question_content": "AI返回了纯文本响应",
                            "student_answer": "无法自动解析",
                            "is_correct": False,
                            "correct_answer": "请查看下方AI分析",
                            "analysis": clean_response[:1000],  # 取前1000个字符作为分析
                            "reinforcement": "请咨询教师获取更多帮助"
                        }
                    ],
                    "summary": {
                        "total_questions": 1,
                        "correct_count": 0,
                        "accuracy": 0.0
                    }
                }
                return json.dumps(formatted_result, ensure_ascii=False)
            except json.JSONDecodeError as e:
                error_msg = f"JSON解析失败: {str(e)}"
                logger.error(error_msg)

                # 如果是普通对话，直接返回文本响应
                if not is_correction_task or not image_data:
                    logger.info("普通对话JSON解析失败，返回原始文本")
                    return clean_response

                # 记录错误到专用日志
                with open("ollama_correction.log", "a", encoding="utf-8") as f:
                    f.write(f"JSON解析错误: {error_msg}\n")
                    f.write(f"尝试解析的内容: {clean_response[:500]}...\n")

                # 将纯文本响应格式化为批改结果结构
                formatted_result = {
                    "questions": [
                        {
                            "question_number": "1",
                            "question_type": "解析失败",
                            "question_content": "AI返回的内容无法解析为JSON",
                            "student_answer": "无法自动解析",
                            "is_correct": False,
                            "correct_answer": "请查看下方AI分析",
                            "analysis": clean_response[:1000],  # 取前1000个字符作为分析
                            "reinforcement": "请咨询教师获取更多帮助"
                        }
                    ],
                    "summary": {
                        "total_questions": 1,
                        "correct_count": 0,
                        "accuracy": 0.0
                    }
                }
                return json.dumps(formatted_result, ensure_ascii=False)
            else:
                logger.error("Ollama API响应缺少'response'字段")
                # 创建错误响应并返回
                error_result = {
                    "error": "Ollama API响应缺少'response'字段",
                    "questions": [
                        {
                            "question_number": "1",
                            "question_type": "API格式错误",
                            "question_content": "Ollama API返回了无效格式的响应",
                            "student_answer": "无法处理",
                            "is_correct": False,
                            "correct_answer": "请手动批改",
                            "analysis": f"API响应格式错误，缺少'response'字段。原始响应: {json.dumps(result)[:300]}...",
                            "reinforcement": "请稍后再试或联系管理员"
                        }
                    ],
                    "summary": {
                        "total_questions": 1,
                        "correct_count": 0,
                        "accuracy": 0.0
                    }
                }
                return json.dumps(error_result, ensure_ascii=False)

    except Exception as e:
        error_msg = f"Ollama API调用失败: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        
        # 记录错误到专用日志
        with open("ollama_correction.log", "a", encoding="utf-8") as f:
            f.write(f"\n\n--- API调用异常 {get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')} ---\n")
            f.write(f"错误信息: {error_msg}\n")
            f.write(f"堆栈跟踪: {traceback.format_exc()}\n")
            f.write(f"提示词前200字符: {prompt[:200]}...\n")
            if image_data:
                f.write(f"包含图片数据: 是 (长度: {len(image_data)} 字符)\n")
        
        # 检查是否是普通对话
        if not is_correction_task or not image_data:
            logger.info("普通对话API调用失败，返回错误信息")
            return f"抱歉，AI助手暂时遇到技术问题：{str(e)[:100]}。请稍后再试或联系管理员。"

        # 在失败时返回模拟批改数据，而不是返回None
        error_result = {
            "error": error_msg,
            "questions": [
                {
                    "question_number": "1",
                    "question_type": "自动批改失败",
                    "question_content": "由于技术原因，自动批改失败",
                    "student_answer": "无法识别",
                    "is_correct": False,
                    "correct_answer": "请手动批改",
                    "analysis": f"Ollama API调用失败，详细错误已记录到日志。错误信息: {str(e)[:200]}",
                    "reinforcement": "请稍后再试或联系管理员"
                }
            ],
            "summary": {
                "total_questions": 1,
                "correct_count": 0,
                "accuracy": 0.0
            }
        }

        # 返回结构化的错误信息，而不是None
        return json.dumps(error_result, ensure_ascii=False)

async def call_qianwen_api(prompt: str, db: Session = None) -> Optional[str]:
    """调用通义千问API"""
    # 获取配置
    config = await get_active_ai_config(db, "qianwen")
    
    api_key = config["api_key"]
    api_endpoint = config["api_endpoint"]
    model_id = config["model_id"]
    
    if not api_key or not api_endpoint:
        logger.error("未配置通义千问API密钥或URL")
        return None
    
    try:
        logger.info(f"调用通义千问API，模型: {model_id}")
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        payload = {
            "model": model_id,
            "input": {
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            },
            "parameters": {
                "temperature": 0.7,
                "top_p": 0.9
            }
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                api_endpoint,
                headers=headers,
                json=payload,
                timeout=30.0
            )
            response.raise_for_status()
            result = response.json()
            
            if "output" in result and "text" in result["output"]:
                return result["output"]["text"]
            
            logger.error(f"未能从通义千问响应中提取内容: {json.dumps(result, ensure_ascii=False)}")
            return "通义千问大模型无法生成回复，请稍后再试。"
    
    except Exception as e:
        logger.error(f"通义千问API调用失败: {str(e)}")
        logger.error(traceback.format_exc())
        return None

async def call_ai_api(prompt: str, db: Session = None, pattern_provider: str = None, ai_config_id: int = None, image_data: str = None) -> Optional[str]:
    """
    调用AI API进行对话
    
    参数:
    prompt: 提示词
    db: 数据库会话
    pattern_provider: 模型提供商
    ai_config_id: AI配置ID
    image_data: 图片数据（base64编码）
    
    返回:
    str: AI响应文本
    """
    # 获取配置（优先使用指定的ai_config_id，其次是pattern_provider）
    config = await get_active_ai_config(db, pattern_provider, ai_config_id)
    if not config:
        logger.error(f"无法获取AI配置: pattern_provider={pattern_provider}, ai_config_id={ai_config_id}")
        return generate_mock_response(prompt)
    
    provider = config["provider"]
    
    logger.info(f"使用AI提供商: {provider}")
    
    try:
        # 调用相应的API
        if provider == "volcano" or provider == "volcengine":
            # 如果有图片数据，调用支持图片的API
            if image_data:
                logger.info(f"调用火山引擎视觉API，模型ID: {config['model_id']}")
                # 检查模型ID是否包含"vision"关键字
                model_id = config["model_id"].lower() if config["model_id"] else ""
                # ID为3的配置是支持图片处理的，不需要检查关键字
                if ai_config_id == 3:
                    logger.info(f"使用ID为3的配置，已确认支持图片处理: {config['model_id']}")
                    # 如果ID为3的配置的model_id不包含vision关键字，强制修改为支持图片的模型ID
                    if not model_id or ("vision" not in model_id and "doubao" not in model_id):
                        config["model_id"] = "Doubao-1.5-vision-pro"
                        logger.info(f"ID为3的配置模型ID不支持图片处理，已修改为: {config['model_id']}")
                elif "vision" not in model_id and "doubao" not in model_id:
                    logger.warning(f"当前模型 {config['model_id']} 可能不支持图片处理。推荐使用包含'vision'的模型，如'Doubao-1.5-vision-pro'")
                
                # 对于ID为3的配置，确保API密钥和端点正确设置
                if ai_config_id == 3:
                    # 确保配置中包含正确的API密钥和端点
                    if not config["api_key"] or config["api_key"].strip() == "":
                        logger.error(f"ID为3的配置缺少API密钥，尝试使用环境变量")
                        config["api_key"] = os.environ.get("VOLCANO_API_KEY", "")
                        # 如果环境变量中也没有API密钥，使用硬编码的默认值（仅用于ID为3的特殊配置）
                        if not config["api_key"] or config["api_key"].strip() == "":
                            config["api_key"] = "sk-2f8e3e6e9b9b4f3e9f9b9b9b9b9b9b9b"  # 默认API密钥
                            logger.info("使用默认API密钥")
                    
                    if not config["api_endpoint"] or config["api_endpoint"].strip() == "":
                        logger.error(f"ID为3的配置缺少API端点，尝试使用环境变量")
                        config["api_endpoint"] = os.environ.get("VOLCANO_ENGINE_URL", "")
                        # 如果环境变量中也没有API端点，使用硬编码的默认值（仅用于ID为3的特殊配置）
                        if not config["api_endpoint"] or config["api_endpoint"].strip() == "":
                            config["api_endpoint"] = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"  # 默认API端点
                            logger.info("使用默认API端点")
                        
                    logger.info(f"使用ID为3的配置调用视觉API，API端点: {config['api_endpoint']}")
                
                result = await call_volcano_engine_vision_api(prompt, image_data, db, ai_config_id)
                if result:
                    logger.info("火山引擎视觉API调用成功")
                    # 检查返回的结果是否是模拟响应
                    if "作为智教云端平台的AI助手" in result or "对于学习中遇到的问题，我建议" in result:
                        logger.error("API返回了模拟响应，而不是实际批改结果")
                        # 尝试使用不同的格式重新调用
                        logger.info("尝试使用不同的请求格式重新调用API")
                        result = await call_volcano_engine_vision_api_alt(prompt, image_data, db, ai_config_id)
                        if result and not ("作为智教云端平台的AI助手" in result or "对于学习中遇到的问题，我建议" in result):
                            return result
                        else:
                            logger.error("重新调用API仍然失败")
                    else:
                        return result
                else:
                    logger.error("火山引擎视觉API调用失败，返回空结果")
            else:
                logger.info(f"调用火山引擎文本API，模型ID: {config['model_id']}")
                result = await call_volcano_engine_api(prompt, db)
                if result:
                    logger.info("火山引擎文本API调用成功")
                    return result
                else:
                    logger.error("火山引擎文本API调用失败，返回空结果")
        elif provider == "qianwen":
            logger.info("调用通义千问API")
            result = await call_qianwen_api(prompt)
            if result:
                logger.info("通义千问API调用成功")
                return result
            else:
                logger.error("通义千问API调用失败，返回空结果")
        else:  # ollama 或其他
            logger.info("调用Ollama API")
            result = await call_ollama_api(prompt, image_data, config["model_id"], config["api_endpoint"])
            if result:
                logger.info("Ollama API调用成功")
                return result
            else:
                error_msg = "Ollama API调用失败，返回空结果"
                logger.error(error_msg)
                
                # 记录错误到专用日志
                with open("ollama_correction.log", "a", encoding="utf-8") as f:
                    f.write(f"\n\n--- API返回空结果 {get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')} ---\n")
                    f.write(f"错误信息: {error_msg}\n")
                    f.write(f"模型ID: {config['model_id']}\n")
                    f.write(f"API端点: {config['api_endpoint']}\n")
                    f.write(f"提示词前200字符: {prompt[:200]}...\n")
                    if image_data:
                        f.write(f"包含图片数据: 是 (长度: {len(image_data)} 字符)\n")
                
                # 确保不返回None，而是返回错误格式的JSON
                error_result = {
                    "error": error_msg,
                    "questions": [
                        {
                            "question_number": "1",
                            "question_type": "API错误",
                            "question_content": "无法获取批改结果",
                            "student_answer": "无法处理",
                            "is_correct": False,
                            "correct_answer": "请手动批改",
                            "analysis": "API调用未返回有效结果，详细错误已记录到日志",
                            "reinforcement": "请稍后再试或联系管理员"
                        }
                    ],
                    "summary": {
                        "total_questions": 1,
                        "correct_count": 0,
                        "accuracy": 0.0
                    }
                }
                return json.dumps(error_result, ensure_ascii=False)
        
        # 如果调用失败，返回模拟响应
        logger.warning("所有真实API调用都失败了，使用模拟响应")
        return generate_mock_response(prompt)
    except Exception as e:
        logger.error(f"AI API调用出错: {str(e)}")
        logger.error(traceback.format_exc())
        # 出错时返回模拟响应
        return generate_mock_response(prompt)

def generate_mock_response(prompt: str) -> str:
    """生成模拟响应，当真实AI服务不可用时使用"""
    logger.info("生成模拟响应")
    
    # 检查是否是批改任务相关的提示
    if "批改" in prompt or "学生作业" in prompt or "题目" in prompt or "json" in prompt.lower():
        # 为批改任务返回有效的JSON结构
        logger.info("生成批改任务模拟数据")
        mock_correction = {
            "questions": [
                {
                    "question_number": "1",
                    "question_type": "模拟批改",
                    "question_content": "模拟批改内容 - 无法连接到AI服务",
                    "student_answer": "模拟学生答案",
                    "is_correct": False,
                    "correct_answer": "模拟正确答案",
                    "analysis": "模拟AI系统当前无法进行批改。请稍后再试或联系管理员。",
                    "reinforcement": "模拟强化训练建议"
                }
            ],
            "summary": {
                "total_questions": 1,
                "correct_count": 0,
                "accuracy": 0.0
            }
        }
        return json.dumps(mock_correction, ensure_ascii=False)
    
    # 对于其他类型的查询，返回普通的文本响应
    prompt_lower = prompt.lower()
    
    if "你好" in prompt_lower or "hello" in prompt_lower or "hi" in prompt_lower:
        return "你好！我是智教云端平台的AI助手，很高兴为您服务。请问有什么我可以帮助您的吗？"
    
    elif "什么是" in prompt_lower or "解释" in prompt_lower or "定义" in prompt_lower:
        return "这是一个很好的问题。作为一个教育领域的AI助手，我可以解释相关概念。但目前我无法访问外部知识库，所以只能提供基本解释。如果您需要更详细的信息，建议查阅相关教材或专业资料。"
    
    elif "如何" in prompt_lower or "怎么" in prompt_lower or "方法" in prompt_lower:
        return "关于这个问题，我建议可以分几个步骤来思考：\n\n1. 首先明确目标和要求\n2. 分析现有资源和条件\n3. 设计合适的解决方案\n4. 执行并及时调整\n\n如果您能提供更具体的信息，我可以给出更针对性的建议。"
    
    else:
        # 默认响应
        return "感谢您的提问。作为智教云端平台的AI助手，我目前正在模拟模式下运行。真实的AI服务暂时不可用，但我仍然可以尝试帮助您。请问有什么具体的教育相关问题我可以协助解答的吗？"

async def analyze_homework_image(image_path: str) -> Optional[dict]:
    """分析作业图片"""
    # 构建分析提示
    prompt = f"""
    你是一位专业教师，请严格按照以下要求批改学生作业图片:
    
    1. 仔细识别图片中的所有题目和学生答案
    2. 判断每个题目的正确与否
    3. 对错误题目提供详细分析
    4. 给出每道题的标准答案
    5. 为错题提供针对性强化训练建议
    
    必须严格按照以下JSON格式返回结果，不要有任何额外文本:
    {{
        "questions": [
            {{
                "question_number": "题目编号",
                "question_type": "题目类型(选择题/填空题/计算题/解答题等)",
                "question_content": "题目内容",
                "student_answer": "学生答案",
                "is_correct": true或false,
                "correct_answer": "正确答案",
                "analysis": "错误分析(仅错误题目需要)",
                "reinforcement": "强化训练建议(针对错题)"
            }}
        ],
        "summary": {{
            "total_questions": 题目总数(数字),
            "correct_count": 正确题目数(数字),
            "accuracy": 正确率(0到1之间的小数)
        }}
    }}
    
    注意:
    - 返回结果必须是有效的JSON格式
    - 不要在JSON外添加任何额外说明
    - 确保格式严格符合要求，包括所有字段
    - 只评价图片中的学生答案，不要自行添加额外题目
    """
    
    # 注意：在实际实现中，需要将图片转换为AI可处理的格式
    # 这里简化处理，假设图片已经处理好并且内容已提取
    # 实际应用中需要使用OCR或其他图像处理技术先提取内容
    
    # 调用AI API进行分析
    result = await call_ai_api(prompt)
    if not result:
        return None
    
    try:
        # 解析JSON结果
        return json.loads(result)
    except json.JSONDecodeError as e:
        logger.error(f"AI返回的结果格式不是有效的JSON: {str(e)}")
        return None

async def process_homework(homework_id: int, db: Session):
    """处理作业（批改和生成错题报告）"""
    try:
        # 获取作业信息
        homework = db.query(Homework).filter(Homework.id == homework_id).first()
        if not homework:
            logger.error(f"作业不存在: {homework_id}")
            return
        
        # 获取作业图片
        images = db.query(HomeworkImage).filter(HomeworkImage.homework_id == homework_id).all()
        if not images:
            logger.error(f"作业 {homework_id} 没有图片")
            return
        
        # 更新作业状态
        homework.status = "grading"
        db.commit()
        
        # 获取作业任务信息和批改模式
        correction_mode = "auto"
        pattern_provider = "volcano"
        ai_config_id = None
        auto_correct_description = None
        reference_description = None
        
        if homework.assignment_id:
            from ..models.homework import HomeworkAssignment
            assignment = db.query(HomeworkAssignment).filter(HomeworkAssignment.id == homework.assignment_id).first()
            if assignment:
                correction_mode = assignment.correction_mode
                pattern_provider = assignment.pattern_provider
                ai_config_id = assignment.ai_config_id
                auto_correct_description = assignment.auto_correct_description
                reference_description = assignment.reference_description
        
        logger.info(f"作业 {homework_id} 使用批改模式: {correction_mode}, 模型提供商: {pattern_provider}, AI配置ID: {ai_config_id}")
        
        # 批改所有页面
        total_score = 0
        total_questions = 0
        wrong_questions = []
        
        for image in images:
            # 构建提示词
            if correction_mode == "reference" and reference_description:
                # 使用参考答案图片批改
                prompt = f"""
                请作为专业教师，根据参考答案和批改标准，批改学生作业。
                
                批改标准补充说明:
                {reference_description}
                
                请分析学生作业图片，识别所有题目，并进行批改。对每个题目，请:
                1. 判断答案是否正确
                2. 对错误题目进行详细分析
                3. 给出正确答案
                4. 计算得分
                
                请严格按照以下JSON格式返回结果，不要包含任何其他文本：
                {{
                    "questions": [
                        {{
                            "question_number": "题目编号",
                            "question_type": "题目类型，如选择题、填空题等",
                            "question_content": "题目内容",
                            "student_answer": "学生答案",
                            "is_correct": true/false,
                            "correct_answer": "正确答案",
                            "analysis": "错误分析（如果答案错误）",
                            "reinforcement": "强化训练建议"
                        }}
                    ],
                    "summary": {{
                        "total_questions": 题目总数,
                        "correct_count": 正确题目数,
                        "accuracy": 正确率（小数形式）
                    }}
                }}
                """
            elif correction_mode == "manual":
                # TODO: 实现手动批改逻辑
                prompt = "手动批改模式暂未实现"
            else:
                # 自动批改
                base_prompt = """
                你是一位专业教师，请严格按照以下要求批改学生作业图片:
                
                1. 仔细识别图片中的所有题目和学生答案
                2. 判断每个题目的正确与否
                3. 对错误题目提供详细分析
                4. 给出每道题的标准答案
                5. 为错题提供针对性强化训练建议
                6. 准确标记每个答案在图片中的坐标位置
                
                关于答案坐标定位的要点:
                - 坐标系以图片左上角为原点(0,0)，x轴向右，y轴向下
                - 所有坐标值使用0到1之间的小数表示相对位置
                - 坐标必须指向学生实际答案所在位置，不是题目位置
                - 对于选择题，坐标应指向学生选择的选项(如A、B、C、D)所在位置
                - 对于填空题，坐标应指向学生填写的内容所在位置
                - 对于解答题，坐标应指向答案的核心区域
                - 坐标必须与题号正确对应，不要错位
                
                你必须严格按照以下JSON格式返回结果，不要有任何额外文本，不要添加任何解释或说明:
                {
                    "questions": [
                        {
                            "question_number": "题目编号",
                            "question_type": "题目类型(选择题/填空题/计算题/解答题等)",
                            "question_content": "题目内容",
                            "student_answer": "学生答案",
                            "is_correct": true或false,
                            "correct_answer": "正确答案",
                            "analysis": "错误分析(仅错误题目需要)",
                            "reinforcement": "强化训练建议(针对错题)",
                            "answer_position": {
                                "x": 0.5,  // 答案在图片中的水平位置（0到1之间的小数，0表示最左边，1表示最右边）
                                "y": 0.5,  // 答案在图片中的垂直位置（0到1之间的小数，0表示最上边，1表示最下边）
                                "width": 0.1,  // 答案区域的宽度（相对于图片宽度）
                                "height": 0.05  // 答案区域的高度（相对于图片高度）
                            }
                        }
                    ],
                    "summary": {
                        "total_questions": 题目总数(数字),
                        "correct_count": 正确题目数(数字),
                        "accuracy": 正确率(0到1之间的小数)
                    }
                }
                
                布局分析提示:
                - 注意分析图片中的整体布局(单栏、双栏、表格等)
                - 对于英语阅读理解等复杂布局，注意题目和答案的对应关系
                - 对于多栏布局，确保正确识别每栏中的题目顺序
                
                严格要求:
                - 只输出有效的JSON格式，不要有任何前缀或后缀
                - 不要添加"```json"或"```"等标记
                - 不要在JSON外添加任何额外说明或解释
                - 确保格式严格符合要求，包括所有字段
                - 只评价图片中的学生答案，不要自行添加额外题目
                - 如果无法识别图片内容，也必须返回有效的JSON格式，可以在analysis字段中说明原因
                - 必须为每个答案提供answer_position坐标信息，即使是估计值也要提供
                """
                
                # 添加自定义提示词
                if auto_correct_description:
                    prompt = f"{base_prompt}\n\n额外批改要求：\n{auto_correct_description}"
                else:
                    prompt = base_prompt
            
            # 处理图片并发送给AI
            try:
                # 获取图片的完整路径（去掉URL前缀）
                image_path = image.image_path
                if image_path.startswith("/uploads/"):
                    image_path = image_path[9:]  # 去掉"/uploads/"前缀
                
                # 构建完整路径（主路径使用UPLOAD_DIR）
                full_path = os.path.join(UPLOAD_DIR, image_path)
                
                # 构建备选路径1（相对于当前目录的uploads文件夹）
                alt_full_path = os.path.join("uploads", image_path)
                
                # 构建备选路径2（包含backend前缀）
                backend_full_path = os.path.join("backend", "uploads", image_path)
                
                # 构建备选路径3（使用绝对路径）
                # 获取当前文件所在目录
                current_dir = os.path.dirname(os.path.abspath(__file__))
                # 向上两级到项目根目录
                project_root = os.path.abspath(os.path.join(current_dir, "..", "..", ".."))
                # 构建绝对路径
                absolute_full_path = os.path.join(project_root, "backend", "uploads", image_path)
                
                logger.debug(f"尝试读取图片，尝试以下路径: \n1. {full_path}\n2. {alt_full_path}\n3. {backend_full_path}\n4. {absolute_full_path}")
                
                # 检查文件是否存在（按优先级尝试不同路径）
                file_to_use = None
                if os.path.exists(full_path):
                    file_to_use = full_path
                    logger.info(f"找到图片文件(主路径): {full_path}")
                    
                    # 读取图片并转换为base64编码
                    import base64
                    with open(full_path, "rb") as img_file:
                        img_data = base64.b64encode(img_file.read()).decode('utf-8')
                    
                    # 构建包含图片的提示词
                    image_prompt = f"{prompt}\n\n[图片内容已上传，请分析图片中的作业内容]"
                    
                    # 调用AI API进行批改（带图片）
                    logger.info(f"准备调用AI API进行批改，模型提供商: {pattern_provider}")
                    
                    # 记录批改请求到专用日志
                    with open("ollama_correction.log", "a", encoding="utf-8") as f:
                        f.write(f"\n\n--- 新的批改请求 {get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')} ---\n")
                        f.write(f"作业ID: {homework_id}, 页面: {image.page_number}\n")
                        f.write(f"模型提供商: {pattern_provider}, AI配置ID: {ai_config_id}\n")
                        f.write(f"图片路径: {file_to_use}\n")
                        f.write(f"图片数据长度: {len(img_data)} 字符\n")
                        f.write(f"提示词前200字符: {image_prompt[:200]}...\n")
                    
                    result = await call_ai_api(image_prompt, db, pattern_provider, ai_config_id, img_data)
                    
                    # 打印结果的前100个字符
                    if result:
                        logger.info(f"AI API返回结果前100个字符: {result[:100]}...")
                        
                        # 记录成功结果到专用日志
                        with open("ollama_correction.log", "a", encoding="utf-8") as f:
                            f.write(f"批改成功，结果前300字符: {result[:300]}...\n")
                    else:
                        error_msg = f"作业 {homework_id} 页面 {image.page_number} 批改失败，AI API返回空结果"
                        logger.error(error_msg)
                        
                        # 记录错误到专用日志
                        with open("ollama_correction.log", "a", encoding="utf-8") as f:
                            f.write(f"批改失败: {error_msg}\n")
                            f.write(f"使用模拟数据替代\n")
                        
                        # 使用模拟数据替代
                        result = generate_mock_correction_data(image.image_path)
                        logger.info(f"使用模拟批改数据替代（AI调用失败）: {result[:100]}...")
                
                elif os.path.exists(alt_full_path):
                    # 找到替代路径1
                    file_to_use = alt_full_path
                    logger.info(f"找到图片文件(替代路径1): {alt_full_path}")
                elif os.path.exists(backend_full_path):
                    # 找到替代路径2
                    file_to_use = backend_full_path
                    logger.info(f"找到图片文件(替代路径2): {backend_full_path}")
                elif os.path.exists(absolute_full_path):
                    # 找到替代路径3
                    file_to_use = absolute_full_path
                    logger.info(f"找到图片文件(替代路径3): {absolute_full_path}")
                
                # 如果找到了文件，读取并处理
                if file_to_use:
                    # 读取图片并转换为base64编码
                    import base64
                    try:
                        with open(file_to_use, "rb") as img_file:
                            img_data = base64.b64encode(img_file.read()).decode('utf-8')
                        
                        # 构建包含图片的提示词
                        image_prompt = f"{prompt}\n\n[图片内容已上传，请分析图片中的作业内容]"
                        
                        # 调用AI API进行批改（带图片）
                        logger.info(f"准备调用AI API进行批改，模型提供商: {pattern_provider}, AI配置ID: {ai_config_id}")
                        logger.info(f"图片数据长度: {len(img_data)} 字符")
                        logger.info(f"提示词前100个字符: {image_prompt[:100]}...")
                        
                        result = await call_ai_api(image_prompt, db, pattern_provider, ai_config_id, img_data)
                        
                        # 打印结果的前100个字符
                        if result:
                            logger.info(f"AI API返回结果前100个字符: {result[:100]}...")
                        else:
                            logger.error(f"作业 {homework_id} 页面 {image.page_number} 批改失败，AI API返回空结果")
                            # 使用模拟数据替代
                            result = generate_mock_correction_data(image.image_path)
                            logger.info(f"使用模拟批改数据替代（AI调用失败）: {result[:100]}...")
                    except Exception as img_error:
                        logger.error(f"读取或处理图片时出错: {str(img_error)}")
                        logger.error(traceback.format_exc())
                        # 使用模拟数据
                        result = generate_mock_correction_data(image.image_path)
                        logger.info(f"使用模拟批改数据替代（图片处理失败）: {result[:100]}...")
                
                else:
                    # 文件不存在，尝试所有可能的路径
                    logger.error(f"图片文件在所有尝试的路径中都不存在")
                    logger.error(f"尝试过的路径: \n1. {full_path}\n2. {alt_full_path}\n3. {backend_full_path}\n4. {absolute_full_path}")
                    
                    # 尝试遍历上传目录，帮助调试
                    try:
                        # 记录多个可能的上传目录内容
                        for dir_path in ["uploads", "backend/uploads", os.path.join(project_root, "backend/uploads"), UPLOAD_DIR]:
                            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                                files = os.listdir(dir_path)
                                logger.info(f"目录 {dir_path} 中的文件: {files[:20] if len(files) > 20 else files}")
                            else:
                                logger.warning(f"目录不存在或不是目录: {dir_path}")
                    except Exception as dir_error:
                        logger.error(f"遍历目录时出错: {str(dir_error)}")
                    
                    # 使用模拟数据
                    result = generate_mock_correction_data(image.image_path)
                    logger.info("使用模拟批改数据替代（图片不存在）")
            except Exception as e:
                logger.error(f"处理图片时出错: {str(e)}")
                # 出错时使用模拟数据
                result = generate_mock_correction_data(image.image_path)
                logger.info("使用模拟批改数据替代（处理异常）")
            
            # 解析批改结果
            try:
                # 尝试解析结果为JSON
                correction_json = None
                try:
                    # 首先确保result不为None
                    if result is None:
                        logger.error("批改结果为None，无法解析")
                        
                        # 记录错误到专用日志
                        with open("ollama_correction.log", "a", encoding="utf-8") as f:
                            f.write(f"\n\n--- 批改结果为空 {get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')} ---\n")
                            f.write(f"错误信息: 批改结果为None，无法解析\n")
                            f.write(f"图片路径: {image.image_path}\n")
                            f.write(f"作业ID: {homework_id}, 页面: {image.page_number}\n")
                            f.write(f"模型提供商: {pattern_provider}, AI配置ID: {ai_config_id}\n")
                        
                        # 创建一个默认的批改结果
                        result = json.dumps({
                            "questions": [{"question_number": "1", "question_type": "错误", "question_content": "批改结果为空", "is_correct": False, "student_answer": "无法解析", "correct_answer": "请联系管理员", "analysis": "系统错误: 批改结果为空，详细信息已记录到日志", "reinforcement": "请尝试重新批改或检查ollama_correction.log"}],
                            "summary": {"total_questions": 1, "correct_count": 0, "accuracy": 0.0}
                        })
                        logger.info("创建了默认批改结果以替代None")
                    
                    # 记录API调用结果
                    logger.info("AI返回批改结果成功")
                    logger.debug(f"完整批改结果: {result}")
                    
                    # 尝试从结果中提取JSON部分
                    # 有时AI会返回带有额外文本的JSON
                    import re
                    json_match = re.search(r'(\{.*\})', result, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(1)
                        try:
                            correction_json = json.loads(json_str)
                            logger.info("成功从提取的JSON字符串解析数据")
                        except json.JSONDecodeError:
                            logger.warning(f"提取的JSON字符串解析失败: {json_str[:200]}...")
                            correction_json = None
                    
                    # 如果提取JSON失败，尝试直接解析整个结果
                    if correction_json is None:
                        try:
                            correction_json = json.loads(result)
                            logger.info("成功从完整结果解析JSON数据")
                        except json.JSONDecodeError as e:
                            logger.error(f"直接解析JSON失败: {str(e)}")
                            raise
                    
                except json.JSONDecodeError:
                    logger.warning("批改结果不是有效的JSON格式")
                    # 如果不是有效的JSON，尝试使用AI解析结果
                    try:
                        # 构建提示词，请求AI帮助解析结果
                        parse_prompt = f"""
                        请将以下文本解析为JSON格式，包含作业批改结果。
                        返回的JSON应该包含以下结构:
                        {{
                            "questions": [
                                {{
                                    "question_number": "题目编号",
                                    "question_type": "题目类型",
                                    "question_content": "题目内容",
                                    "student_answer": "学生答案",
                                    "is_correct": true/false,
                                    "correct_answer": "正确答案",
                                    "analysis": "错误分析",
                                    "reinforcement": "强化训练建议"
                                }}
                            ],
                            "summary": {{
                                "total_questions": 题目总数,
                                "correct_count": 正确题目数,
                                "accuracy": 正确率
                            }}
                        }}
                        
                        需要解析的文本:
                        {result}
                        
                        仅返回有效的JSON，不要包含任何其他文本。
                        """
                        
                        # 调用AI解析结果
                        logger.info("尝试使用AI解析批改结果")
                        parsed_result = await call_ai_api(parse_prompt, db)
                        
                        if parsed_result:
                            # 尝试从AI解析结果中提取JSON
                            json_match = re.search(r'(\{.*\})', parsed_result, re.DOTALL)
                            if json_match:
                                json_str = json_match.group(1)
                                try:
                                    correction_json = json.loads(json_str)
                                    logger.info("成功使用AI解析批改结果")
                                except:
                                    logger.warning("AI解析的JSON仍然无效")
                    except Exception as e:
                        logger.error(f"使用AI解析结果失败: {str(e)}")
                    
                    # 如果所有尝试都失败，创建一个简单的JSON结构
                    if correction_json is None:
                        correction_json = {
                            "raw_result": result,
                            "questions": [
                                {
                                    "question_number": "1",
                                    "question_type": "未知类型",
                                    "question_content": "无法解析题目内容",
                                    "student_answer": "无法解析学生答案",
                                    "is_correct": False,
                                    "correct_answer": "无法解析正确答案",
                                    "analysis": "AI返回的结果不是标准JSON格式，无法自动解析。原始结果:\n" + result[:500],
                                    "reinforcement": "请联系教师进行手动批改"
                                }
                            ],
                            "summary": {
                                "total_questions": 1,
                                "correct_count": 0,
                                "accuracy": 0
                            }
                        }
                    
                    result = json.dumps(correction_json)
                
                # 保存批改结果
                try:
                    # 先检查是否已存在此作业页面的批改结果
                    existing_correction = db.query(HomeworkCorrection).filter(
                        HomeworkCorrection.homework_id == homework_id,
                        HomeworkCorrection.page_number == image.page_number
                    ).first()
                    
                    if existing_correction:
                        logger.info(f"更新现有批改结果，作业ID: {homework_id}, 页面: {image.page_number}")
                        existing_correction.correction_data = result
                        existing_correction.updated_at = datetime.utcnow()
                    else:
                        logger.info(f"创建新的批改结果，作业ID: {homework_id}, 页面: {image.page_number}")
                        correction = HomeworkCorrection(
                            homework_id=homework_id,
                            page_number=image.page_number,
                            correction_data=result
                        )
                        db.add(correction)
                    
                    # 立即提交，确保批改结果保存到数据库
                    db.commit()
                    logger.info(f"批改结果已保存到数据库，作业ID: {homework_id}, 页面: {image.page_number}")
                except Exception as e:
                    logger.error(f"保存批改结果到数据库失败: {str(e)}")
                    logger.error(traceback.format_exc())
                    # 回滚事务
                    db.rollback()
                    # 重试一次
                    try:
                        logger.info("尝试重新保存批改结果")
                        correction = HomeworkCorrection(
                            homework_id=homework_id,
                            page_number=image.page_number,
                            correction_data=result
                        )
                        db.add(correction)
                        db.commit()
                        logger.info("重试保存批改结果成功")
                    except Exception as retry_error:
                        logger.error(f"重试保存批改结果失败: {str(retry_error)}")
                        logger.error(traceback.format_exc())
                
                # 解析批改结果，提取分数和错题
                if correction_json:
                    # 提取总体统计信息
                    summary = correction_json.get("summary", {})
                    page_total_questions = summary.get("total_questions", 0)
                    page_correct_count = summary.get("correct_count", 0)
                    page_accuracy = summary.get("accuracy", 0)
                    
                    # 验证AI返回的数据是否一致
                    # 计算实际的正确题目数量
                    questions = correction_json.get("questions", [])
                    actual_total = len(questions)
                    actual_correct = sum(1 for q in questions if q.get("is_correct", False))
                    
                    # 记录原始数据和实际计算结果的差异
                    if actual_total != page_total_questions or actual_correct != page_correct_count:
                        logger.warning(f"AI返回的批改统计数据与实际不符: 返回总数={page_total_questions}, 实际总数={actual_total}, 返回正确数={page_correct_count}, 实际正确数={actual_correct}")
                        # 使用实际计算的数值替代AI返回的数值
                        page_total_questions = actual_total
                        page_correct_count = actual_correct
                        # 更新summary中的值
                        summary["total_questions"] = actual_total
                        summary["correct_count"] = actual_correct
                        if actual_total > 0:
                            summary["accuracy"] = actual_correct / actual_total
                        else:
                            summary["accuracy"] = 0.0
                        # 更新correction_json中的summary
                        correction_json["summary"] = summary
                        # 重新序列化为JSON字符串
                        result = json.dumps(correction_json)
                        # 更新数据库中的correction_data
                        correction.correction_data = result
                        db.commit()
                        logger.info(f"已修正批改统计数据: 总数={actual_total}, 正确数={actual_correct}, 正确率={summary['accuracy']}")
                    
                    total_questions += page_total_questions
                    total_score += page_correct_count
                    
                    # 提取错题
                    for q in questions:
                        if not q.get("is_correct", True):
                            # 创建错题记录
                            wrong_question = WrongQuestion(
                                homework_id=homework_id,
                                student_id=homework.student_id,
                                question_type=q.get("question_type", "未知类型"),
                                question_content=q.get("question_content", ""),
                                correct_answer=q.get("correct_answer", ""),
                                wrong_answer=q.get("student_answer", ""),
                                analysis=q.get("analysis", "")
                            )
                            db.add(wrong_question)
                            wrong_questions.append(wrong_question)
                
                db.commit()
                
            except Exception as e:
                logger.error(f"解析批改结果失败: {str(e)}")
                logger.error(traceback.format_exc())
        
        # 更新作业状态和分数
        homework.status = "graded"
        homework.graded_at = get_beijing_time()
        
        # 计算真实的分数和正确率
        if total_questions > 0:
            homework.accuracy = total_score / total_questions
            homework.score = homework.accuracy * 100  # 将正确率转换为百分制分数
        else:
            # 如果没有检测到题目，设置默认值
            homework.accuracy = 0.0
            homework.score = 0.0
        
        db.commit()
        
        # 跳过生成强化练习，提高速度
        # await generate_reinforcement_exercises(homework_id, db)
        
        # 自动生成批注图片
        logger.info(f"开始自动生成作业 {homework_id} 的批注图片")
        try:
            # 获取所有作业图片
            images = db.query(HomeworkImage).filter(HomeworkImage.homework_id == homework_id).all()
            
            # 获取所有批改结果
            corrections = db.query(HomeworkCorrection).filter(
                HomeworkCorrection.homework_id == homework_id
            ).all()
            
            if images and corrections:
                # 删除现有的批注图片（如果有）
                existing_annotated_images = db.query(HomeworkAnnotatedImage).filter(
                    HomeworkAnnotatedImage.homework_id == homework_id
                ).all()
                
                for annotated_image in existing_annotated_images:
                    # 尝试删除文件
                    try:
                        if annotated_image.image_path.startswith("/uploads/"):
                            file_path = annotated_image.image_path[9:]  # 去掉"/uploads/"前缀
                            full_path = os.path.join(UPLOAD_DIR, file_path)
                            if os.path.exists(full_path):
                                os.remove(full_path)
                                logger.info(f"删除旧的批注图片文件: {full_path}")
                    except Exception as e:
                        logger.error(f"删除旧的批注图片文件失败: {str(e)}")
                    
                    # 从数据库中删除记录
                    db.delete(annotated_image)
                
                db.commit()
                logger.info(f"已删除 {len(existing_annotated_images)} 个旧的批注图片记录")
                
                # 为每个图片生成批注
                generated_count = 0
                for image in images:
                    # 查找对应的批改结果
                    correction = next((c for c in corrections if c.page_number == image.page_number), None)
                    if not correction:
                        logger.warning(f"作业 {homework_id} 第 {image.page_number} 页没有对应的批改结果")
                        continue
                    
                    # 生成批注图片
                    annotated_image_path = await generate_annotated_image(
                        homework_id=homework_id,
                        image_id=image.id,
                        page_number=image.page_number,
                        original_image_path=image.image_path,
                        correction_data=correction.correction_data,
                        db=db
                    )
                    
                    if annotated_image_path:
                        generated_count += 1
                        logger.info(f"成功生成第 {image.page_number} 页批注图片: {annotated_image_path}")
                
                logger.info(f"作业 {homework_id} 自动生成批注图片完成，共生成 {generated_count} 个批注图片")
            else:
                logger.warning(f"作业 {homework_id} 没有图片或批改结果，跳过批注图片生成")
                
        except Exception as e:
            logger.error(f"自动生成批注图片失败: {str(e)}")
            logger.error(traceback.format_exc())
        
    except Exception as e:
        logger.error(f"处理作业失败: {str(e)}")
        logger.error(traceback.format_exc())
        
        # 更新作业状态为错误
        try:
            homework = db.query(Homework).filter(Homework.id == homework_id).first()
            if homework:
                homework.status = "error"
                db.commit()
        except:
            pass

async def generate_reinforcement_exercises(homework_id: int, db: Session):
    """为错题生成强化训练题目"""
    # 获取该作业的所有错题
    wrong_questions = db.query(WrongQuestion).filter(
        WrongQuestion.homework_id == homework_id
    ).all()
    
    if not wrong_questions:
        return
    
    from ..models.homework import ReinforcementExercise
    
    # 为每个错题生成强化训练题
    for wrong_question in wrong_questions:
        # 构建提示
        prompt = f"""
        基于以下错题，请生成3个相关的强化训练题目：
        
        错题内容：{wrong_question.question_content}
        正确答案：{wrong_question.correct_answer}
        错误分析：{wrong_question.analysis}
        
        请生成与这个概念相关但不同的题目，帮助学生加深理解。
        
        请以JSON格式返回结果，格式如下：
        {{
            "exercises": [
                {{
                    "content": "题目内容",
                    "answer": "答案",
                    "analysis": "答案解析"
                }}
            ]
        }}
        """
        
        # 调用AI API生成训练题
        result = await call_ai_api(prompt)
        if not result:
            continue
        
        try:
            # 解析JSON结果
            exercises = json.loads(result)
            
            # 保存强化训练题
            if "exercises" in exercises:
                for ex in exercises["exercises"]:
                    exercise = ReinforcementExercise(
                        wrong_question_id=wrong_question.id,
                        student_id=wrong_question.student_id,
                        exercise_content=ex.get("content", ""),
                        answer=ex.get("answer", ""),
                        analysis=ex.get("analysis", "")
                    )
                    db.add(exercise)
            
            db.commit()
            
        except json.JSONDecodeError:
            logger.error("AI返回的强化训练题格式不是有效的JSON")
            continue

def generate_mock_correction_data(image_path: str) -> str:
    """生成模拟批改数据，用于测试或当AI服务不可用时"""
    logger.info(f"生成模拟批改数据，图片路径: {image_path}")
    
    # 从图片路径中提取文件名，可能包含学生信息
    filename = os.path.basename(image_path)
    
    # 随机生成题目数量
    question_count = random.randint(3, 8)
    correct_count = random.randint(1, question_count)
    accuracy = correct_count / question_count if question_count > 0 else 0
    
    # 生成模拟题目
    questions = []
    for i in range(question_count):
        # 随机决定是否正确
        is_correct = i < correct_count
        
        # 生成随机的答案位置（相对坐标，0-1之间）
        # 根据题号生成垂直位置，均匀分布在图片上
        y_position = (i + 1) / (question_count + 1)  # 垂直位置
        x_position = random.uniform(0.3, 0.6)  # 水平位置，在图片中间区域
        width = random.uniform(0.05, 0.15)  # 答案区域宽度
        height = random.uniform(0.02, 0.05)  # 答案区域高度
        
        question = {
            "question_number": str(i + 1),
            "question_type": random.choice(["选择题", "填空题", "计算题", "解答题"]),
            "question_content": f"这是第{i+1}题的内容，模拟生成的题目。",
            "student_answer": f"学生的答案 {i+1}",
            "is_correct": is_correct,
            "correct_answer": f"正确答案 {i+1}",
            "analysis": "" if is_correct else f"这道题学生的解题思路有误，应该...",
            "reinforcement": f"建议多练习此类题目，掌握{random.choice(['解题技巧', '基本概念', '公式应用'])}",
            "answer_position": {
                "x": x_position,
                "y": y_position,
                "width": width,
                "height": height
            }
        }
        questions.append(question)
    
    # 构建完整的批改数据
    correction_data = {
        "questions": questions,
        "summary": {
            "total_questions": question_count,
            "correct_count": correct_count,
            "accuracy": accuracy
        }
    }
    
    # 转换为JSON字符串
    return json.dumps(correction_data, ensure_ascii=False)

def check_image_file(file_path: str) -> bool:
    """检查图片文件是否存在并且可以被正确读取
    
    参数:
        file_path: 图片文件路径
        
    返回:
        bool: 图片是否有效
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"图片文件不存在: {file_path}")
            return False
        
        # 尝试读取图片
        try:
            img = Image.open(file_path)
            img.verify()  # 验证图片完整性
            logger.info(f"图片有效: {file_path}, 格式: {img.format}, 大小: {img.size}")
            return True
        except Exception as e:
            logger.error(f"图片无效或损坏: {file_path}, 错误: {str(e)}")
            return False
    except Exception as e:
        logger.error(f"检查图片时出错: {str(e)}")
        return False

async def test_ai_service(db: Session = None) -> Optional[str]:
    """测试AI服务是否正常工作
    
    参数:
        db: 数据库会话
        
    返回:
        str: AI服务返回的结果
    """
    try:
        # 获取配置
        config = await get_active_ai_config(db)
        provider = config["provider"]
        model_id = config["model_id"]
        
        logger.info(f"测试AI服务: 提供商={provider}, 模型ID={model_id}")
        
        # 构建简单的测试提示词
        test_prompt = "请回答：1+1=?"
        
        # 调用AI API
        result = await call_ai_api(test_prompt, db)
        
        if result:
            logger.info(f"AI服务测试成功，返回: {result}")
            return result
        else:
            logger.error("AI服务测试失败，返回空结果")
            return None
    except Exception as e:
        logger.error(f"测试AI服务时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return None 

async def call_ollama_deepseek_api(prompt: str, model: str = "deepseek-r1:8b", temperature: float = 0.7):
    """调用Ollama API"""
    url = "http://localhost:11434/api/generate"
    
    payload = {
        "model": model,
        "prompt": prompt,
        "stream": False,
        "temperature": temperature,
        "max_tokens": 800  # 限制输出长度
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=payload)
            if response.status_code == 200:
                result = response.json()
                logger.info(f"Ollama API响应: {result.get('response')[:100]}...")
                return result
            else:
                error_text = response.text
                logger.error(f"Ollama API错误: {response.status_code} - {error_text}")
                raise Exception(f"Ollama API错误: {response.status_code}")
    except Exception as e:
        logger.error(f"调用Ollama API异常: {str(e)}")
        raise

async def analyze_error_with_deepseek(question_content, student_answer, correct_answer, question_type="未知类型"):
    """使用deepseek-r1:8b模型分析学生答案的错误原因和生成强化建议"""
    try:
        logger.info(f"开始分析错误原因，问题内容: {question_content[:50]}...")
        
        # 构建提示词
        prompt = f"""分析以下学生的错误答案，并提供详细的错误分析和针对性的强化建议。
        
问题类型: {question_type}
问题内容: {question_content}
学生答案: {student_answer}
正确答案: {correct_answer}

请提供以下内容:
1. 错误分析: 详细分析学生答案中的错误，包括概念理解错误、计算错误、方法应用错误等。
2. 强化建议: 针对错误类型，提供具体的学习建议，包括需要复习的知识点、推荐的练习方法等。

输出格式:
{{
  "analysis": "详细的错误分析...",
  "reinforcement": "针对性的强化建议..."
}}
"""
        
        # 调用deepseek模型
        response = await call_ollama_api("deepseek-r1:8b", prompt)
        
        # 尝试解析JSON响应
        try:
            # 查找JSON内容
            json_content = extract_json_from_text(response)
            if json_content:
                result = json.loads(json_content)
                logger.info(f"成功解析JSON响应: {result.keys()}")
                return result
        except Exception as json_error:
            logger.warning(f"无法解析JSON响应: {str(json_error)}，将返回原始文本")
        
        # 如果无法解析为JSON，返回原始响应
        return response
    
    except Exception as e:
        logger.error(f"分析错误原因失败: {str(e)}")
        raise Exception(f"分析错误原因失败: {str(e)}")

def analyze_error(student_answer, correct_answer, question_content, db: Session = None):
    """分析学生答案的错误原因"""
    try:
        # 获取错题分析的AI配置
        if db:
            config = get_ai_config_by_usage(db, "error_analysis")
            model_id = config.model_id or "deepseek-r1:8b"
        else:
            model_id = "deepseek-r1:8b"  # 后备方案
        # 构建提示词
        prompt = f"""你是一位经验丰富的教师，正在修改学生作业的批改结果。请针对以下题目提供专业的错误分析。
        
问题内容: {question_content}
学生答案: {student_answer}
正确答案: {correct_answer}

请直接输出一段简洁的错误分析文本，适合教师在批改系统中使用。用中文分析，内容必须包括：
1. 试题解析：解释题目的含义和考察点（如果是英语题目，先翻译再分析）
2. 正确答案解析：说明为什么正确答案是对的，解题思路和方法
3. 学生错误分析：具体指出学生答案中的错误点
4. 错误原因：分析学生可能存在的知识点误解或解题方法问题

重要提示：这是面向教师的批改系统，不是直接面向学生的批改，称呼为第二人称"你"或"同学"。你的分析将被教师用来修改批改结果。
不要输出任何思考过程，直接给出最终的专业分析结果。不要使用"我认为"、"我的分析"等字眼。
字数控制在150字以内，简明扼要。
严禁使用<think>标签或任何形式的思考过程标记。
"""
        
        # 同步调用AI模型
        response = call_ollama_api_sync(model_id, prompt)
        
        # 尝试提取最终结果，去除思考过程
        result = response.strip()
        
        # 移除<think>标签及其内容
        think_pattern = r'<think>.*?</think>'
        result = re.sub(think_pattern, '', result, flags=re.DOTALL)
        
        # 移除没有结束标签的<think>开始的内容
        if '<think>' in result.lower():
            parts = result.lower().split('<think>')
            if len(parts) > 1:
                result = parts[0].strip()
        
        # 移除常见的思考过程标记和前缀
        prefixes_to_remove = [
            "好的，", "我将", "下面是", "以下是", "我会", "我来", "我的分析", "错误分析", "分析如下", "最终分析",
            "首先，", "首先", "根据题目", "根据问题", "根据所给", "分析：", "分析:", "回答：", "回答:", 
            "这道题", "这个问题", "这题", "对于这道题", "对于这个问题", "让我", "我需要", "我应该",
            "我想", "我可以", "我们来", "我们需要", "我们可以", "我们应该", "现在我", "接下来",
            "基于提供的", "根据提供的", "针对这个", "对于这个", "对这个", "关于这个", "嗯，", "那么，"
        ]
        
        for prefix in prefixes_to_remove:
            if result.lower().startswith(prefix.lower()):
                result = result[len(prefix):].strip()
        
        # 移除常见的思考过程段落和标记
        thinking_markers = [
            "让我分析", "我需要分析", "我会按照要求", "我将按照", "我将分析", "我来分析", 
            "让我思考", "让我看看", "让我们来分析", "让我们来看", "分析如下", "我想分析",
            "我会提供", "我将提供", "以下是我的分析", "我认为", "我觉得", "我的看法是",
            "我的理解是", "我的分析是", "我的想法是", "我会这样分析", "我要分析",
            "首先，我会", "首先，我将", "首先，我需要", "首先，我应该", "首先，让我",
            "我将按照以下步骤", "我会按照以下步骤", "我需要按照以下步骤", "我应该按照以下步骤",
            "用户让我模拟", "用户要求我", "嗯，用户让我", "嗯，用户要求我"
        ]
        
        for marker in thinking_markers:
            if marker.lower() in result.lower():
                # 找到所有出现的位置
                start_idx = result.lower().find(marker.lower())
                if start_idx >= 0:
                    # 查找这段思考过程后面的实际内容
                    # 通常思考过程后面会有一个分段或冒号
                    end_markers = ["：", ":", "。", "\n", "\r"]
                    end_idx = -1
                    for end_marker in end_markers:
                        pos = result.find(end_marker, start_idx + len(marker))
                        if pos >= 0 and (end_idx == -1 or pos < end_idx):
                            end_idx = pos + 1
                    
                    if end_idx > 0:
                        # 移除这段思考过程
                        result = result[:start_idx] + result[end_idx:]
                        result = result.strip()
        
        # 移除结尾的总结性语句
        endings_to_remove = [
            "希望这个分析有所帮助", "希望这个分析能够帮助", "希望这个分析对", "希望这对", "希望能帮助",
            "以上是", "这是我的分析", "这就是我的分析", "以上就是分析", "这是对", "这就是对",
            "以上就是", "这是我对", "这就是我对", "希望我的分析", "希望我的回答", "希望以上分析",
            "这些就是", "以上就是我的", "这就是我给出的", "这是针对", "这就是针对"
        ]
        
        for ending in endings_to_remove:
            if ending.lower() in result.lower():
                parts = result.lower().split(ending.lower())
                if len(parts) > 1:
                    # 保留第一部分
                    end_idx = result.lower().find(ending.lower())
                    result = result[:end_idx].strip()
        
        # 检查是否包含"我"、"我的"、"我们"等字样，如果包含则进一步处理
        personal_references = ["我认为", "我的分析", "我觉得", "我们可以看到", "我们可以发现", "我们需要", "我们应该", "我想", "我们"]
        for ref in personal_references:
            if ref.lower() in result.lower():
                # 尝试重新组织文本，去除个人引用
                sentences = result.split("。")
                filtered_sentences = []
                for sentence in sentences:
                    if not any(ref.lower() in sentence.lower() for ref in personal_references):
                        filtered_sentences.append(sentence)
                
                if filtered_sentences:
                    result = "。".join(filtered_sentences)
                    if not result.endswith("。") and result:
                        result += "。"
        
        # 如果结果太长，尝试截取关键部分
        if len(result) > 250:
            sentences = result.split('。')
            if len(sentences) > 5:
                result = '。'.join(sentences[:5]) + '。'
        
        return result
    
    except Exception as e:
        logger.error(f"分析错误原因失败: {str(e)}")
        raise Exception(f"分析错误原因失败: {str(e)}")

def generate_reinforcement_exercises(question_content, correct_answer, wrong_answer, analysis, count=1, db: Session = None):
    """生成强化训练题目"""
    try:
        # 获取强化练习生成的AI配置
        if db:
            config = get_ai_config_by_usage(db, "reinforcement_exercise")
            model_id = config.model_id or "deepseek-r1:8b"
        else:
            model_id = "deepseek-r1:8b"  # 后备方案
        # 构建提示词
        prompt = f"""你是一位经验丰富的教师，请基于以下错题生成{count}道相关的强化训练题目。

原始错题: {question_content}
正确答案: {correct_answer}
学生错误答案: {wrong_answer}
错误分析: {analysis}

请生成与这个概念相关但不同的题目，帮助学生加深理解和巩固知识点。

要求：
1. 题目内容要与原题相关，但不能完全相同
2. 难度适中，适合强化训练
3. 答案要准确
4. 提供详细的解析说明

请以JSON格式返回结果：
[
  {{
    "content": "题目内容（完整的题目描述）",
    "answer": "标准答案",
    "analysis": "详细的答案解析"
  }}
]

注意：只返回JSON格式的数据，不要包含其他文字说明。"""
        
        # 同步调用AI模型
        response = call_ollama_api_sync(model_id, prompt)
        logger.info(f"AI原始返回内容: {response}")

        # 如果AI调用失败，返回模拟数据
        if not response or response.strip() == "":
            logger.warning("AI返回为空，使用模拟数据")
            return [{
                "content": f"根据原题'{question_content}'，请完成以下练习：\n\n请填入正确的词汇：The student should _____ the difference between prepositions and verbs.",
                "answer": "understand",
                "analysis": "这道题考查词汇理解能力。需要根据语境选择合适的动词形式。"
            }]
        
        # 移除<think>标签及其内容
        think_pattern = r'<think>.*?</think>'
        response = re.sub(think_pattern, '', response, flags=re.DOTALL)
        
        # 移除没有结束标签的<think>开始的内容
        if '<think>' in response.lower():
            parts = response.lower().split('<think>')
            if len(parts) > 1:
                response = parts[0].strip()
        
        # 直接尝试手动提取字段，跳过复杂的JSON解析
        logger.info("开始手动提取练习字段")
        logger.info(f"AI返回内容前200字符: {response[:200]}")

        # 使用更宽松的正则表达式提取
        content_match = re.search(r'"content":\s*"([^"]*(?:\\.[^"]*)*)"', response, re.DOTALL)
        answer_match = re.search(r'"answer":\s*"([^"]*(?:\\.[^"]*)*)"', response, re.DOTALL)
        analysis_match = re.search(r'"analysis":\s*"([^"]*(?:\\.[^"]*)*)"', response, re.DOTALL)

        if content_match and answer_match:
            content = content_match.group(1).strip()
            answer = answer_match.group(1).strip()
            analysis = analysis_match.group(1).strip() if analysis_match else "基于错题生成的强化训练，帮助巩固相关知识点。"

            # 清理转义字符
            content = content.replace('\\"', '"').replace('\\n', '\n')
            answer = answer.replace('\\"', '"').replace('\\n', '\n')
            analysis = analysis.replace('\\"', '"').replace('\\n', '\n')

            logger.info(f"手动提取成功 - 内容: {content[:50]}...")
            logger.info(f"手动提取成功 - 答案: {answer}")

            return [{
                "content": content,
                "answer": answer,
                "analysis": analysis
            }]
        else:
            logger.warning("手动提取也失败，使用模拟数据")
            logger.warning(f"content_match: {bool(content_match)}")
            logger.warning(f"answer_match: {bool(answer_match)}")
            if content_match:
                logger.warning(f"找到的content: {content_match.group(1)[:50]}...")
            if answer_match:
                logger.warning(f"找到的answer: {answer_match.group(1)}")

        # 尝试解析JSON响应（作为备用方案）
        try:
            # 查找JSON内容
            json_content = extract_json_from_text(response)
            if json_content:
                logger.info(f"提取到JSON内容: {json_content[:200]}...")
                exercises = json.loads(json_content)
                if isinstance(exercises, list) and len(exercises) > 0:
                    logger.info(f"成功生成{len(exercises)}道强化训练题目")
                    return exercises

            # 使用正则表达式提取content和answer
            content_match = re.search(r'content:\s*([^,\n]+)', response, re.IGNORECASE)
            answer_match = re.search(r'answer:\s*([^,\n]+)', response, re.IGNORECASE)

            if content_match and answer_match:
                content = content_match.group(1).strip()
                answer = answer_match.group(1).strip()

                # 清理内容
                content = content.replace('\\', '').replace('"', '').strip()
                answer = answer.replace('\\', '').replace('"', '').strip()

                logger.info(f"从文本中提取到练习内容: {content[:50]}...")
                logger.info(f"从文本中提取到答案: {answer}")

                return [{
                    "content": content,
                    "answer": answer,
                    "analysis": "基于错题生成的强化训练，帮助巩固相关知识点。"
                }]

            # 如果都失败了，返回模拟数据
            logger.warning("无法从AI返回中提取有效内容，使用模拟数据")
            return [{
                "content": f"根据原题'{question_content}'，请完成以下练习：\n\n请填入正确的词汇：The student should _____ the difference between prepositions and verbs.",
                "answer": "understand",
                "analysis": "这道题考查词汇理解能力。需要根据语境选择合适的动词形式。"
            }]

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            logger.error(f"原始响应: {response}")

            # 尝试从文本中提取内容作为备用方案
            exercises = []
            # 暂时跳过这个逻辑，直接返回空列表
            return exercises
        except Exception as json_error:
            logger.warning(f"无法解析JSON响应: {str(json_error)}，将尝试构造默认格式")
        
        # 如果无法解析为JSON，尝试提取纯文本建议
        try:
            # 清理响应文本
            clean_text = response.strip()
            
            # 移除JSON格式和引号
            clean_text = re.sub(r'^\s*\[\s*{', '', clean_text)
            clean_text = re.sub(r'}\s*\]\s*$', '', clean_text)
            clean_text = re.sub(r'"analysis"\s*:\s*"([^"]*)"', r'\1', clean_text)
            clean_text = re.sub(r'"content"\s*:\s*"[^"]*",\s*"answer"\s*:\s*"[^"]*",\s*', '', clean_text)
            
            # 如果还有引号和大括号，尝试进一步清理
            clean_text = clean_text.replace('"', '').replace('{', '').replace('}', '')
            
            # 移除"建议说明"及之前的所有内容
            if "建议说明" in clean_text:
                parts = clean_text.split("建议说明")
                if len(parts) > 1:
                    # 只保留"建议说明"后面的内容
                    clean_text = parts[1].strip()
                    # 如果以冒号或其他分隔符开头，去除它
                    if clean_text.startswith(":") or clean_text.startswith("："):
                        clean_text = clean_text[1:].strip()
            
            # 移除常见的前缀
            prefixes = [
                "强化建议：", "强化建议:", "建议：", "建议:", "analysis:", "analysis：", 
                "针对这道题", "针对此题", "对于这道题", "对于此题", "对这道题",
                "教学建议：", "教学建议:", "针对性建议：", "针对性建议:"
            ]
            for prefix in prefixes:
                if clean_text.lower().startswith(prefix.lower()):
                    clean_text = clean_text[len(prefix):].strip()
            
            # 移除思考过程和引导语
            thinking_markers = [
                "我建议", "我的建议是", "我的强化建议是", "建议学生", "我认为", "我觉得",
                "以下是我的建议", "以下是强化建议", "我会建议", "我想建议", "我要建议",
                "作为教师", "作为老师", "我们可以", "我们应该", "我们需要", "我们建议",
                "首先", "其次", "最后", "另外", "此外", "总的来说", "综上所述",
                "用户让我", "用户要求我", "嗯，用户让我", "嗯，用户要求我"
            ]
            
            for marker in thinking_markers:
                if marker.lower() in clean_text.lower():
                    # 找到所有出现的位置
                    start_idx = clean_text.lower().find(marker.lower())
                    if start_idx >= 0:
                        # 查找这段思考过程后面的实际内容
                        end_markers = ["：", ":", "。", "\n", "\r"]
                        end_idx = -1
                        for end_marker in end_markers:
                            pos = clean_text.find(end_marker, start_idx + len(marker))
                            if pos >= 0 and (end_idx == -1 or pos < end_idx):
                                end_idx = pos + 1
                        
                        if end_idx > 0:
                            # 移除这段思考过程
                            clean_text = clean_text[:start_idx] + clean_text[end_idx:]
                            clean_text = clean_text.strip()
            
            # 移除结尾的总结性语句
            endings = [
                "希望这个建议有所帮助", "希望这个建议能够帮助", "希望这些建议能", 
                "以上是", "这是我的建议", "这就是我的建议", "以上就是建议",
                "希望对教师有所帮助", "希望能够帮助教师", "希望能帮助教师"
            ]
            for ending in endings:
                if ending.lower() in clean_text.lower():
                    end_idx = clean_text.lower().find(ending.lower())
                    if end_idx > 0:
                        clean_text = clean_text[:end_idx].strip()
            
            # 检查是否包含"我"、"我的"等字样，如果包含则进一步处理
            personal_refs = ["我", "我的", "我们", "我们的"]
            if any(ref in clean_text for ref in personal_refs):
                sentences = clean_text.split("。")
                filtered_sentences = []
                for sentence in sentences:
                    if not any(ref in sentence for ref in personal_refs):
                        filtered_sentences.append(sentence)
                
                if filtered_sentences:
                    clean_text = "。".join(filtered_sentences)
                    if not clean_text.endswith("。") and clean_text:
                        clean_text += "。"
            
            if clean_text and clean_text != "请参考原题的解析。":
                return [{
                    "content": "",
                    "answer": "",
                    "analysis": clean_text
                }]
        except Exception as text_error:
            logger.warning(f"提取纯文本建议失败: {str(text_error)}")
        
        # 如果所有尝试都失败，构造默认格式，但提供更有用的建议
        return [{
            "content": "",
            "answer": "",
            "analysis": "建议针对该题型进行专项练习，重点复习相关概念和解题方法，注意解题步骤的完整性和准确性。可以通过对比正确答案与错误答案的差异，理解解题思路中的关键环节。"
        }]
    
    except Exception as e:
        logger.error(f"生成强化训练题目失败: {str(e)}")
        raise Exception(f"生成强化训练题目失败: {str(e)}")

def evaluate_exercise_answer(exercise_content, correct_answer, student_answer, db: Session = None):
    """评估学生的练习答案"""
    try:
        # 获取强化练习生成的AI配置（评估也使用同样的配置）
        if db:
            config = get_ai_config_by_usage(db, "reinforcement_exercise")
            model_id = config.model_id or "deepseek-r1:8b"
        else:
            model_id = "deepseek-r1:8b"  # 后备方案
        # 构建提示词
        prompt = f"""评估学生对以下练习题的回答是否正确。

练习题: {exercise_content}
正确答案: {correct_answer}
学生答案: {student_answer}

评估标准：
1. 如果是多个填空题，只要大部分答案正确（60%以上）就判断为正确
2. 允许拼写的微小差异和同义词
3. 重点关注学生是否理解了核心概念
4. 对于部分正确的答案，给予鼓励性的反馈

请评估学生答案的正确性，并提供详细的解析。

输出格式:
{{
  "is_correct": true或false,
  "explanation": "详细解析，包括正确的部分和需要改进的部分..."
}}
"""
        
        # 同步调用AI模型
        response = call_ollama_api_sync(model_id, prompt)
        
        # 尝试解析JSON响应
        try:
            # 查找JSON内容
            json_content = extract_json_from_text(response)
            if json_content:
                result = json.loads(json_content)
                logger.info(f"成功解析评估结果: {result}")
                return result
        except Exception as json_error:
            logger.warning(f"无法解析JSON响应: {str(json_error)}，将构造默认格式")
        
        # 如果无法解析为JSON，构造默认格式
        # 简单比较答案是否相同（仅作为后备方案）
        is_correct = student_answer.strip().lower() == correct_answer.strip().lower()
        return {
            "is_correct": is_correct,
            "explanation": response
        }
    
    except Exception as e:
        logger.error(f"评估练习答案失败: {str(e)}")
        raise Exception(f"评估练习答案失败: {str(e)}")

def extract_json_from_text(text):
    """从文本中提取JSON内容"""
    try:
        # 查找第一个{和最后一个}之间的内容
        start_idx = text.find('{')
        end_idx = text.rfind('}')
        
        # 如果找到了JSON对象
        if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
            json_str = text[start_idx:end_idx+1]
            return json_str
        
        # 查找第一个[和最后一个]之间的内容
        start_idx = text.find('[')
        end_idx = text.rfind(']')
        
        # 如果找到了JSON数组
        if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
            json_str = text[start_idx:end_idx+1]
            return json_str
        
        return None
    except Exception:
        return None

def call_ollama_api_sync(model, prompt):
    """同步调用Ollama API"""
    try:
        logger.info(f"同步调用Ollama API，模型: {model}")
        
        # Ollama API地址
        ollama_api_url = "http://localhost:11434/api/generate"
        
        # 构建请求体
        request_body = {
            "model": model,
            "prompt": prompt,
            "stream": False
        }
        
        # 发送请求
        try:
            import httpx
            with httpx.Client(timeout=60.0) as client:
                response = client.post(ollama_api_url, json=request_body)
                response.raise_for_status()
                
                # 解析响应
                result = response.json()
                return result.get("response", "")
        except ImportError:
            # 如果没有httpx，使用requests
            import requests
            response = requests.post(ollama_api_url, json=request_body, timeout=60)
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            return result.get("response", "")
    
    except Exception as e:
        logger.error(f"调用Ollama API失败: {str(e)}")
        raise Exception(f"调用Ollama API失败: {str(e)}")
async def generate_annotated_image(
    homework_id: int,
    image_id: int,
    page_number: int,
    original_image_path: str,
    correction_data: str,
    db: Session
) -> Optional[str]:
    """
    根据批改结果在原始图片上添加批注,并保存为新的图片
    """
    logger.info("🚨🚨🚨 进入generate_annotated_image函数 🚨🚨🚨")
    logger.info(f"开始为作业 {homework_id} 第{page_number}页生成批注图片")
    
    try:
        # 解析批改数据
        correction_json = json.loads(correction_data)
        logger.info("成功解析批改数据")
        
        # 获取图片的完整路径
        if original_image_path.startswith("/uploads/"):
            image_path = original_image_path[9:]  # 去掉"/uploads/"前缀
        else:
            image_path = os.path.basename(original_image_path)
        
        # 构建完整路径
        UPLOAD_DIR = "backend/uploads"
        full_path = os.path.join(UPLOAD_DIR, image_path)
        logger.info(f"📁 图片路径: {full_path}")
        
        # 检查文件是否存在
        if not os.path.exists(full_path):
            logger.error(f"原始图片不存在: {full_path}")
            
            # 尝试修复路径 - 方法2：使用反斜杠替换正斜杠
            alt_file_path = image_path.replace('/', '\\')
            alt_full_path = os.path.join(UPLOAD_DIR, alt_file_path)
            
            if os.path.exists(alt_full_path):
                logger.info(f"找到替代路径: {alt_full_path}")
                full_path = alt_full_path
            else:
                # 尝试修复路径 - 方法3：仅使用文件名
                filename = os.path.basename(image_path)
                filename_full_path = os.path.join(UPLOAD_DIR, filename)
                
                if os.path.exists(filename_full_path):
                    logger.info(f"找到替代路径: {filename_full_path}")
                    full_path = filename_full_path
                else:
                    logger.error(f"无法找到图片文件: {original_image_path}")
                    return None
        
        # 打开原始图片
        img = Image.open(full_path)
        draw = ImageDraw.Draw(img)
        logger.info("成功打开原始图片")
        
        # 处理批改结果中的问题
        if "questions" in correction_json:
            questions = correction_json["questions"]
            img_width, img_height = img.size
            logger.info(f"📊 开始处理{len(questions)}个问题,图片尺寸: {img_width}x{img_height}")
            # 添加批注的逻辑
            font_paths = [
                "C:/Windows/Fonts/simsun.ttc",  # 宋体
                "C:/Windows/Fonts/simhei.ttf",  # 黑体
                "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
                "C:/Windows/Fonts/calibri.ttf",
                "C:/Windows/Fonts/tahoma.ttf",
                "/System/Library/Fonts/PingFang.ttc",  # macOS 苹方字体
                "/System/Library/Fonts/STHeiti Light.ttc",  # macOS 华文黑体
                "/System/Library/Fonts/Supplemental/Arial.ttf"
            ]
            font = None
            for path in font_paths:
                if os.path.exists(path):
                    try:
                        font = ImageFont.truetype(path, 48)  # 放大字体到48
                        logger.info(f"成功加载字体: {path}")
                        break
                    except Exception as e:
                        logger.warning(f"加载字体失败 {path}: {str(e)}")
                        continue
            
            if font is None:
                try:
                    font = ImageFont.truetype("arial", 48)
                    logger.info("使用默认arial字体")
                except Exception as e:
                    logger.warning(f"加载arial字体失败: {str(e)},使用默认字体")
                    font = ImageFont.load_default()
            
            # 为错误原因创建小号字体
            small_font = None
            for path in font_paths:
                if os.path.exists(path):
                    try:
                        small_font = ImageFont.truetype(path, 24)  # 缩小字体为原来的一半
                        logger.info(f"成功加载小号字体: {path}")
                        break
                    except Exception as e:
                        logger.warning(f"加载小号字体失败 {path}: {str(e)}")
                        continue
            
            if small_font is None:
                try:
                    small_font = ImageFont.truetype("arial", 24)
                    logger.info("使用默认arial小号字体")
                except Exception as e:
                    logger.warning(f"加载arial小号字体失败: {str(e)},使用默认字体")
                    small_font = ImageFont.load_default()
            
            # 为错误原因创建更小号字体（12号）
            tiny_font = None
            for path in font_paths:
                if os.path.exists(path):
                    try:
                        tiny_font = ImageFont.truetype(path, 12)
                        logger.info(f"成功加载超小号字体: {path}")
                        break
                    except Exception as e:
                        logger.warning(f"加载超小号字体失败 {path}: {str(e)}")
                        continue
            if tiny_font is None:
                try:
                    tiny_font = ImageFont.truetype("arial", 12)
                    logger.info("使用默认arial超小号字体")
                except Exception as e:
                    logger.warning(f"加载arial超小号字体失败: {str(e)},使用默认字体")
                    tiny_font = ImageFont.load_default()
                    
        # 标注颜色定义
        colors = {
            "correct": (0, 200, 0),  # 绿色
            "incorrect": (255, 0, 0),  # 红色
            "info": (0, 0, 255),  # 蓝色
        }
        logger.info("颜色定义完成")

        # 处理批改结果中的问题
        if "questions" in correction_json:
            questions = correction_json["questions"]
            img_width, img_height = img.size
            logger.info(f"📊 开始处理{len(questions)}个问题,图片尺寸: {img_width}x{img_height}")
            
            for i, question in enumerate(questions):
                logger.info(f"🔍 处理第{i+1}个问题")
                
                # 优先使用AI批改结果中的坐标信息
                pos = None
                student_answer = question.get("student_answer", "")
                question_content = question.get("question_content", "")
                question_number = question.get("question_number", str(i+1))
                
                # 首先检查新添加的answer_position字段
                if "answer_position" in question and isinstance(question["answer_position"], dict):
                    position = question["answer_position"]
                    if "x" in position and "y" in position:
                        # 使用模型提供的精确坐标
                        x_rel = float(position["x"])
                        y_rel = float(position["y"])
                        # 将相对坐标转换为绝对像素坐标
                        x_abs = int(x_rel * img_width)
                        y_abs = int(y_rel * img_height)
                        pos = (x_abs, y_abs)
                        logger.info(f"📍 使用模型提供的answer_position精确坐标: {pos}, 相对坐标: ({x_rel}, {y_rel})")
                        
                        # 如果提供了宽度和高度信息，记录下来以便后续使用
                        width_rel = float(position.get("width", 0.1))
                        height_rel = float(position.get("height", 0.05))
                        width_abs = int(width_rel * img_width)
                        height_abs = int(height_rel * img_height)
                        logger.info(f"📐 答案区域大小: {width_abs}x{height_abs}像素, 相对大小: ({width_rel}, {height_rel})")
                
                # 如果没有answer_position，尝试从答案内容推断位置
                if not pos:
                    try:
                        # 使用题号估算位置 - 假设题目按顺序从上到下排列
                        try:
                            # 首先尝试从question_number中提取数字部分
                            q_num_str = question_number.strip()
                            # 移除非数字字符
                            q_num_str = ''.join(filter(str.isdigit, q_num_str))
                            
                            if q_num_str:
                                q_num = int(q_num_str)
                                # 确保题号与索引匹配（修复错位问题）
                                if q_num != i + 1:
                                    logger.info(f"⚠️ 题号不匹配: AI返回题号={q_num}, 当前索引={i+1}, 使用AI返回的题号")
                                
                                # 根据题号估算垂直位置 (1/6到5/6之间平均分布)
                                y_ratio = 1/6 + (q_num - 1) * 4/(6 * max(len(questions), 1))
                                y_pos = int(y_ratio * img_height)
                            else:
                                # 如果题号字符串中没有数字，使用索引估算
                                y_pos = int((i + 1) * img_height / (len(questions) + 1))
                        except:
                            # 如果题号不是数字，使用索引估算
                            y_pos = int((i + 1) * img_height / (len(questions) + 1))
                            logger.info(f"⚠️ 题号解析失败: {question_number}, 使用索引 {i+1} 估算位置")
                        
                        # 查看图片中是否有学生答案的位置
                        # 根据图片分析，学生答案通常在横线上或空白处
                        # 对于英语题目，通常在横线中间位置
                        x_pos = int(img_width * 0.45)  # 调整到更靠近答案的位置
                        
                        # 根据题目类型调整位置
                        question_type = question.get("question_type", "").lower()
                        
                        # 选择题特殊处理
                        if ("选择题" in question_type or "单选题" in question_type or "多选题" in question_type) and student_answer and len(student_answer) <= 2:
                            # 选择题答案通常是A、B、C、D，尝试根据选项调整水平位置
                            options = ["A", "B", "C", "D"]
                            answer_upper = student_answer.upper()
                            
                            # 处理单个选项
                            if answer_upper in options:
                                option_idx = options.index(answer_upper)
                                # 选项通常在中间位置，从左到右排列
                                x_pos = int(img_width * (0.3 + option_idx * 0.08))
                            # 处理多选题的情况 (如"AB", "AC"等)
                            elif len(answer_upper) == 2 and all(c in options for c in answer_upper):
                                # 取第一个选项的位置
                                option_idx = options.index(answer_upper[0])
                                x_pos = int(img_width * (0.3 + option_idx * 0.08))
                        
                        # 填空题特殊处理
                        elif "填空题" in question_type or student_answer.strip() not in ["A", "B", "C", "D"]:
                            # 填空题答案通常在横线上
                            # 根据图片分析，答案通常在横线中间位置
                            if student_answer:
                                # 调整到更靠近答案的位置
                                x_pos = int(img_width * 0.45)
                                # 确保标记在答案旁边，而不是上方或下方
                                # 对于英语试卷，通常答案在横线上
                                x_offset = min(len(student_answer) * 5, 30)  # 根据答案长度调整偏移
                                x_pos += x_offset
                        
                        # 计算题特殊处理
                        elif "计算题" in question_type:
                            # 计算题答案通常在右下方
                            x_pos = int(img_width * 0.5)
                            # 稍微向下调整
                            y_pos = min(y_pos + 20, img_height - 50)
                        
                        logger.info(f"📍 根据答案内容推断位置: ({x_pos}, {y_pos}), 题型: {question_type}")
                    except Exception as e:
                        logger.error(f"推断位置失败: {str(e)}")
                    
                    # 检查批改结果中的其他坐标信息字段
                    if "answer_location" in question and isinstance(question["answer_location"], dict):
                        loc = question["answer_location"]
                        if "x" in loc and "y" in loc:
                            pos = (int(loc["x"] * img_width), int(loc["y"] * img_height))
                            logger.info(f"📍 使用answer_location坐标: {pos}")
                    elif "answer_box" in question and isinstance(question["answer_box"], dict):
                        box = question["answer_box"]
                        if "x" in box and "y" in box:
                            pos = (int(box["x"] * img_width), int(box["y"] * img_height))
                            logger.info(f"📍 使用answer_box坐标: {pos}")
                    elif "error_location" in question and isinstance(question["error_location"], dict):
                        loc = question["error_location"]
                        if "x" in loc and "y" in loc:
                            pos = (int(loc["x"] * img_width), int(loc["y"] * img_height))
                            logger.info(f"📍 使用error_location坐标: {pos}")
                    
                    # 如果没有明确的坐标信息，使用我们推断的位置
                    if not pos:
                        if 'x_pos' in locals() and 'y_pos' in locals():
                            pos = (x_pos, y_pos)
                            logger.info(f"📍 使用推断坐标: {pos}")
                        else:
                            # 完全找不到位置信息，使用默认位置
                            x_pos = int(img_width * 0.75)
                            y_pos = int((i + 1) * img_height / (len(questions) + 1))
                            pos = (x_pos, y_pos)
                            logger.info(f"📍 使用默认坐标: {pos}")
                
                # 选择符号和颜色 - 使用图形绘制
                if question.get("is_correct", False):
                    # 绘制绿色圆形对勾
                    color = colors["correct"]
                    logger.info(f"🟢第{i+1}题正确,绘制绿色圆形对勾")
                    
                    # 计算标记大小，根据答案长度调整
                    answer_length = len(student_answer) if student_answer else 1
                    
                    # 使用answer_position中的信息精确定位标记位置
                    if "answer_position" in question and isinstance(question["answer_position"], dict):
                        position = question["answer_position"]
                        if all(k in position for k in ["x", "y", "width"]):
                            # 使用答案区域信息来精确放置标记
                            x_rel = float(position["x"])
                            y_rel = float(position["y"])
                            width_rel = float(position["width"])
                            
                            # 将相对坐标转换为绝对像素坐标
                            x_abs = int(x_rel * img_width)
                            y_abs = int(y_rel * img_height)
                            width_abs = int(width_rel * img_width)
                            
                            # 标记放在答案的右侧，垂直居中
                            mark_pos = (x_abs + width_abs + 5, y_abs)
                            logger.info(f"📌 使用答案区域信息精确放置正确标记: {mark_pos}")
                        else:
                            # 使用基本坐标，但更靠近答案
                            mark_pos = (pos[0] + 5, pos[1])
                            logger.info(f"📌 使用基本坐标放置正确标记: {mark_pos}")
                    else:
                        # 如果没有answer_position信息，使用传统方法
                        
                        # 对于英语题目，标记应该紧贴在答案旁边
                        # 根据图片分析，调整为更靠近答案的位置
                        
                        # 对于选择题，标记放在选项旁边
                        if "选择题" in question.get("question_type", "").lower() and student_answer and len(student_answer) <= 2:
                            # 标记放在选项旁边
                            mark_pos = (pos[0] + 15, pos[1])
                        # 对于填空题，标记放在答案右侧
                        elif answer_length > 0:
                            # 标记放在答案右侧，紧贴答案
                            mark_pos = (pos[0] + 10, pos[1])
                        else:
                            # 标记放在答案位置
                            mark_pos = pos
                        
                        logger.info(f"📌 使用传统方法放置正确标记: {mark_pos}")
                    
                    # 绘制圆形
                    circle_radius = 15  # 稍微小一点的圆形
                    circle_bbox = [
                        mark_pos[0] - circle_radius, 
                        mark_pos[1] - circle_radius, 
                        mark_pos[0] + circle_radius, 
                        mark_pos[1] + circle_radius
                    ]
                    draw.ellipse(circle_bbox, fill=color, outline=color)
                    
                    # 绘制白色对勾
                    check_color = (255, 255, 255)  # 白色
                    # 对勾的坐标点
                    check_points = [
                        (mark_pos[0] - 6, mark_pos[1] - 1),  # 起点
                        (mark_pos[0] - 1, mark_pos[1] + 3),  # 中间
                        (mark_pos[0] + 6, mark_pos[1] - 5)   # 终点
                    ]
                    draw.line(check_points, fill=check_color, width=3)
                    
                    # 记录标记位置
                    logger.info(f"✅ 在位置 {mark_pos} 绘制了正确标记")
                    
                else:
                    # 绘制红色圆形叉号
                    color = colors["incorrect"]
                    logger.info(f"🔴第{i+1}题错误,绘制红色圆形叉号")
                    
                    # 计算标记大小，根据答案长度调整
                    answer_length = len(student_answer) if student_answer else 1
                    
                    # 使用answer_position中的信息精确定位标记位置
                    if "answer_position" in question and isinstance(question["answer_position"], dict):
                        position = question["answer_position"]
                        if all(k in position for k in ["x", "y", "width"]):
                            # 使用答案区域信息来精确放置标记
                            x_rel = float(position["x"])
                            y_rel = float(position["y"])
                            width_rel = float(position["width"])
                            
                            # 将相对坐标转换为绝对像素坐标
                            x_abs = int(x_rel * img_width)
                            y_abs = int(y_rel * img_height)
                            width_abs = int(width_rel * img_width)
                            
                            # 标记放在答案的右侧，垂直居中
                            mark_pos = (x_abs + width_abs + 5, y_abs)
                            logger.info(f"📌 使用答案区域信息精确放置错误标记: {mark_pos}")
                        else:
                            # 使用基本坐标，但更靠近答案
                            mark_pos = (pos[0] + 5, pos[1])
                            logger.info(f"📌 使用基本坐标放置错误标记: {mark_pos}")
                    else:
                        # 如果没有answer_position信息，使用传统方法
                        
                        # 对于英语题目，标记应该紧贴在答案旁边
                        # 根据图片分析，调整为更靠近答案的位置
                        
                        # 对于选择题，标记放在选项旁边
                        if "选择题" in question.get("question_type", "").lower() and student_answer and len(student_answer) <= 2:
                            # 标记放在选项旁边
                            mark_pos = (pos[0] + 15, pos[1])
                        # 对于填空题，标记放在答案右侧
                        elif answer_length > 0:
                            # 标记放在答案右侧，紧贴答案
                            mark_pos = (pos[0] + 10, pos[1])
                        else:
                            # 标记放在答案位置
                            mark_pos = pos
                        
                        logger.info(f"📌 使用传统方法放置错误标记: {mark_pos}")
                    
                    # 绘制圆形
                    circle_radius = 15  # 稍微小一点的圆形
                    circle_bbox = [
                        mark_pos[0] - circle_radius, 
                        mark_pos[1] - circle_radius, 
                        mark_pos[0] + circle_radius, 
                        mark_pos[1] + circle_radius
                    ]
                    draw.ellipse(circle_bbox, fill=color, outline=color)
                    
                    # 绘制白色叉号
                    cross_color = (255, 255, 255)  # 白色
                    # 叉号的两条线
                    line1_points = [(mark_pos[0] - 6, mark_pos[1] - 6), (mark_pos[0] + 6, mark_pos[1] + 6)]
                    line2_points = [(mark_pos[0] - 6, mark_pos[1] + 6), (mark_pos[0] + 6, mark_pos[1] - 6)]
                    draw.line(line1_points, fill=cross_color, width=3)
                    draw.line(line2_points, fill=cross_color, width=3)
                    
                    # 记录标记位置
                    logger.info(f"❌ 在位置 {mark_pos} 绘制了错误标记")
                
                # 错误时可附加错误原因
                if not question.get("is_correct", False):
                    error_reason = question.get("error_reason", "")
                    if not error_reason and "analysis" in question:
                        error_reason = question["analysis"]
                    
                    if error_reason:
                        # 自动多行换行，最大宽度为图片宽度的20%
                        max_text_width = int(img_width * 0.20)
                        # 计算错误原因显示位置，紧贴标注符号右侧
                        if 'mark_pos' in locals():
                            error_text_x = mark_pos[0] + 18  # 圆半径15+3像素间距
                            error_text_y = mark_pos[1] - 8   # 垂直居中或略微下移
                        else:
                            error_text_x = pos[0] + 18
                            error_text_y = pos[1] - 8
                        
                        # 自动分行，按像素宽度分割
                        lines = []
                        for line in error_reason.split('\n'):
                            # 使用textwrap进行文本换行
                            wrapped_lines = textwrap.wrap(line, width=30)  # 30字符一行
                            lines.extend(wrapped_lines)
                        
                        # 如果没有成功分行，使用原始文本
                        if not lines:
                            lines = [error_reason]
                        
                        # 逐行绘制
                        for idx, line in enumerate(lines):
                            draw.text((error_text_x, error_text_y + idx * 14), line, fill=color, font=tiny_font)
                        
                        logger.info(f"📝 多行添加错误原因: {error_reason}")
        else:
            logger.warning("⚠️ 批改数据中没有questions字段")

        # 添加总体评分信息
        if "summary" in correction_json:
            summary = correction_json["summary"]
            accuracy = summary.get("accuracy", 0) * 100
            logger.info(f"📊 添加总体评分: 正确率: {accuracy:.1f}%")
            
            # 为正确率创建专门的字体和颜色
            try:
                # 尝试加载56号字体
                accuracy_font_paths = [
                    "C:/Windows/Fonts/simsun.ttc",  # 宋体
                    "C:/Windows/Fonts/simhei.ttf",  # 黑体
                    "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
                    "C:/Windows/Fonts/calibri.ttf",
                    "C:/Windows/Fonts/tahoma.ttf",
                    "/System/Library/Fonts/PingFang.ttc",  # macOS 苹方字体
                    "/System/Library/Fonts/STHeiti Light.ttc",  # macOS 华文黑体
                    "/System/Library/Fonts/Supplemental/Arial.ttf"
                ]
                
                accuracy_font = None
                for path in accuracy_font_paths:
                    if os.path.exists(path):
                        try:
                            accuracy_font = ImageFont.truetype(path, 56)  # 使用56号字体
                            logger.info(f"成功加载正确率字体: {path}")
                            break
                        except Exception as e:
                            logger.warning(f"加载正确率字体失败 {path}: {str(e)}")
                            continue
                
                if accuracy_font is None:
                    try:
                        accuracy_font = ImageFont.truetype("arial", 56)
                        logger.info("使用默认arial字体作为正确率字体")
                    except Exception as e:
                        logger.warning(f"加载arial字体失败: {str(e)},使用默认字体")
                        accuracy_font = ImageFont.load_default()
                
                # 尝试显示中文，如果失败则使用英文
                accuracy_text = f"正确率: {accuracy:.1f}%"
                draw.text(
                    (20, 20),
                    accuracy_text,
                    fill=colors["incorrect"],  # 使用红色
                    font=accuracy_font
                )
            except Exception as e:
                logger.error(f"绘制中文正确率文字失败: {str(e)}")
                # 如果绘制中文失败，使用英文
                accuracy_text = f"Accuracy: {accuracy:.1f}%"
                draw.text(
                    (20, 20),
                    accuracy_text,
                    fill=colors["incorrect"],  # 使用红色
                    font=accuracy_font
                )

        # 生成新的文件名和路径
        file_extension = os.path.splitext(full_path)[1]
        annotated_filename = f"annotated_{uuid.uuid4()}{file_extension}"
        annotated_path = os.path.join(UPLOAD_DIR, annotated_filename)
        logger.info(f"📁 生成批注图片路径: {annotated_path}")

        # 保存带批注的图片
        img.save(annotated_path)
        logger.info(f"💾 批注图片已保存到数据库,作业ID: {homework_id}, 页面: {page_number}")

        # 创建批注图片记录
        annotated_image = HomeworkAnnotatedImage(
            homework_id=homework_id,
            original_image_id=image_id,
            image_path=f"/uploads/{annotated_filename}",
            page_number=page_number,
            created_at=datetime.utcnow()
        )

        db.add(annotated_image)
        db.commit()
        logger.info(f"💾 批注图片记录已保存到数据库,作业ID: {homework_id}, 页面: {page_number}")

        logger.info("🎉 批注图片生成完成")
        return f"/uploads/{annotated_filename}"

    except Exception as e:
        logger.error(f"生成批注图片失败: {str(e)}")
        logger.error(traceback.format_exc())
        return None

async def test_ai_service(db: Session = None) -> Optional[str]:
    """测试AI服务是否正常工作
    
    参数:
        db: 数据库会话
        
    返回:
        str: AI服务返回的结果
    """
    try:
        # 获取配置
        config = await get_active_ai_config(db)
        provider = config["provider"]
        model_id = config["model_id"]
        
        logger.info(f"测试AI服务: 提供商={provider}, 模型ID={model_id}")
        
        # 构建简单的测试提示词
        test_prompt = "请回答：1+1=?"
        
        # 调用AI API
        result = await call_ai_api(test_prompt, db)
        
        if result:
            logger.info(f"AI服务测试成功，返回: {result}")
            return result
        else:
            logger.error("AI服务测试失败，返回空结果")
            return None
    except Exception as e:
        logger.error(f"测试AI服务时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return None 

async def call_ollama_deepseek_api(prompt: str, model: str = "deepseek-r1:8b", temperature: float = 0.7):
    """调用Ollama API"""
    url = "http://localhost:11434/api/generate"
    
    payload = {
        "model": model,
        "prompt": prompt,
        "stream": False,
        "temperature": temperature,
        "max_tokens": 800  # 限制输出长度
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=payload)
            if response.status_code == 200:
                result = response.json()
                logger.info(f"Ollama API响应: {result.get('response')[:100]}...")
                return result
            else:
                error_text = response.text
                logger.error(f"Ollama API错误: {response.status_code} - {error_text}")
                raise Exception(f"Ollama API错误: {response.status_code}")
    except Exception as e:
        logger.error(f"调用Ollama API异常: {str(e)}")
        raise

async def analyze_error_with_deepseek(question_content, student_answer, correct_answer, question_type="未知类型"):
    """使用deepseek-r1:8b模型分析学生答案的错误原因和生成强化建议"""
    try:
        logger.info(f"开始分析错误原因，问题内容: {question_content[:50]}...")
        
        # 构建提示词
        prompt = f"""分析以下学生的错误答案，并提供详细的错误分析和针对性的强化建议。
        
问题类型: {question_type}
问题内容: {question_content}
学生答案: {student_answer}
正确答案: {correct_answer}

请提供以下内容:
1. 错误分析: 详细分析学生答案中的错误，包括概念理解错误、计算错误、方法应用错误等。
2. 强化建议: 针对错误类型，提供具体的学习建议，包括需要复习的知识点、推荐的练习方法等。

输出格式:
{{
  "analysis": "详细的错误分析...",
  "reinforcement": "针对性的强化建议..."
}}
"""
        
        # 调用deepseek模型
        response = await call_ollama_api("deepseek-r1:8b", prompt)
        
        # 尝试解析JSON响应
        try:
            # 查找JSON内容
            json_content = extract_json_from_text(response)
            if json_content:
                result = json.loads(json_content)
                logger.info(f"成功解析JSON响应: {result.keys()}")
                return result
        except Exception as json_error:
            logger.warning(f"无法解析JSON响应: {str(json_error)}，将返回原始文本")
        
        # 如果无法解析为JSON，返回原始响应
        return response
    
    except Exception as e:
        logger.error(f"分析错误原因失败: {str(e)}")
        raise Exception(f"分析错误原因失败: {str(e)}")

def analyze_error(student_answer, correct_answer, question_content, db: Session = None):
    """分析学生答案的错误原因"""
    try:
        # 获取错题分析的AI配置
        if db:
            config = get_ai_config_by_usage(db, "error_analysis")
            model_id = config.model_id or "deepseek-r1:8b"
        else:
            model_id = "deepseek-r1:8b"  # 后备方案
        # 构建提示词
        prompt = f"""你是一位经验丰富的教师，正在修改学生作业的批改结果。请针对以下题目提供专业的错误分析。
        
问题内容: {question_content}
学生答案: {student_answer}
正确答案: {correct_answer}

请直接输出一段简洁的错误分析文本，适合教师在批改系统中使用。用中文分析，内容必须包括：
1. 试题解析：解释题目的含义和考察点（如果是英语题目，先翻译再分析）
2. 正确答案解析：说明为什么正确答案是对的，解题思路和方法
3. 学生错误分析：具体指出学生答案中的错误点
4. 错误原因：分析学生可能存在的知识点误解或解题方法问题

重要提示：这是面向教师的批改系统，不是直接面向学生的批改，称呼为第二人称"你"或"同学"。你的分析将被教师用来修改批改结果。
不要输出任何思考过程，直接给出最终的专业分析结果。不要使用"我认为"、"我的分析"等字眼。
字数控制在150字以内，简明扼要。
严禁使用<think>标签或任何形式的思考过程标记。
"""
        
        # 同步调用AI模型
        response = call_ollama_api_sync(model_id, prompt)
        
        # 尝试提取最终结果，去除思考过程
        result = response.strip()
        
        # 移除<think>标签及其内容
        think_pattern = r'<think>.*?</think>'
        result = re.sub(think_pattern, '', result, flags=re.DOTALL)
        
        # 移除没有结束标签的<think>开始的内容
        if '<think>' in result.lower():
            parts = result.lower().split('<think>')
            if len(parts) > 1:
                result = parts[0].strip()
        
        # 移除常见的思考过程标记和前缀
        prefixes_to_remove = [
            "好的，", "我将", "下面是", "以下是", "我会", "我来", "我的分析", "错误分析", "分析如下", "最终分析",
            "首先，", "首先", "根据题目", "根据问题", "根据所给", "分析：", "分析:", "回答：", "回答:", 
            "这道题", "这个问题", "这题", "对于这道题", "对于这个问题", "让我", "我需要", "我应该",
            "我想", "我可以", "我们来", "我们需要", "我们可以", "我们应该", "现在我", "接下来",
            "基于提供的", "根据提供的", "针对这个", "对于这个", "对这个", "关于这个", "嗯，", "那么，"
        ]
        
        for prefix in prefixes_to_remove:
            if result.lower().startswith(prefix.lower()):
                result = result[len(prefix):].strip()
        
        # 移除常见的思考过程段落和标记
        thinking_markers = [
            "让我分析", "我需要分析", "我会按照要求", "我将按照", "我将分析", "我来分析", 
            "让我思考", "让我看看", "让我们来分析", "让我们来看", "分析如下", "我想分析",
            "我会提供", "我将提供", "以下是我的分析", "我认为", "我觉得", "我的看法是",
            "我的理解是", "我的分析是", "我的想法是", "我会这样分析", "我要分析",
            "首先，我会", "首先，我将", "首先，我需要", "首先，我应该", "首先，让我",
            "我将按照以下步骤", "我会按照以下步骤", "我需要按照以下步骤", "我应该按照以下步骤",
            "用户让我模拟", "用户要求我", "嗯，用户让我", "嗯，用户要求我"
        ]
        
        for marker in thinking_markers:
            if marker.lower() in result.lower():
                # 找到所有出现的位置
                start_idx = result.lower().find(marker.lower())
                if start_idx >= 0:
                    # 查找这段思考过程后面的实际内容
                    # 通常思考过程后面会有一个分段或冒号
                    end_markers = ["：", ":", "。", "\n", "\r"]
                    end_idx = -1
                    for end_marker in end_markers:
                        pos = result.find(end_marker, start_idx + len(marker))
                        if pos >= 0 and (end_idx == -1 or pos < end_idx):
                            end_idx = pos + 1
                    
                    if end_idx > 0:
                        # 移除这段思考过程
                        result = result[:start_idx] + result[end_idx:]
                        result = result.strip()
        
        # 移除结尾的总结性语句
        endings_to_remove = [
            "希望这个分析有所帮助", "希望这个分析能够帮助", "希望这个分析对", "希望这对", "希望能帮助",
            "以上是", "这是我的分析", "这就是我的分析", "以上就是分析", "这是对", "这就是对",
            "以上就是", "这是我对", "这就是我对", "希望我的分析", "希望我的回答", "希望以上分析",
            "这些就是", "以上就是我的", "这就是我给出的", "这是针对", "这就是针对"
        ]
        
        for ending in endings_to_remove:
            if ending.lower() in result.lower():
                parts = result.lower().split(ending.lower())
                if len(parts) > 1:
                    # 保留第一部分
                    end_idx = result.lower().find(ending.lower())
                    result = result[:end_idx].strip()
        
        # 检查是否包含"我"、"我的"、"我们"等字样，如果包含则进一步处理
        personal_references = ["我认为", "我的分析", "我觉得", "我们可以看到", "我们可以发现", "我们需要", "我们应该", "我想", "我们"]
        for ref in personal_references:
            if ref.lower() in result.lower():
                # 尝试重新组织文本，去除个人引用
                sentences = result.split("。")
                filtered_sentences = []
                for sentence in sentences:
                    if not any(ref.lower() in sentence.lower() for ref in personal_references):
                        filtered_sentences.append(sentence)
                
                if filtered_sentences:
                    result = "。".join(filtered_sentences)
                    if not result.endswith("。") and result:
                        result += "。"
        
        # 如果结果太长，尝试截取关键部分
        if len(result) > 250:
            sentences = result.split('。')
            if len(sentences) > 5:
                result = '。'.join(sentences[:5]) + '。'
        
        return result
    
    except Exception as e:
        logger.error(f"分析错误原因失败: {str(e)}")
        raise Exception(f"分析错误原因失败: {str(e)}")

def generate_reinforcement_exercises(question_content, correct_answer, wrong_answer, analysis, count=1, db: Session = None):
    """生成强化训练题目"""
    try:
        # 获取强化练习生成的AI配置
        if db:
            config = get_ai_config_by_usage(db, "reinforcement_exercise")
            model_id = config.model_id or "deepseek-r1:8b"
        else:
            model_id = "deepseek-r1:8b"  # 后备方案
        # 构建提示词
        prompt = f"""你是一位经验丰富的教师，请基于以下错题生成{count}道相关的强化训练题目。

原始错题: {question_content}
正确答案: {correct_answer}
学生错误答案: {wrong_answer}
错误分析: {analysis}

请生成与这个概念相关但不同的题目，帮助学生加深理解和巩固知识点。

要求：
1. 题目内容要与原题相关，但不能完全相同
2. 难度适中，适合强化训练
3. 答案要准确
4. 提供详细的解析说明

请以JSON格式返回结果：
[
  {{
    "content": "题目内容（完整的题目描述）",
    "answer": "标准答案",
    "analysis": "详细的答案解析"
  }}
]

注意：只返回JSON格式的数据，不要包含其他文字说明。"""
        
        # 同步调用AI模型
        response = call_ollama_api_sync(model_id, prompt)
        logger.info(f"AI原始返回内容: {response}")

        # 如果AI调用失败，返回模拟数据
        if not response or response.strip() == "":
            logger.warning("AI返回为空，使用模拟数据")
            return [{
                "content": f"根据原题'{question_content}'，请完成以下练习：\n\n请填入正确的词汇：The student should _____ the difference between prepositions and verbs.",
                "answer": "understand",
                "analysis": "这道题考查词汇理解能力。需要根据语境选择合适的动词形式。"
            }]
        
        # 移除<think>标签及其内容
        think_pattern = r'<think>.*?</think>'
        response = re.sub(think_pattern, '', response, flags=re.DOTALL)
        
        # 移除没有结束标签的<think>开始的内容
        if '<think>' in response.lower():
            parts = response.lower().split('<think>')
            if len(parts) > 1:
                response = parts[0].strip()
        
        # 直接尝试手动提取字段，跳过复杂的JSON解析
        logger.info("开始手动提取练习字段")
        logger.info(f"AI返回内容前200字符: {response[:200]}")

        # 使用更宽松的正则表达式提取
        content_match = re.search(r'"content":\s*"([^"]*(?:\\.[^"]*)*)"', response, re.DOTALL)
        answer_match = re.search(r'"answer":\s*"([^"]*(?:\\.[^"]*)*)"', response, re.DOTALL)
        analysis_match = re.search(r'"analysis":\s*"([^"]*(?:\\.[^"]*)*)"', response, re.DOTALL)

        if content_match and answer_match:
            content = content_match.group(1).strip()
            answer = answer_match.group(1).strip()
            analysis = analysis_match.group(1).strip() if analysis_match else "基于错题生成的强化训练，帮助巩固相关知识点。"

            # 清理转义字符
            content = content.replace('\\"', '"').replace('\\n', '\n')
            answer = answer.replace('\\"', '"').replace('\\n', '\n')
            analysis = analysis.replace('\\"', '"').replace('\\n', '\n')

            logger.info(f"手动提取成功 - 内容: {content[:50]}...")
            logger.info(f"手动提取成功 - 答案: {answer}")

            return [{
                "content": content,
                "answer": answer,
                "analysis": analysis
            }]
        else:
            logger.warning("手动提取也失败，使用模拟数据")
            logger.warning(f"content_match: {bool(content_match)}")
            logger.warning(f"answer_match: {bool(answer_match)}")
            if content_match:
                logger.warning(f"找到的content: {content_match.group(1)[:50]}...")
            if answer_match:
                logger.warning(f"找到的answer: {answer_match.group(1)}")

        # 尝试解析JSON响应（作为备用方案）
        try:
            # 查找JSON内容
            json_content = extract_json_from_text(response)
            if json_content:
                logger.info(f"提取到JSON内容: {json_content[:200]}...")
                exercises = json.loads(json_content)
                if isinstance(exercises, list) and len(exercises) > 0:
                    logger.info(f"成功生成{len(exercises)}道强化训练题目")
                    return exercises

            # 使用正则表达式提取content和answer
            content_match = re.search(r'content:\s*([^,\n]+)', response, re.IGNORECASE)
            answer_match = re.search(r'answer:\s*([^,\n]+)', response, re.IGNORECASE)

            if content_match and answer_match:
                content = content_match.group(1).strip()
                answer = answer_match.group(1).strip()

                # 清理内容
                content = content.replace('\\', '').replace('"', '').strip()
                answer = answer.replace('\\', '').replace('"', '').strip()

                logger.info(f"从文本中提取到练习内容: {content[:50]}...")
                logger.info(f"从文本中提取到答案: {answer}")

                return [{
                    "content": content,
                    "answer": answer,
                    "analysis": "基于错题生成的强化训练，帮助巩固相关知识点。"
                }]

            # 如果都失败了，返回模拟数据
            logger.warning("无法从AI返回中提取有效内容，使用模拟数据")
            return [{
                "content": f"根据原题'{question_content}'，请完成以下练习：\n\n请填入正确的词汇：The student should _____ the difference between prepositions and verbs.",
                "answer": "understand",
                "analysis": "这道题考查词汇理解能力。需要根据语境选择合适的动词形式。"
            }]

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            logger.error(f"原始响应: {response}")

            # 尝试从文本中提取内容作为备用方案
            exercises = []
            # 暂时跳过这个逻辑，直接返回空列表
            return exercises
        except Exception as json_error:
            logger.warning(f"无法解析JSON响应: {str(json_error)}，将尝试构造默认格式")
        
        # 如果无法解析为JSON，尝试提取纯文本建议
        try:
            # 清理响应文本
            clean_text = response.strip()
            
            # 移除JSON格式和引号
            clean_text = re.sub(r'^\s*\[\s*{', '', clean_text)
            clean_text = re.sub(r'}\s*\]\s*$', '', clean_text)
            clean_text = re.sub(r'"analysis"\s*:\s*"([^"]*)"', r'\1', clean_text)
            clean_text = re.sub(r'"content"\s*:\s*"[^"]*",\s*"answer"\s*:\s*"[^"]*",\s*', '', clean_text)
            
            # 如果还有引号和大括号，尝试进一步清理
            clean_text = clean_text.replace('"', '').replace('{', '').replace('}', '')
            
            # 移除"建议说明"及之前的所有内容
            if "建议说明" in clean_text:
                parts = clean_text.split("建议说明")
                if len(parts) > 1:
                    # 只保留"建议说明"后面的内容
                    clean_text = parts[1].strip()
                    # 如果以冒号或其他分隔符开头，去除它
                    if clean_text.startswith(":") or clean_text.startswith("："):
                        clean_text = clean_text[1:].strip()
            
            # 移除常见的前缀
            prefixes = [
                "强化建议：", "强化建议:", "建议：", "建议:", "analysis:", "analysis：", 
                "针对这道题", "针对此题", "对于这道题", "对于此题", "对这道题",
                "教学建议：", "教学建议:", "针对性建议：", "针对性建议:"
            ]
            for prefix in prefixes:
                if clean_text.lower().startswith(prefix.lower()):
                    clean_text = clean_text[len(prefix):].strip()
            
            # 移除思考过程和引导语
            thinking_markers = [
                "我建议", "我的建议是", "我的强化建议是", "建议学生", "我认为", "我觉得",
                "以下是我的建议", "以下是强化建议", "我会建议", "我想建议", "我要建议",
                "作为教师", "作为老师", "我们可以", "我们应该", "我们需要", "我们建议",
                "首先", "其次", "最后", "另外", "此外", "总的来说", "综上所述",
                "用户让我", "用户要求我", "嗯，用户让我", "嗯，用户要求我"
            ]
            
            for marker in thinking_markers:
                if marker.lower() in clean_text.lower():
                    # 找到所有出现的位置
                    start_idx = clean_text.lower().find(marker.lower())
                    if start_idx >= 0:
                        # 查找这段思考过程后面的实际内容
                        end_markers = ["：", ":", "。", "\n", "\r"]
                        end_idx = -1
                        for end_marker in end_markers:
                            pos = clean_text.find(end_marker, start_idx + len(marker))
                            if pos >= 0 and (end_idx == -1 or pos < end_idx):
                                end_idx = pos + 1
                        
                        if end_idx > 0:
                            # 移除这段思考过程
                            clean_text = clean_text[:start_idx] + clean_text[end_idx:]
                            clean_text = clean_text.strip()
            
            # 移除结尾的总结性语句
            endings = [
                "希望这个建议有所帮助", "希望这个建议能够帮助", "希望这些建议能", 
                "以上是", "这是我的建议", "这就是我的建议", "以上就是建议",
                "希望对教师有所帮助", "希望能够帮助教师", "希望能帮助教师"
            ]
            for ending in endings:
                if ending.lower() in clean_text.lower():
                    end_idx = clean_text.lower().find(ending.lower())
                    if end_idx > 0:
                        clean_text = clean_text[:end_idx].strip()
            
            # 检查是否包含"我"、"我的"等字样，如果包含则进一步处理
            personal_refs = ["我", "我的", "我们", "我们的"]
            if any(ref in clean_text for ref in personal_refs):
                sentences = clean_text.split("。")
                filtered_sentences = []
                for sentence in sentences:
                    if not any(ref in sentence for ref in personal_refs):
                        filtered_sentences.append(sentence)
                
                if filtered_sentences:
                    clean_text = "。".join(filtered_sentences)
                    if not clean_text.endswith("。") and clean_text:
                        clean_text += "。"
            
            if clean_text and clean_text != "请参考原题的解析。":
                return [{
                    "content": "",
                    "answer": "",
                    "analysis": clean_text
                }]
        except Exception as text_error:
            logger.warning(f"提取纯文本建议失败: {str(text_error)}")
        
        # 如果所有尝试都失败，构造默认格式，但提供更有用的建议
        return [{
            "content": "",
            "answer": "",
            "analysis": "建议针对该题型进行专项练习，重点复习相关概念和解题方法，注意解题步骤的完整性和准确性。可以通过对比正确答案与错误答案的差异，理解解题思路中的关键环节。"
        }]
    
    except Exception as e:
        logger.error(f"生成强化训练题目失败: {str(e)}")
        raise Exception(f"生成强化训练题目失败: {str(e)}")

def evaluate_exercise_answer(exercise_content, correct_answer, student_answer, db: Session = None):
    """评估学生的练习答案"""
    try:
        # 获取强化练习生成的AI配置（评估也使用同样的配置）
        if db:
            config = get_ai_config_by_usage(db, "reinforcement_exercise")
            model_id = config.model_id or "deepseek-r1:8b"
        else:
            model_id = "deepseek-r1:8b"  # 后备方案
        # 构建提示词
        prompt = f"""评估学生对以下练习题的回答是否正确。

练习题: {exercise_content}
正确答案: {correct_answer}
学生答案: {student_answer}

评估标准：
1. 如果是多个填空题，只要大部分答案正确（60%以上）就判断为正确
2. 允许拼写的微小差异和同义词
3. 重点关注学生是否理解了核心概念
4. 对于部分正确的答案，给予鼓励性的反馈

请评估学生答案的正确性，并提供详细的解析。

输出格式:
{{
  "is_correct": true或false,
  "explanation": "详细解析，包括正确的部分和需要改进的部分..."
}}
"""
        
        # 同步调用AI模型
        response = call_ollama_api_sync(model_id, prompt)
        
        # 尝试解析JSON响应
        try:
            # 查找JSON内容
            json_content = extract_json_from_text(response)
            if json_content:
                result = json.loads(json_content)
                logger.info(f"成功解析评估结果: {result}")
                return result
        except Exception as json_error:
            logger.warning(f"无法解析JSON响应: {str(json_error)}，将构造默认格式")
        
        # 如果无法解析为JSON，构造默认格式
        # 简单比较答案是否相同（仅作为后备方案）
        is_correct = student_answer.strip().lower() == correct_answer.strip().lower()
        return {
            "is_correct": is_correct,
            "explanation": response
        }
    
    except Exception as e:
        logger.error(f"评估练习答案失败: {str(e)}")
        raise Exception(f"评估练习答案失败: {str(e)}")

def extract_json_from_text(text):
    """从文本中提取JSON内容"""
    try:
        # 查找第一个{和最后一个}之间的内容
        start_idx = text.find('{')
        end_idx = text.rfind('}')
        
        # 如果找到了JSON对象
        if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
            json_str = text[start_idx:end_idx+1]
            return json_str
        
        # 查找第一个[和最后一个]之间的内容
        start_idx = text.find('[')
        end_idx = text.rfind(']')
        
        # 如果找到了JSON数组
        if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
            json_str = text[start_idx:end_idx+1]
            return json_str
        
        return None
    except Exception:
        return None

def call_ollama_api_sync(model, prompt):
    """同步调用Ollama API"""
    try:
        logger.info(f"同步调用Ollama API，模型: {model}")
        
        # Ollama API地址
        ollama_api_url = "http://localhost:11434/api/generate"
        
        # 构建请求体
        request_body = {
            "model": model,
            "prompt": prompt,
            "stream": False
        }
        
        # 发送请求
        try:
            import httpx
            with httpx.Client(timeout=60.0) as client:
                response = client.post(ollama_api_url, json=request_body)
                response.raise_for_status()
                
                # 解析响应
                result = response.json()
                return result.get("response", "")
        except ImportError:
            # 如果没有httpx，使用requests
            import requests
            response = requests.post(ollama_api_url, json=request_body, timeout=60)
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            return result.get("response", "")
    
    except Exception as e:
        logger.error(f"调用Ollama API失败: {str(e)}")
        raise Exception(f"调用Ollama API失败: {str(e)}")
