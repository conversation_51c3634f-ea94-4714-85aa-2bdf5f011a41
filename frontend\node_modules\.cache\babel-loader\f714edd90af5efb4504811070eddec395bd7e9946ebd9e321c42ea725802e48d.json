{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport VerticalAlignBottomOutlinedSvg from \"@ant-design/icons-svg/es/asn/VerticalAlignBottomOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar VerticalAlignBottomOutlined = function VerticalAlignBottomOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: VerticalAlignBottomOutlinedSvg\n  }));\n};\n\n/**![vertical-align-bottom](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1OS45IDc4MEgxNjQuMWMtNC41IDAtOC4xIDMuNi04LjEgOHY2MGMwIDQuNCAzLjYgOCA4LjEgOGg2OTUuOGM0LjUgMCA4LjEtMy42IDguMS04di02MGMwLTQuNC0zLjYtOC04LjEtOHpNNTA1LjcgNjY5YTggOCAwIDAwMTIuNiAwbDExMi0xNDEuN2M0LjEtNS4yLjQtMTIuOS02LjMtMTIuOWgtNzQuMVYxNzZjMC00LjQtMy42LTgtOC04aC02MGMtNC40IDAtOCAzLjYtOCA4djMzOC4zSDQwMGMtNi43IDAtMTAuNCA3LjctNi4zIDEyLjlsMTEyIDE0MS44eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(VerticalAlignBottomOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'VerticalAlignBottomOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}