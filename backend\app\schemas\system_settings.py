from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime

class SystemSettingsBase(BaseModel):
    key: str
    value: Optional[str] = None
    description: Optional[str] = None

class SystemSettingsCreate(SystemSettingsBase):
    pass

class SystemSettingsUpdate(BaseModel):
    value: Optional[str] = None
    description: Optional[str] = None

class SystemSettings(SystemSettingsBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# 专门用于注册开关的Schema（兼容旧版本）
class RegistrationSettings(BaseModel):
    global_registration_enabled: bool = True
    allow_student_registration: bool = True
    allow_teacher_registration: bool = True

# 新的高级注册设置Schema
class AdvancedRegistrationSettings(BaseModel):
    global_registration_enabled: bool = True
    roles: Dict[str, Dict[str, Any]]
    allow_school_creation: bool = True
    student_binding_verification: Dict[str, Any] = {
        "methods": ["code", "admin_approval"],
        "code_expiry_minutes": 30
    }

# 角色配置Schema
class RoleConfig(BaseModel):
    enabled: bool = True
    requires_approval: bool = False
    approval_level: Optional[str] = None
    fields: Dict[str, Dict[str, Any]] = {}

# 更新特定角色配置的Schema
class RoleConfigUpdate(BaseModel):
    enabled: Optional[bool] = None
    requires_approval: Optional[bool] = None
    approval_level: Optional[str] = None
    fields: Optional[Dict[str, Dict[str, Any]]] = None 