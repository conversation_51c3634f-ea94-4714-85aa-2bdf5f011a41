{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BookFilledSvg from \"@ant-design/icons-svg/es/asn/BookFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BookFilled = function BookFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BookFilledSvg\n  }));\n};\n\n/**![book](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWOTZjMC0xNy43LTE0LjMtMzItMzItMzJ6TTY2OCAzNDUuOUw2MjEuNSAzMTIgNTcyIDM0Ny40VjEyNGg5NnYyMjEuOXoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BookFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BookFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}