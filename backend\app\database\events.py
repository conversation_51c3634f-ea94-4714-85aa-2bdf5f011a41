"""
数据库事件处理器
确保用户创建时自动同步到user_roles表
"""

from sqlalchemy import event
from sqlalchemy.orm import Session
import logging

from ..models.user import User
from ..models.role import Role
from ..models.user_role import UserRole

logger = logging.getLogger(__name__)

def auto_create_user_role(mapper, connection, target):
    """
    用户创建后自动在user_roles表中创建对应记录
    
    Args:
        mapper: SQLAlchemy mapper
        connection: 数据库连接
        target: 被创建的用户对象
    """
    try:
        # 检查是否已存在user_roles记录（避免重复创建）
        existing_role = connection.execute(
            "SELECT id FROM user_roles WHERE user_id = ?", 
            (target.id,)
        ).fetchone()
        
        if existing_role:
            logger.debug(f"用户 {target.username} (ID: {target.id}) 已有角色记录，跳过自动创建")
            return
        
        # 确定角色名称
        role_name = target.role
        if not role_name:
            if target.is_admin and target.is_teacher:
                role_name = "超级管理员"
            elif target.is_admin:
                role_name = "学校管理员"
            elif target.is_teacher:
                role_name = "教师"
            else:
                role_name = "学生"
        
        # 查找角色ID
        role_result = connection.execute(
            "SELECT id FROM roles WHERE name = ? OR code = ? LIMIT 1",
            (role_name, role_name)
        ).fetchone()
        
        if not role_result:
            logger.warning(f"未找到角色: {role_name}，使用默认学生角色")
            role_result = connection.execute(
                "SELECT id FROM roles WHERE code = 'student' LIMIT 1"
            ).fetchone()
        
        if role_result:
            role_id = role_result[0]
            
            # 创建user_roles记录
            connection.execute(
                """INSERT INTO user_roles (user_id, role_id, school_id, class_id, created_at) 
                   VALUES (?, ?, ?, ?, datetime('now'))""",
                (target.id, role_id, target.school_id, getattr(target, 'class_id', None))
            )
            
            logger.info(f"自动为用户 {target.username} (ID: {target.id}) 创建角色记录: {role_name} (角色ID: {role_id})")
        else:
            logger.error(f"无法为用户 {target.username} (ID: {target.id}) 创建角色记录：未找到任何可用角色")
            
    except Exception as e:
        logger.error(f"自动创建用户角色记录失败: {str(e)}")
        # 不抛出异常，避免影响用户创建

def setup_database_events():
    """设置数据库事件监听器"""
    try:
        # 监听用户创建后事件
        event.listen(User, 'after_insert', auto_create_user_role)
        logger.info("数据库事件监听器设置成功")
    except Exception as e:
        logger.error(f"设置数据库事件监听器失败: {str(e)}")

# 自动设置事件监听器
setup_database_events()
