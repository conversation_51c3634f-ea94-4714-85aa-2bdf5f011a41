#!/usr/bin/env python3
"""
文件元数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Enum, ForeignKey, Index
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from ..database import Base

class FileType(enum.Enum):
    """文件类型枚举"""
    ORIGINAL = "original"           # 原始作业图片
    ANNOTATED = "annotated"         # 批注图片
    EXPORT = "export"              # 导出文件
    TEMP = "temp"                  # 临时文件

class FileMetadata(Base):
    """文件元数据表 - 支持新的命名规范"""
    __tablename__ = "file_metadata"

    id = Column(Integer, primary_key=True, index=True)

    # 文件基本信息
    file_type = Column(Enum(FileType), nullable=False, comment="文件类型")
    file_path = Column(String(500), nullable=False, comment="完整文件路径")
    url_path = Column(String(500), nullable=False, comment="URL访问路径")
    stored_filename = Column(String(255), nullable=False, comment="存储文件名")
    original_filename = Column(String(255), comment="原始文件名")

    # 文件属性
    file_size = Column(Integer, comment="文件大小(字节)")
    mime_type = Column(String(100), comment="MIME类型")

    # 层级结构信息
    school_id = Column(Integer, ForeignKey("schools.id"), nullable=False, comment="学校ID")
    grade_class_code = Column(String(10), nullable=False, comment="年级班级代码(如:701,702,801)")
    subject_id = Column(Integer, ForeignKey("subjects.id"), comment="科目ID")
    assignment_id = Column(Integer, ForeignKey("homework_assignments.id"), comment="作业任务ID")

    # 学生和作业信息
    student_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="学生用户ID")
    homework_id = Column(Integer, ForeignKey("homeworks.id"), comment="作业ID")

    # 页面信息
    page_number = Column(Integer, nullable=False, comment="页码(1-4)")
    total_pages = Column(Integer, comment="该作业总页数")
    sequence_in_homework = Column(Integer, comment="在作业中的序号")

    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    created_by = Column(Integer, ForeignKey("users.id"), comment="创建者ID")

    # 状态信息
    is_active = Column(Integer, default=1, comment="是否有效(1:有效, 0:已删除)")
    
    # 关系
    school = relationship("School", foreign_keys=[school_id])
    subject = relationship("Subject", foreign_keys=[subject_id])
    student = relationship("User", foreign_keys=[student_id])
    homework = relationship("Homework", foreign_keys=[homework_id])
    assignment = relationship("HomeworkAssignment", foreign_keys=[assignment_id])
    creator = relationship("User", foreign_keys=[created_by])

    # 索引
    __table_args__ = (
        Index('idx_school_grade_class', 'school_id', 'grade_class_code'),
        Index('idx_assignment_student', 'assignment_id', 'student_id'),
        Index('idx_homework_page', 'homework_id', 'page_number'),
        Index('idx_file_type_created', 'file_type', 'created_at'),
        Index('idx_student_homework', 'student_id', 'homework_id'),
        Index('idx_active_files', 'is_active', 'file_type'),
    )
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'file_type': self.file_type.value if self.file_type else None,
            'file_path': self.file_path,
            'url_path': self.url_path,
            'stored_filename': self.stored_filename,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'mime_type': self.mime_type,
            'school_id': self.school_id,
            'grade_class_code': self.grade_class_code,
            'subject_id': self.subject_id,
            'assignment_id': self.assignment_id,
            'student_id': self.student_id,
            'homework_id': self.homework_id,
            'page_number': self.page_number,
            'total_pages': self.total_pages,
            'sequence_in_homework': self.sequence_in_homework,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'created_by': self.created_by,
            'is_active': self.is_active
        }

    def get_display_name(self):
        """获取显示用的文件名（用于导出时转换为学生姓名）"""
        if self.student and hasattr(self.student, 'full_name'):
            student_name = self.student.full_name or self.student.username
            page_label = f"第{self.page_number}页"
            if self.file_type == FileType.ANNOTATED:
                return f"{student_name}_{page_label}_批注"
            else:
                return f"{student_name}_{page_label}"
        return self.stored_filename

    def get_page_label(self, subject_name=None):
        """根据科目获取页面标签"""
        page_labels = {
            'math': ['第1页', '第2页', '第3页', '第4页'],
            'english': ['听力部分', '阅读部分', '写作部分', '第4页'],
            'physics': ['实验步骤', '实验数据', '实验结果', '实验总结'],
            'chemistry': ['实验过程', '化学方程式', '实验现象', '实验结论'],
            'biology': ['观察记录', '实验步骤', '实验结果', '分析总结']
        }

        if subject_name and subject_name.lower() in page_labels:
            labels = page_labels[subject_name.lower()]
            if self.page_number <= len(labels):
                return labels[self.page_number - 1]

        return f"第{self.page_number}页"

class FileAccessLog(Base):
    """文件访问日志表"""
    __tablename__ = "file_access_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(Integer, ForeignKey("file_metadata.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    access_time = Column(DateTime, default=datetime.utcnow)
    access_type = Column(String(50), comment="访问类型: view, download, delete")
    ip_address = Column(String(45), comment="IP地址")
    user_agent = Column(String(500), comment="用户代理")
    
    # 关系
    file_metadata = relationship("FileMetadata")
    user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index('idx_file_access', 'file_id', 'access_time'),
        Index('idx_user_access', 'user_id', 'access_time'),
    )
