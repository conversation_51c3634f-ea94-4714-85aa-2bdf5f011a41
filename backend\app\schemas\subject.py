from pydantic import BaseModel
from typing import Optional, List, Dict
from datetime import datetime

# 年级-科目关联模型
class GradeSubjectBase(BaseModel):
    grade: str
    is_required: bool = True
    order: int = 0

class GradeSubjectCreate(GradeSubjectBase):
    pass

class GradeSubject(GradeSubjectBase):
    id: int
    subject_id: int
    created_at: datetime

    model_config = {
        "from_attributes": True
    }

# 科目分类模型
class SubjectCategoryBase(BaseModel):
    name: str
    description: Optional[str] = None
    order: int = 0
    is_active: bool = True

class SubjectCategoryCreate(SubjectCategoryBase):
    pass

class SubjectCategoryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    order: Optional[int] = None
    is_active: Optional[bool] = None

class SubjectCategory(SubjectCategoryBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

class SubjectCategoryWithSubjects(SubjectCategory):
    subjects: List["Subject"] = []

    model_config = {
        "from_attributes": True
    }

# 科目模型
class SubjectBase(BaseModel):
    name: str
    pattern: Optional[str] = None
    is_active: bool = True
    category_id: Optional[int] = None

class SubjectCreate(SubjectBase):
    grades: Optional[List[GradeSubjectCreate]] = None

class SubjectUpdate(BaseModel):
    name: Optional[str] = None
    pattern: Optional[str] = None
    is_active: Optional[bool] = None
    category_id: Optional[int] = None
    grades: Optional[List[GradeSubjectCreate]] = None

class Subject(SubjectBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    } 

class SubjectDetail(Subject):
    category: Optional[SubjectCategory] = None
    grade_subjects: List[GradeSubject] = []

    model_config = {
        "from_attributes": True
    }

# 避免循环引用
SubjectCategoryWithSubjects.model_rebuild()

# 返回科目列表的响应模型
class SubjectListResponse(BaseModel):
    total: int
    items: List[SubjectDetail]

# 返回科目分类列表的响应模型
class SubjectCategoryListResponse(BaseModel):
    total: int
    items: List[SubjectCategoryWithSubjects] 