# -*- coding: utf-8 -*-
from app.models.user import User
from app.models.role import Role
from app.models.school import School
from app.models.class_model import Class
from app.models.subject import Subject
from app.models.grade import Grade
from app.models.homework import Homework
from app.models.ai_config import AIModelConfig
from app.models.region import Province, City, District
from fastapi import APIRouter, Request
from fastapi.responses import RedirectResponse
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 增强重定向路由，将/classes的所有HTTP方法重定向到/admin/classes
@router.api_route("/classes", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def redirect_classes(request: Request):
    logger.info(f"重定向请求: {request.method} {request.url.path} -> /api/admin/classes")
    target_path = "/api/admin/classes"
    return RedirectResponse(url=target_path, status_code=307)

# 添加对/classes/{class_id}的重定向支持
@router.api_route("/classes/{class_id}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def redirect_class_detail(request: Request, class_id: str):
    logger.info(f"重定向请求: {request.method} {request.url.path} -> /api/admin/classes/{class_id}")
    target_path = f"/api/admin/classes/{class_id}"
    return RedirectResponse(url=target_path, status_code=307)

# 添加对/admin/query的重定向支持
@router.api_route("/admin/query", methods=["GET", "POST"])
async def redirect_admin_query(request: Request):
    logger.info(f"重定向请求: {request.method} {request.url.path} -> /api/admin/query")
    # 构建目标URL，保留查询参数
    target_path = "/api/admin/query"
    if request.url.query:
        # 检查查询参数是否已经包含在请求URL中
        if "query=" in request.url.query:
            target_path = f"{target_path}?{request.url.query}"
        else:
            # 如果没有query参数，不添加查询参数
            pass
    return RedirectResponse(url=target_path, status_code=307)

__all__ = [
    "User",
    "Role", 
    "School",
    "Class",
    "Subject",
    "Grade",
    "Homework",
    "AIModelConfig",
    "Province",
    "City",
    "District",
    "router"
]
