{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\correcthomework4\\\\frontend\\\\src\\\\components\\\\HomeworkAnalysis\\\\SmartSuggestions.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Button, List, Tag, Space, Typography, Alert, Divider, Collapse, Timeline, Progress } from 'antd';\nimport api from '../../utils/api';\nimport { BulbOutlined, ClockCircleOutlined, UserOutlined, BookOutlined, ExclamationCircleOutlined, CheckCircleOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  Panel\n} = Collapse;\nconst SmartSuggestions = ({\n  assignmentId,\n  user,\n  onLoading\n}) => {\n  _s();\n  const [suggestionsData, setSuggestionsData] = useState(null);\n  const [generating, setGenerating] = useState(false);\n  const [error, setError] = useState(null);\n\n  // 获取智能建议数据\n  const fetchSuggestionsData = async () => {\n    if (!assignmentId) return;\n    try {\n      onLoading(true);\n      const response = await api.get(`/homework-analysis/suggestions/${assignmentId}`);\n      if (response.success) {\n        setSuggestionsData(response.data);\n        setError(null);\n      } else {\n        throw new Error(response.message || '获取数据失败');\n      }\n    } catch (err) {\n      console.error('获取智能建议失败:', err);\n      setError(err.message);\n    } finally {\n      onLoading(false);\n    }\n  };\n\n  // 生成新的智能建议\n  const generateSuggestions = async () => {\n    try {\n      setGenerating(true);\n      const response = await api.post(`/homework-analysis/generate-suggestions/${assignmentId}`);\n      if (response.success) {\n        setSuggestionsData(response.data);\n        setError(null);\n      } else {\n        throw new Error(response.message || '生成建议失败');\n      }\n    } catch (err) {\n      console.error('生成智能建议失败:', err);\n      setError(err.message);\n    } finally {\n      setGenerating(false);\n    }\n  };\n  useEffect(() => {\n    fetchSuggestionsData();\n  }, [assignmentId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // 获取优先级颜色\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case '高':\n        return 'red';\n      case '中':\n        return 'orange';\n      case '低':\n        return 'blue';\n      default:\n        return 'default';\n    }\n  };\n\n  // 渲染评讲建议\n  const renderTeachingSuggestions = () => {\n    if (!(suggestionsData !== null && suggestionsData !== void 0 && suggestionsData.teaching_suggestions)) return null;\n    const {\n      teaching_suggestions\n    } = suggestionsData;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), \"\\u8BC4\\u8BB2\\u5EFA\\u8BAE\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this),\n      extra: /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: [\"\\u5171 \", teaching_suggestions.length, \" \\u9053\\u9898\\u9700\\u8981\\u91CD\\u70B9\\u5173\\u6CE8\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(List, {\n        dataSource: teaching_suggestions,\n        renderItem: suggestion => /*#__PURE__*/_jsxDEV(List.Item, {\n          children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: [\"\\u7B2C\", suggestion.question_number, \"\\u9898\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: getPriorityColor(suggestion.priority),\n                children: [suggestion.priority, \"\\u4F18\\u5148\\u7EA7\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: [\"\\u6B63\\u786E\\u7387: \", suggestion.accuracy_rate, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 19\n            }, this),\n            description: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                children: [/*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n                  style: {\n                    color: '#faad14'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 23\n                }, this), ' ', \"\\u4E3B\\u8981\\u95EE\\u9898: \", suggestion.main_problem]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n                  style: {\n                    color: '#1890ff'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 23\n                }, this), ' ', \"\\u5EFA\\u8BAE\\u65F6\\u957F: \", suggestion.suggested_time]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                children: [/*#__PURE__*/_jsxDEV(BulbOutlined, {\n                  style: {\n                    color: '#52c41a'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 23\n                }, this), ' ', \"\\u6559\\u5B66\\u65B9\\u6CD5: \", suggestion.teaching_method]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染个别辅导建议\n  const renderIndividualGuidance = () => {\n    if (!(suggestionsData !== null && suggestionsData !== void 0 && suggestionsData.individual_guidance)) return null;\n    const {\n      individual_guidance\n    } = suggestionsData;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this), \"\\u4E2A\\u522B\\u8F85\\u5BFC\\u5EFA\\u8BAE\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Collapse, {\n        children: [individual_guidance.need_attention.length > 0 && /*#__PURE__*/_jsxDEV(Panel, {\n          header: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n              style: {\n                color: '#ff4d4f'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 19\n            }, this), \"\\u9700\\u8981\\u91CD\\u70B9\\u5173\\u6CE8 (\", individual_guidance.need_attention.length, \"\\u4EBA)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: individual_guidance.need_attention,\n            renderItem: student => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: student.student_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"red\",\n                    children: [student.score, \"\\u5206\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 25\n                }, this),\n                description: student.suggestion\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this)\n        }, \"attention\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this), individual_guidance.need_care.length > 0 && /*#__PURE__*/_jsxDEV(Panel, {\n          header: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n              style: {\n                color: '#faad14'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 19\n            }, this), \"\\u9700\\u8981\\u9002\\u5F53\\u5173\\u6CE8 (\", individual_guidance.need_care.length, \"\\u4EBA)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: individual_guidance.need_care,\n            renderItem: student => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: student.student_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"orange\",\n                    children: [student.score, \"\\u5206\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 25\n                }, this),\n                description: student.suggestion\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)\n        }, \"care\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this), individual_guidance.not_submitted.length > 0 && /*#__PURE__*/_jsxDEV(Panel, {\n          header: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n              style: {\n                color: '#722ed1'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 19\n            }, this), \"\\u672A\\u63D0\\u4EA4\\u4F5C\\u4E1A (\", individual_guidance.not_submitted.length, \"\\u4EBA)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: individual_guidance.not_submitted,\n            renderItem: student => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: student.student_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 30\n                }, this),\n                description: student.suggestion\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)\n        }, \"not_submitted\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染班级总结\n  const renderClassSummary = () => {\n    if (!(suggestionsData !== null && suggestionsData !== void 0 && suggestionsData.class_summary)) return null;\n    const {\n      class_summary\n    } = suggestionsData;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u73ED\\u7EA7\\u603B\\u7ED3\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u6574\\u4F53\\u8868\\u73B0: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: class_summary.overall_performance === '优秀' ? 'green' : class_summary.overall_performance === '良好' ? 'blue' : class_summary.overall_performance === '一般' ? 'orange' : 'red',\n            children: class_summary.overall_performance\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\" (\\u5E73\\u5747\\u5206: \", class_summary.average_score, \"\\u5206)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), class_summary.main_problems.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E3B\\u8981\\u95EE\\u9898:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            size: \"small\",\n            dataSource: class_summary.main_problems,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: [\"\\u2022 \", item]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u6539\\u8FDB\\u5EFA\\u8BAE:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            size: \"small\",\n            dataSource: class_summary.improvement_suggestions,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: [\"\\u2022 \", item]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 35\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染下次作业建议\n  const renderNextAssignmentSuggestions = () => {\n    if (!(suggestionsData !== null && suggestionsData !== void 0 && suggestionsData.next_assignment_suggestions)) return null;\n    const {\n      next_assignment_suggestions\n    } = suggestionsData;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4E0B\\u6B21\\u4F5C\\u4E1A\\u5EFA\\u8BAE\",\n      children: /*#__PURE__*/_jsxDEV(List, {\n        dataSource: next_assignment_suggestions,\n        renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n            style: {\n              color: '#52c41a',\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this), item]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this);\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u52A0\\u8F7D\\u5931\\u8D25\",\n      description: error,\n      type: \"error\",\n      showIcon: true,\n      action: /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        onClick: fetchSuggestionsData,\n        children: \"\\u91CD\\u8BD5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this);\n  }\n  if (!suggestionsData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"\\u52A0\\u8F7D\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        children: \"\\u667A\\u80FD\\u5EFA\\u8BAE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 17\n        }, this),\n        loading: generating,\n        onClick: generateSuggestions,\n        children: \"\\u91CD\\u65B0\\u751F\\u6210\\u5EFA\\u8BAE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: \"large\",\n      style: {\n        width: '100%'\n      },\n      children: [renderTeachingSuggestions(), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 12,\n          children: renderClassSummary()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 12,\n          children: renderNextAssignmentSuggestions()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), renderIndividualGuidance()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 330,\n    columnNumber: 5\n  }, this);\n};\n_s(SmartSuggestions, \"Gu1PEFPyVrneN0ZZQguzTQq7sI4=\");\n_c = SmartSuggestions;\nexport default SmartSuggestions;\nvar _c;\n$RefreshReg$(_c, \"SmartSuggestions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "<PERSON><PERSON>", "List", "Tag", "Space", "Typography", "<PERSON><PERSON>", "Divider", "Collapse", "Timeline", "Progress", "api", "BulbOutlined", "ClockCircleOutlined", "UserOutlined", "BookOutlined", "ExclamationCircleOutlined", "CheckCircleOutlined", "ReloadOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "Panel", "SmartSuggestions", "assignmentId", "user", "onLoading", "_s", "suggestionsData", "setSuggestionsData", "generating", "setGenerating", "error", "setError", "fetchSuggestionsData", "response", "get", "success", "data", "Error", "message", "err", "console", "generateSuggestions", "post", "getPriorityColor", "priority", "renderTeachingSuggestions", "teaching_suggestions", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "extra", "type", "length", "dataSource", "renderItem", "suggestion", "<PERSON><PERSON>", "Meta", "strong", "question_number", "color", "accuracy_rate", "description", "direction", "style", "width", "main_problem", "suggested_time", "teaching_method", "renderIndividualGuidance", "individual_guidance", "need_attention", "header", "student", "student_name", "score", "need_care", "not_submitted", "renderClassSummary", "class_summary", "overall_performance", "average_score", "main_problems", "size", "item", "improvement_suggestions", "renderNextAssignmentSuggestions", "next_assignment_suggestions", "marginRight", "showIcon", "action", "onClick", "display", "justifyContent", "alignItems", "marginBottom", "level", "icon", "loading", "gutter", "xs", "lg", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/correcthomework4/frontend/src/components/HomeworkAnalysis/SmartSuggestions.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card, Row, Col, Button, List, Tag, Space, Typography,\n  Alert, Divider, Collapse, Timeline, Progress\n} from 'antd';\nimport api from '../../utils/api';\nimport {\n  BulbOutlined,\n  ClockCircleOutlined,\n  UserOutlined,\n  BookOutlined,\n  ExclamationCircleOutlined,\n  CheckCircleOutlined,\n  ReloadOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Panel } = Collapse;\n\nconst SmartSuggestions = ({ assignmentId, user, onLoading }) => {\n  const [suggestionsData, setSuggestionsData] = useState(null);\n  const [generating, setGenerating] = useState(false);\n  const [error, setError] = useState(null);\n\n  // 获取智能建议数据\n  const fetchSuggestionsData = async () => {\n    if (!assignmentId) return;\n    \n    try {\n      onLoading(true);\n      const response = await api.get(`/homework-analysis/suggestions/${assignmentId}`);\n\n      if (response.success) {\n        setSuggestionsData(response.data);\n        setError(null);\n      } else {\n        throw new Error(response.message || '获取数据失败');\n      }\n    } catch (err) {\n      console.error('获取智能建议失败:', err);\n      setError(err.message);\n    } finally {\n      onLoading(false);\n    }\n  };\n\n  // 生成新的智能建议\n  const generateSuggestions = async () => {\n    try {\n      setGenerating(true);\n      const response = await api.post(`/homework-analysis/generate-suggestions/${assignmentId}`);\n\n      if (response.success) {\n        setSuggestionsData(response.data);\n        setError(null);\n      } else {\n        throw new Error(response.message || '生成建议失败');\n      }\n    } catch (err) {\n      console.error('生成智能建议失败:', err);\n      setError(err.message);\n    } finally {\n      setGenerating(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchSuggestionsData();\n  }, [assignmentId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // 获取优先级颜色\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case '高': return 'red';\n      case '中': return 'orange';\n      case '低': return 'blue';\n      default: return 'default';\n    }\n  };\n\n  // 渲染评讲建议\n  const renderTeachingSuggestions = () => {\n    if (!suggestionsData?.teaching_suggestions) return null;\n    \n    const { teaching_suggestions } = suggestionsData;\n    \n    return (\n      <Card \n        title={\n          <Space>\n            <BookOutlined />\n            评讲建议\n          </Space>\n        }\n        extra={\n          <Text type=\"secondary\">\n            共 {teaching_suggestions.length} 道题需要重点关注\n          </Text>\n        }\n      >\n        <List\n          dataSource={teaching_suggestions}\n          renderItem={(suggestion) => (\n            <List.Item>\n              <List.Item.Meta\n                title={\n                  <Space>\n                    <Text strong>第{suggestion.question_number}题</Text>\n                    <Tag color={getPriorityColor(suggestion.priority)}>\n                      {suggestion.priority}优先级\n                    </Tag>\n                    <Text type=\"secondary\">\n                      正确率: {suggestion.accuracy_rate}%\n                    </Text>\n                  </Space>\n                }\n                description={\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <Text>\n                      <ExclamationCircleOutlined style={{ color: '#faad14' }} />\n                      {' '}主要问题: {suggestion.main_problem}\n                    </Text>\n                    <Text>\n                      <ClockCircleOutlined style={{ color: '#1890ff' }} />\n                      {' '}建议时长: {suggestion.suggested_time}\n                    </Text>\n                    <Text>\n                      <BulbOutlined style={{ color: '#52c41a' }} />\n                      {' '}教学方法: {suggestion.teaching_method}\n                    </Text>\n                  </Space>\n                }\n              />\n            </List.Item>\n          )}\n        />\n      </Card>\n    );\n  };\n\n  // 渲染个别辅导建议\n  const renderIndividualGuidance = () => {\n    if (!suggestionsData?.individual_guidance) return null;\n    \n    const { individual_guidance } = suggestionsData;\n    \n    return (\n      <Card \n        title={\n          <Space>\n            <UserOutlined />\n            个别辅导建议\n          </Space>\n        }\n      >\n        <Collapse>\n          {individual_guidance.need_attention.length > 0 && (\n            <Panel \n              header={\n                <Space>\n                  <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />\n                  需要重点关注 ({individual_guidance.need_attention.length}人)\n                </Space>\n              } \n              key=\"attention\"\n            >\n              <List\n                dataSource={individual_guidance.need_attention}\n                renderItem={(student) => (\n                  <List.Item>\n                    <List.Item.Meta\n                      title={\n                        <Space>\n                          <Text strong>{student.student_name}</Text>\n                          <Tag color=\"red\">{student.score}分</Tag>\n                        </Space>\n                      }\n                      description={student.suggestion}\n                    />\n                  </List.Item>\n                )}\n              />\n            </Panel>\n          )}\n          \n          {individual_guidance.need_care.length > 0 && (\n            <Panel \n              header={\n                <Space>\n                  <ExclamationCircleOutlined style={{ color: '#faad14' }} />\n                  需要适当关注 ({individual_guidance.need_care.length}人)\n                </Space>\n              } \n              key=\"care\"\n            >\n              <List\n                dataSource={individual_guidance.need_care}\n                renderItem={(student) => (\n                  <List.Item>\n                    <List.Item.Meta\n                      title={\n                        <Space>\n                          <Text strong>{student.student_name}</Text>\n                          <Tag color=\"orange\">{student.score}分</Tag>\n                        </Space>\n                      }\n                      description={student.suggestion}\n                    />\n                  </List.Item>\n                )}\n              />\n            </Panel>\n          )}\n          \n          {individual_guidance.not_submitted.length > 0 && (\n            <Panel \n              header={\n                <Space>\n                  <ClockCircleOutlined style={{ color: '#722ed1' }} />\n                  未提交作业 ({individual_guidance.not_submitted.length}人)\n                </Space>\n              } \n              key=\"not_submitted\"\n            >\n              <List\n                dataSource={individual_guidance.not_submitted}\n                renderItem={(student) => (\n                  <List.Item>\n                    <List.Item.Meta\n                      title={<Text strong>{student.student_name}</Text>}\n                      description={student.suggestion}\n                    />\n                  </List.Item>\n                )}\n              />\n            </Panel>\n          )}\n        </Collapse>\n      </Card>\n    );\n  };\n\n  // 渲染班级总结\n  const renderClassSummary = () => {\n    if (!suggestionsData?.class_summary) return null;\n    \n    const { class_summary } = suggestionsData;\n    \n    return (\n      <Card title=\"班级总结\">\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>整体表现: </Text>\n            <Tag color={\n              class_summary.overall_performance === '优秀' ? 'green' :\n              class_summary.overall_performance === '良好' ? 'blue' :\n              class_summary.overall_performance === '一般' ? 'orange' : 'red'\n            }>\n              {class_summary.overall_performance}\n            </Tag>\n            <Text type=\"secondary\"> (平均分: {class_summary.average_score}分)</Text>\n          </div>\n          \n          {class_summary.main_problems.length > 0 && (\n            <div>\n              <Text strong>主要问题:</Text>\n              <List\n                size=\"small\"\n                dataSource={class_summary.main_problems}\n                renderItem={item => <List.Item>• {item}</List.Item>}\n              />\n            </div>\n          )}\n          \n          <div>\n            <Text strong>改进建议:</Text>\n            <List\n              size=\"small\"\n              dataSource={class_summary.improvement_suggestions}\n              renderItem={item => <List.Item>• {item}</List.Item>}\n            />\n          </div>\n        </Space>\n      </Card>\n    );\n  };\n\n  // 渲染下次作业建议\n  const renderNextAssignmentSuggestions = () => {\n    if (!suggestionsData?.next_assignment_suggestions) return null;\n    \n    const { next_assignment_suggestions } = suggestionsData;\n    \n    return (\n      <Card title=\"下次作业建议\">\n        <List\n          dataSource={next_assignment_suggestions}\n          renderItem={item => (\n            <List.Item>\n              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />\n              {item}\n            </List.Item>\n          )}\n        />\n      </Card>\n    );\n  };\n\n  if (error) {\n    return (\n      <Alert\n        message=\"加载失败\"\n        description={error}\n        type=\"error\"\n        showIcon\n        action={\n          <Button size=\"small\" onClick={fetchSuggestionsData}>\n            重试\n          </Button>\n        }\n      />\n    );\n  }\n\n  if (!suggestionsData) {\n    return <div>加载中...</div>;\n  }\n\n  return (\n    <div>\n      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>\n        <Title level={2}>智能建议</Title>\n        <Button \n          type=\"primary\" \n          icon={<ReloadOutlined />}\n          loading={generating}\n          onClick={generateSuggestions}\n        >\n          重新生成建议\n        </Button>\n      </div>\n      \n      <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n        {/* 评讲建议 */}\n        {renderTeachingSuggestions()}\n        \n        <Row gutter={[16, 16]}>\n          {/* 班级总结 */}\n          <Col xs={24} lg={12}>\n            {renderClassSummary()}\n          </Col>\n          \n          {/* 下次作业建议 */}\n          <Col xs={24} lg={12}>\n            {renderNextAssignmentSuggestions()}\n          </Col>\n        </Row>\n        \n        {/* 个别辅导建议 */}\n        {renderIndividualGuidance()}\n      </Space>\n    </div>\n  );\n};\n\nexport default SmartSuggestions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EACpDC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,QACvC,MAAM;AACb,OAAOC,GAAG,MAAM,iBAAiB;AACjC,SACEC,YAAY,EACZC,mBAAmB,EACnBC,YAAY,EACZC,YAAY,EACZC,yBAAyB,EACzBC,mBAAmB,EACnBC,cAAc,QACT,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGlB,UAAU;AAC7C,MAAM;EAAEmB;AAAM,CAAC,GAAGhB,QAAQ;AAE1B,MAAMiB,gBAAgB,GAAGA,CAAC;EAAEC,YAAY;EAAEC,IAAI;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMwC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACV,YAAY,EAAE;IAEnB,IAAI;MACFE,SAAS,CAAC,IAAI,CAAC;MACf,MAAMS,QAAQ,GAAG,MAAM1B,GAAG,CAAC2B,GAAG,CAAC,kCAAkCZ,YAAY,EAAE,CAAC;MAEhF,IAAIW,QAAQ,CAACE,OAAO,EAAE;QACpBR,kBAAkB,CAACM,QAAQ,CAACG,IAAI,CAAC;QACjCL,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACL,MAAM,IAAIM,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACV,KAAK,CAAC,WAAW,EAAES,GAAG,CAAC;MAC/BR,QAAQ,CAACQ,GAAG,CAACD,OAAO,CAAC;IACvB,CAAC,SAAS;MACRd,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMiB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFZ,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMI,QAAQ,GAAG,MAAM1B,GAAG,CAACmC,IAAI,CAAC,2CAA2CpB,YAAY,EAAE,CAAC;MAE1F,IAAIW,QAAQ,CAACE,OAAO,EAAE;QACpBR,kBAAkB,CAACM,QAAQ,CAACG,IAAI,CAAC;QACjCL,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACL,MAAM,IAAIM,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACV,KAAK,CAAC,WAAW,EAAES,GAAG,CAAC;MAC/BR,QAAQ,CAACQ,GAAG,CAACD,OAAO,CAAC;IACvB,CAAC,SAAS;MACRT,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAEDpC,SAAS,CAAC,MAAM;IACduC,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACV,YAAY,CAAC,CAAC,CAAC,CAAC;;EAEpB;EACA,MAAMqB,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,GAAG;QAAE,OAAO,KAAK;MACtB,KAAK,GAAG;QAAE,OAAO,QAAQ;MACzB,KAAK,GAAG;QAAE,OAAO,MAAM;MACvB;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,EAACnB,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEoB,oBAAoB,GAAE,OAAO,IAAI;IAEvD,MAAM;MAAEA;IAAqB,CAAC,GAAGpB,eAAe;IAEhD,oBACEV,OAAA,CAACtB,IAAI;MACHqD,KAAK,eACH/B,OAAA,CAAChB,KAAK;QAAAgD,QAAA,gBACJhC,OAAA,CAACL,YAAY;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAElB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;MACDC,KAAK,eACHrC,OAAA,CAACE,IAAI;QAACoC,IAAI,EAAC,WAAW;QAAAN,QAAA,GAAC,SACnB,EAACF,oBAAoB,CAACS,MAAM,EAAC,mDACjC;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP;MAAAJ,QAAA,eAEDhC,OAAA,CAAClB,IAAI;QACH0D,UAAU,EAAEV,oBAAqB;QACjCW,UAAU,EAAGC,UAAU,iBACrB1C,OAAA,CAAClB,IAAI,CAAC6D,IAAI;UAAAX,QAAA,eACRhC,OAAA,CAAClB,IAAI,CAAC6D,IAAI,CAACC,IAAI;YACbb,KAAK,eACH/B,OAAA,CAAChB,KAAK;cAAAgD,QAAA,gBACJhC,OAAA,CAACE,IAAI;gBAAC2C,MAAM;gBAAAb,QAAA,GAAC,QAAC,EAACU,UAAU,CAACI,eAAe,EAAC,QAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDpC,OAAA,CAACjB,GAAG;gBAACgE,KAAK,EAAEpB,gBAAgB,CAACe,UAAU,CAACd,QAAQ,CAAE;gBAAAI,QAAA,GAC/CU,UAAU,CAACd,QAAQ,EAAC,oBACvB;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpC,OAAA,CAACE,IAAI;gBAACoC,IAAI,EAAC,WAAW;gBAAAN,QAAA,GAAC,sBAChB,EAACU,UAAU,CAACM,aAAa,EAAC,GACjC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACR;YACDa,WAAW,eACTjD,OAAA,CAAChB,KAAK;cAACkE,SAAS,EAAC,UAAU;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAApB,QAAA,gBACnDhC,OAAA,CAACE,IAAI;gBAAA8B,QAAA,gBACHhC,OAAA,CAACJ,yBAAyB;kBAACuD,KAAK,EAAE;oBAAEJ,KAAK,EAAE;kBAAU;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACzD,GAAG,EAAC,4BAAM,EAACM,UAAU,CAACW,YAAY;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACPpC,OAAA,CAACE,IAAI;gBAAA8B,QAAA,gBACHhC,OAAA,CAACP,mBAAmB;kBAAC0D,KAAK,EAAE;oBAAEJ,KAAK,EAAE;kBAAU;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACnD,GAAG,EAAC,4BAAM,EAACM,UAAU,CAACY,cAAc;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACPpC,OAAA,CAACE,IAAI;gBAAA8B,QAAA,gBACHhC,OAAA,CAACR,YAAY;kBAAC2D,KAAK,EAAE;oBAAEJ,KAAK,EAAE;kBAAU;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC5C,GAAG,EAAC,4BAAM,EAACM,UAAU,CAACa,eAAe;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;;EAED;EACA,MAAMoB,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,EAAC9C,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE+C,mBAAmB,GAAE,OAAO,IAAI;IAEtD,MAAM;MAAEA;IAAoB,CAAC,GAAG/C,eAAe;IAE/C,oBACEV,OAAA,CAACtB,IAAI;MACHqD,KAAK,eACH/B,OAAA,CAAChB,KAAK;QAAAgD,QAAA,gBACJhC,OAAA,CAACN,YAAY;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,wCAElB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;MAAAJ,QAAA,eAEDhC,OAAA,CAACZ,QAAQ;QAAA4C,QAAA,GACNyB,mBAAmB,CAACC,cAAc,CAACnB,MAAM,GAAG,CAAC,iBAC5CvC,OAAA,CAACI,KAAK;UACJuD,MAAM,eACJ3D,OAAA,CAAChB,KAAK;YAAAgD,QAAA,gBACJhC,OAAA,CAACJ,yBAAyB;cAACuD,KAAK,EAAE;gBAAEJ,KAAK,EAAE;cAAU;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,0CAClD,EAACqB,mBAAmB,CAACC,cAAc,CAACnB,MAAM,EAAC,SACrD;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UAAAJ,QAAA,eAGDhC,OAAA,CAAClB,IAAI;YACH0D,UAAU,EAAEiB,mBAAmB,CAACC,cAAe;YAC/CjB,UAAU,EAAGmB,OAAO,iBAClB5D,OAAA,CAAClB,IAAI,CAAC6D,IAAI;cAAAX,QAAA,eACRhC,OAAA,CAAClB,IAAI,CAAC6D,IAAI,CAACC,IAAI;gBACbb,KAAK,eACH/B,OAAA,CAAChB,KAAK;kBAAAgD,QAAA,gBACJhC,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAAb,QAAA,EAAE4B,OAAO,CAACC;kBAAY;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1CpC,OAAA,CAACjB,GAAG;oBAACgE,KAAK,EAAC,KAAK;oBAAAf,QAAA,GAAE4B,OAAO,CAACE,KAAK,EAAC,QAAC;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CACR;gBACDa,WAAW,EAAEW,OAAO,CAAClB;cAAW;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjBE,WAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBV,CACR,EAEAqB,mBAAmB,CAACM,SAAS,CAACxB,MAAM,GAAG,CAAC,iBACvCvC,OAAA,CAACI,KAAK;UACJuD,MAAM,eACJ3D,OAAA,CAAChB,KAAK;YAAAgD,QAAA,gBACJhC,OAAA,CAACJ,yBAAyB;cAACuD,KAAK,EAAE;gBAAEJ,KAAK,EAAE;cAAU;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,0CAClD,EAACqB,mBAAmB,CAACM,SAAS,CAACxB,MAAM,EAAC,SAChD;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UAAAJ,QAAA,eAGDhC,OAAA,CAAClB,IAAI;YACH0D,UAAU,EAAEiB,mBAAmB,CAACM,SAAU;YAC1CtB,UAAU,EAAGmB,OAAO,iBAClB5D,OAAA,CAAClB,IAAI,CAAC6D,IAAI;cAAAX,QAAA,eACRhC,OAAA,CAAClB,IAAI,CAAC6D,IAAI,CAACC,IAAI;gBACbb,KAAK,eACH/B,OAAA,CAAChB,KAAK;kBAAAgD,QAAA,gBACJhC,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAAb,QAAA,EAAE4B,OAAO,CAACC;kBAAY;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1CpC,OAAA,CAACjB,GAAG;oBAACgE,KAAK,EAAC,QAAQ;oBAAAf,QAAA,GAAE4B,OAAO,CAACE,KAAK,EAAC,QAAC;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CACR;gBACDa,WAAW,EAAEW,OAAO,CAAClB;cAAW;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjBE,MAAM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBL,CACR,EAEAqB,mBAAmB,CAACO,aAAa,CAACzB,MAAM,GAAG,CAAC,iBAC3CvC,OAAA,CAACI,KAAK;UACJuD,MAAM,eACJ3D,OAAA,CAAChB,KAAK;YAAAgD,QAAA,gBACJhC,OAAA,CAACP,mBAAmB;cAAC0D,KAAK,EAAE;gBAAEJ,KAAK,EAAE;cAAU;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oCAC7C,EAACqB,mBAAmB,CAACO,aAAa,CAACzB,MAAM,EAAC,SACnD;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UAAAJ,QAAA,eAGDhC,OAAA,CAAClB,IAAI;YACH0D,UAAU,EAAEiB,mBAAmB,CAACO,aAAc;YAC9CvB,UAAU,EAAGmB,OAAO,iBAClB5D,OAAA,CAAClB,IAAI,CAAC6D,IAAI;cAAAX,QAAA,eACRhC,OAAA,CAAClB,IAAI,CAAC6D,IAAI,CAACC,IAAI;gBACbb,KAAK,eAAE/B,OAAA,CAACE,IAAI;kBAAC2C,MAAM;kBAAAb,QAAA,EAAE4B,OAAO,CAACC;gBAAY;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAE;gBAClDa,WAAW,EAAEW,OAAO,CAAClB;cAAW;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAZE,eAAe;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAad,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEX,CAAC;;EAED;EACA,MAAM6B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,EAACvD,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEwD,aAAa,GAAE,OAAO,IAAI;IAEhD,MAAM;MAAEA;IAAc,CAAC,GAAGxD,eAAe;IAEzC,oBACEV,OAAA,CAACtB,IAAI;MAACqD,KAAK,EAAC,0BAAM;MAAAC,QAAA,eAChBhC,OAAA,CAAChB,KAAK;QAACkE,SAAS,EAAC,UAAU;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAApB,QAAA,gBACnDhC,OAAA;UAAAgC,QAAA,gBACEhC,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAAb,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BpC,OAAA,CAACjB,GAAG;YAACgE,KAAK,EACRmB,aAAa,CAACC,mBAAmB,KAAK,IAAI,GAAG,OAAO,GACpDD,aAAa,CAACC,mBAAmB,KAAK,IAAI,GAAG,MAAM,GACnDD,aAAa,CAACC,mBAAmB,KAAK,IAAI,GAAG,QAAQ,GAAG,KACzD;YAAAnC,QAAA,EACEkC,aAAa,CAACC;UAAmB;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACNpC,OAAA,CAACE,IAAI;YAACoC,IAAI,EAAC,WAAW;YAAAN,QAAA,GAAC,wBAAO,EAACkC,aAAa,CAACE,aAAa,EAAC,SAAE;UAAA;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,EAEL8B,aAAa,CAACG,aAAa,CAAC9B,MAAM,GAAG,CAAC,iBACrCvC,OAAA;UAAAgC,QAAA,gBACEhC,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAAb,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBpC,OAAA,CAAClB,IAAI;YACHwF,IAAI,EAAC,OAAO;YACZ9B,UAAU,EAAE0B,aAAa,CAACG,aAAc;YACxC5B,UAAU,EAAE8B,IAAI,iBAAIvE,OAAA,CAAClB,IAAI,CAAC6D,IAAI;cAAAX,QAAA,GAAC,SAAE,EAACuC,IAAI;YAAA;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDpC,OAAA;UAAAgC,QAAA,gBACEhC,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAAb,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBpC,OAAA,CAAClB,IAAI;YACHwF,IAAI,EAAC,OAAO;YACZ9B,UAAU,EAAE0B,aAAa,CAACM,uBAAwB;YAClD/B,UAAU,EAAE8B,IAAI,iBAAIvE,OAAA,CAAClB,IAAI,CAAC6D,IAAI;cAAAX,QAAA,GAAC,SAAE,EAACuC,IAAI;YAAA;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEX,CAAC;;EAED;EACA,MAAMqC,+BAA+B,GAAGA,CAAA,KAAM;IAC5C,IAAI,EAAC/D,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEgE,2BAA2B,GAAE,OAAO,IAAI;IAE9D,MAAM;MAAEA;IAA4B,CAAC,GAAGhE,eAAe;IAEvD,oBACEV,OAAA,CAACtB,IAAI;MAACqD,KAAK,EAAC,sCAAQ;MAAAC,QAAA,eAClBhC,OAAA,CAAClB,IAAI;QACH0D,UAAU,EAAEkC,2BAA4B;QACxCjC,UAAU,EAAE8B,IAAI,iBACdvE,OAAA,CAAClB,IAAI,CAAC6D,IAAI;UAAAX,QAAA,gBACRhC,OAAA,CAACH,mBAAmB;YAACsD,KAAK,EAAE;cAAEJ,KAAK,EAAE,SAAS;cAAE4B,WAAW,EAAE;YAAE;UAAE;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnEmC,IAAI;QAAA;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;EAED,IAAItB,KAAK,EAAE;IACT,oBACEd,OAAA,CAACd,KAAK;MACJoC,OAAO,EAAC,0BAAM;MACd2B,WAAW,EAAEnC,KAAM;MACnBwB,IAAI,EAAC,OAAO;MACZsC,QAAQ;MACRC,MAAM,eACJ7E,OAAA,CAACnB,MAAM;QAACyF,IAAI,EAAC,OAAO;QAACQ,OAAO,EAAE9D,oBAAqB;QAAAgB,QAAA,EAAC;MAEpD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEN;EAEA,IAAI,CAAC1B,eAAe,EAAE;IACpB,oBAAOV,OAAA;MAAAgC,QAAA,EAAK;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC1B;EAEA,oBACEpC,OAAA;IAAAgC,QAAA,gBACEhC,OAAA;MAAKmD,KAAK,EAAE;QAAE4B,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAlD,QAAA,gBACvGhC,OAAA,CAACC,KAAK;QAACkF,KAAK,EAAE,CAAE;QAAAnD,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC7BpC,OAAA,CAACnB,MAAM;QACLyD,IAAI,EAAC,SAAS;QACd8C,IAAI,eAAEpF,OAAA,CAACF,cAAc;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBiD,OAAO,EAAEzE,UAAW;QACpBkE,OAAO,EAAErD,mBAAoB;QAAAO,QAAA,EAC9B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENpC,OAAA,CAAChB,KAAK;MAACkE,SAAS,EAAC,UAAU;MAACoB,IAAI,EAAC,OAAO;MAACnB,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAApB,QAAA,GAE/DH,yBAAyB,CAAC,CAAC,eAE5B7B,OAAA,CAACrB,GAAG;QAAC2G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAtD,QAAA,gBAEpBhC,OAAA,CAACpB,GAAG;UAAC2G,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAxD,QAAA,EACjBiC,kBAAkB,CAAC;QAAC;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAGNpC,OAAA,CAACpB,GAAG;UAAC2G,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAxD,QAAA,EACjByC,+BAA+B,CAAC;QAAC;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLoB,wBAAwB,CAAC,CAAC;IAAA;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAxVIJ,gBAAgB;AAAAoF,EAAA,GAAhBpF,gBAAgB;AA0VtB,eAAeA,gBAAgB;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}