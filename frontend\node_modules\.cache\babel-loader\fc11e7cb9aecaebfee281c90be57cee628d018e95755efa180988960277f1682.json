{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport LeftCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/LeftCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar LeftCircleOutlined = function LeftCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: LeftCircleOutlinedSvg\n  }));\n};\n\n/**![left-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYwMy4zIDMyNy41bC0yNDYgMTc4YTcuOTUgNy45NSAwIDAwMCAxMi45bDI0NiAxNzhjNS4zIDMuOCAxMi43IDAgMTIuNy02LjVWNjQzYzAtMTAuMi00LjktMTkuOS0xMy4yLTI1LjlMNDU3LjQgNTEybDE0NS40LTEwNS4yYzguMy02IDEzLjItMTUuNiAxMy4yLTI1LjlWMzM0YzAtNi41LTcuNC0xMC4zLTEyLjctNi41eiIgLz48cGF0aCBkPSJNNTEyIDY0QzI2NC42IDY0IDY0IDI2NC42IDY0IDUxMnMyMDAuNiA0NDggNDQ4IDQ0OCA0NDgtMjAwLjYgNDQ4LTQ0OFM3NTkuNCA2NCA1MTIgNjR6bTAgODIwYy0yMDUuNCAwLTM3Mi0xNjYuNi0zNzItMzcyczE2Ni42LTM3MiAzNzItMzcyIDM3MiAxNjYuNiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzIgMzcyeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(LeftCircleOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'LeftCircleOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}