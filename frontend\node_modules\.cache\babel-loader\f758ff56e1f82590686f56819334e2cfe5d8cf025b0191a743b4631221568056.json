{"ast": null, "code": "\"use client\";\n\nimport React, { useState } from 'react';\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport UpOutlined from \"@ant-design/icons/es/icons/UpOutlined\";\nimport classNames from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nfunction getValidNumber(num) {\n  return typeof num === 'number' && !Number.isNaN(num) ? Math.round(num) : 0;\n}\nconst SplitBar = props => {\n  const {\n    prefixCls,\n    vertical,\n    index,\n    active,\n    ariaNow,\n    ariaMin,\n    ariaMax,\n    resizable,\n    startCollapsible,\n    endCollapsible,\n    onOffsetStart,\n    onOffsetUpdate,\n    onOffsetEnd,\n    onCollapse,\n    lazy,\n    containerSize\n  } = props;\n  const splitBarPrefixCls = `${prefixCls}-bar`;\n  // ======================== Resize ========================\n  const [startPos, setStartPos] = useState(null);\n  const [constrainedOffset, setConstrainedOffset] = useState(0);\n  const constrainedOffsetX = vertical ? 0 : constrainedOffset;\n  const constrainedOffsetY = vertical ? constrainedOffset : 0;\n  const onMouseDown = e => {\n    if (resizable && e.currentTarget) {\n      setStartPos([e.pageX, e.pageY]);\n      onOffsetStart(index);\n    }\n  };\n  const onTouchStart = e => {\n    if (resizable && e.touches.length === 1) {\n      const touch = e.touches[0];\n      setStartPos([touch.pageX, touch.pageY]);\n      onOffsetStart(index);\n    }\n  };\n  // Updated constraint calculation\n  const getConstrainedOffset = rawOffset => {\n    const currentPos = containerSize * ariaNow / 100;\n    const newPos = currentPos + rawOffset;\n    // Calculate available space\n    const minAllowed = Math.max(0, containerSize * ariaMin / 100);\n    const maxAllowed = Math.min(containerSize, containerSize * ariaMax / 100);\n    // Constrain new position within bounds\n    const clampedPos = Math.max(minAllowed, Math.min(maxAllowed, newPos));\n    return clampedPos - currentPos;\n  };\n  const handleLazyMove = useEvent((offsetX, offsetY) => {\n    const constrainedOffsetValue = getConstrainedOffset(vertical ? offsetY : offsetX);\n    setConstrainedOffset(constrainedOffsetValue);\n  });\n  const handleLazyEnd = useEvent(() => {\n    onOffsetUpdate(index, constrainedOffsetX, constrainedOffsetY, true);\n    setConstrainedOffset(0);\n    onOffsetEnd(true);\n  });\n  React.useEffect(() => {\n    if (startPos) {\n      const onMouseMove = e => {\n        const {\n          pageX,\n          pageY\n        } = e;\n        const offsetX = pageX - startPos[0];\n        const offsetY = pageY - startPos[1];\n        if (lazy) {\n          handleLazyMove(offsetX, offsetY);\n        } else {\n          onOffsetUpdate(index, offsetX, offsetY);\n        }\n      };\n      const onMouseUp = () => {\n        if (lazy) {\n          handleLazyEnd();\n        } else {\n          onOffsetEnd();\n        }\n        setStartPos(null);\n      };\n      const handleTouchMove = e => {\n        if (e.touches.length === 1) {\n          const touch = e.touches[0];\n          const offsetX = touch.pageX - startPos[0];\n          const offsetY = touch.pageY - startPos[1];\n          if (lazy) {\n            handleLazyMove(offsetX, offsetY);\n          } else {\n            onOffsetUpdate(index, offsetX, offsetY);\n          }\n        }\n      };\n      const handleTouchEnd = () => {\n        if (lazy) {\n          handleLazyEnd();\n        } else {\n          onOffsetEnd();\n        }\n        setStartPos(null);\n      };\n      window.addEventListener('touchmove', handleTouchMove);\n      window.addEventListener('touchend', handleTouchEnd);\n      window.addEventListener('mousemove', onMouseMove);\n      window.addEventListener('mouseup', onMouseUp);\n      return () => {\n        window.removeEventListener('mousemove', onMouseMove);\n        window.removeEventListener('mouseup', onMouseUp);\n        window.removeEventListener('touchmove', handleTouchMove);\n        window.removeEventListener('touchend', handleTouchEnd);\n      };\n    }\n  }, [startPos, lazy, vertical, index, containerSize, ariaNow, ariaMin, ariaMax]);\n  const transformStyle = {\n    [`--${splitBarPrefixCls}-preview-offset`]: `${constrainedOffset}px`\n  };\n  // ======================== Render ========================\n  const StartIcon = vertical ? UpOutlined : LeftOutlined;\n  const EndIcon = vertical ? DownOutlined : RightOutlined;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: splitBarPrefixCls,\n    role: \"separator\",\n    \"aria-valuenow\": getValidNumber(ariaNow),\n    \"aria-valuemin\": getValidNumber(ariaMin),\n    \"aria-valuemax\": getValidNumber(ariaMax)\n  }, lazy && (/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-preview`, {\n      [`${splitBarPrefixCls}-preview-active`]: !!constrainedOffset\n    }),\n    style: transformStyle\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-dragger`, {\n      [`${splitBarPrefixCls}-dragger-disabled`]: !resizable,\n      [`${splitBarPrefixCls}-dragger-active`]: active\n    }),\n    onMouseDown: onMouseDown,\n    onTouchStart: onTouchStart\n  }), startCollapsible && (/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-collapse-bar`, `${splitBarPrefixCls}-collapse-bar-start`),\n    onClick: () => onCollapse(index, 'start')\n  }, /*#__PURE__*/React.createElement(StartIcon, {\n    className: classNames(`${splitBarPrefixCls}-collapse-icon`, `${splitBarPrefixCls}-collapse-start`)\n  }))), endCollapsible && (/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-collapse-bar`, `${splitBarPrefixCls}-collapse-bar-end`),\n    onClick: () => onCollapse(index, 'end')\n  }, /*#__PURE__*/React.createElement(EndIcon, {\n    className: classNames(`${splitBarPrefixCls}-collapse-icon`, `${splitBarPrefixCls}-collapse-end`)\n  }))));\n};\nexport default SplitBar;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}