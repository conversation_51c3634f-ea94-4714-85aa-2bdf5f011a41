from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from sqlalchemy import func, distinct

from ..database import get_db
from ..models.user import User, Class, ClassTeacher
from ..models.school import School
from ..models.subject import Subject
from ..models.region import Province, City, District
from ..schemas import school_schema
from ..schemas import subject as subject_schema
from ..schemas import user as user_schema
from ..services.auth_service import get_current_user
import logging

router = APIRouter()

# 检查是否有管理学校的权限
def check_school_admin_permission(current_user: User):
    # 超级管理员有所有权限
    if current_user.is_admin:
        return True

    # 检查是否为学校管理员或校长
    if current_user.role in ["学校管理员", "super_admin", "principal", "校长"]:
        return True

    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="没有管理学校的权限"
    )

# 公开API - 获取学校列表，用于注册页面
@router.get("/public", response_model=List[school_schema.SchoolPublic])
async def get_public_schools(
    province: Optional[str] = None,
    city: Optional[str] = None,
    district: Optional[str] = None,
    skip: int = 0, 
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取公开学校列表，用于注册页面选择学校"""
    # 构建查询
    query = db.query(School).filter(School.is_active == True)
    
    # 按地区筛选
    if province:
        query = query.filter(School.province == province)
    if city:
        query = query.filter(School.city == city)
    if district:
        query = query.filter(School.district == district)
    
    # 执行查询
    schools = query.offset(skip).limit(limit).all()
    
    # 构建响应数据
    result = []
    for school in schools:
        school_response = school_schema.SchoolPublic(
            id=school.id,
            school_name=school.name,
            province=school.province,
            city=school.city,
            district=school.district
        )
        result.append(school_response)
    
    return result

# 公开API - 获取班级列表，用于注册页面
@router.get("/public/classes", response_model=List[school_schema.ClassPublic])
async def get_public_classes(
    school_id: Optional[int] = None,
    grade: Optional[str] = None,
    skip: int = 0, 
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取公开班级列表，用于注册页面选择班级"""
    # 构建查询
    query = db.query(Class)
    
    # 按学校ID筛选
    if school_id:
        query = query.filter(Class.school_id == school_id)
    
    # 按年级筛选
    if grade:
        query = query.filter(Class.grade == grade)
    
    # 执行查询
    classes = query.offset(skip).limit(limit).all()
    
    # 构建响应数据
    result = []
    for class_ in classes:
        class_response = school_schema.ClassPublic(
            id=class_.id,
            name=class_.name,
            grade=class_.grade,
            school_id=class_.school_id
        )
        result.append(class_response)
    
    return result

# 公开API - 获取科目列表，用于注册页面
@router.get("/public/subjects", response_model=List[school_schema.SubjectPublic])
async def get_public_subjects(
    school_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取公开科目列表，用于注册页面选择任教学科"""
    # 导入Subject模型
    from ..models.subject import Subject
    from ..models.subject import GradeSubject

    if school_id:
        # 如果指定了学校ID，根据学校的年级范围筛选科目
        # 1. 获取该学校的所有年级
        school_grades = db.query(Class.grade).filter(
            Class.school_id == school_id
        ).distinct().all()

        if school_grades:
            grade_list = [grade[0] for grade in school_grades if grade[0]]

            # 2. 根据年级获取对应的科目
            query = db.query(Subject).join(
                GradeSubject, Subject.id == GradeSubject.subject_id
            ).filter(
                Subject.is_active == True,
                GradeSubject.grade.in_(grade_list)
            ).distinct()
        else:
            # 如果学校没有班级，返回所有科目
            query = db.query(Subject).filter(Subject.is_active == True)
    else:
        # 如果没有指定学校ID，返回所有激活的科目
        query = db.query(Subject).filter(Subject.is_active == True)

    # 执行查询
    subjects = query.offset(skip).limit(limit).all()

    # 构建响应数据
    result = []
    for subject in subjects:
        subject_response = school_schema.SubjectPublic(
            id=subject.id,
            name=subject.name
        )
        result.append(subject_response)

    return result

# 地区相关API - 放在/{school_id}路径之前避免冲突
@router.get("/region/provinces")
async def get_provinces(db: Session = Depends(get_db)):
    """获取所有省份"""
    try:
        provinces = db.query(Province).order_by(Province.id).all()
        return [{"id": p.id, "name": p.name, "code": p.code} for p in provinces]
    except Exception as e:
        logger.error(f"获取省份列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取省份列表失败: {str(e)}")


@router.get("/region/cities")
async def get_cities(
    province_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """获取城市列表，可按省份筛选"""
    try:
        query = db.query(City)
        if province_id:
            query = query.filter(City.province_id == province_id)

        cities = query.order_by(City.id).all()
        return [{"id": c.id, "name": c.name, "code": c.code, "province_id": c.province_id} for c in cities]
    except Exception as e:
        logger.error(f"获取城市列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取城市列表失败: {str(e)}")


@router.get("/region/districts")
async def get_districts(
    city_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """获取区县列表，可按城市筛选"""
    try:
        query = db.query(District)
        if city_id:
            query = query.filter(District.city_id == city_id)

        districts = query.order_by(District.id).all()
        return [{"id": d.id, "name": d.name, "code": d.code, "city_id": d.city_id} for d in districts]
    except Exception as e:
        logger.error(f"获取区县列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取区县列表失败: {str(e)}")

# 获取学校列表
@router.get("/", response_model=List[school_schema.SchoolResponse])
async def get_schools(
    skip: int = 0, 
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取学校列表"""
    # 检查权限
    check_school_admin_permission(current_user)
    
    # 查询学校列表
    schools = db.query(School).offset(skip).limit(limit).all()
    
    # 构建响应数据，包含统计信息
    result = []
    for school in schools:
        # 查询统计数据
        class_count = db.query(func.count(Class.id)).filter(Class.school_id == school.id).scalar()
        teacher_count = db.query(func.count(User.id)).filter(
            User.school_id == school.id, 
            User.is_teacher == True
        ).scalar()
        student_count = db.query(func.count(User.id)).filter(
            User.school_id == school.id, 
            User.is_teacher == False,
            User.is_admin == False
        ).scalar()
        
        # 构建响应数据
        school_response = school_schema.SchoolResponse(
            id=school.id,
            name=school.name,
            province=school.province,
            city=school.city,
            district=school.district,
            address=school.address,
            contact_info=school.contact_info,
            is_active=school.is_active,
            created_at=school.created_at,
            class_count=class_count or 0,
            teacher_count=teacher_count or 0,
            student_count=student_count or 0
        )
        result.append(school_response)
    
    return result

# 获取学校详情
@router.get("/{school_id}", response_model=school_schema.SchoolResponse)
async def get_school(
    school_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取学校详情"""
    # 检查权限
    check_school_admin_permission(current_user)
    
    # 查询学校
    school = db.query(School).filter(School.id == school_id).first()
    if not school:
        raise HTTPException(status_code=404, detail="学校不存在")
    
    # 查询统计数据
    class_count = db.query(func.count(Class.id)).filter(Class.school_id == school_id).scalar()
    teacher_count = db.query(func.count(User.id)).filter(
        User.school_id == school_id, 
        User.is_teacher == True
    ).scalar()
    student_count = db.query(func.count(User.id)).filter(
        User.school_id == school_id, 
        User.is_teacher == False,
        User.is_admin == False
    ).scalar()
    
    # 构建响应数据
    result = school_schema.SchoolResponse(
        id=school.id,
        name=school.name,
        province=school.province,
        city=school.city,
        district=school.district,
        address=school.address,
        contact_info=school.contact_info,
        is_active=school.is_active,
        created_at=school.created_at,
        class_count=class_count or 0,
        teacher_count=teacher_count or 0,
        student_count=student_count or 0
    )
    
    return result

# 创建学校
@router.post("/", response_model=school_schema.SchoolResponse)
async def create_school(
    school: school_schema.SchoolCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建学校"""
    # 检查权限
    check_school_admin_permission(current_user)
    
    # 检查学校名称是否已存在
    existing_school = db.query(School).filter(School.name == school.name).first()
    if existing_school:
        raise HTTPException(status_code=400, detail="学校名称已存在")
    
    # 创建学校
    db_school = School(
        name=school.name,
        province=school.province,
        city=school.city,
        district=school.district,
        address=school.address,
        contact_info=school.contact_info,
        is_active=True
    )
    db.add(db_school)
    db.commit()
    db.refresh(db_school)
    
    # 构建响应数据
    result = school_schema.SchoolResponse(
        id=db_school.id,
        name=db_school.name,
        province=db_school.province,
        city=db_school.city,
        district=db_school.district,
        address=db_school.address,
        contact_info=db_school.contact_info,
        is_active=db_school.is_active,
        created_at=db_school.created_at,
        class_count=0,
        teacher_count=0,
        student_count=0
    )
    
    return result

# 更新学校
@router.put("/{school_id}", response_model=school_schema.SchoolResponse)
async def update_school(
    school_id: int,
    school: school_schema.SchoolUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新学校"""
    # 检查权限
    check_school_admin_permission(current_user)
    
    # 查询学校
    db_school = db.query(School).filter(School.id == school_id).first()
    if not db_school:
        raise HTTPException(status_code=404, detail="学校不存在")
    
    # 如果更新名称，检查名称是否已存在
    if school.name and school.name != db_school.name:
        existing_school = db.query(School).filter(School.name == school.name).first()
        if existing_school:
            raise HTTPException(status_code=400, detail="学校名称已存在")
    
    # 更新学校信息
    if school.name:
        db_school.name = school.name
    if school.address is not None:
        db_school.address = school.address
    if school.contact_info is not None:
        db_school.contact_info = school.contact_info
    if school.is_active is not None:
        db_school.is_active = school.is_active
    
    # 更新地理位置信息
    if school.province is not None:
        db_school.province = school.province
    if school.city is not None:
        db_school.city = school.city
    if school.district is not None:
        db_school.district = school.district
    
    db.commit()
    db.refresh(db_school)
    
    # 查询统计数据
    class_count = db.query(func.count(Class.id)).filter(Class.school_id == school_id).scalar()
    teacher_count = db.query(func.count(User.id)).filter(
        User.school_id == school_id, 
        User.is_teacher == True
    ).scalar()
    student_count = db.query(func.count(User.id)).filter(
        User.school_id == school_id, 
        User.is_teacher == False,
        User.is_admin == False
    ).scalar()
    
    # 构建响应数据
    result = school_schema.SchoolResponse(
        id=db_school.id,
        name=db_school.name,
        province=db_school.province,
        city=db_school.city,
        district=db_school.district,
        address=db_school.address,
        contact_info=db_school.contact_info,
        is_active=db_school.is_active,
        created_at=db_school.created_at,
        class_count=class_count or 0,
        teacher_count=teacher_count or 0,
        student_count=student_count or 0
    )
    
    return result

# 删除学校
@router.delete("/{school_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_school(
    school_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除学校"""
    # 检查权限
    check_school_admin_permission(current_user)
    
    # 查询学校
    db_school = db.query(School).filter(School.id == school_id).first()
    if not db_school:
        raise HTTPException(status_code=404, detail="学校不存在")
    
    # 检查学校是否有关联的班级或用户
    class_count = db.query(func.count(Class.id)).filter(Class.school_id == school_id).scalar()
    user_count = db.query(func.count(User.id)).filter(User.school_id == school_id).scalar()
    
    if class_count > 0 or user_count > 0:
        raise HTTPException(
            status_code=400, 
            detail="无法删除学校，该学校下还有关联的班级或用户"
        )
    
    # 删除学校
    db.delete(db_school)
    db.commit()
    
    return None
    
# 获取地区列表API
@router.get("/regions", response_model=Dict[str, List[str]])
async def get_regions(
    province: Optional[str] = None,
    city: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取地区列表（省份、城市、区县），用于级联选择"""
    result = {}
    
    # 如果没有传入省份，返回所有省份列表
    if not province:
        provinces = db.query(distinct(School.province)).filter(
            School.is_active == True,
            School.province != None,
            School.province != ""
        ).all()
        result["provinces"] = [p[0] for p in provinces if p[0]]
        return result
    
    # 如果传入了省份，返回该省的城市列表
    cities = db.query(distinct(School.city)).filter(
        School.is_active == True,
        School.province == province,
        School.city != None,
        School.city != ""
    ).all()
    result["cities"] = [c[0] for c in cities if c[0]]
    
    # 如果传入了城市，返回该城市的区县列表
    if city:
        districts = db.query(distinct(School.district)).filter(
            School.is_active == True,
            School.province == province,
            School.city == city,
            School.district != None,
            School.district != ""
        ).all()
        result["districts"] = [d[0] for d in districts if d[0]]
    
    return result

# 根据学校ID获取对应科目的API
@router.get("/subjects", response_model=List[subject_schema.Subject])
async def get_school_subjects(
    school_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """根据学校ID获取对应的科目列表"""
    try:
        if school_id:
            # 根据学校ID获取该学校的年级
            from ..models.user import Class
            from ..models.subject import GradeSubject

            school_grades = db.query(Class.grade).filter(
                Class.school_id == school_id,
                Class.grade.isnot(None)
            ).distinct().all()

            if school_grades:
                # 提取年级名称列表
                grade_list = [grade[0] for grade in school_grades if grade[0]]
                logger.info(f"学校ID {school_id} 的年级: {grade_list}")

                # 根据年级获取对应的科目
                subjects = db.query(Subject).join(
                    GradeSubject, Subject.id == GradeSubject.subject_id
                ).filter(
                    Subject.is_active == True,
                    GradeSubject.grade.in_(grade_list)
                ).distinct().offset(skip).limit(limit).all()

                logger.info(f"学校ID {school_id} 对应的科目数量: {len(subjects)}")
                return subjects
            else:
                logger.warning(f"学校ID {school_id} 没有找到年级信息，返回所有科目")

        # 如果没有指定学校ID或没有找到年级信息，返回所有激活的科目
        subjects = db.query(Subject).filter(Subject.is_active == True).offset(skip).limit(limit).all()
        return subjects
    except Exception as e:
        logger.error(f"获取学校科目列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取科目列表失败: {str(e)}")


@router.get("/{school_id}/classes", response_model=List[school_schema.ClassWithStudentCount])
async def get_school_classes(
    school_id: int,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取学校的班级列表
    """
    try:
        # 检查学校是否存在
        school = db.query(School).filter(School.id == school_id).first()
        if not school:
            raise HTTPException(status_code=404, detail="学校不存在")
        
        # 查询班级
        classes = db.query(Class).filter(Class.school_id == school_id).offset(skip).limit(limit).all()
        
        # 获取每个班级的学生数量
        result = []
        for class_ in classes:
            # 统计学生数量
            student_count = db.query(func.count(User.id)).filter(
                User.class_id == class_.id,
                User.role == "学生"
            ).scalar()
            
            # 构建响应
            class_response = school_schema.ClassWithStudentCount(
                id=class_.id,
                name=class_.name,
                grade=class_.grade,
                school_id=class_.school_id,
                created_at=class_.created_at,
                student_count=student_count or 0
            )
            result.append(class_response)
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取班级列表失败: {str(e)}")

@router.post("/{school_id}/classes", response_model=school_schema.ClassDetail)
async def create_class(
    school_id: int,
    class_data: school_schema.ClassCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建班级
    """
    try:
        # 检查学校是否存在
        school = db.query(School).filter(School.id == school_id).first()
        if not school:
            raise HTTPException(status_code=404, detail="学校不存在")
        
        # 检查是否有权限创建班级
        if not current_user.is_admin and current_user.school_id != school_id:
            raise HTTPException(status_code=403, detail="没有权限在此学校创建班级")
        
        # 创建班级
        new_class = Class(
            name=class_data.name,
            grade=class_data.grade,
            school_id=school_id
        )
        
        db.add(new_class)
        db.commit()
        db.refresh(new_class)
        
        return new_class
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建班级失败: {str(e)}")

@router.put("/{school_id}/classes/{class_id}", response_model=school_schema.ClassDetail)
async def update_class(
    school_id: int,
    class_id: int,
    class_data: school_schema.ClassUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    更新班级信息
    """
    try:
        # 检查班级是否存在
        class_ = db.query(Class).filter(
            Class.id == class_id,
            Class.school_id == school_id
        ).first()
        
        if not class_:
            raise HTTPException(status_code=404, detail="班级不存在或不属于指定学校")
        
        # 检查是否有权限更新班级
        if not current_user.is_admin and current_user.school_id != school_id:
            raise HTTPException(status_code=403, detail="没有权限更新此班级")
        
        # 更新班级信息
        if class_data.name is not None:
            class_.name = class_data.name
        
        if class_data.grade is not None:
            class_.grade = class_data.grade
        
        db.commit()
        db.refresh(class_)
        
        return class_
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新班级失败: {str(e)}")

@router.delete("/{school_id}/classes/{class_id}", response_model=school_schema.Message)
async def delete_class(
    school_id: int,
    class_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除班级
    """
    try:
        # 检查班级是否存在
        class_ = db.query(Class).filter(
            Class.id == class_id,
            Class.school_id == school_id
        ).first()
        
        if not class_:
            raise HTTPException(status_code=404, detail="班级不存在或不属于指定学校")
        
        # 检查是否有权限删除班级
        if not current_user.is_admin and current_user.school_id != school_id:
            raise HTTPException(status_code=403, detail="没有权限删除此班级")
        
        # 检查班级是否有关联的学生或教师
        student_count = db.query(func.count(User.id)).filter(User.class_id == class_id).scalar()
        if student_count > 0:
            raise HTTPException(status_code=400, detail="班级中还有学生，无法删除")
        
        # 删除班级
        db.delete(class_)
        db.commit()
        
        return {"message": "班级删除成功"}
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除班级失败: {str(e)}")

# 添加额外的API路径，以兼容现有前端组件
@router.get("/admin/schools/{school_id}/classes", response_model=List[school_schema.ClassWithStudentCount])
async def get_admin_school_classes(
    school_id: int,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    超级管理员获取指定学校的班级列表
    """
    # 检查用户是否为管理员
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="只有管理员可以访问此接口")
    
    # 复用现有的获取班级列表逻辑
    return await get_school_classes(school_id, skip, limit, current_user, db)

@router.post("/admin/classes", response_model=school_schema.ClassDetail)
async def create_admin_class(
    class_data: school_schema.ClassCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    超级管理员创建班级
    """
    # 检查用户是否为管理员
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="只有管理员可以访问此接口")
    
    # 复用现有的创建班级逻辑
    return await create_class(class_data.school_id, class_data, current_user, db)

@router.put("/admin/classes/{class_id}", response_model=school_schema.ClassDetail)
async def update_admin_class(
    class_id: int,
    class_data: school_schema.ClassUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    超级管理员更新班级信息
    """
    # 检查用户是否为管理员
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="只有管理员可以访问此接口")
    
    # 获取班级信息
    class_ = db.query(Class).filter(Class.id == class_id).first()
    if not class_:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 复用现有的更新班级逻辑
    return await update_class(class_.school_id, class_id, class_data, current_user, db)

@router.delete("/admin/classes/{class_id}", response_model=school_schema.Message)
async def delete_admin_class(
    class_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    超级管理员删除班级
    """
    # 检查用户是否为管理员
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="只有管理员可以访问此接口")
    
    # 获取班级信息
    class_ = db.query(Class).filter(Class.id == class_id).first()
    if not class_:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 复用现有的删除班级逻辑
    return await delete_class(class_.school_id, class_id, current_user, db)

# 添加获取年级列表的API
@router.get("/admin/classes/grades", response_model=List[str])
async def get_available_grades(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取所有可用的年级列表
    """
    try:
        # 从数据库中获取所有已使用的年级
        grades = db.query(Class.grade).distinct().all()
        
        # 提取年级名称
        grade_names = [grade[0] for grade in grades if grade[0]]
        
        # 如果没有年级数据，返回默认年级列表
        if not grade_names:
            return [
                "小学一年级", "小学二年级", "小学三年级", 
                "小学四年级", "小学五年级", "小学六年级",
                "初中一年级", "初中二年级", "初中三年级",
                "高中一年级", "高中二年级", "高中三年级"
            ]
        
        return grade_names
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取年级列表失败: {str(e)}")

# 添加教师查看班级学生的API
@router.get("/{school_id}/classes/{class_id}/students", response_model=List[user_schema.User])
async def get_class_students(
    school_id: int,
    class_id: int,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取指定班级的学生列表，教师可以查看自己任教的班级
    """
    try:
        # 检查学校是否存在
        school = db.query(School).filter(School.id == school_id).first()
        if not school:
            raise HTTPException(status_code=404, detail="学校不存在")
        
        # 检查班级是否存在
        class_ = db.query(Class).filter(Class.id == class_id, Class.school_id == school_id).first()
        if not class_:
            raise HTTPException(status_code=404, detail="班级不存在或不属于指定学校")
        
        # 检查权限
        is_admin = current_user.is_admin
        is_school_admin = current_user.is_admin and current_user.school_id == school_id
        is_class_teacher = db.query(ClassTeacher).filter(
            ClassTeacher.class_id == class_id,
            ClassTeacher.teacher_id == current_user.id
        ).first() is not None
        
        # 只有管理员、学校管理员或班级教师可以查看
        if not (is_admin or is_school_admin or is_class_teacher):
            raise HTTPException(status_code=403, detail="没有权限查看此班级的学生")
        
        # 查询学生
        query = db.query(User).filter(
            User.class_id == class_id,
            User.role == "学生"
        )
        
        # 计算总数
        total = query.count()
        
        # 分页处理
        students = query.offset(skip).limit(limit).all()
        
        return students
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取班级学生列表失败: {str(e)}")