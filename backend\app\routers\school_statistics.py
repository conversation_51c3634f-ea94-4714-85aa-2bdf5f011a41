from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

from ..database import get_db
from ..models.user import User, Class, ClassTeacher, ClassStudent
from ..models.homework import Homework, HomeworkAssignment
from ..models.school import School
from ..routers.auth import get_current_user

router = APIRouter()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_user_school_filter(current_user: User, db: Session):
    """获取用户学校过滤条件"""
    if not current_user.school_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户未分配学校，无法查看统计数据"
        )
    
    # 获取学校信息
    school = db.query(School).filter(School.id == current_user.school_id).first()
    if not school:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户所属学校不存在"
        )
    
    return current_user.school_id, school.name

def get_teacher_class_filter(current_user: User, db: Session):
    """获取教师班级过滤条件"""
    if current_user.is_admin:
        # 管理员可以看到学校所有班级
        return db.query(Class).filter(Class.school_id == current_user.school_id).all()
    elif current_user.is_teacher:
        # 教师只能看到自己负责的班级
        return db.query(Class).join(
            ClassTeacher, Class.id == ClassTeacher.class_id
        ).filter(
            ClassTeacher.teacher_id == current_user.id,
            Class.school_id == current_user.school_id
        ).all()
    else:
        # 普通用户无法访问
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有教师和管理员可以查看统计数据"
        )

@router.get("/school_statistics/teacher")
async def get_school_teacher_statistics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户所属学校的教师统计数据"""
    try:
        logger.info(f"用户 {current_user.username} (ID: {current_user.id}) 请求学校教师统计数据")
        
        # 检查权限
        if not current_user.is_admin and not current_user.is_teacher:
            logger.warning(f"用户 {current_user.username} 尝试访问教师统计数据，权限不足")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有教师和管理员可以查看统计数据"
            )
        
        # 获取学校信息
        school_id, school_name = get_user_school_filter(current_user, db)
        logger.info(f"用户所属学校: {school_name} (ID: {school_id})")
        
        # 获取教师管理的班级
        teacher_classes = get_teacher_class_filter(current_user, db)
        class_ids = [cls.id for cls in teacher_classes]
        
        # 统计学校内班级数量
        if current_user.is_admin:
            class_count = db.query(func.count(Class.id)).filter(
                Class.school_id == school_id
            ).scalar() or 0
        else:
            class_count = len(teacher_classes)
        
        # 统计学校内学生数量
        if current_user.is_admin:
            student_count = db.query(func.count(User.id)).filter(
                User.is_teacher == False,
                User.is_admin == False,
                User.school_id == school_id
            ).scalar() or 0
        else:
            student_count = db.query(func.count(func.distinct(ClassStudent.student_id))).filter(
                ClassStudent.class_id.in_(class_ids) if class_ids else False
            ).scalar() or 0
        
        # 统计学校内作业数量
        if current_user.is_admin:
            homework_count = db.query(func.count(Homework.id)).filter(
                Homework.class_id.in_(
                    db.query(Class.id).filter(Class.school_id == school_id).subquery()
                )
            ).scalar() or 0
        else:
            homework_count = db.query(func.count(Homework.id)).filter(
                Homework.class_id.in_(class_ids) if class_ids else False
            ).scalar() or 0
        
        # 统计已批改作业数量
        if current_user.is_admin:
            corrected_count = db.query(func.count(Homework.id)).filter(
                Homework.status == "graded",
                Homework.class_id.in_(
                    db.query(Class.id).filter(Class.school_id == school_id).subquery()
                )
            ).scalar() or 0
        else:
            corrected_count = db.query(func.count(Homework.id)).filter(
                Homework.class_id.in_(class_ids) if class_ids else False,
                Homework.status == "graded"
            ).scalar() or 0
        
        # 计算平均分数
        if current_user.is_admin:
            average_score = db.query(func.avg(Homework.score)).filter(
                Homework.status == "graded",
                Homework.score.isnot(None),
                Homework.class_id.in_(
                    db.query(Class.id).filter(Class.school_id == school_id).subquery()
                )
            ).scalar() or 0
        else:
            average_score = db.query(func.avg(Homework.score)).filter(
                Homework.class_id.in_(class_ids) if class_ids else False,
                Homework.status == "graded",
                Homework.score.isnot(None)
            ).scalar() or 0
        
        # 计算平均正确率
        if current_user.is_admin:
            average_accuracy = db.query(func.avg(Homework.accuracy)).filter(
                Homework.status == "graded",
                Homework.accuracy.isnot(None),
                Homework.class_id.in_(
                    db.query(Class.id).filter(Class.school_id == school_id).subquery()
                )
            ).scalar() or 0.0
        else:
            average_accuracy = db.query(func.avg(Homework.accuracy)).filter(
                Homework.class_id.in_(class_ids) if class_ids else False,
                Homework.status == "graded",
                Homework.accuracy.isnot(None)
            ).scalar() or 0.0
        
        # 获取最近作业
        if current_user.is_admin:
            # 管理员查看学校内所有最近作业任务
            recent_assignments = db.query(HomeworkAssignment).filter(
                HomeworkAssignment.school_id == school_id
            ).order_by(HomeworkAssignment.created_at.desc()).limit(5).all()
        else:
            # 教师只查看自己班级的最近作业任务
            recent_assignments = db.query(HomeworkAssignment).filter(
                HomeworkAssignment.class_id.in_(class_ids) if class_ids else False
            ).order_by(HomeworkAssignment.created_at.desc()).limit(5).all()
        
        # 格式化最近作业数据
        recent_assignments_data = []
        for assignment in recent_assignments:
            # 获取该作业任务的提交数量
            submission_count = db.query(func.count(Homework.id)).filter(
                Homework.assignment_id == assignment.id
            ).scalar() or 0
            
            # 获取该作业任务的平均分
            assignment_avg_score = db.query(func.avg(Homework.score)).filter(
                Homework.assignment_id == assignment.id,
                Homework.status == "graded",
                Homework.score.isnot(None)
            ).scalar() or 0
            
            # 获取班级名称
            class_name = ""
            if assignment.class_id:
                class_query = db.query(Class).filter(Class.id == assignment.class_id).first()
                if class_query:
                    class_name = class_query.name
            
            # 获取教师名称
            teacher_name = ""
            if assignment.teacher_id:
                teacher = db.query(User).filter(User.id == assignment.teacher_id).first()
                if teacher:
                    teacher_name = teacher.full_name or teacher.username
            
            # 获取科目名称
            subject_name = ""
            if assignment.subject_id:
                from ..models.subject import Subject
                subject = db.query(Subject).filter(Subject.id == assignment.subject_id).first()
                if subject:
                    subject_name = subject.name
            
            # 计算提交率
            # 获取该班级的学生数量
            student_count = db.query(func.count(func.distinct(ClassStudent.student_id))).filter(
                ClassStudent.class_id == assignment.class_id
            ).scalar() or 0
            
            submission_rate = 0
            if student_count > 0:
                submission_rate = submission_count / student_count
            
            recent_assignments_data.append({
                "id": assignment.id,
                "title": assignment.title,
                "class_name": class_name,
                "teacher_name": teacher_name,
                "subject_name": subject_name,
                "submission_count": submission_count,
                "student_count": student_count,
                "submission_rate": round(submission_rate, 2),
                "average_score": round(float(assignment_avg_score), 1),
                "created_at": assignment.created_at.isoformat() if assignment.created_at else None
            })
        
        # 统计学校内作业任务数量
        if current_user.is_admin:
            assignment_count = db.query(func.count(HomeworkAssignment.id)).filter(
                HomeworkAssignment.school_id == school_id
            ).scalar() or 0
        else:
            assignment_count = db.query(func.count(HomeworkAssignment.id)).filter(
                HomeworkAssignment.class_id.in_(class_ids) if class_ids else False
            ).scalar() or 0
        
        # 获取班级统计数据
        class_statistics = {}
        for cls in teacher_classes:
            cls_homework_count = db.query(func.count(Homework.id)).filter(
                Homework.class_id == cls.id
            ).scalar() or 0
            
            cls_corrected_count = db.query(func.count(Homework.id)).filter(
                Homework.class_id == cls.id,
                Homework.status == "graded"
            ).scalar() or 0
            
            cls_student_count = db.query(func.count(func.distinct(ClassStudent.student_id))).filter(
                ClassStudent.class_id == cls.id
            ).scalar() or 0
            
            # 获取班级作业任务数量
            cls_assignment_count = db.query(func.count(HomeworkAssignment.id)).filter(
                HomeworkAssignment.class_id == cls.id
            ).scalar() or 0
            
            # 计算班级平均分
            cls_avg_score = db.query(func.avg(Homework.score)).filter(
                Homework.class_id == cls.id,
                Homework.status == "graded",
                Homework.score.isnot(None)
            ).scalar() or 0
            
            # 计算班级作业提交率
            total_possible = cls_student_count * cls_assignment_count
            submitted_count = cls_homework_count
            submission_rate = submitted_count / total_possible if total_possible > 0 else 0

            
            # 打印日志，便于调试
            logger.info(f"班级统计数据 - 班级ID: {cls.id}, 名称: {cls.name}")
            logger.info(f"  作业任务数: {cls_assignment_count}")
            logger.info(f"  作业提交数: {cls_homework_count}")
            logger.info(f"  已批改作业数: {cls_corrected_count}")
            logger.info(f"  平均分: {cls_avg_score}")
            
            # 使用class_id作为键，创建字典
            key = f"class_{cls.id}"
            class_statistics[key] = {
                "class_id": cls.id,
                "class_name": cls.name,
                "homework_count": cls_homework_count,
                "corrected_count": cls_corrected_count,
                "student_count": cls_student_count,
                "assignment_count": cls_assignment_count,
                "average_score": round(float(cls_avg_score), 1),
                "submission_rate": round(submission_rate, 2)
            }
        
        return {
            "school_id": school_id,
            "school_name": school_name,
            "assignment_count": assignment_count,
            "class_count": class_count,
            "student_count": student_count,
            "homework_count": homework_count,
            "corrected_count": corrected_count,
            "average_score": float(average_score) if average_score else 0,
            "average_accuracy": float(average_accuracy) if average_accuracy else 0,
            "class_statistics": class_statistics,
            "recent_assignments": recent_assignments_data,
            # 添加班级名称列表以保持前后端一致
            "class_names": [cls.name for cls in teacher_classes]
        }
    except Exception as e:
        logger.error(f"获取学校教师统计数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计数据失败: {str(e)}"
        )

@router.get("/school_statistics/dashboard")
async def get_school_dashboard_statistics(
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    class_id: Optional[int] = Query(None, description="班级ID，用于筛选特定班级的统计数据"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户所属学校的仪表盘统计数据"""
    try:
        logger.info(f"用户 {current_user.username} (ID: {current_user.id}) 请求学校仪表盘统计数据")
        
        # 检查权限
        if not current_user.is_admin and not current_user.is_teacher:
            logger.warning(f"用户 {current_user.username} 尝试访问仪表盘统计数据，权限不足")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有教师和管理员可以查看统计数据"
            )
        
        # 获取学校信息
        school_id, school_name = get_user_school_filter(current_user, db)
        logger.info(f"用户所属学校: {school_name} (ID: {school_id})")
        
        # 处理日期筛选
        date_filter_applied = False
        start_date_obj = None
        end_date_obj = None
        
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
                date_filter_applied = True
            except ValueError:
                logger.warning(f"日期格式错误: {start_date}")
                raise HTTPException(status_code=400, detail="开始日期格式无效，请使用YYYY-MM-DD格式")
        
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
                end_date_obj = end_date_obj + timedelta(days=1, microseconds=-1)
                date_filter_applied = True
            except ValueError:
                logger.warning(f"日期格式错误: {end_date}")
                raise HTTPException(status_code=400, detail="结束日期格式无效，请使用YYYY-MM-DD格式")
        
        # 获取教师管理的班级
        teacher_classes = get_teacher_class_filter(current_user, db)
        class_ids = [cls.id for cls in teacher_classes]
        
        # 统计班级总数
        if current_user.is_admin:
            class_count = db.query(func.count(Class.id)).filter(
                Class.school_id == school_id
            ).scalar() or 0
        else:
            class_count = len(teacher_classes)
        
        # 应用班级筛选
        class_filter_applied = class_id is not None
        if class_filter_applied:
            if class_id not in class_ids:
                raise HTTPException(status_code=403, detail=f"无权访问班级ID {class_id}")
            class_ids = [class_id]
        
        # 构建基础查询条件
        base_conditions = []
        if class_ids:
            base_conditions.append(Homework.class_id.in_(class_ids))
        
        if start_date_obj:
            base_conditions.append(Homework.created_at >= start_date_obj)
        
        if end_date_obj:
            base_conditions.append(Homework.created_at <= end_date_obj)
        
        # 统计总作业数
        total_homework = db.query(func.count(Homework.id)).filter(*base_conditions).scalar() or 0
        
        # 统计已批改作业数
        graded_conditions = base_conditions + [Homework.status == "graded"]
        graded_homework = db.query(func.count(Homework.id)).filter(*graded_conditions).scalar() or 0
        
        # 统计待批改作业数
        pending_conditions = base_conditions + [Homework.status == "submitted"]
        pending_homework = db.query(func.count(Homework.id)).filter(*pending_conditions).scalar() or 0
        
        # 计算平均分数
        score_conditions = base_conditions + [
            Homework.status == "graded",
            Homework.score.isnot(None)
        ]
        average_score = db.query(func.avg(Homework.score)).filter(*score_conditions).scalar() or 0
        
        # 计算平均正确率
        accuracy_conditions = base_conditions + [
            Homework.status == "graded",
            Homework.accuracy.isnot(None)
        ]
        average_accuracy = db.query(func.avg(Homework.accuracy)).filter(*accuracy_conditions).scalar() or 0
        
        # 统计学生数量
        if current_user.is_admin:
            student_count = db.query(func.count(User.id)).filter(
                User.is_teacher == False,
                User.is_admin == False,
                User.school_id == school_id
            ).scalar() or 0
        else:
            student_count = db.query(func.count(func.distinct(ClassStudent.student_id))).filter(
                ClassStudent.class_id.in_(class_ids) if class_ids else False
            ).scalar() or 0
        
        # 统计教师数量
        if current_user.is_admin:
            teacher_count = db.query(func.count(User.id)).filter(
                User.is_teacher == True,
                User.school_id == school_id
            ).scalar() or 0
        else:
            teacher_count = 1
        
        # 获取最近活动
        recent_activities = []
        recent_homeworks = db.query(Homework).filter(*base_conditions).order_by(
            Homework.created_at.desc()
        ).limit(10).all()
        
        for homework in recent_homeworks:
            recent_activities.append({
                "type": "homework",
                "id": homework.id,
                "title": homework.title,
                "status": homework.status,
                "created_at": homework.created_at.isoformat() if homework.created_at else None
            })
        
        # 按状态统计作业
        status_stats = {}
        status_query = db.query(
            Homework.status,
            func.count(Homework.id)
        ).filter(*base_conditions).group_by(Homework.status)
        
        for status, count in status_query.all():
            status_stats[status] = count
        
        return {
            "school_id": school_id,
            "school_name": school_name,
            "school_count": 1,  # 学校级统计只显示1所学校
            "total_homework": total_homework,
            "graded_homework": graded_homework,
            "pending_homework": pending_homework,
            "student_count": student_count,
            "teacher_count": teacher_count,
            "class_count": class_count,
            # 统一使用class_count字段
            "class_count": class_count,
            "average_score": float(average_score) if average_score else 0,
            "average_accuracy": float(average_accuracy) if average_accuracy else 0,
            "status_statistics": status_stats,
            "recent_activities": recent_activities,
            "filters_applied": {
                "date_filter": date_filter_applied,
                "start_date": start_date,
                "end_date": end_date,
                "class_filter": class_filter_applied,
                "class_id": class_id
            }
        }
    except Exception as e:
        logger.error(f"获取学校仪表盘统计数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计数据失败: {str(e)}"
        )