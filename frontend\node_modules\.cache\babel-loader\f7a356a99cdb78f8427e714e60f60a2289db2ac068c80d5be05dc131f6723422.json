{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport EnterOutlined from \"@ant-design/icons/es/icons/EnterOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { cloneElement } from '../_util/reactNode';\nimport TextArea from '../input/TextArea';\nimport useStyle from './style';\nconst Editable = props => {\n  const {\n    prefixCls,\n    'aria-label': ariaLabel,\n    className,\n    style,\n    direction,\n    maxLength,\n    autoSize = true,\n    value,\n    onSave,\n    onCancel,\n    onEnd,\n    component,\n    enterIcon = /*#__PURE__*/React.createElement(EnterOutlined, null)\n  } = props;\n  const ref = React.useRef(null);\n  const inComposition = React.useRef(false);\n  const lastKeyCode = React.useRef(null);\n  const [current, setCurrent] = React.useState(value);\n  React.useEffect(() => {\n    setCurrent(value);\n  }, [value]);\n  React.useEffect(() => {\n    var _a;\n    if ((_a = ref.current) === null || _a === void 0 ? void 0 : _a.resizableTextArea) {\n      const {\n        textArea\n      } = ref.current.resizableTextArea;\n      textArea.focus();\n      const {\n        length\n      } = textArea.value;\n      textArea.setSelectionRange(length, length);\n    }\n  }, []);\n  const onChange = ({\n    target\n  }) => {\n    setCurrent(target.value.replace(/[\\n\\r]/g, ''));\n  };\n  const onCompositionStart = () => {\n    inComposition.current = true;\n  };\n  const onCompositionEnd = () => {\n    inComposition.current = false;\n  };\n  const onKeyDown = ({\n    keyCode\n  }) => {\n    // We don't record keyCode when IME is using\n    if (inComposition.current) return;\n    lastKeyCode.current = keyCode;\n  };\n  const confirmChange = () => {\n    onSave(current.trim());\n  };\n  const onKeyUp = ({\n    keyCode,\n    ctrlKey,\n    altKey,\n    metaKey,\n    shiftKey\n  }) => {\n    // Check if it's a real key\n    if (lastKeyCode.current !== keyCode || inComposition.current || ctrlKey || altKey || metaKey || shiftKey) {\n      return;\n    }\n    if (keyCode === KeyCode.ENTER) {\n      confirmChange();\n      onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n    } else if (keyCode === KeyCode.ESC) {\n      onCancel();\n    }\n  };\n  const onBlur = () => {\n    confirmChange();\n  };\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const textAreaClassName = classNames(prefixCls, `${prefixCls}-edit-content`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-${component}`]: !!component\n  }, className, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: textAreaClassName,\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, {\n    ref: ref,\n    maxLength: maxLength,\n    value: current,\n    onChange: onChange,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBlur: onBlur,\n    \"aria-label\": ariaLabel,\n    rows: 1,\n    autoSize: autoSize\n  }), enterIcon !== null ? cloneElement(enterIcon, {\n    className: `${prefixCls}-edit-content-confirm`\n  }) : null));\n};\nexport default Editable;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}