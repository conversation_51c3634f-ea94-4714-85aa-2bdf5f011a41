import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import {
  Layout, Menu, Dropdown, Avatar, Button, Space,
  Typography, Divider, Drawer
} from 'antd';
import {
  UserOutlined, HomeOutlined, BookOutlined, SettingOutlined,
  LogoutOutlined, Bar<PERSON>hartOutlined, TeamOutlined, ExperimentOutlined,
  MessageOutlined, FileTextOutlined, GlobalOutlined, LineChartOutlined,
  CameraOutlined, MenuFoldOutlined, MenuUnfoldOutlined,
  UsergroupAddOutlined, BankOutlined, DatabaseOutlined, DashboardOutlined,
  CloudUploadOutlined, EnvironmentOutlined
} from '@ant-design/icons';
import { useAuth } from '../utils/auth';
import { getUserRoleTags, isSuperAdmin, hasRoleLevel } from '../utils/roleUtils';
import api from '../utils/api';
import AIAssistant from './AIAssistant';

const { Header, Content, Sider } = Layout;
const { Title } = Typography;

const AppLayout = ({ children, user, onLogout, pageTitle }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [aiDrawerVisible, setAiDrawerVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  const location = useLocation();
  const navigate = useNavigate();



  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      if (mobile) {
        setCollapsed(true);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // 初始化

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLogout = () => {
    if (onLogout) {
      onLogout();
    }
    navigate('/login');
  };

  const showAIAssistant = () => {
    setAiDrawerVisible(true);
  };

  const closeAIAssistant = () => {
    setAiDrawerVisible(false);
  };

  const items = [
    {
      label: '个人中心',
      key: 'profile',
      icon: <UserOutlined />,
      onClick: () => navigate('/profile')
    },
    // 只有教师和管理员才显示统计数据
    ...(user && (user.is_teacher || user.is_admin) ? [{
      label: '统计数据',
      key: 'statistics',
      icon: <BarChartOutlined />,
      onClick: () => navigate('/profile/statistics')
    }] : []),
    {
      label: '退出登录',
      key: 'logout',
      icon: <LogoutOutlined />,
      onClick: handleLogout
    },
  ];

  // 根据用户类型生成不同的菜单
  const menuItems = (user?.role === 'parent' || user?.role === '家长')
    ? [
        // 家长专用菜单
        {
          key: '/',
          icon: <HomeOutlined />,
          label: '首页',
          onClick: () => navigate('/')
        },
        {
          key: '/parent',
          icon: <UserOutlined />,
          label: '我的孩子',
          onClick: () => navigate('/parent')
        },
        {
          key: '/parent/homework',
          icon: <BookOutlined />,
          label: '作业详情',
          onClick: async () => {
            try {
              // 获取绑定的学生列表
              const response = await api.get('/parent/bound-students');
              const students = response.data || [];

              if (students.length === 1) {
                // 只有一个孩子，直接跳转到该孩子的作业详情
                const student = students[0];
                navigate(`/parent/student/${student.student_id}/homework`, {
                  state: {
                    studentName: student.student_name,
                    studentId: student.student_id
                  }
                });
              } else {
                // 多个孩子或没有孩子，跳转到孩子选择页面
                navigate('/parent');
              }
            } catch (error) {
              console.error('获取学生列表失败:', error);
              // 出错时跳转到孩子选择页面
              navigate('/parent');
            }
          }
        },
        {
          key: '/parent/report',
          icon: <BarChartOutlined />,
          label: '学习报告',
          onClick: async () => {
            try {
              // 获取绑定的学生列表
              const response = await api.get('/parent/bound-students');
              const students = response.data || [];

              if (students.length === 1) {
                // 只有一个孩子，直接跳转到该孩子的学习报告
                const student = students[0];
                navigate(`/parent/student/${student.student_id}/report`, {
                  state: {
                    studentName: student.student_name,
                    studentId: student.student_id
                  }
                });
              } else {
                // 多个孩子或没有孩子，跳转到孩子选择页面
                navigate('/parent');
              }
            } catch (error) {
              console.error('获取学生列表失败:', error);
              // 出错时跳转到孩子选择页面
              navigate('/parent');
            }
          }
        }
      ]
    : user && (user.is_teacher || user.is_admin)
    ? [
        // 教师和管理员菜单
        {
          key: '/',
          icon: <HomeOutlined />,
          label: '首页',
          onClick: () => navigate('/')
        },
        {
          key: '/homework',
          icon: <BookOutlined />,
          label: '作业管理',
          onClick: () => navigate('/homework')
        },
        {
          key: '/training',
          icon: <ExperimentOutlined />,
          label: '错题训练',
          onClick: () => navigate('/training')
        },
        {
          key: '/statistics',
          icon: <BarChartOutlined />,
          label: '统计报表',
          onClick: () => navigate('/statistics')
        }
      ]
    : [
        // 学生菜单
        {
          key: '/',
          icon: <HomeOutlined />,
          label: '首页',
          onClick: () => navigate('/')
        },
        {
          key: '/homework',
          icon: <FileTextOutlined />,
          label: '作业任务',
          onClick: () => navigate('/homework')
        },
        {
          key: '/homework/submit',
          icon: <CloudUploadOutlined />,
          label: '提交作业',
          onClick: () => navigate('/homework/submit')
        },
        {
          key: '/homework/review',
          icon: <MessageOutlined />,
          label: '作业点评',
          onClick: () => navigate('/homework/review')
        },
        {
          key: '/homework/history',
          icon: <FileTextOutlined />,
          label: '往日作业',
          onClick: () => navigate('/homework/history')
        },
        {
          key: '/photo-solve',
          icon: <CameraOutlined />,
          label: '拍照解题',
          onClick: () => navigate('/photo-solve')
        },
        {
          key: '/training',
          icon: <ExperimentOutlined />,
          label: '错题训练',
          onClick: () => navigate('/training')
        }
      ];

  // 如果是教师或管理员，添加班级管理和作业分析菜单
  if (user && (user.is_teacher || user.is_admin)) {
    menuItems.push(
      {
        key: '/class-management',
        icon: <TeamOutlined />,
        label: '班级信息',
        onClick: () => navigate('/class-management')
      },
      {
        key: '/homework-analysis',
        icon: <DashboardOutlined />,
        label: '作业分析',
        onClick: () => navigate('/homework-analysis')
      }
    );
  }

  // 系统级菜单：班主任及以上角色可以看到（级别40以上）
  if (user && hasRoleLevel(user, 40)) {

    // 系统作业管理
    menuItems.push({
      key: '/system-homework',
      icon: <FileTextOutlined />,
      label: '系统作业管理',
      onClick: () => navigate('/system-homework')
    });

    // 系统错题训练
    menuItems.push({
      key: '/system-training',
      icon: <GlobalOutlined />,
      label: '系统错题训练',
      onClick: () => navigate('/system-training')
    });

    // 系统统计报表
    menuItems.push({
      key: '/system-statistics',
      icon: <LineChartOutlined />,
      label: '系统统计报表',
      onClick: () => navigate('/system-statistics')
    });
    
    // 系统作业分析
    menuItems.push({
      key: '/system-homework-analysis',
      icon: <DashboardOutlined />,
      label: '系统作业分析',
      onClick: () => navigate('/system-homework-analysis')
    });

    // 系统班级管理
    menuItems.push({
      key: '/system-class',
      icon: <TeamOutlined />,
      label: '系统班级管理',
      onClick: () => navigate('/system-class')
    });

    // 系统用户管理
    menuItems.push({
      key: '/system-user',
      icon: <UsergroupAddOutlined />,
      label: '系统用户管理',
      onClick: () => navigate('/system-user')
    });

    // 系统学校管理
    menuItems.push({
      key: '/schools',
      icon: <BankOutlined />,
      label: '系统学校管理',
      onClick: () => navigate('/schools')
    });

    // 地区管理（只有超级管理员能看到）
    if (isSuperAdmin(user)) {
      menuItems.push({
        key: '/region-management',
        icon: <EnvironmentOutlined />,
        label: '地区管理',
        onClick: () => navigate('/region-management')
      });
    }

    // 数据库管理（只有超级管理员能看到）
    if (isSuperAdmin(user)) {
      menuItems.push({
        key: '/database-management',
        icon: <DatabaseOutlined />,
        label: '数据库管理',
        onClick: () => navigate('/database-management')
      });
    }
  }

  // 超级管理员专有菜单（只有超级管理员能看到的特殊功能）
  if (user && isSuperAdmin(user)) {
    // 系统管理（超级管理员专有）
    menuItems.push({
      key: '/admin',
      icon: <SettingOutlined />,
      label: '系统管理',
      onClick: () => navigate('/admin')
    });
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header className="app-header" style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        padding: '0 24px',
        background: '#fff',
        boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)'
      }}>
        <div className="logo" style={{ display: 'flex', alignItems: 'center' }}>
          <Link to="/" style={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}>
            <img 
              src="/logo192.png" 
              alt="智教云端" 
              style={{ height: '32px', marginRight: '10px' }} 
            />
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>智教云端</Title>
          </Link>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {isMobile && (
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ marginRight: '16px' }}
            />
          )}

          <Button
            type="text"
            icon={<MessageOutlined />}
            onClick={showAIAssistant}
            style={{ marginRight: '16px' }}
          >
            {isMobile ? '' : 'AI助手'}
          </Button>
          
          <Dropdown menu={{ items }} placement="bottomRight">
            <Space>
              <Avatar
                style={{ backgroundColor: '#1890ff' }}
                icon={<UserOutlined />}
              />
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                lineHeight: '1.2',
                minWidth: '80px'
              }}>
                <span style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  marginBottom: '2px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  maxWidth: '120px'
                }}>
                  {user?.full_name || user?.username || '用户'}
                </span>
                <div style={{
                  fontSize: '11px',
                  color: '#666',
                  lineHeight: '1.1'
                }}>
                  {getUserRoleTags(user)}
                </div>
              </div>
            </Space>
          </Dropdown>
        </div>
      </Header>
      
      <Layout>
        {isMobile ? (
          <Drawer
            title="菜单"
            placement="left"
            onClose={() => setCollapsed(true)}
            open={!collapsed}
            bodyStyle={{ padding: 0 }}
            width={250}
          >
            <Menu
              theme="dark"
              mode="inline"
              selectedKeys={[location.pathname]}
              items={menuItems}
              style={{ height: '100%', borderRight: 0 }}
              onClick={(info) => {
                // 关闭抽屉
                setCollapsed(true);

                // 递归查找菜单项（支持嵌套菜单）
                const findMenuItem = (items, key) => {
                  for (const item of items) {
                    if (item.key === key) {
                      return item;
                    }
                    if (item.children) {
                      const found = findMenuItem(item.children, key);
                      if (found) return found;
                    }
                  }
                  return null;
                };

                // 执行菜单项的点击处理
                const menuItem = findMenuItem(menuItems, info.key);
                if (menuItem && menuItem.onClick) {
                  menuItem.onClick();
                }
              }}
            />
          </Drawer>
        ) : (
          <Sider
            collapsible
            collapsed={collapsed}
            onCollapse={value => setCollapsed(value)}
            width={200}
            style={{ background: '#fff' }}
          >
            <Menu
              mode="inline"
              selectedKeys={[location.pathname]}
              style={{ height: '100%', borderRight: 0 }}
              items={menuItems}
            />
          </Sider>
        )}
        
        <Layout style={{ padding: '0 24px 24px' }}>
          <div style={{ margin: '16px 0', padding: '16px', background: '#fff' }}>
            <Title level={3}>{pageTitle}</Title>
            <Divider style={{ margin: '12px 0' }} />
            <Content style={{ padding: '12px 0', minHeight: 'calc(100vh - 200px)' }}>
              {children}
            </Content>
          </div>
        </Layout>
      </Layout>
      
      <Drawer
        title="AI智能助手"
        placement="right"
        onClose={closeAIAssistant}
        open={aiDrawerVisible}
        width={400}
      >
        <AIAssistant user={user} />
      </Drawer>


    </Layout>
  );
};

export default AppLayout;
