[{"D:\\pythonproject\\checkingsys\\frontend\\src\\index.js": "1", "D:\\pythonproject\\checkingsys\\frontend\\src\\App.js": "2", "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\auth.js": "3", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Register.js": "4", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StudentHomeworkDetail.js": "5", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Login.js": "6", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Home.js": "7", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ParentDashboard.js": "8", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StudentReport.js": "9", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ClassManagement.js": "10", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ImprovedSystemClassManagement.js": "11", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ImprovedClassManagement.js": "12", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\WrongQuestionTraining.js": "13", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\UserProfile.js": "14", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\AdminDashboard.js": "15", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StatisticsPage.js": "16", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\UserManagementPage.js": "17", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\HomeworkManagement.js": "18", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\DatabaseManagementPage.js": "19", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\TeacherUserManagementPage.js": "20", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\PhotoSolvePage.js": "21", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PrivateRoute.js": "22", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolManagement.js": "23", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\AppLayout.js": "24", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StandaloneRegister.js": "25", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SuperSchoolManagement.js": "26", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\RegionManagement.js": "27", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\index.js": "28", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SystemHomeworkAnalysis\\index.js": "29", "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\api.js": "30", "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\detector.js": "31", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentBindingVerification.js": "32", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\AIAssistant.js": "33", "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\teacherDataFix.js": "34", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\UserSchoolApplications.js": "35", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SubjectManagement.js": "36", "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\roleUtils.js": "37", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\UserManagement.js": "38", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\RegistrationApproval.js": "39", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SubjectPermissionManager.js": "40", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolApplicationReview.js": "41", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ParentFeatureManager.js": "42", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkDetail.js": "43", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkUpload.js": "44", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentList.js": "45", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentDetail.js": "46", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentHomeworkUpload.js": "47", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentCreate.js": "48", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkUploadOptions.js": "49", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkStatistics.js": "50", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkCalendar.js": "51", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentProgress.js": "52", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PendingReviewHomeworks.js": "53", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\FinishedHomeworks.js": "54", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentSubmitChoice.js": "55", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentHomeworkAssignmentList.js": "56", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PhotoSolve.js": "57", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\TeacherUserManagement.js": "58", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkHistory.js": "59", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkReview.js": "60", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkDetail.js": "61", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ParentRegistration.js": "62", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\Overview.js": "63", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolApplicationForm.js": "64", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\QuestionAnalysis.js": "65", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\SmartSuggestions.js": "66", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\StudentDetails.js": "67", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\ParentReport.js": "68", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\DataExport.js": "69", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\AssignmentSelector.js": "70", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SystemHomeworkAnalysis\\SystemAssignmentSelector.js": "71", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkCorrectionEditor.js": "72", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\CameraCapture.js": "73", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentCard.js": "74", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\QuickActions.js": "75", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ImageCropper.js": "76", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\SmartReminder.js": "77", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StatusBadge.js": "78", "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\permissionUtils.js": "79"}, {"size": 946, "mtime": 1751554505035, "results": "80", "hashOfConfig": "81"}, {"size": 10660, "mtime": 1754316658627, "results": "82", "hashOfConfig": "81"}, {"size": 5159, "mtime": 1753841883936, "results": "83", "hashOfConfig": "81"}, {"size": 32727, "mtime": 1754148836891, "results": "84", "hashOfConfig": "81"}, {"size": 12949, "mtime": 1754404252108, "results": "85", "hashOfConfig": "81"}, {"size": 24152, "mtime": 1754060190688, "results": "86", "hashOfConfig": "81"}, {"size": 17003, "mtime": 1754369937941, "results": "87", "hashOfConfig": "81"}, {"size": 17810, "mtime": 1754405000757, "results": "88", "hashOfConfig": "81"}, {"size": 11818, "mtime": 1754314069631, "results": "89", "hashOfConfig": "81"}, {"size": 47854, "mtime": 1754117724792, "results": "90", "hashOfConfig": "81"}, {"size": 125852, "mtime": 1754369937941, "results": "91", "hashOfConfig": "81"}, {"size": 163918, "mtime": 1754408711998, "results": "92", "hashOfConfig": "81"}, {"size": 44995, "mtime": 1753493006030, "results": "93", "hashOfConfig": "81"}, {"size": 5304, "mtime": 1754013068990, "results": "94", "hashOfConfig": "81"}, {"size": 49290, "mtime": 1754308902757, "results": "95", "hashOfConfig": "81"}, {"size": 35955, "mtime": 1754060562254, "results": "96", "hashOfConfig": "81"}, {"size": 701, "mtime": 1752896943241, "results": "97", "hashOfConfig": "81"}, {"size": 22217, "mtime": 1754057362141, "results": "98", "hashOfConfig": "81"}, {"size": 25276, "mtime": 1754039146720, "results": "99", "hashOfConfig": "81"}, {"size": 511, "mtime": 1754369937938, "results": "100", "hashOfConfig": "81"}, {"size": 190, "mtime": 1754012887356, "results": "101", "hashOfConfig": "81"}, {"size": 1696, "mtime": 1753841864292, "results": "102", "hashOfConfig": "81"}, {"size": 35911, "mtime": 1753929968606, "results": "103", "hashOfConfig": "81"}, {"size": 15662, "mtime": 1754403749620, "results": "104", "hashOfConfig": "81"}, {"size": 52337, "mtime": 1754150863988, "results": "105", "hashOfConfig": "81"}, {"size": 88102, "mtime": 1754060622237, "results": "106", "hashOfConfig": "81"}, {"size": 13198, "mtime": 1754320653657, "results": "107", "hashOfConfig": "81"}, {"size": 13237, "mtime": 1754368765296, "results": "108", "hashOfConfig": "81"}, {"size": 11387, "mtime": 1754233114255, "results": "109", "hashOfConfig": "81"}, {"size": 140310, "mtime": 1754409133882, "results": "110", "hashOfConfig": "81"}, {"size": 3371, "mtime": 1754059746620, "results": "111", "hashOfConfig": "81"}, {"size": 5562, "mtime": 1753866053592, "results": "112", "hashOfConfig": "81"}, {"size": 6195, "mtime": 1751763301656, "results": "113", "hashOfConfig": "81"}, {"size": 6428, "mtime": 1753963570645, "results": "114", "hashOfConfig": "81"}, {"size": 3679, "mtime": 1753866642399, "results": "115", "hashOfConfig": "81"}, {"size": 18369, "mtime": 1753888239807, "results": "116", "hashOfConfig": "81"}, {"size": 9249, "mtime": 1754096127608, "results": "117", "hashOfConfig": "81"}, {"size": 31243, "mtime": 1754369937938, "results": "118", "hashOfConfig": "81"}, {"size": 13815, "mtime": 1754190768286, "results": "119", "hashOfConfig": "81"}, {"size": 9163, "mtime": 1753890457468, "results": "120", "hashOfConfig": "81"}, {"size": 12218, "mtime": 1753866691991, "results": "121", "hashOfConfig": "81"}, {"size": 12566, "mtime": 1754401523932, "results": "122", "hashOfConfig": "81"}, {"size": 26829, "mtime": 1753880881610, "results": "123", "hashOfConfig": "81"}, {"size": 50649, "mtime": 1754293080652, "results": "124", "hashOfConfig": "81"}, {"size": 29942, "mtime": 1753881846609, "results": "125", "hashOfConfig": "81"}, {"size": 6242, "mtime": 1751432649676, "results": "126", "hashOfConfig": "81"}, {"size": 27313, "mtime": 1753425767631, "results": "127", "hashOfConfig": "81"}, {"size": 24892, "mtime": 1753888407511, "results": "128", "hashOfConfig": "81"}, {"size": 3516, "mtime": 1751335113862, "results": "129", "hashOfConfig": "81"}, {"size": 32329, "mtime": 1751554727149, "results": "130", "hashOfConfig": "81"}, {"size": 11262, "mtime": 1754285220077, "results": "131", "hashOfConfig": "81"}, {"size": 8091, "mtime": 1751866086521, "results": "132", "hashOfConfig": "81"}, {"size": 30342, "mtime": 1754285130469, "results": "133", "hashOfConfig": "81"}, {"size": 18939, "mtime": 1754285168670, "results": "134", "hashOfConfig": "81"}, {"size": 9682, "mtime": 1753022516134, "results": "135", "hashOfConfig": "81"}, {"size": 17938, "mtime": 1753017445917, "results": "136", "hashOfConfig": "81"}, {"size": 29074, "mtime": 1754023698366, "results": "137", "hashOfConfig": "81"}, {"size": 8962, "mtime": 1754369937934, "results": "138", "hashOfConfig": "81"}, {"size": 13670, "mtime": 1753020805101, "results": "139", "hashOfConfig": "81"}, {"size": 27862, "mtime": 1754036301656, "results": "140", "hashOfConfig": "81"}, {"size": 34660, "mtime": 1754287382971, "results": "141", "hashOfConfig": "81"}, {"size": 9376, "mtime": 1754146200136, "results": "142", "hashOfConfig": "81"}, {"size": 12355, "mtime": 1752847450307, "results": "143", "hashOfConfig": "81"}, {"size": 8091, "mtime": 1753866614546, "results": "144", "hashOfConfig": "81"}, {"size": 11248, "mtime": 1752926637327, "results": "145", "hashOfConfig": "81"}, {"size": 10791, "mtime": 1752742130570, "results": "146", "hashOfConfig": "81"}, {"size": 26842, "mtime": 1753026180860, "results": "147", "hashOfConfig": "81"}, {"size": 14246, "mtime": 1754039496657, "results": "148", "hashOfConfig": "81"}, {"size": 20025, "mtime": 1754296994877, "results": "149", "hashOfConfig": "81"}, {"size": 18350, "mtime": 1754232452423, "results": "150", "hashOfConfig": "81"}, {"size": 23426, "mtime": 1754277486476, "results": "151", "hashOfConfig": "81"}, {"size": 28479, "mtime": 1753508100688, "results": "152", "hashOfConfig": "81"}, {"size": 20148, "mtime": 1754026190054, "results": "153", "hashOfConfig": "81"}, {"size": 13076, "mtime": 1753016487323, "results": "154", "hashOfConfig": "81"}, {"size": 11151, "mtime": 1753016418193, "results": "155", "hashOfConfig": "81"}, {"size": 32106, "mtime": 1754021636789, "results": "156", "hashOfConfig": "81"}, {"size": 12543, "mtime": 1753010983078, "results": "157", "hashOfConfig": "81"}, {"size": 7641, "mtime": 1754031754988, "results": "158", "hashOfConfig": "81"}, {"size": 3644, "mtime": 1754367697296, "results": "159", "hashOfConfig": "81"}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1t86jd4", {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\pythonproject\\checkingsys\\frontend\\src\\index.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\App.js", ["397", "398"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\auth.js", ["399"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Register.js", ["400", "401", "402", "403", "404", "405", "406"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StudentHomeworkDetail.js", ["407", "408", "409", "410", "411", "412"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Login.js", ["413", "414", "415", "416"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Home.js", ["417", "418", "419", "420", "421", "422", "423", "424"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ParentDashboard.js", ["425", "426", "427", "428"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StudentReport.js", ["429", "430"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ClassManagement.js", ["431", "432", "433", "434", "435", "436", "437", "438", "439", "440", "441", "442", "443", "444", "445", "446"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ImprovedSystemClassManagement.js", ["447", "448", "449", "450", "451", "452", "453"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ImprovedClassManagement.js", ["454", "455", "456"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\WrongQuestionTraining.js", ["457", "458", "459", "460", "461", "462", "463", "464", "465", "466", "467", "468", "469", "470", "471", "472", "473", "474", "475", "476"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\UserProfile.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\AdminDashboard.js", ["477", "478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StatisticsPage.js", ["510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\UserManagementPage.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\HomeworkManagement.js", [], ["521"], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\DatabaseManagementPage.js", ["522", "523", "524", "525", "526", "527", "528", "529"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\TeacherUserManagementPage.js", ["530"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\PhotoSolvePage.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PrivateRoute.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolManagement.js", ["531", "532", "533", "534", "535", "536"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\AppLayout.js", ["537"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StandaloneRegister.js", ["538", "539", "540", "541", "542", "543", "544", "545"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SuperSchoolManagement.js", ["546", "547", "548", "549"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\RegionManagement.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\index.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SystemHomeworkAnalysis\\index.js", ["550", "551"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\api.js", ["552"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\detector.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentBindingVerification.js", ["553", "554", "555", "556", "557"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\AIAssistant.js", ["558", "559"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\teacherDataFix.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\UserSchoolApplications.js", ["560"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SubjectManagement.js", ["561", "562", "563", "564", "565", "566", "567", "568", "569", "570"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\roleUtils.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\UserManagement.js", ["571", "572", "573", "574"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\RegistrationApproval.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SubjectPermissionManager.js", ["575", "576", "577", "578", "579", "580", "581", "582", "583", "584"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolApplicationReview.js", ["585", "586", "587", "588", "589"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ParentFeatureManager.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkDetail.js", ["590", "591", "592", "593", "594", "595", "596", "597", "598", "599"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkUpload.js", ["600", "601", "602", "603", "604"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentList.js", ["605", "606", "607"], ["608", "609"], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentDetail.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentHomeworkUpload.js", ["610", "611", "612"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentCreate.js", ["613", "614", "615", "616"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkUploadOptions.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkStatistics.js", ["617", "618"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkCalendar.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentProgress.js", ["619", "620", "621", "622", "623"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PendingReviewHomeworks.js", ["624", "625", "626", "627", "628", "629", "630"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\FinishedHomeworks.js", ["631", "632", "633", "634", "635", "636"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentSubmitChoice.js", ["637", "638"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentHomeworkAssignmentList.js", ["639", "640", "641", "642", "643", "644", "645", "646", "647"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PhotoSolve.js", ["648", "649"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\TeacherUserManagement.js", ["650", "651"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkHistory.js", ["652"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkReview.js", ["653", "654", "655", "656", "657", "658"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkDetail.js", ["659", "660", "661", "662", "663", "664"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ParentRegistration.js", ["665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\Overview.js", ["677", "678", "679", "680"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolApplicationForm.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\QuestionAnalysis.js", ["681", "682", "683", "684", "685"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\SmartSuggestions.js", ["686", "687", "688", "689"], ["690"], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\StudentDetails.js", ["691", "692", "693"], ["694", "695"], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\ParentReport.js", ["696", "697"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\DataExport.js", ["698", "699", "700", "701", "702"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\AssignmentSelector.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SystemHomeworkAnalysis\\SystemAssignmentSelector.js", ["703", "704"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkCorrectionEditor.js", ["705", "706", "707", "708", "709", "710", "711", "712", "713"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\CameraCapture.js", ["714"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentCard.js", ["715", "716", "717"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\QuickActions.js", ["718", "719"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ImageCropper.js", ["720", "721", "722", "723", "724"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\SmartReminder.js", ["725", "726"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StatusBadge.js", ["727"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\permissionUtils.js", [], [], {"ruleId": "728", "severity": 1, "message": "729", "line": 3, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 16}, {"ruleId": "728", "severity": 1, "message": "732", "line": 9, "column": 8, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 16}, {"ruleId": "733", "severity": 1, "message": "734", "line": 89, "column": 6, "nodeType": "735", "endLine": 89, "endColumn": 19, "suggestions": "736"}, {"ruleId": "728", "severity": 1, "message": "737", "line": 3, "column": 79, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 84}, {"ruleId": "728", "severity": 1, "message": "738", "line": 3, "column": 86, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 93}, {"ruleId": "728", "severity": 1, "message": "739", "line": 4, "column": 67, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 86}, {"ruleId": "728", "severity": 1, "message": "740", "line": 15, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 15, "endColumn": 25}, {"ruleId": "728", "severity": 1, "message": "741", "line": 23, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 23, "endColumn": 16}, {"ruleId": "728", "severity": 1, "message": "742", "line": 23, "column": 18, "nodeType": "730", "messageId": "731", "endLine": 23, "endColumn": 27}, {"ruleId": "728", "severity": 1, "message": "743", "line": 47, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 47, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "744", "line": 12, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 12, "endColumn": 11}, {"ruleId": "728", "severity": 1, "message": "745", "line": 14, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 14, "endColumn": 11}, {"ruleId": "728", "severity": 1, "message": "746", "line": 15, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 15, "endColumn": 15}, {"ruleId": "728", "severity": 1, "message": "738", "line": 18, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 18, "endColumn": 10}, {"ruleId": "728", "severity": 1, "message": "747", "line": 19, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 19, "endColumn": 9}, {"ruleId": "733", "severity": 1, "message": "748", "line": 115, "column": 6, "nodeType": "735", "endLine": 115, "endColumn": 17, "suggestions": "749"}, {"ruleId": "728", "severity": 1, "message": "750", "line": 3, "column": 79, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 87}, {"ruleId": "728", "severity": 1, "message": "751", "line": 5, "column": 19, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 27}, {"ruleId": "728", "severity": 1, "message": "752", "line": 36, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 36, "endColumn": 26}, {"ruleId": "733", "severity": 1, "message": "753", "line": 265, "column": 6, "nodeType": "735", "endLine": 265, "endColumn": 8, "suggestions": "754"}, {"ruleId": "728", "severity": 1, "message": "755", "line": 9, "column": 37, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 49}, {"ruleId": "728", "severity": 1, "message": "756", "line": 10, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 10, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "757", "line": 10, "column": 24, "nodeType": "730", "messageId": "731", "endLine": 10, "endColumn": 36}, {"ruleId": "728", "severity": 1, "message": "758", "line": 12, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 12, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "759", "line": 12, "column": 24, "nodeType": "730", "messageId": "731", "endLine": 12, "endColumn": 49}, {"ruleId": "728", "severity": 1, "message": "760", "line": 12, "column": 51, "nodeType": "730", "messageId": "731", "endLine": 12, "endColumn": 65}, {"ruleId": "728", "severity": 1, "message": "761", "line": 13, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 13, "endColumn": 19}, {"ruleId": "733", "severity": 1, "message": "762", "line": 146, "column": 6, "nodeType": "735", "endLine": 146, "endColumn": 12, "suggestions": "763"}, {"ruleId": "728", "severity": 1, "message": "758", "line": 24, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 24, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "759", "line": 26, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 26, "endColumn": 28}, {"ruleId": "728", "severity": 1, "message": "764", "line": 47, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 47, "endColumn": 25}, {"ruleId": "733", "severity": 1, "message": "765", "line": 161, "column": 6, "nodeType": "735", "endLine": 161, "endColumn": 8, "suggestions": "766"}, {"ruleId": "728", "severity": 1, "message": "767", "line": 33, "column": 22, "nodeType": "730", "messageId": "731", "endLine": 33, "endColumn": 31}, {"ruleId": "733", "severity": 1, "message": "768", "line": 64, "column": 6, "nodeType": "735", "endLine": 64, "endColumn": 17, "suggestions": "769"}, {"ruleId": "728", "severity": 1, "message": "729", "line": 3, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 9}, {"ruleId": "728", "severity": 1, "message": "747", "line": 5, "column": 22, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 28}, {"ruleId": "728", "severity": 1, "message": "738", "line": 5, "column": 35, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 42}, {"ruleId": "728", "severity": 1, "message": "770", "line": 9, "column": 21, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 33}, {"ruleId": "728", "severity": 1, "message": "771", "line": 9, "column": 35, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 47}, {"ruleId": "728", "severity": 1, "message": "759", "line": 10, "column": 17, "nodeType": "730", "messageId": "731", "endLine": 10, "endColumn": 42}, {"ruleId": "728", "severity": 1, "message": "772", "line": 16, "column": 38, "nodeType": "730", "messageId": "731", "endLine": 16, "endColumn": 56}, {"ruleId": "728", "severity": 1, "message": "773", "line": 21, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 21, "endColumn": 16}, {"ruleId": "728", "severity": 1, "message": "774", "line": 46, "column": 20, "nodeType": "730", "messageId": "731", "endLine": 46, "endColumn": 31}, {"ruleId": "733", "severity": 1, "message": "775", "line": 63, "column": 6, "nodeType": "735", "endLine": 63, "endColumn": 12, "suggestions": "776"}, {"ruleId": "728", "severity": 1, "message": "777", "line": 275, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 275, "endColumn": 23}, {"ruleId": "728", "severity": 1, "message": "778", "line": 387, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 387, "endColumn": 35}, {"ruleId": "728", "severity": 1, "message": "779", "line": 670, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 670, "endColumn": 23}, {"ruleId": "728", "severity": 1, "message": "780", "line": 716, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 716, "endColumn": 28}, {"ruleId": "781", "severity": 1, "message": "782", "line": 1005, "column": 31, "nodeType": "783", "endLine": 1005, "endColumn": 80}, {"ruleId": "781", "severity": 1, "message": "782", "line": 1012, "column": 33, "nodeType": "783", "endLine": 1012, "endColumn": 36}, {"ruleId": "728", "severity": 1, "message": "784", "line": 9, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "785", "line": 646, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 646, "endColumn": 21}, {"ruleId": "733", "severity": 1, "message": "786", "line": 1791, "column": 6, "nodeType": "735", "endLine": 1791, "endColumn": 8, "suggestions": "787"}, {"ruleId": "733", "severity": 1, "message": "788", "line": 1795, "column": 6, "nodeType": "735", "endLine": 1795, "endColumn": 117, "suggestions": "789"}, {"ruleId": "733", "severity": 1, "message": "790", "line": 1799, "column": 6, "nodeType": "735", "endLine": 1799, "endColumn": 48, "suggestions": "791"}, {"ruleId": "733", "severity": 1, "message": "792", "line": 1804, "column": 6, "nodeType": "735", "endLine": 1804, "endColumn": 35, "suggestions": "793"}, {"ruleId": "733", "severity": 1, "message": "792", "line": 1809, "column": 6, "nodeType": "735", "endLine": 1809, "endColumn": 35, "suggestions": "794"}, {"ruleId": "733", "severity": 1, "message": "795", "line": 2009, "column": 6, "nodeType": "735", "endLine": 2009, "endColumn": 12, "suggestions": "796"}, {"ruleId": "733", "severity": 1, "message": "788", "line": 2013, "column": 6, "nodeType": "735", "endLine": 2013, "endColumn": 57, "suggestions": "797"}, {"ruleId": "733", "severity": 1, "message": "792", "line": 2018, "column": 6, "nodeType": "735", "endLine": 2018, "endColumn": 35, "suggestions": "798"}, {"ruleId": "728", "severity": 1, "message": "799", "line": 2, "column": 49, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 53}, {"ruleId": "728", "severity": 1, "message": "800", "line": 4, "column": 23, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 33}, {"ruleId": "728", "severity": 1, "message": "801", "line": 6, "column": 54, "nodeType": "730", "messageId": "731", "endLine": 6, "endColumn": 62}, {"ruleId": "728", "severity": 1, "message": "747", "line": 6, "column": 64, "nodeType": "730", "messageId": "731", "endLine": 6, "endColumn": 70}, {"ruleId": "728", "severity": 1, "message": "802", "line": 10, "column": 58, "nodeType": "730", "messageId": "731", "endLine": 10, "endColumn": 73}, {"ruleId": "728", "severity": 1, "message": "770", "line": 10, "column": 75, "nodeType": "730", "messageId": "731", "endLine": 10, "endColumn": 87}, {"ruleId": "728", "severity": 1, "message": "803", "line": 10, "column": 89, "nodeType": "730", "messageId": "731", "endLine": 10, "endColumn": 105}, {"ruleId": "728", "severity": 1, "message": "804", "line": 10, "column": 107, "nodeType": "730", "messageId": "731", "endLine": 10, "endColumn": 121}, {"ruleId": "728", "severity": 1, "message": "805", "line": 12, "column": 8, "nodeType": "730", "messageId": "731", "endLine": 12, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "806", "line": 28, "column": 18, "nodeType": "730", "messageId": "731", "endLine": 28, "endColumn": 24}, {"ruleId": "728", "severity": 1, "message": "807", "line": 38, "column": 11, "nodeType": "730", "messageId": "731", "endLine": 38, "endColumn": 27}, {"ruleId": "728", "severity": 1, "message": "808", "line": 38, "column": 29, "nodeType": "730", "messageId": "731", "endLine": 38, "endColumn": 43}, {"ruleId": "728", "severity": 1, "message": "809", "line": 38, "column": 45, "nodeType": "730", "messageId": "731", "endLine": 38, "endColumn": 57}, {"ruleId": "810", "severity": 1, "message": "811", "line": 49, "column": 25, "nodeType": "812", "messageId": "813", "endLine": 49, "endColumn": 27}, {"ruleId": "810", "severity": 1, "message": "811", "line": 49, "column": 52, "nodeType": "812", "messageId": "813", "endLine": 49, "endColumn": 54}, {"ruleId": "728", "severity": 1, "message": "814", "line": 519, "column": 11, "nodeType": "730", "messageId": "731", "endLine": 519, "endColumn": 24}, {"ruleId": "728", "severity": 1, "message": "815", "line": 529, "column": 14, "nodeType": "730", "messageId": "731", "endLine": 529, "endColumn": 29}, {"ruleId": "728", "severity": 1, "message": "816", "line": 796, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 796, "endColumn": 21}, {"ruleId": "728", "severity": 1, "message": "814", "line": 801, "column": 11, "nodeType": "730", "messageId": "731", "endLine": 801, "endColumn": 24}, {"ruleId": "728", "severity": 1, "message": "817", "line": 1162, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 1162, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "818", "line": 2, "column": 18, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "800", "line": 2, "column": 24, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 34}, {"ruleId": "728", "severity": 1, "message": "737", "line": 2, "column": 176, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 181}, {"ruleId": "728", "severity": 1, "message": "801", "line": 2, "column": 193, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 201}, {"ruleId": "728", "severity": 1, "message": "747", "line": 2, "column": 203, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 209}, {"ruleId": "728", "severity": 1, "message": "819", "line": 3, "column": 41, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 58}, {"ruleId": "728", "severity": 1, "message": "804", "line": 3, "column": 158, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 172}, {"ruleId": "728", "severity": 1, "message": "802", "line": 3, "column": 174, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 189}, {"ruleId": "728", "severity": 1, "message": "803", "line": 3, "column": 191, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 207}, {"ruleId": "728", "severity": 1, "message": "759", "line": 3, "column": 229, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 254}, {"ruleId": "728", "severity": 1, "message": "799", "line": 4, "column": 36, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 40}, {"ruleId": "728", "severity": 1, "message": "820", "line": 5, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "821", "line": 8, "column": 8, "nodeType": "730", "messageId": "731", "endLine": 8, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "806", "line": 15, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 15, "endColumn": 15}, {"ruleId": "728", "severity": 1, "message": "822", "line": 15, "column": 26, "nodeType": "730", "messageId": "731", "endLine": 15, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "823", "line": 18, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 18, "endColumn": 15}, {"ruleId": "728", "severity": 1, "message": "824", "line": 24, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 24, "endColumn": 15}, {"ruleId": "728", "severity": 1, "message": "825", "line": 26, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 26, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "826", "line": 47, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 47, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "827", "line": 63, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 63, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "827", "line": 75, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 75, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "827", "line": 106, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 106, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "827", "line": 186, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 186, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "827", "line": 256, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 256, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "827", "line": 288, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 288, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "827", "line": 372, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 372, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "827", "line": 419, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 419, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "827", "line": 463, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 463, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "828", "line": 516, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 516, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "829", "line": 530, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 530, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "830", "line": 672, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 672, "endColumn": 35}, {"ruleId": "728", "severity": 1, "message": "831", "line": 677, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 677, "endColumn": 29}, {"ruleId": "728", "severity": 1, "message": "832", "line": 784, "column": 25, "nodeType": "730", "messageId": "731", "endLine": 784, "endColumn": 36}, {"ruleId": "728", "severity": 1, "message": "833", "line": 1, "column": 38, "nodeType": "730", "messageId": "731", "endLine": 1, "endColumn": 44}, {"ruleId": "728", "severity": 1, "message": "834", "line": 6, "column": 8, "nodeType": "730", "messageId": "731", "endLine": 6, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "835", "line": 7, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 7, "endColumn": 32}, {"ruleId": "728", "severity": 1, "message": "836", "line": 88, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 88, "endColumn": 27}, {"ruleId": "733", "severity": 1, "message": "837", "line": 236, "column": 6, "nodeType": "735", "endLine": 236, "endColumn": 21, "suggestions": "838"}, {"ruleId": "728", "severity": 1, "message": "839", "line": 277, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 277, "endColumn": 21}, {"ruleId": "728", "severity": 1, "message": "840", "line": 279, "column": 28, "nodeType": "730", "messageId": "731", "endLine": 279, "endColumn": 39}, {"ruleId": "728", "severity": 1, "message": "841", "line": 279, "column": 41, "nodeType": "730", "messageId": "731", "endLine": 279, "endColumn": 54}, {"ruleId": "728", "severity": 1, "message": "842", "line": 279, "column": 56, "nodeType": "730", "messageId": "731", "endLine": 279, "endColumn": 70}, {"ruleId": "728", "severity": 1, "message": "843", "line": 279, "column": 72, "nodeType": "730", "messageId": "731", "endLine": 279, "endColumn": 87}, {"ruleId": "728", "severity": 1, "message": "844", "line": 281, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 281, "endColumn": 21}, {"ruleId": "733", "severity": 1, "message": "845", "line": 210, "column": 6, "nodeType": "735", "endLine": 210, "endColumn": 26, "suggestions": "846", "suppressions": "847"}, {"ruleId": "728", "severity": 1, "message": "848", "line": 2, "column": 95, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 100}, {"ruleId": "728", "severity": 1, "message": "849", "line": 2, "column": 113, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 123}, {"ruleId": "728", "severity": 1, "message": "850", "line": 3, "column": 121, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 136}, {"ruleId": "728", "severity": 1, "message": "851", "line": 3, "column": 152, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 174}, {"ruleId": "728", "severity": 1, "message": "852", "line": 5, "column": 8, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 10}, {"ruleId": "728", "severity": 1, "message": "853", "line": 34, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 34, "endColumn": 21}, {"ruleId": "733", "severity": 1, "message": "854", "line": 80, "column": 6, "nodeType": "735", "endLine": 80, "endColumn": 8, "suggestions": "855"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 435, "column": 15, "nodeType": "858", "messageId": "859", "endLine": 439, "endColumn": 37}, {"ruleId": "728", "severity": 1, "message": "729", "line": 2, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 16}, {"ruleId": "728", "severity": 1, "message": "860", "line": 2, "column": 92, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 99}, {"ruleId": "728", "severity": 1, "message": "759", "line": 8, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 8, "endColumn": 35}, {"ruleId": "733", "severity": 1, "message": "861", "line": 74, "column": 6, "nodeType": "735", "endLine": 74, "endColumn": 40, "suggestions": "862"}, {"ruleId": "733", "severity": 1, "message": "861", "line": 100, "column": 6, "nodeType": "735", "endLine": 100, "endColumn": 16, "suggestions": "863"}, {"ruleId": "733", "severity": 1, "message": "864", "line": 150, "column": 6, "nodeType": "735", "endLine": 150, "endColumn": 24, "suggestions": "865"}, {"ruleId": "728", "severity": 1, "message": "866", "line": 507, "column": 11, "nodeType": "730", "messageId": "731", "endLine": 507, "endColumn": 25}, {"ruleId": "728", "severity": 1, "message": "867", "line": 15, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 15, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "868", "line": 2, "column": 64, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 69}, {"ruleId": "728", "severity": 1, "message": "869", "line": 3, "column": 67, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 79}, {"ruleId": "728", "severity": 1, "message": "771", "line": 3, "column": 81, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 93}, {"ruleId": "728", "severity": 1, "message": "870", "line": 3, "column": 95, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 107}, {"ruleId": "728", "severity": 1, "message": "871", "line": 4, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "799", "line": 5, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 14}, {"ruleId": "733", "severity": 1, "message": "872", "line": 207, "column": 6, "nodeType": "735", "endLine": 207, "endColumn": 22, "suggestions": "873"}, {"ruleId": "733", "severity": 1, "message": "874", "line": 220, "column": 6, "nodeType": "735", "endLine": 220, "endColumn": 45, "suggestions": "875"}, {"ruleId": "728", "severity": 1, "message": "876", "line": 2, "column": 119, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 122}, {"ruleId": "728", "severity": 1, "message": "877", "line": 2, "column": 124, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 127}, {"ruleId": "728", "severity": 1, "message": "878", "line": 62, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 62, "endColumn": 25}, {"ruleId": "728", "severity": 1, "message": "879", "line": 821, "column": 15, "nodeType": "730", "messageId": "731", "endLine": 821, "endColumn": 34}, {"ruleId": "728", "severity": 1, "message": "880", "line": 41, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 41, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "881", "line": 173, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 173, "endColumn": 25}, {"ruleId": "728", "severity": 1, "message": "882", "line": 418, "column": 15, "nodeType": "730", "messageId": "731", "endLine": 418, "endColumn": 24}, {"ruleId": "728", "severity": 1, "message": "883", "line": 2, "column": 54, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 59}, {"ruleId": "728", "severity": 1, "message": "756", "line": 3, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 29}, {"ruleId": "728", "severity": 1, "message": "884", "line": 3, "column": 31, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 50}, {"ruleId": "728", "severity": 1, "message": "885", "line": 6, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 6, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "886", "line": 13, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 13, "endColumn": 24}, {"ruleId": "728", "severity": 1, "message": "887", "line": 2, "column": 37, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 44}, {"ruleId": "728", "severity": 1, "message": "888", "line": 95, "column": 11, "nodeType": "730", "messageId": "731", "endLine": 95, "endColumn": 26}, {"ruleId": "728", "severity": 1, "message": "889", "line": 7, "column": 16, "nodeType": "730", "messageId": "731", "endLine": 7, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "890", "line": 4, "column": 27, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "860", "line": 4, "column": 52, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 59}, {"ruleId": "728", "severity": 1, "message": "891", "line": 4, "column": 61, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 65}, {"ruleId": "728", "severity": 1, "message": "738", "line": 5, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "759", "line": 9, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 28}, {"ruleId": "728", "severity": 1, "message": "892", "line": 10, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 10, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "893", "line": 10, "column": 24, "nodeType": "730", "messageId": "731", "endLine": 10, "endColumn": 36}, {"ruleId": "728", "severity": 1, "message": "889", "line": 18, "column": 16, "nodeType": "730", "messageId": "731", "endLine": 18, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "741", "line": 26, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 26, "endColumn": 16}, {"ruleId": "733", "severity": 1, "message": "894", "line": 116, "column": 6, "nodeType": "735", "endLine": 116, "endColumn": 26, "suggestions": "895"}, {"ruleId": "728", "severity": 1, "message": "870", "line": 3, "column": 40, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 52}, {"ruleId": "728", "severity": 1, "message": "896", "line": 35, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 35, "endColumn": 15}, {"ruleId": "733", "severity": 1, "message": "897", "line": 110, "column": 6, "nodeType": "735", "endLine": 110, "endColumn": 8, "suggestions": "898"}, {"ruleId": "728", "severity": 1, "message": "899", "line": 430, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 430, "endColumn": 30}, {"ruleId": "728", "severity": 1, "message": "737", "line": 2, "column": 33, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 38}, {"ruleId": "728", "severity": 1, "message": "900", "line": 2, "column": 76, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 79}, {"ruleId": "728", "severity": 1, "message": "870", "line": 4, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 15}, {"ruleId": "728", "severity": 1, "message": "901", "line": 4, "column": 17, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "756", "line": 5, "column": 30, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 49}, {"ruleId": "728", "severity": 1, "message": "884", "line": 5, "column": 51, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 70}, {"ruleId": "728", "severity": 1, "message": "902", "line": 21, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 21, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "903", "line": 21, "column": 24, "nodeType": "730", "messageId": "731", "endLine": 21, "endColumn": 39}, {"ruleId": "728", "severity": 1, "message": "904", "line": 22, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 22, "endColumn": 14}, {"ruleId": "733", "severity": 1, "message": "905", "line": 121, "column": 6, "nodeType": "735", "endLine": 121, "endColumn": 30, "suggestions": "906"}, {"ruleId": "728", "severity": 1, "message": "907", "line": 3, "column": 25, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 38}, {"ruleId": "728", "severity": 1, "message": "759", "line": 3, "column": 40, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 65}, {"ruleId": "728", "severity": 1, "message": "889", "line": 6, "column": 16, "nodeType": "730", "messageId": "731", "endLine": 6, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "767", "line": 6, "column": 22, "nodeType": "730", "messageId": "731", "endLine": 6, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "908", "line": 12, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 12, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "909", "line": 5, "column": 33, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 37}, {"ruleId": "728", "severity": 1, "message": "737", "line": 5, "column": 39, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 44}, {"ruleId": "728", "severity": 1, "message": "758", "line": 9, "column": 36, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 55}, {"ruleId": "728", "severity": 1, "message": "759", "line": 9, "column": 57, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 82}, {"ruleId": "728", "severity": 1, "message": "910", "line": 9, "column": 84, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 101}, {"ruleId": "728", "severity": 1, "message": "911", "line": 12, "column": 8, "nodeType": "730", "messageId": "731", "endLine": 12, "endColumn": 21}, {"ruleId": "728", "severity": 1, "message": "885", "line": 17, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 17, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "912", "line": 23, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 23, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "913", "line": 37, "column": 17, "nodeType": "730", "messageId": "731", "endLine": 37, "endColumn": 25}, {"ruleId": "728", "severity": 1, "message": "914", "line": 533, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 533, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "915", "line": 1, "column": 46, "nodeType": "730", "messageId": "731", "endLine": 1, "endColumn": 57}, {"ruleId": "728", "severity": 1, "message": "916", "line": 51, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 51, "endColumn": 22}, {"ruleId": "733", "severity": 1, "message": "917", "line": 216, "column": 6, "nodeType": "735", "endLine": 216, "endColumn": 12, "suggestions": "918"}, {"ruleId": "733", "severity": 1, "message": "919", "line": 230, "column": 6, "nodeType": "735", "endLine": 230, "endColumn": 42, "suggestions": "920"}, {"ruleId": "733", "severity": 1, "message": "919", "line": 246, "column": 6, "nodeType": "735", "endLine": 246, "endColumn": 28, "suggestions": "921"}, {"ruleId": "728", "severity": 1, "message": "922", "line": 47, "column": 20, "nodeType": "730", "messageId": "731", "endLine": 47, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "923", "line": 54, "column": 19, "nodeType": "730", "messageId": "731", "endLine": 54, "endColumn": 29}, {"ruleId": "728", "severity": 1, "message": "924", "line": 321, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 321, "endColumn": 26}, {"ruleId": "733", "severity": 1, "message": "925", "line": 226, "column": 6, "nodeType": "735", "endLine": 226, "endColumn": 20, "suggestions": "926", "suppressions": "927"}, {"ruleId": "733", "severity": 1, "message": "925", "line": 238, "column": 6, "nodeType": "735", "endLine": 238, "endColumn": 51, "suggestions": "928", "suppressions": "929"}, {"ruleId": "728", "severity": 1, "message": "930", "line": 22, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 22, "endColumn": 23}, {"ruleId": "728", "severity": 1, "message": "931", "line": 33, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 33, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "932", "line": 34, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 34, "endColumn": 25}, {"ruleId": "728", "severity": 1, "message": "933", "line": 22, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 22, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "934", "line": 24, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 24, "endColumn": 26}, {"ruleId": "728", "severity": 1, "message": "935", "line": 390, "column": 15, "nodeType": "730", "messageId": "731", "endLine": 390, "endColumn": 28}, {"ruleId": "733", "severity": 1, "message": "936", "line": 488, "column": 6, "nodeType": "735", "endLine": 488, "endColumn": 18, "suggestions": "937"}, {"ruleId": "733", "severity": 1, "message": "768", "line": 58, "column": 6, "nodeType": "735", "endLine": 58, "endColumn": 12, "suggestions": "938"}, {"ruleId": "733", "severity": 1, "message": "768", "line": 83, "column": 6, "nodeType": "735", "endLine": 83, "endColumn": 52, "suggestions": "939"}, {"ruleId": "728", "severity": 1, "message": "848", "line": 5, "column": 17, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "940", "line": 8, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 8, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "941", "line": 8, "column": 32, "nodeType": "730", "messageId": "731", "endLine": 8, "endColumn": 44}, {"ruleId": "728", "severity": 1, "message": "759", "line": 9, "column": 24, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 49}, {"ruleId": "733", "severity": 1, "message": "942", "line": 71, "column": 6, "nodeType": "735", "endLine": 71, "endColumn": 20, "suggestions": "943"}, {"ruleId": "728", "severity": 1, "message": "915", "line": 1, "column": 38, "nodeType": "730", "messageId": "731", "endLine": 1, "endColumn": 49}, {"ruleId": "728", "severity": 1, "message": "833", "line": 1, "column": 51, "nodeType": "730", "messageId": "731", "endLine": 1, "endColumn": 57}, {"ruleId": "728", "severity": 1, "message": "747", "line": 5, "column": 69, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 75}, {"ruleId": "728", "severity": 1, "message": "944", "line": 14, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 14, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "945", "line": 18, "column": 7, "nodeType": "730", "messageId": "731", "endLine": 18, "endColumn": 15}, {"ruleId": "733", "severity": 1, "message": "925", "line": 238, "column": 6, "nodeType": "735", "endLine": 238, "endColumn": 8, "suggestions": "946"}, {"ruleId": "733", "severity": 1, "message": "947", "line": 271, "column": 6, "nodeType": "735", "endLine": 271, "endColumn": 73, "suggestions": "948"}, {"ruleId": "728", "severity": 1, "message": "915", "line": 1, "column": 38, "nodeType": "730", "messageId": "731", "endLine": 1, "endColumn": 49}, {"ruleId": "728", "severity": 1, "message": "949", "line": 9, "column": 38, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 53}, {"ruleId": "728", "severity": 1, "message": "944", "line": 14, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 14, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "945", "line": 18, "column": 7, "nodeType": "730", "messageId": "731", "endLine": 18, "endColumn": 15}, {"ruleId": "733", "severity": 1, "message": "925", "line": 226, "column": 6, "nodeType": "735", "endLine": 226, "endColumn": 8, "suggestions": "950"}, {"ruleId": "733", "severity": 1, "message": "947", "line": 259, "column": 6, "nodeType": "735", "endLine": 259, "endColumn": 73, "suggestions": "951"}, {"ruleId": "728", "severity": 1, "message": "834", "line": 6, "column": 8, "nodeType": "730", "messageId": "731", "endLine": 6, "endColumn": 14}, {"ruleId": "733", "severity": 1, "message": "952", "line": 51, "column": 6, "nodeType": "735", "endLine": 51, "endColumn": 8, "suggestions": "953"}, {"ruleId": "728", "severity": 1, "message": "890", "line": 4, "column": 27, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "954", "line": 4, "column": 62, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 67}, {"ruleId": "728", "severity": 1, "message": "955", "line": 7, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 7, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "760", "line": 7, "column": 37, "nodeType": "730", "messageId": "731", "endLine": 7, "endColumn": 51}, {"ruleId": "728", "severity": 1, "message": "885", "line": 16, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 16, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "889", "line": 16, "column": 16, "nodeType": "730", "messageId": "731", "endLine": 16, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "956", "line": 59, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 59, "endColumn": 27}, {"ruleId": "733", "severity": 1, "message": "925", "line": 104, "column": 6, "nodeType": "735", "endLine": 104, "endColumn": 8, "suggestions": "957"}, {"ruleId": "733", "severity": 1, "message": "958", "line": 114, "column": 6, "nodeType": "735", "endLine": 114, "endColumn": 35, "suggestions": "959"}, {"ruleId": "728", "severity": 1, "message": "738", "line": 11, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 11, "endColumn": 10}, {"ruleId": "728", "severity": 1, "message": "848", "line": 14, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 14, "endColumn": 8}, {"ruleId": "728", "severity": 1, "message": "887", "line": 2, "column": 31, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 38}, {"ruleId": "733", "severity": 1, "message": "775", "line": 32, "column": 6, "nodeType": "735", "endLine": 32, "endColumn": 12, "suggestions": "960"}, {"ruleId": "728", "severity": 1, "message": "954", "line": 3, "column": 59, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 64}, {"ruleId": "728", "severity": 1, "message": "747", "line": 5, "column": 48, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 54}, {"ruleId": "728", "severity": 1, "message": "940", "line": 8, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 8, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "761", "line": 12, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 12, "endColumn": 19}, {"ruleId": "728", "severity": 1, "message": "885", "line": 21, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 21, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "961", "line": 33, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 33, "endColumn": 18}, {"ruleId": "733", "severity": 1, "message": "734", "line": 190, "column": 6, "nodeType": "735", "endLine": 190, "endColumn": 8, "suggestions": "962"}, {"ruleId": "728", "severity": 1, "message": "744", "line": 5, "column": 8, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 16}, {"ruleId": "728", "severity": 1, "message": "738", "line": 5, "column": 24, "nodeType": "730", "messageId": "731", "endLine": 5, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "760", "line": 9, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "885", "line": 22, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 22, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "963", "line": 127, "column": 27, "nodeType": "730", "messageId": "731", "endLine": 127, "endColumn": 39}, {"ruleId": "733", "severity": 1, "message": "964", "line": 300, "column": 6, "nodeType": "735", "endLine": 300, "endColumn": 34, "suggestions": "965"}, {"ruleId": "728", "severity": 1, "message": "883", "line": 2, "column": 16, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 21}, {"ruleId": "728", "severity": 1, "message": "966", "line": 2, "column": 23, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 29}, {"ruleId": "728", "severity": 1, "message": "737", "line": 2, "column": 60, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 65}, {"ruleId": "728", "severity": 1, "message": "868", "line": 2, "column": 67, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 72}, {"ruleId": "728", "severity": 1, "message": "770", "line": 3, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "784", "line": 3, "column": 24, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 38}, {"ruleId": "728", "severity": 1, "message": "885", "line": 6, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 6, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "889", "line": 6, "column": 16, "nodeType": "730", "messageId": "731", "endLine": 6, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "823", "line": 7, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 7, "endColumn": 15}, {"ruleId": "728", "severity": 1, "message": "967", "line": 10, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 10, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "968", "line": 13, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 13, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "969", "line": 13, "column": 19, "nodeType": "730", "messageId": "731", "endLine": 13, "endColumn": 29}, {"ruleId": "728", "severity": 1, "message": "738", "line": 4, "column": 34, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 41}, {"ruleId": "728", "severity": 1, "message": "890", "line": 4, "column": 64, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 68}, {"ruleId": "728", "severity": 1, "message": "756", "line": 10, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 10, "endColumn": 22}, {"ruleId": "733", "severity": 1, "message": "970", "line": 49, "column": 6, "nodeType": "735", "endLine": 49, "endColumn": 20, "suggestions": "971"}, {"ruleId": "728", "severity": 1, "message": "876", "line": 3, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 12}, {"ruleId": "728", "severity": 1, "message": "877", "line": 3, "column": 14, "nodeType": "730", "messageId": "731", "endLine": 3, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "909", "line": 4, "column": 19, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 23}, {"ruleId": "728", "severity": 1, "message": "747", "line": 4, "column": 25, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 31}, {"ruleId": "733", "severity": 1, "message": "972", "line": 65, "column": 6, "nodeType": "735", "endLine": 65, "endColumn": 20, "suggestions": "973"}, {"ruleId": "728", "severity": 1, "message": "738", "line": 4, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "745", "line": 4, "column": 29, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 37}, {"ruleId": "728", "severity": 1, "message": "744", "line": 4, "column": 39, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 47}, {"ruleId": "728", "severity": 1, "message": "767", "line": 17, "column": 22, "nodeType": "730", "messageId": "731", "endLine": 17, "endColumn": 31}, {"ruleId": "733", "severity": 1, "message": "974", "line": 69, "column": 6, "nodeType": "735", "endLine": 69, "endColumn": 20, "suggestions": "975", "suppressions": "976"}, {"ruleId": "728", "severity": 1, "message": "977", "line": 14, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 14, "endColumn": 21}, {"ruleId": "728", "severity": 1, "message": "978", "line": 16, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 16, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "827", "line": 138, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 138, "endColumn": 18}, {"ruleId": "733", "severity": 1, "message": "979", "line": 208, "column": 6, "nodeType": "735", "endLine": 208, "endColumn": 73, "suggestions": "980", "suppressions": "981"}, {"ruleId": "733", "severity": 1, "message": "982", "line": 214, "column": 6, "nodeType": "735", "endLine": 214, "endColumn": 40, "suggestions": "983", "suppressions": "984"}, {"ruleId": "733", "severity": 1, "message": "979", "line": 87, "column": 6, "nodeType": "735", "endLine": 87, "endColumn": 20, "suggestions": "985"}, {"ruleId": "733", "severity": 1, "message": "986", "line": 93, "column": 6, "nodeType": "735", "endLine": 93, "endColumn": 37, "suggestions": "987"}, {"ruleId": "728", "severity": 1, "message": "988", "line": 16, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 16, "endColumn": 21}, {"ruleId": "728", "severity": 1, "message": "989", "line": 17, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 17, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "990", "line": 31, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 31, "endColumn": 26}, {"ruleId": "728", "severity": 1, "message": "991", "line": 31, "column": 28, "nodeType": "730", "messageId": "731", "endLine": 31, "endColumn": 47}, {"ruleId": "733", "severity": 1, "message": "992", "line": 38, "column": 6, "nodeType": "735", "endLine": 38, "endColumn": 20, "suggestions": "993"}, {"ruleId": "728", "severity": 1, "message": "994", "line": 41, "column": 12, "nodeType": "730", "messageId": "731", "endLine": 41, "endColumn": 26}, {"ruleId": "733", "severity": 1, "message": "925", "line": 443, "column": 6, "nodeType": "735", "endLine": 443, "endColumn": 8, "suggestions": "995"}, {"ruleId": "728", "severity": 1, "message": "890", "line": 4, "column": 31, "nodeType": "730", "messageId": "731", "endLine": 4, "endColumn": 35}, {"ruleId": "728", "severity": 1, "message": "851", "line": 8, "column": 45, "nodeType": "730", "messageId": "731", "endLine": 8, "endColumn": 67}, {"ruleId": "728", "severity": 1, "message": "885", "line": 14, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 14, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "904", "line": 17, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 17, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "814", "line": 78, "column": 11, "nodeType": "730", "messageId": "731", "endLine": 78, "endColumn": 24}, {"ruleId": "728", "severity": 1, "message": "996", "line": 79, "column": 11, "nodeType": "730", "messageId": "731", "endLine": 79, "endColumn": 24}, {"ruleId": "728", "severity": 1, "message": "827", "line": 290, "column": 13, "nodeType": "730", "messageId": "731", "endLine": 290, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "997", "line": 428, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 428, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "998", "line": 433, "column": 9, "nodeType": "730", "messageId": "731", "endLine": 433, "endColumn": 31}, {"ruleId": "733", "severity": 1, "message": "999", "line": 327, "column": 6, "nodeType": "735", "endLine": 327, "endColumn": 15, "suggestions": "1000"}, {"ruleId": "728", "severity": 1, "message": "737", "line": 2, "column": 24, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 29}, {"ruleId": "728", "severity": 1, "message": "1001", "line": 22, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 22, "endColumn": 21}, {"ruleId": "728", "severity": 1, "message": "1002", "line": 22, "column": 23, "nodeType": "730", "messageId": "731", "endLine": 22, "endColumn": 37}, {"ruleId": "728", "severity": 1, "message": "755", "line": 8, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 8, "endColumn": 15}, {"ruleId": "728", "severity": 1, "message": "1003", "line": 11, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 11, "endColumn": 15}, {"ruleId": "728", "severity": 1, "message": "1004", "line": 8, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 8, "endColumn": 17}, {"ruleId": "728", "severity": 1, "message": "1005", "line": 9, "column": 3, "nodeType": "730", "messageId": "731", "endLine": 9, "endColumn": 18}, {"ruleId": "1006", "severity": 1, "message": "1007", "line": 144, "column": 9, "nodeType": "1008", "messageId": "1009", "endLine": 179, "endColumn": 10}, {"ruleId": "1006", "severity": 1, "message": "1007", "line": 268, "column": 9, "nodeType": "1008", "messageId": "1009", "endLine": 303, "endColumn": 10}, {"ruleId": "733", "severity": 1, "message": "1010", "line": 317, "column": 6, "nodeType": "735", "endLine": 317, "endColumn": 99, "suggestions": "1011"}, {"ruleId": "733", "severity": 1, "message": "1012", "line": 212, "column": 6, "nodeType": "735", "endLine": 212, "endColumn": 19, "suggestions": "1013"}, {"ruleId": "733", "severity": 1, "message": "1014", "line": 225, "column": 6, "nodeType": "735", "endLine": 225, "endColumn": 28, "suggestions": "1015"}, {"ruleId": "728", "severity": 1, "message": "900", "line": 2, "column": 10, "nodeType": "730", "messageId": "731", "endLine": 2, "endColumn": 13}, "no-unused-vars", "'Layout' is defined but never used.", "Identifier", "unusedVar", "'Register' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'user'. Either include it or remove the dependency array.", "ArrayExpression", ["1016"], "'Space' is defined but never used.", "'Divider' is defined but never used.", "'EnvironmentOutlined' is defined but never used.", "'preventRedirect' is assigned a value but never used.", "'grades' is assigned a value but never used.", "'setGrades' is assigned a value but never used.", "'hasSubmitted' is assigned a value but never used.", "'Progress' is defined but never used.", "'Timeline' is defined but never used.", "'Descriptions' is defined but never used.", "'Avatar' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchHomework' and 'fetchStatistics'. Either include them or remove the dependency array.", ["1017"], "'Cascader' is defined but never used.", "'apiLogin' is defined but never used.", "'selectedDistrict' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchProvinces'. Either include it or remove the dependency array.", ["1018"], "'BookOutlined' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'SyncOutlined' is defined but never used.", "'ClockCircleOutlined' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'TrophyOutlined' is defined but never used.", "'CalendarOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'calculateStudentStats'. Either include it or remove the dependency array.", ["1019"], "'studentHomework' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchBoundStudents'. Either include it or remove the dependency array.", ["1020"], "'Paragraph' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchStatistics'. Either include it or remove the dependency array.", ["1021"], "'UserOutlined' is defined but never used.", "'TeamOutlined' is defined but never used.", "'getClassesBySchool' is defined but never used.", "'TabPane' is assigned a value but never used.", "'setPageSize' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchClasses'. Either include it or remove the dependency array.", ["1022"], "'handleAddClass' is assigned a value but never used.", "'handleClassSelectionChange' is assigned a value but never used.", "'studentColumns' is assigned a value but never used.", "'showAddStudentModal' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'SearchOutlined' is defined but never used.", "'totalClasses' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchClassesData', 'fetchRegionsData', and 'fetchSubjectsData'. Either include them or remove the dependency array.", ["1023"], "React Hook useEffect has a missing dependency: 'filterData'. Either include it or remove the dependency array.", ["1024"], "React Hook useEffect has a missing dependency: 'generateTreeData'. Either include it or remove the dependency array.", ["1025"], "React Hook useEffect has a missing dependency: 'updateFilteredSubjects'. Either include it or remove the dependency array.", ["1026"], ["1027"], "React Hook useEffect has missing dependencies: 'fetchClassesData' and 'fetchSubjectsData'. Either include them or remove the dependency array.", ["1028"], ["1029"], ["1030"], "'Link' is defined but never used.", "'Breadcrumb' is defined but never used.", "'Dropdown' is defined but never used.", "'MessageOutlined' is defined but never used.", "'BarChartOutlined' is defined but never used.", "'LogoutOutlined' is defined but never used.", "'AppLayout' is defined but never used.", "'Header' is assigned a value but never used.", "'question_content' is assigned a value but never used.", "'correct_answer' is assigned a value but never used.", "'wrong_answer' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'studentAnswer' is assigned a value but never used.", "'markAsCompleted' is defined but never used.", "'ExerciseItem' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'Menu' is defined but never used.", "'DashboardOutlined' is defined but never used.", "'getUsers' is defined but never used.", "'UserManagement' is defined but never used.", "'Sider' is assigned a value but never used.", "'Option' is assigned a value but never used.", "'users' is assigned a value but never used.", "'systemInfo' is assigned a value but never used.", "'studentStats' is assigned a value but never used.", "'token' is assigned a value but never used.", "'handleAddUser' is assigned a value but never used.", "'userColumns' is assigned a value but never used.", "'navigateToSchoolManagement' is assigned a value but never used.", "'renderStatsDashboard' is assigned a value but never used.", "'roleEnabled' is assigned a value but never used.", "'useRef' is defined but never used.", "'moment' is defined but never used.", "'getDashboardStatistics' is defined but never used.", "'fetchSchoolClasses' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isSystemLevel'. Either include it or remove the dependency array.", ["1031"], "'handleExport' is assigned a value but never used.", "'class_count' is assigned a value but never used.", "'student_count' is assigned a value but never used.", "'homework_count' is assigned a value but never used.", "'corrected_count' is assigned a value but never used.", "'filename' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchGrades', 'fetchSchools', and 'onFilterChange'. Either include them or remove the dependency array. If 'onFilterChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1032"], ["1033"], "'Modal' is defined but never used.", "'Popconfirm' is defined but never used.", "'WarningOutlined' is defined but never used.", "'QuestionCircleOutlined' is defined but never used.", "'qs' is defined but never used.", "'selectedTab' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getHeaders'. Either include it or remove the dependency array.", ["1034"], "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", "'Tooltip' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSchoolData'. Either include it or remove the dependency array.", ["1035"], ["1036"], "React Hook useEffect has missing dependencies: 'activeTab' and 'fetchSchoolData'. Either include them or remove the dependency array.", ["1037"], "'targetSchoolId' is assigned a value but never used.", "'useAuth' is defined but never used.", "'Radio' is defined but never used.", "'HomeOutlined' is defined but never used.", "'PlusOutlined' is defined but never used.", "'register' is defined but never used.", "React Hook useEffect has missing dependencies: 'availableRoles', 'isStudent', 'registrationSettings', and 'selectedRole'. Either include them or remove the dependency array.", ["1038"], "React Hook useEffect has a missing dependency: 'getInitialValues'. Either include it or remove the dependency array.", ["1039"], "'Row' is defined but never used.", "'Col' is defined but never used.", "'selectedClassId' is assigned a value but never used.", "'batchCreateStudents' is assigned a value but never used.", "'isMobile' is assigned a value but never used.", "'isFeatureEnabled' is assigned a value but never used.", "'tempParts' is assigned a value but never used.", "'Input' is defined but never used.", "'CloseCircleOutlined' is defined but never used.", "'Title' is assigned a value but never used.", "'currentBinding' is assigned a value but never used.", "'message' is defined but never used.", "'lastUserMessage' is assigned a value but never used.", "'Text' is assigned a value but never used.", "'Spin' is defined but never used.", "'Card' is defined but never used.", "'OrderedListOutlined' is defined but never used.", "'BarsOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSubjects'. Either include it or remove the dependency array.", ["1040"], "'roles' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1041"], "'renderMobileUserCards' is assigned a value but never used.", "'Tag' is defined but never used.", "'DeleteOutlined' is defined but never used.", "'modalVisible' is assigned a value but never used.", "'setModalVisible' is assigned a value but never used.", "'form' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPermissions'. Either include it or remove the dependency array.", ["1042"], "'CloseOutlined' is defined but never used.", "'applications' is assigned a value but never used.", "'List' is defined but never used.", "'FileImageOutlined' is defined but never used.", "'ReactMarkdown' is defined but never used.", "'location' is assigned a value but never used.", "'authUser' is assigned a value but never used.", "'subjectName' is assigned a value but never used.", "'useCallback' is defined but never used.", "'assignmentId' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'form' and 'location.state?.assignmentId'. Either include them or remove the dependency array.", ["1043"], "React Hook useEffect has a missing dependency: 'fetchAssignmentDetails'. Either include it or remove the dependency array.", ["1044"], ["1045"], "'setViewMode' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'handleBatchDelete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAssignments'. Either include it or remove the dependency array.", ["1046"], ["1047"], ["1048"], ["1049"], "'submitSuccess' is assigned a value but never used.", "'students' is assigned a value but never used.", "'selectedStudent' is assigned a value but never used.", "'classes' is assigned a value but never used.", "'defaultSubjectId' is assigned a value but never used.", "'isSystemLevel' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'location.state?.filters' and 'location.state?.isSystemLevel'. Either include them or remove the dependency array.", ["1050"], ["1051"], ["1052"], "'EyeOutlined' is defined but never used.", "'EditOutlined' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchAssignment' and 'fetchHomeworkProgress'. Either include them or remove the dependency array.", ["1053"], "'RangePicker' is assigned a value but never used.", "'debounce' is assigned a value but never used.", ["1054"], "React Hook useEffect has missing dependencies: 'fetchAssignments' and 'fetchHomeworks'. Either include them or remove the dependency array.", ["1055"], "'HistoryOutlined' is defined but never used.", ["1056"], ["1057"], "React Hook useEffect has a missing dependency: 'calculateStats'. Either include it or remove the dependency array.", ["1058"], "'Empty' is defined but never used.", "'UploadOutlined' is defined but never used.", "'isMakeupSubmission' is assigned a value but never used.", ["1059"], "React Hook useEffect has a missing dependency: 'filterAssignments'. Either include it or remove the dependency array.", ["1060"], ["1061"], "'subjects' is assigned a value but never used.", ["1062"], "'correctCount' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'user.id'. Either include it or remove the dependency array.", ["1063"], "'Button' is defined but never used.", "'searchForm' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchOverviewData'. Either include it or remove the dependency array.", ["1064"], "React Hook useEffect has a missing dependency: 'fetchQuestionsData'. Either include it or remove the dependency array.", ["1065"], "React Hook useEffect has a missing dependency: 'fetchSuggestionsData'. Either include it or remove the dependency array.", ["1066"], ["1067"], "'InfoCircleOutlined' is defined but never used.", "'BugOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudentsData'. Either include it or remove the dependency array.", ["1068"], ["1069"], "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["1070"], ["1071"], ["1072"], "React Hook useEffect has a missing dependency: 'fetchParentReport'. Either include it or remove the dependency array.", ["1073"], "'FolderOpenOutlined' is defined but never used.", "'FilterOutlined' is defined but never used.", "'selectedStudents' is assigned a value but never used.", "'setSelectedStudents' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["1074"], "'setAssignments' is assigned a value but never used.", ["1075"], "'correctAnswer' is assigned a value but never used.", "'requestAIErrorAnalysis' is assigned a value but never used.", "'requestAIReinforcement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'startCamera' and 'stopCamera'. Either include them or remove the dependency array.", ["1076"], "'showDetails' is assigned a value but never used.", "'setShowDetails' is assigned a value but never used.", "'StarOutlined' is defined but never used.", "'ZoomInOutlined' is defined but never used.", "'ZoomOutOutlined' is defined but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useCallback has a missing dependency: 'isPinching'. Either include it or remove the dependency array.", ["1077"], "React Hook useEffect has a missing dependency: 'checkReminders'. Either include it or remove the dependency array.", ["1078"], "React Hook useEffect has a missing dependency: 'showNotification'. Either include it or remove the dependency array.", ["1079"], {"desc": "1080", "fix": "1081"}, {"desc": "1082", "fix": "1083"}, {"desc": "1084", "fix": "1085"}, {"desc": "1086", "fix": "1087"}, {"desc": "1088", "fix": "1089"}, {"desc": "1090", "fix": "1091"}, {"desc": "1092", "fix": "1093"}, {"desc": "1094", "fix": "1095"}, {"desc": "1096", "fix": "1097"}, {"desc": "1098", "fix": "1099"}, {"desc": "1100", "fix": "1101"}, {"desc": "1100", "fix": "1102"}, {"desc": "1103", "fix": "1104"}, {"desc": "1105", "fix": "1106"}, {"desc": "1100", "fix": "1107"}, {"desc": "1108", "fix": "1109"}, {"desc": "1110", "fix": "1111"}, {"kind": "1112", "justification": "1113"}, {"desc": "1114", "fix": "1115"}, {"desc": "1116", "fix": "1117"}, {"desc": "1118", "fix": "1119"}, {"desc": "1120", "fix": "1121"}, {"desc": "1122", "fix": "1123"}, {"desc": "1124", "fix": "1125"}, {"desc": "1126", "fix": "1127"}, {"desc": "1128", "fix": "1129"}, {"desc": "1130", "fix": "1131"}, {"desc": "1132", "fix": "1133"}, {"desc": "1134", "fix": "1135"}, {"desc": "1136", "fix": "1137"}, {"desc": "1138", "fix": "1139"}, {"kind": "1112", "justification": "1113"}, {"desc": "1140", "fix": "1141"}, {"kind": "1112", "justification": "1113"}, {"desc": "1142", "fix": "1143"}, {"desc": "1144", "fix": "1145"}, {"desc": "1146", "fix": "1147"}, {"desc": "1148", "fix": "1149"}, {"desc": "1150", "fix": "1151"}, {"desc": "1152", "fix": "1153"}, {"desc": "1150", "fix": "1154"}, {"desc": "1152", "fix": "1155"}, {"desc": "1156", "fix": "1157"}, {"desc": "1150", "fix": "1158"}, {"desc": "1159", "fix": "1160"}, {"desc": "1092", "fix": "1161"}, {"desc": "1162", "fix": "1163"}, {"desc": "1164", "fix": "1165"}, {"desc": "1166", "fix": "1167"}, {"desc": "1168", "fix": "1169"}, {"desc": "1170", "fix": "1171"}, {"kind": "1112", "justification": "1113"}, {"desc": "1172", "fix": "1173"}, {"kind": "1112", "justification": "1113"}, {"desc": "1174", "fix": "1175"}, {"kind": "1112", "justification": "1113"}, {"desc": "1176", "fix": "1177"}, {"desc": "1178", "fix": "1179"}, {"desc": "1180", "fix": "1181"}, {"desc": "1150", "fix": "1182"}, {"desc": "1183", "fix": "1184"}, {"desc": "1185", "fix": "1186"}, {"desc": "1187", "fix": "1188"}, {"desc": "1189", "fix": "1190"}, "Update the dependencies array to be: [initialized, user]", {"range": "1191", "text": "1192"}, "Update the dependencies array to be: [fetchHomework, fetchStatistics, studentId]", {"range": "1193", "text": "1194"}, "Update the dependencies array to be: [fetchProvinces]", {"range": "1195", "text": "1196"}, "Update the dependencies array to be: [calculateStudentStats, user]", {"range": "1197", "text": "1198"}, "Update the dependencies array to be: [fetchBoundStudents]", {"range": "1199", "text": "1200"}, "Update the dependencies array to be: [fetchStatistics, studentId]", {"range": "1201", "text": "1202"}, "Update the dependencies array to be: [fetchClasses, user]", {"range": "1203", "text": "1204"}, "Update the dependencies array to be: [fetchClassesData, fetchRegionsData, fetchSubjectsData]", {"range": "1205", "text": "1206"}, "Update the dependencies array to be: [searchText, gradeFilter, schoolFilter, classesData, regionSchools, provinceFilter, cityFilter, districtFilter, filterData]", {"range": "1207", "text": "1208"}, "Update the dependencies array to be: [filteredData, generateTreeData, showTreeView, treeViewMode]", {"range": "1209", "text": "1210"}, "Update the dependencies array to be: [selectedClass, subjectsData, updateFilteredSubjects]", {"range": "1211", "text": "1212"}, {"range": "1213", "text": "1212"}, "Update the dependencies array to be: [fetchClassesData, fetchSubjectsData, user]", {"range": "1214", "text": "1215"}, "Update the dependencies array to be: [searchText, gradeFilter, classFilter, classesData, filterData]", {"range": "1216", "text": "1217"}, {"range": "1218", "text": "1212"}, "Update the dependencies array to be: [user, filters, isSystemLevel]", {"range": "1219", "text": "1220"}, "Update the dependencies array to be: [user, isSuperAdmin, fetchSchools, fetchGrades, onFilterChange]", {"range": "1221", "text": "1222"}, "directive", "", "Update the dependencies array to be: [getHeaders]", {"range": "1223", "text": "1224"}, "Update the dependencies array to be: [selectedSchoolId, schoolId, user, fetchSchoolData]", {"range": "1225", "text": "1226"}, "Update the dependencies array to be: [fetchSchoolData, schoolId]", {"range": "1227", "text": "1228"}, "Update the dependencies array to be: [activeTab, fetchSchoolData, selectedSchoolId]", {"range": "1229", "text": "1230"}, "Update the dependencies array to be: [navigate, form, isStudent, selectedRole, availableRoles, registrationSettings]", {"range": "1231", "text": "1232"}, "Update the dependencies array to be: [settingsLoading, availableRoles, form, getInitialValues]", {"range": "1233", "text": "1234"}, "Update the dependencies array to be: [fetchSubjects, selectedCategoryId]", {"range": "1235", "text": "1236"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1237", "text": "1238"}, "Update the dependencies array to be: [fetchPermissions, selectedRole, subjects]", {"range": "1239", "text": "1240"}, "Update the dependencies array to be: [form, location.state?.assignmentId, user]", {"range": "1241", "text": "1242"}, "Update the dependencies array to be: [location.state.assignmentId, form, fetchAssignmentDetails]", {"range": "1243", "text": "1244"}, "Update the dependencies array to be: [location.state, form, fetchAssignmentDetails]", {"range": "1245", "text": "1246"}, "Update the dependencies array to be: [fetchAssignments, searchParams]", {"range": "1247", "text": "1248"}, "Update the dependencies array to be: [location.state, navigate, location.pathname, fetchAssignments]", {"range": "1249", "text": "1250"}, "Update the dependencies array to be: [form, location.state?.filters, location.state?.isSystemLevel, user]", {"range": "1251", "text": "1252"}, "Update the dependencies array to be: [fetchStatistics, user]", {"range": "1253", "text": "1254"}, "Update the dependencies array to be: [selectedClass, selectedAssignment, dateRange, fetchStatistics]", {"range": "1255", "text": "1256"}, "Update the dependencies array to be: [assignmentId, fetchAssignment, fetchHomeworkProgress]", {"range": "1257", "text": "1258"}, "Update the dependencies array to be: [fetchAssignments]", {"range": "1259", "text": "1260"}, "Update the dependencies array to be: [location.state, navigate, location.pathname, filters.assignmentId, fetchAssignments, fetchHomeworks]", {"range": "1261", "text": "1262"}, {"range": "1263", "text": "1260"}, {"range": "1264", "text": "1262"}, "Update the dependencies array to be: [calculateStats]", {"range": "1265", "text": "1266"}, {"range": "1267", "text": "1260"}, "Update the dependencies array to be: [assignments, filterAssignments, location.state]", {"range": "1268", "text": "1269"}, {"range": "1270", "text": "1204"}, "Update the dependencies array to be: [user]", {"range": "1271", "text": "1272"}, "Update the dependencies array to be: [homeworkId, location.state, user.id]", {"range": "1273", "text": "1274"}, "Update the dependencies array to be: [assignmentId, fetchOverviewData]", {"range": "1275", "text": "1276"}, "Update the dependencies array to be: [assignmentId, fetchQuestionsData]", {"range": "1277", "text": "1278"}, "Update the dependencies array to be: [assignmentId, fetchSuggestionsData]", {"range": "1279", "text": "1280"}, "Update the dependencies array to be: [assignmentId, filters.sortBy, filters.order, filters.filterStatus, fetchStudentsData]", {"range": "1281", "text": "1282"}, "Update the dependencies array to be: [applyFilters, filters.searchText, studentsData]", {"range": "1283", "text": "1284"}, "Update the dependencies array to be: [assignmentId, fetchStudentsData]", {"range": "1285", "text": "1286"}, "Update the dependencies array to be: [selectedStudent, assignmentId, fetchParentReport]", {"range": "1287", "text": "1288"}, "Update the dependencies array to be: [assignmentId, fetchStudents]", {"range": "1289", "text": "1290"}, {"range": "1291", "text": "1260"}, "Update the dependencies array to be: [startCamera, stopCamera, visible]", {"range": "1292", "text": "1293"}, "Update the dependencies array to be: [isPinching, isDragging, isResizing, dragStart.x, dragStart.y, imageSize.width, imageSize.height, cropArea.width, cropArea.height, resizeHandle]", {"range": "1294", "text": "1295"}, "Update the dependencies array to be: [assignments, checkReminders]", {"range": "1296", "text": "1297"}, "Update the dependencies array to be: [reminders, lastCheck, showNotification]", {"range": "1298", "text": "1299"}, [3289, 3302], "[initialized, user]", [2854, 2865], "[fetchHomework, fetchStatistics, studentId]", [8049, 8051], "[fetchProvinces]", [5043, 5049], "[calculateStudentStats, user]", [4275, 4277], "[fetchBoundStudents]", [1376, 1387], "[fetchStatistics, studentId]", [2613, 2619], "[fetchClasses, user]", [52315, 52317], "[fetchClassesData, fetchRegionsData, fetchSubjectsData]", [52364, 52475], "[searchText, gradeFilter, schoolFilter, classesData, regionSchools, provinceFilter, cityFilter, districtFilter, filterData]", [52540, 52582], "[filteredData, generateTreeData, showTreeView, treeViewMode]", [52668, 52697], "[selectedClass, subjectsData, updateFilteredSubjects]", [52783, 52812], [60091, 60097], "[fetchClassesData, fetchSubjectsData, user]", [60144, 60195], "[searchText, gradeFilter, classFilter, classesData, filterData]", [60281, 60310], [7796, 7811], "[user, filters, isSystemLevel]", [6764, 6784], "[user, isSuperAdmin, fetchSchools, fetchGrades, onFilterChange]", [3014, 3016], "[getHeaders]", [2940, 2974], "[selectedSchoolId, schoolId, user, fetchSchoolData]", [3794, 3804], "[fetchSchoolData, schoolId]", [5266, 5284], "[activeTab, fetchSchoolData, selectedSchoolId]", [8384, 8400], "[navigate, form, isStudent, selectedRole, availableRoles, registrationSettings]", [8792, 8831], "[settingsLoading, availableRoles, form, getInitialValues]", [3573, 3593], "[fetchSubjects, selectedCategoryId]", [3465, 3467], "[fetchUsers]", [3378, 3402], "[fetchPermissions, selectedRole, subjects]", [7589, 7595], "[form, location.state?.assignmentId, user]", [8024, 8060], "[location.state.assignmentId, form, fetchAssignmentDetails]", [8533, 8555], "[location.state, form, fetchAssignmentDetails]", [7034, 7048], "[fetchAssignments, searchParams]", [7388, 7433], "[location.state, navigate, location.pathname, fetchAssignments]", [16240, 16252], "[form, location.state?.filters, location.state?.isSystemLevel, user]", [1938, 1944], "[fetchStatistics, user]", [2632, 2678], "[selectedClass, selectedAssignment, dateRange, fetchStatistics]", [2238, 2252], "[assignmentId, fetchAssignment, fetchHomeworkProgress]", [7266, 7268], "[fetchAssignments]", [8289, 8356], "[location.state, navigate, location.pathname, filters.assignmentId, fetchAssignments, fetchHomeworks]", [6813, 6815], [7836, 7903], [1629, 1631], "[calculateStats]", [3326, 3328], [3650, 3679], "[assignments, filterAssignments, location.state]", [1049, 1055], [6701, 6703], "[user]", [13444, 13472], "[homeworkId, location.state, user.id]", [1278, 1292], "[assignmentId, fetchOverviewData]", [2179, 2193], "[assignmentId, fetchQuestionsData]", [1796, 1810], "[assignmentId, fetchSuggestionsData]", [6102, 6169], "[assignmentId, filters.sortBy, filters.order, filters.filterStatus, fetchStudentsData]", [6322, 6356], "[applyFilters, filters.searchText, studentsData]", [2413, 2427], "[assignmentId, fetchStudentsData]", [2531, 2562], "[selectedStudent, assignmentId, fetchParentReport]", [1106, 1120], "[assignmentId, fetchStudents]", [11519, 11521], [8717, 8726], "[startCamera, stopCamera, visible]", [10893, 10986], "[isPinching, isDragging, isResizing, dragStart.x, dragStart.y, imageSize.width, imageSize.height, cropArea.width, cropArea.height, resizeHandle]", [5890, 5903], "[assignments, checkReminders]", [6246, 6268], "[reminders, lastCheck, showNotification]"]