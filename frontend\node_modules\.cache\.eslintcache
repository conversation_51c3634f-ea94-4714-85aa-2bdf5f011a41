[{"D:\\pythonproject\\checkingsys\\frontend\\src\\index.js": "1", "D:\\pythonproject\\checkingsys\\frontend\\src\\App.js": "2", "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\auth.js": "3", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Register.js": "4", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StudentHomeworkDetail.js": "5", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Login.js": "6", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Home.js": "7", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ParentDashboard.js": "8", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StudentReport.js": "9", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ClassManagement.js": "10", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ImprovedSystemClassManagement.js": "11", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ImprovedClassManagement.js": "12", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\WrongQuestionTraining.js": "13", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\UserProfile.js": "14", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\AdminDashboard.js": "15", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StatisticsPage.js": "16", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\UserManagementPage.js": "17", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\HomeworkManagement.js": "18", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\DatabaseManagementPage.js": "19", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\TeacherUserManagementPage.js": "20", "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\PhotoSolvePage.js": "21", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PrivateRoute.js": "22", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolManagement.js": "23", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\AppLayout.js": "24", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StandaloneRegister.js": "25", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SuperSchoolManagement.js": "26", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\RegionManagement.js": "27", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\index.js": "28", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SystemHomeworkAnalysis\\index.js": "29", "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\api.js": "30", "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\detector.js": "31", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentBindingVerification.js": "32", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\AIAssistant.js": "33", "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\teacherDataFix.js": "34", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\UserSchoolApplications.js": "35", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SubjectManagement.js": "36", "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\roleUtils.js": "37", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\UserManagement.js": "38", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\RegistrationApproval.js": "39", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SubjectPermissionManager.js": "40", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolApplicationReview.js": "41", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ParentFeatureManager.js": "42", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkDetail.js": "43", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkUpload.js": "44", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentList.js": "45", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentDetail.js": "46", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentHomeworkUpload.js": "47", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentCreate.js": "48", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkUploadOptions.js": "49", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkStatistics.js": "50", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkCalendar.js": "51", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentProgress.js": "52", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PendingReviewHomeworks.js": "53", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\FinishedHomeworks.js": "54", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentSubmitChoice.js": "55", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentHomeworkAssignmentList.js": "56", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PhotoSolve.js": "57", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\TeacherUserManagement.js": "58", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkHistory.js": "59", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkReview.js": "60", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkDetail.js": "61", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ParentRegistration.js": "62", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\Overview.js": "63", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolApplicationForm.js": "64", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\QuestionAnalysis.js": "65", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\SmartSuggestions.js": "66", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\StudentDetails.js": "67", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\ParentReport.js": "68", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\DataExport.js": "69", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\AssignmentSelector.js": "70", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SystemHomeworkAnalysis\\SystemAssignmentSelector.js": "71", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkCorrectionEditor.js": "72", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\CameraCapture.js": "73", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentCard.js": "74", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\QuickActions.js": "75", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ImageCropper.js": "76", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\SmartReminder.js": "77", "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StatusBadge.js": "78"}, {"size": 946, "mtime": 1751554505035, "results": "79", "hashOfConfig": "80"}, {"size": 10660, "mtime": 1754316658627, "results": "81", "hashOfConfig": "80"}, {"size": 5159, "mtime": 1753841883936, "results": "82", "hashOfConfig": "80"}, {"size": 32727, "mtime": 1754148836891, "results": "83", "hashOfConfig": "80"}, {"size": 12949, "mtime": 1754404252108, "results": "84", "hashOfConfig": "80"}, {"size": 24152, "mtime": 1754060190688, "results": "85", "hashOfConfig": "80"}, {"size": 17003, "mtime": 1754369937941, "results": "86", "hashOfConfig": "80"}, {"size": 17810, "mtime": 1754405000757, "results": "87", "hashOfConfig": "80"}, {"size": 11818, "mtime": 1754314069631, "results": "88", "hashOfConfig": "80"}, {"size": 47854, "mtime": 1754117724792, "results": "89", "hashOfConfig": "80"}, {"size": 125852, "mtime": 1754369937941, "results": "90", "hashOfConfig": "80"}, {"size": 161140, "mtime": 1754369937941, "results": "91", "hashOfConfig": "80"}, {"size": 44995, "mtime": 1753493006030, "results": "92", "hashOfConfig": "80"}, {"size": 5304, "mtime": 1754013068990, "results": "93", "hashOfConfig": "80"}, {"size": 49290, "mtime": 1754308902757, "results": "94", "hashOfConfig": "80"}, {"size": 35955, "mtime": 1754060562254, "results": "95", "hashOfConfig": "80"}, {"size": 701, "mtime": 1752896943241, "results": "96", "hashOfConfig": "80"}, {"size": 22217, "mtime": 1754057362141, "results": "97", "hashOfConfig": "80"}, {"size": 25276, "mtime": 1754039146720, "results": "98", "hashOfConfig": "80"}, {"size": 511, "mtime": 1754369937938, "results": "99", "hashOfConfig": "80"}, {"size": 190, "mtime": 1754012887356, "results": "100", "hashOfConfig": "80"}, {"size": 1696, "mtime": 1753841864292, "results": "101", "hashOfConfig": "80"}, {"size": 35911, "mtime": 1753929968606, "results": "102", "hashOfConfig": "80"}, {"size": 15662, "mtime": 1754403749620, "results": "103", "hashOfConfig": "80"}, {"size": 52337, "mtime": 1754150863988, "results": "104", "hashOfConfig": "80"}, {"size": 88102, "mtime": 1754060622237, "results": "105", "hashOfConfig": "80"}, {"size": 13198, "mtime": 1754320653657, "results": "106", "hashOfConfig": "80"}, {"size": 13237, "mtime": 1754368765296, "results": "107", "hashOfConfig": "80"}, {"size": 11387, "mtime": 1754233114255, "results": "108", "hashOfConfig": "80"}, {"size": 139546, "mtime": 1754321573079, "results": "109", "hashOfConfig": "80"}, {"size": 3371, "mtime": 1754059746620, "results": "110", "hashOfConfig": "80"}, {"size": 5562, "mtime": 1753866053592, "results": "111", "hashOfConfig": "80"}, {"size": 6195, "mtime": 1751763301656, "results": "112", "hashOfConfig": "80"}, {"size": 6428, "mtime": 1753963570645, "results": "113", "hashOfConfig": "80"}, {"size": 3679, "mtime": 1753866642399, "results": "114", "hashOfConfig": "80"}, {"size": 18369, "mtime": 1753888239807, "results": "115", "hashOfConfig": "80"}, {"size": 9249, "mtime": 1754096127608, "results": "116", "hashOfConfig": "80"}, {"size": 31243, "mtime": 1754369937938, "results": "117", "hashOfConfig": "80"}, {"size": 13815, "mtime": 1754190768286, "results": "118", "hashOfConfig": "80"}, {"size": 9163, "mtime": 1753890457468, "results": "119", "hashOfConfig": "80"}, {"size": 12218, "mtime": 1753866691991, "results": "120", "hashOfConfig": "80"}, {"size": 12566, "mtime": 1754401523932, "results": "121", "hashOfConfig": "80"}, {"size": 26829, "mtime": 1753880881610, "results": "122", "hashOfConfig": "80"}, {"size": 50649, "mtime": 1754293080652, "results": "123", "hashOfConfig": "80"}, {"size": 29942, "mtime": 1753881846609, "results": "124", "hashOfConfig": "80"}, {"size": 6242, "mtime": 1751432649676, "results": "125", "hashOfConfig": "80"}, {"size": 27313, "mtime": 1753425767631, "results": "126", "hashOfConfig": "80"}, {"size": 24892, "mtime": 1753888407511, "results": "127", "hashOfConfig": "80"}, {"size": 3516, "mtime": 1751335113862, "results": "128", "hashOfConfig": "80"}, {"size": 32329, "mtime": 1751554727149, "results": "129", "hashOfConfig": "80"}, {"size": 11262, "mtime": 1754285220077, "results": "130", "hashOfConfig": "80"}, {"size": 8091, "mtime": 1751866086521, "results": "131", "hashOfConfig": "80"}, {"size": 30342, "mtime": 1754285130469, "results": "132", "hashOfConfig": "80"}, {"size": 18939, "mtime": 1754285168670, "results": "133", "hashOfConfig": "80"}, {"size": 9682, "mtime": 1753022516134, "results": "134", "hashOfConfig": "80"}, {"size": 17938, "mtime": 1753017445917, "results": "135", "hashOfConfig": "80"}, {"size": 29074, "mtime": 1754023698366, "results": "136", "hashOfConfig": "80"}, {"size": 8962, "mtime": 1754369937934, "results": "137", "hashOfConfig": "80"}, {"size": 13670, "mtime": 1753020805101, "results": "138", "hashOfConfig": "80"}, {"size": 27862, "mtime": 1754036301656, "results": "139", "hashOfConfig": "80"}, {"size": 34660, "mtime": 1754287382971, "results": "140", "hashOfConfig": "80"}, {"size": 9376, "mtime": 1754146200136, "results": "141", "hashOfConfig": "80"}, {"size": 12355, "mtime": 1752847450307, "results": "142", "hashOfConfig": "80"}, {"size": 8091, "mtime": 1753866614546, "results": "143", "hashOfConfig": "80"}, {"size": 11248, "mtime": 1752926637327, "results": "144", "hashOfConfig": "80"}, {"size": 10791, "mtime": 1752742130570, "results": "145", "hashOfConfig": "80"}, {"size": 26842, "mtime": 1753026180860, "results": "146", "hashOfConfig": "80"}, {"size": 14246, "mtime": 1754039496657, "results": "147", "hashOfConfig": "80"}, {"size": 20025, "mtime": 1754296994877, "results": "148", "hashOfConfig": "80"}, {"size": 18350, "mtime": 1754232452423, "results": "149", "hashOfConfig": "80"}, {"size": 23426, "mtime": 1754277486476, "results": "150", "hashOfConfig": "80"}, {"size": 28479, "mtime": 1753508100688, "results": "151", "hashOfConfig": "80"}, {"size": 20148, "mtime": 1754026190054, "results": "152", "hashOfConfig": "80"}, {"size": 13076, "mtime": 1753016487323, "results": "153", "hashOfConfig": "80"}, {"size": 11151, "mtime": 1753016418193, "results": "154", "hashOfConfig": "80"}, {"size": 32106, "mtime": 1754021636789, "results": "155", "hashOfConfig": "80"}, {"size": 12543, "mtime": 1753010983078, "results": "156", "hashOfConfig": "80"}, {"size": 7641, "mtime": 1754031754988, "results": "157", "hashOfConfig": "80"}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1t86jd4", {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\pythonproject\\checkingsys\\frontend\\src\\index.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\App.js", ["392", "393"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\auth.js", ["394"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Register.js", ["395", "396", "397", "398", "399", "400", "401"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StudentHomeworkDetail.js", ["402", "403", "404", "405", "406", "407"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Login.js", ["408", "409", "410", "411"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\Home.js", ["412", "413", "414", "415", "416", "417", "418", "419"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ParentDashboard.js", ["420", "421", "422", "423"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StudentReport.js", ["424", "425"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ClassManagement.js", ["426", "427", "428", "429", "430", "431", "432", "433", "434", "435", "436", "437", "438", "439", "440", "441"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ImprovedSystemClassManagement.js", ["442", "443", "444", "445", "446", "447", "448"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\ImprovedClassManagement.js", ["449", "450", "451"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\WrongQuestionTraining.js", ["452", "453", "454", "455", "456", "457", "458", "459", "460", "461", "462", "463", "464", "465", "466", "467", "468", "469", "470", "471"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\UserProfile.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\AdminDashboard.js", ["472", "473", "474", "475", "476", "477", "478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\StatisticsPage.js", ["505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\UserManagementPage.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\HomeworkManagement.js", [], ["516"], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\DatabaseManagementPage.js", ["517", "518", "519", "520", "521", "522", "523", "524"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\TeacherUserManagementPage.js", ["525"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\pages\\PhotoSolvePage.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PrivateRoute.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolManagement.js", ["526", "527", "528", "529", "530", "531"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\AppLayout.js", ["532"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StandaloneRegister.js", ["533", "534", "535", "536", "537", "538", "539", "540"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SuperSchoolManagement.js", ["541", "542", "543", "544"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\RegionManagement.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\index.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SystemHomeworkAnalysis\\index.js", ["545", "546"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\api.js", ["547"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\detector.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentBindingVerification.js", ["548", "549", "550", "551", "552"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\AIAssistant.js", ["553", "554"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\teacherDataFix.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\UserSchoolApplications.js", ["555"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SubjectManagement.js", ["556", "557", "558", "559", "560", "561", "562", "563", "564", "565"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\utils\\roleUtils.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\UserManagement.js", ["566", "567", "568", "569"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\RegistrationApproval.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SubjectPermissionManager.js", ["570", "571", "572", "573", "574", "575", "576", "577", "578", "579"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolApplicationReview.js", ["580", "581", "582", "583", "584"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ParentFeatureManager.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkDetail.js", ["585", "586", "587", "588", "589", "590", "591", "592", "593", "594"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkUpload.js", ["595", "596", "597", "598", "599"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentList.js", ["600", "601", "602"], ["603", "604"], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentDetail.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentHomeworkUpload.js", ["605", "606", "607"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentCreate.js", ["608", "609", "610", "611"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkUploadOptions.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkStatistics.js", ["612", "613"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkCalendar.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAssignmentProgress.js", ["614", "615", "616", "617", "618"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PendingReviewHomeworks.js", ["619", "620", "621", "622", "623", "624", "625"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\FinishedHomeworks.js", ["626", "627", "628", "629", "630", "631"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentSubmitChoice.js", ["632", "633"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\StudentHomeworkAssignmentList.js", ["634", "635", "636", "637", "638", "639", "640", "641", "642"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\PhotoSolve.js", ["643", "644"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\TeacherUserManagement.js", ["645", "646"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkHistory.js", ["647"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkReview.js", ["648", "649", "650", "651", "652", "653"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentHomeworkDetail.js", ["654", "655", "656", "657", "658", "659"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ParentRegistration.js", ["660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\Overview.js", ["672", "673", "674", "675"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SchoolApplicationForm.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\QuestionAnalysis.js", ["676", "677", "678", "679", "680"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\SmartSuggestions.js", ["681", "682", "683", "684"], ["685"], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\StudentDetails.js", ["686", "687", "688"], ["689", "690"], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\ParentReport.js", ["691", "692"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\DataExport.js", ["693", "694", "695", "696", "697"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkAnalysis\\AssignmentSelector.js", [], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\SystemHomeworkAnalysis\\SystemAssignmentSelector.js", ["698", "699"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\HomeworkCorrectionEditor.js", ["700", "701", "702", "703", "704", "705", "706", "707", "708"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\CameraCapture.js", ["709"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StudentCard.js", ["710", "711", "712"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\QuickActions.js", ["713", "714"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\ImageCropper.js", ["715", "716", "717", "718", "719"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\SmartReminder.js", ["720", "721"], [], "D:\\pythonproject\\checkingsys\\frontend\\src\\components\\student\\StatusBadge.js", ["722"], [], {"ruleId": "723", "severity": 1, "message": "724", "line": 3, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 16}, {"ruleId": "723", "severity": 1, "message": "727", "line": 9, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 16}, {"ruleId": "728", "severity": 1, "message": "729", "line": 89, "column": 6, "nodeType": "730", "endLine": 89, "endColumn": 19, "suggestions": "731"}, {"ruleId": "723", "severity": 1, "message": "732", "line": 3, "column": 79, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 84}, {"ruleId": "723", "severity": 1, "message": "733", "line": 3, "column": 86, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 93}, {"ruleId": "723", "severity": 1, "message": "734", "line": 4, "column": 67, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 86}, {"ruleId": "723", "severity": 1, "message": "735", "line": 15, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 15, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "736", "line": 23, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 23, "endColumn": 16}, {"ruleId": "723", "severity": 1, "message": "737", "line": 23, "column": 18, "nodeType": "725", "messageId": "726", "endLine": 23, "endColumn": 27}, {"ruleId": "723", "severity": 1, "message": "738", "line": 47, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 47, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "739", "line": 12, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 12, "endColumn": 11}, {"ruleId": "723", "severity": 1, "message": "740", "line": 14, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 14, "endColumn": 11}, {"ruleId": "723", "severity": 1, "message": "741", "line": 15, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 15, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "733", "line": 18, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 18, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "742", "line": 19, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 19, "endColumn": 9}, {"ruleId": "728", "severity": 1, "message": "743", "line": 115, "column": 6, "nodeType": "730", "endLine": 115, "endColumn": 17, "suggestions": "744"}, {"ruleId": "723", "severity": 1, "message": "745", "line": 3, "column": 79, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 87}, {"ruleId": "723", "severity": 1, "message": "746", "line": 5, "column": 19, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 27}, {"ruleId": "723", "severity": 1, "message": "747", "line": 36, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 36, "endColumn": 26}, {"ruleId": "728", "severity": 1, "message": "748", "line": 265, "column": 6, "nodeType": "730", "endLine": 265, "endColumn": 8, "suggestions": "749"}, {"ruleId": "723", "severity": 1, "message": "750", "line": 9, "column": 37, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 49}, {"ruleId": "723", "severity": 1, "message": "751", "line": 10, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "752", "line": 10, "column": 24, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 36}, {"ruleId": "723", "severity": 1, "message": "753", "line": 12, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 12, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "754", "line": 12, "column": 24, "nodeType": "725", "messageId": "726", "endLine": 12, "endColumn": 49}, {"ruleId": "723", "severity": 1, "message": "755", "line": 12, "column": 51, "nodeType": "725", "messageId": "726", "endLine": 12, "endColumn": 65}, {"ruleId": "723", "severity": 1, "message": "756", "line": 13, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 13, "endColumn": 19}, {"ruleId": "728", "severity": 1, "message": "757", "line": 146, "column": 6, "nodeType": "730", "endLine": 146, "endColumn": 12, "suggestions": "758"}, {"ruleId": "723", "severity": 1, "message": "753", "line": 24, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 24, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "754", "line": 26, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 26, "endColumn": 28}, {"ruleId": "723", "severity": 1, "message": "759", "line": 47, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 47, "endColumn": 25}, {"ruleId": "728", "severity": 1, "message": "760", "line": 161, "column": 6, "nodeType": "730", "endLine": 161, "endColumn": 8, "suggestions": "761"}, {"ruleId": "723", "severity": 1, "message": "762", "line": 33, "column": 22, "nodeType": "725", "messageId": "726", "endLine": 33, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "763", "line": 64, "column": 6, "nodeType": "730", "endLine": 64, "endColumn": 17, "suggestions": "764"}, {"ruleId": "723", "severity": 1, "message": "724", "line": 3, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 9}, {"ruleId": "723", "severity": 1, "message": "742", "line": 5, "column": 22, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 28}, {"ruleId": "723", "severity": 1, "message": "733", "line": 5, "column": 35, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 42}, {"ruleId": "723", "severity": 1, "message": "765", "line": 9, "column": 21, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 33}, {"ruleId": "723", "severity": 1, "message": "766", "line": 9, "column": 35, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 47}, {"ruleId": "723", "severity": 1, "message": "754", "line": 10, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 42}, {"ruleId": "723", "severity": 1, "message": "767", "line": 16, "column": 38, "nodeType": "725", "messageId": "726", "endLine": 16, "endColumn": 56}, {"ruleId": "723", "severity": 1, "message": "768", "line": 21, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 21, "endColumn": 16}, {"ruleId": "723", "severity": 1, "message": "769", "line": 46, "column": 20, "nodeType": "725", "messageId": "726", "endLine": 46, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "770", "line": 63, "column": 6, "nodeType": "730", "endLine": 63, "endColumn": 12, "suggestions": "771"}, {"ruleId": "723", "severity": 1, "message": "772", "line": 275, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 275, "endColumn": 23}, {"ruleId": "723", "severity": 1, "message": "773", "line": 387, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 387, "endColumn": 35}, {"ruleId": "723", "severity": 1, "message": "774", "line": 670, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 670, "endColumn": 23}, {"ruleId": "723", "severity": 1, "message": "775", "line": 716, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 716, "endColumn": 28}, {"ruleId": "776", "severity": 1, "message": "777", "line": 1005, "column": 31, "nodeType": "778", "endLine": 1005, "endColumn": 80}, {"ruleId": "776", "severity": 1, "message": "777", "line": 1012, "column": 33, "nodeType": "778", "endLine": 1012, "endColumn": 36}, {"ruleId": "723", "severity": 1, "message": "779", "line": 9, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "780", "line": 646, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 646, "endColumn": 21}, {"ruleId": "728", "severity": 1, "message": "781", "line": 1791, "column": 6, "nodeType": "730", "endLine": 1791, "endColumn": 8, "suggestions": "782"}, {"ruleId": "728", "severity": 1, "message": "783", "line": 1795, "column": 6, "nodeType": "730", "endLine": 1795, "endColumn": 117, "suggestions": "784"}, {"ruleId": "728", "severity": 1, "message": "785", "line": 1799, "column": 6, "nodeType": "730", "endLine": 1799, "endColumn": 48, "suggestions": "786"}, {"ruleId": "728", "severity": 1, "message": "787", "line": 1804, "column": 6, "nodeType": "730", "endLine": 1804, "endColumn": 35, "suggestions": "788"}, {"ruleId": "728", "severity": 1, "message": "787", "line": 1809, "column": 6, "nodeType": "730", "endLine": 1809, "endColumn": 35, "suggestions": "789"}, {"ruleId": "728", "severity": 1, "message": "790", "line": 1983, "column": 6, "nodeType": "730", "endLine": 1983, "endColumn": 12, "suggestions": "791"}, {"ruleId": "728", "severity": 1, "message": "783", "line": 1987, "column": 6, "nodeType": "730", "endLine": 1987, "endColumn": 57, "suggestions": "792"}, {"ruleId": "728", "severity": 1, "message": "787", "line": 1992, "column": 6, "nodeType": "730", "endLine": 1992, "endColumn": 35, "suggestions": "793"}, {"ruleId": "723", "severity": 1, "message": "794", "line": 2, "column": 49, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 53}, {"ruleId": "723", "severity": 1, "message": "795", "line": 4, "column": 23, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 33}, {"ruleId": "723", "severity": 1, "message": "796", "line": 6, "column": 54, "nodeType": "725", "messageId": "726", "endLine": 6, "endColumn": 62}, {"ruleId": "723", "severity": 1, "message": "742", "line": 6, "column": 64, "nodeType": "725", "messageId": "726", "endLine": 6, "endColumn": 70}, {"ruleId": "723", "severity": 1, "message": "797", "line": 10, "column": 58, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 73}, {"ruleId": "723", "severity": 1, "message": "765", "line": 10, "column": 75, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 87}, {"ruleId": "723", "severity": 1, "message": "798", "line": 10, "column": 89, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 105}, {"ruleId": "723", "severity": 1, "message": "799", "line": 10, "column": 107, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 121}, {"ruleId": "723", "severity": 1, "message": "800", "line": 12, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 12, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "801", "line": 28, "column": 18, "nodeType": "725", "messageId": "726", "endLine": 28, "endColumn": 24}, {"ruleId": "723", "severity": 1, "message": "802", "line": 38, "column": 11, "nodeType": "725", "messageId": "726", "endLine": 38, "endColumn": 27}, {"ruleId": "723", "severity": 1, "message": "803", "line": 38, "column": 29, "nodeType": "725", "messageId": "726", "endLine": 38, "endColumn": 43}, {"ruleId": "723", "severity": 1, "message": "804", "line": 38, "column": 45, "nodeType": "725", "messageId": "726", "endLine": 38, "endColumn": 57}, {"ruleId": "805", "severity": 1, "message": "806", "line": 49, "column": 25, "nodeType": "807", "messageId": "808", "endLine": 49, "endColumn": 27}, {"ruleId": "805", "severity": 1, "message": "806", "line": 49, "column": 52, "nodeType": "807", "messageId": "808", "endLine": 49, "endColumn": 54}, {"ruleId": "723", "severity": 1, "message": "809", "line": 519, "column": 11, "nodeType": "725", "messageId": "726", "endLine": 519, "endColumn": 24}, {"ruleId": "723", "severity": 1, "message": "810", "line": 529, "column": 14, "nodeType": "725", "messageId": "726", "endLine": 529, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "811", "line": 796, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 796, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "809", "line": 801, "column": 11, "nodeType": "725", "messageId": "726", "endLine": 801, "endColumn": 24}, {"ruleId": "723", "severity": 1, "message": "812", "line": 1162, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 1162, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "813", "line": 2, "column": 18, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "795", "line": 2, "column": 24, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 34}, {"ruleId": "723", "severity": 1, "message": "732", "line": 2, "column": 176, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 181}, {"ruleId": "723", "severity": 1, "message": "796", "line": 2, "column": 193, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 201}, {"ruleId": "723", "severity": 1, "message": "742", "line": 2, "column": 203, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 209}, {"ruleId": "723", "severity": 1, "message": "814", "line": 3, "column": 41, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 58}, {"ruleId": "723", "severity": 1, "message": "799", "line": 3, "column": 158, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 172}, {"ruleId": "723", "severity": 1, "message": "797", "line": 3, "column": 174, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 189}, {"ruleId": "723", "severity": 1, "message": "798", "line": 3, "column": 191, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 207}, {"ruleId": "723", "severity": 1, "message": "754", "line": 3, "column": 229, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 254}, {"ruleId": "723", "severity": 1, "message": "794", "line": 4, "column": 36, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 40}, {"ruleId": "723", "severity": 1, "message": "815", "line": 5, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "816", "line": 8, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 8, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "801", "line": 15, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 15, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "817", "line": 15, "column": 26, "nodeType": "725", "messageId": "726", "endLine": 15, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "818", "line": 18, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 18, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "819", "line": 24, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 24, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "820", "line": 26, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 26, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "821", "line": 47, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 47, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "822", "line": 63, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 63, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "822", "line": 75, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 75, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "822", "line": 106, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 106, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "822", "line": 186, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 186, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "822", "line": 256, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 256, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "822", "line": 288, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 288, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "822", "line": 372, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 372, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "822", "line": 419, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 419, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "822", "line": 463, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 463, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "823", "line": 516, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 516, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "824", "line": 530, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 530, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "825", "line": 672, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 672, "endColumn": 35}, {"ruleId": "723", "severity": 1, "message": "826", "line": 677, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 677, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "827", "line": 784, "column": 25, "nodeType": "725", "messageId": "726", "endLine": 784, "endColumn": 36}, {"ruleId": "723", "severity": 1, "message": "828", "line": 1, "column": 38, "nodeType": "725", "messageId": "726", "endLine": 1, "endColumn": 44}, {"ruleId": "723", "severity": 1, "message": "829", "line": 6, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 6, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "830", "line": 7, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 7, "endColumn": 32}, {"ruleId": "723", "severity": 1, "message": "831", "line": 88, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 88, "endColumn": 27}, {"ruleId": "728", "severity": 1, "message": "832", "line": 236, "column": 6, "nodeType": "730", "endLine": 236, "endColumn": 21, "suggestions": "833"}, {"ruleId": "723", "severity": 1, "message": "834", "line": 277, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 277, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "835", "line": 279, "column": 28, "nodeType": "725", "messageId": "726", "endLine": 279, "endColumn": 39}, {"ruleId": "723", "severity": 1, "message": "836", "line": 279, "column": 41, "nodeType": "725", "messageId": "726", "endLine": 279, "endColumn": 54}, {"ruleId": "723", "severity": 1, "message": "837", "line": 279, "column": 56, "nodeType": "725", "messageId": "726", "endLine": 279, "endColumn": 70}, {"ruleId": "723", "severity": 1, "message": "838", "line": 279, "column": 72, "nodeType": "725", "messageId": "726", "endLine": 279, "endColumn": 87}, {"ruleId": "723", "severity": 1, "message": "839", "line": 281, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 281, "endColumn": 21}, {"ruleId": "728", "severity": 1, "message": "840", "line": 210, "column": 6, "nodeType": "730", "endLine": 210, "endColumn": 26, "suggestions": "841", "suppressions": "842"}, {"ruleId": "723", "severity": 1, "message": "843", "line": 2, "column": 95, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 100}, {"ruleId": "723", "severity": 1, "message": "844", "line": 2, "column": 113, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 123}, {"ruleId": "723", "severity": 1, "message": "845", "line": 3, "column": 121, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 136}, {"ruleId": "723", "severity": 1, "message": "846", "line": 3, "column": 152, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 174}, {"ruleId": "723", "severity": 1, "message": "847", "line": 5, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "848", "line": 34, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 34, "endColumn": 21}, {"ruleId": "728", "severity": 1, "message": "849", "line": 80, "column": 6, "nodeType": "730", "endLine": 80, "endColumn": 8, "suggestions": "850"}, {"ruleId": "851", "severity": 1, "message": "852", "line": 435, "column": 15, "nodeType": "853", "messageId": "854", "endLine": 439, "endColumn": 37}, {"ruleId": "723", "severity": 1, "message": "724", "line": 2, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 16}, {"ruleId": "723", "severity": 1, "message": "855", "line": 2, "column": 92, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 99}, {"ruleId": "723", "severity": 1, "message": "754", "line": 8, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 8, "endColumn": 35}, {"ruleId": "728", "severity": 1, "message": "856", "line": 74, "column": 6, "nodeType": "730", "endLine": 74, "endColumn": 40, "suggestions": "857"}, {"ruleId": "728", "severity": 1, "message": "856", "line": 100, "column": 6, "nodeType": "730", "endLine": 100, "endColumn": 16, "suggestions": "858"}, {"ruleId": "728", "severity": 1, "message": "859", "line": 150, "column": 6, "nodeType": "730", "endLine": 150, "endColumn": 24, "suggestions": "860"}, {"ruleId": "723", "severity": 1, "message": "861", "line": 507, "column": 11, "nodeType": "725", "messageId": "726", "endLine": 507, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "862", "line": 15, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 15, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "863", "line": 2, "column": 64, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 69}, {"ruleId": "723", "severity": 1, "message": "864", "line": 3, "column": 67, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 79}, {"ruleId": "723", "severity": 1, "message": "766", "line": 3, "column": 81, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 93}, {"ruleId": "723", "severity": 1, "message": "865", "line": 3, "column": 95, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 107}, {"ruleId": "723", "severity": 1, "message": "866", "line": 4, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "794", "line": 5, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "867", "line": 207, "column": 6, "nodeType": "730", "endLine": 207, "endColumn": 22, "suggestions": "868"}, {"ruleId": "728", "severity": 1, "message": "869", "line": 220, "column": 6, "nodeType": "730", "endLine": 220, "endColumn": 45, "suggestions": "870"}, {"ruleId": "723", "severity": 1, "message": "871", "line": 2, "column": 119, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 122}, {"ruleId": "723", "severity": 1, "message": "872", "line": 2, "column": 124, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 127}, {"ruleId": "723", "severity": 1, "message": "873", "line": 62, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 62, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "874", "line": 821, "column": 15, "nodeType": "725", "messageId": "726", "endLine": 821, "endColumn": 34}, {"ruleId": "723", "severity": 1, "message": "875", "line": 41, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 41, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "876", "line": 173, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 173, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "877", "line": 418, "column": 15, "nodeType": "725", "messageId": "726", "endLine": 418, "endColumn": 24}, {"ruleId": "723", "severity": 1, "message": "878", "line": 2, "column": 54, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 59}, {"ruleId": "723", "severity": 1, "message": "751", "line": 3, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "879", "line": 3, "column": 31, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 50}, {"ruleId": "723", "severity": 1, "message": "880", "line": 6, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 6, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "881", "line": 13, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 13, "endColumn": 24}, {"ruleId": "723", "severity": 1, "message": "882", "line": 2, "column": 37, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 44}, {"ruleId": "723", "severity": 1, "message": "883", "line": 95, "column": 11, "nodeType": "725", "messageId": "726", "endLine": 95, "endColumn": 26}, {"ruleId": "723", "severity": 1, "message": "884", "line": 7, "column": 16, "nodeType": "725", "messageId": "726", "endLine": 7, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "885", "line": 4, "column": 27, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "855", "line": 4, "column": 52, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 59}, {"ruleId": "723", "severity": 1, "message": "886", "line": 4, "column": 61, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 65}, {"ruleId": "723", "severity": 1, "message": "733", "line": 5, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "754", "line": 9, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 28}, {"ruleId": "723", "severity": 1, "message": "887", "line": 10, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "888", "line": 10, "column": 24, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 36}, {"ruleId": "723", "severity": 1, "message": "884", "line": 18, "column": 16, "nodeType": "725", "messageId": "726", "endLine": 18, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "736", "line": 26, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 26, "endColumn": 16}, {"ruleId": "728", "severity": 1, "message": "889", "line": 116, "column": 6, "nodeType": "730", "endLine": 116, "endColumn": 26, "suggestions": "890"}, {"ruleId": "723", "severity": 1, "message": "865", "line": 3, "column": 40, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 52}, {"ruleId": "723", "severity": 1, "message": "891", "line": 35, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 35, "endColumn": 15}, {"ruleId": "728", "severity": 1, "message": "892", "line": 110, "column": 6, "nodeType": "730", "endLine": 110, "endColumn": 8, "suggestions": "893"}, {"ruleId": "723", "severity": 1, "message": "894", "line": 430, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 430, "endColumn": 30}, {"ruleId": "723", "severity": 1, "message": "732", "line": 2, "column": 33, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 38}, {"ruleId": "723", "severity": 1, "message": "895", "line": 2, "column": 76, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 79}, {"ruleId": "723", "severity": 1, "message": "865", "line": 4, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "896", "line": 4, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "751", "line": 5, "column": 30, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 49}, {"ruleId": "723", "severity": 1, "message": "879", "line": 5, "column": 51, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 70}, {"ruleId": "723", "severity": 1, "message": "897", "line": 21, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 21, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "898", "line": 21, "column": 24, "nodeType": "725", "messageId": "726", "endLine": 21, "endColumn": 39}, {"ruleId": "723", "severity": 1, "message": "899", "line": 22, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 22, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "900", "line": 121, "column": 6, "nodeType": "730", "endLine": 121, "endColumn": 30, "suggestions": "901"}, {"ruleId": "723", "severity": 1, "message": "902", "line": 3, "column": 25, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 38}, {"ruleId": "723", "severity": 1, "message": "754", "line": 3, "column": 40, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 65}, {"ruleId": "723", "severity": 1, "message": "884", "line": 6, "column": 16, "nodeType": "725", "messageId": "726", "endLine": 6, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "762", "line": 6, "column": 22, "nodeType": "725", "messageId": "726", "endLine": 6, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "903", "line": 12, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 12, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "904", "line": 5, "column": 33, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 37}, {"ruleId": "723", "severity": 1, "message": "732", "line": 5, "column": 39, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 44}, {"ruleId": "723", "severity": 1, "message": "753", "line": 9, "column": 36, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 55}, {"ruleId": "723", "severity": 1, "message": "754", "line": 9, "column": 57, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 82}, {"ruleId": "723", "severity": 1, "message": "905", "line": 9, "column": 84, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 101}, {"ruleId": "723", "severity": 1, "message": "906", "line": 12, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 12, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "880", "line": 17, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 17, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "907", "line": 23, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 23, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "908", "line": 37, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 37, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "909", "line": 533, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 533, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "910", "line": 1, "column": 46, "nodeType": "725", "messageId": "726", "endLine": 1, "endColumn": 57}, {"ruleId": "723", "severity": 1, "message": "911", "line": 51, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 51, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "912", "line": 216, "column": 6, "nodeType": "730", "endLine": 216, "endColumn": 12, "suggestions": "913"}, {"ruleId": "728", "severity": 1, "message": "914", "line": 230, "column": 6, "nodeType": "730", "endLine": 230, "endColumn": 42, "suggestions": "915"}, {"ruleId": "728", "severity": 1, "message": "914", "line": 246, "column": 6, "nodeType": "730", "endLine": 246, "endColumn": 28, "suggestions": "916"}, {"ruleId": "723", "severity": 1, "message": "917", "line": 47, "column": 20, "nodeType": "725", "messageId": "726", "endLine": 47, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "918", "line": 54, "column": 19, "nodeType": "725", "messageId": "726", "endLine": 54, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "919", "line": 321, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 321, "endColumn": 26}, {"ruleId": "728", "severity": 1, "message": "920", "line": 226, "column": 6, "nodeType": "730", "endLine": 226, "endColumn": 20, "suggestions": "921", "suppressions": "922"}, {"ruleId": "728", "severity": 1, "message": "920", "line": 238, "column": 6, "nodeType": "730", "endLine": 238, "endColumn": 51, "suggestions": "923", "suppressions": "924"}, {"ruleId": "723", "severity": 1, "message": "925", "line": 22, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 22, "endColumn": 23}, {"ruleId": "723", "severity": 1, "message": "926", "line": 33, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 33, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "927", "line": 34, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 34, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "928", "line": 22, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 22, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "929", "line": 24, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 24, "endColumn": 26}, {"ruleId": "723", "severity": 1, "message": "930", "line": 390, "column": 15, "nodeType": "725", "messageId": "726", "endLine": 390, "endColumn": 28}, {"ruleId": "728", "severity": 1, "message": "931", "line": 488, "column": 6, "nodeType": "730", "endLine": 488, "endColumn": 18, "suggestions": "932"}, {"ruleId": "728", "severity": 1, "message": "763", "line": 58, "column": 6, "nodeType": "730", "endLine": 58, "endColumn": 12, "suggestions": "933"}, {"ruleId": "728", "severity": 1, "message": "763", "line": 83, "column": 6, "nodeType": "730", "endLine": 83, "endColumn": 52, "suggestions": "934"}, {"ruleId": "723", "severity": 1, "message": "843", "line": 5, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "935", "line": 8, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 8, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "936", "line": 8, "column": 32, "nodeType": "725", "messageId": "726", "endLine": 8, "endColumn": 44}, {"ruleId": "723", "severity": 1, "message": "754", "line": 9, "column": 24, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 49}, {"ruleId": "728", "severity": 1, "message": "937", "line": 71, "column": 6, "nodeType": "730", "endLine": 71, "endColumn": 20, "suggestions": "938"}, {"ruleId": "723", "severity": 1, "message": "910", "line": 1, "column": 38, "nodeType": "725", "messageId": "726", "endLine": 1, "endColumn": 49}, {"ruleId": "723", "severity": 1, "message": "828", "line": 1, "column": 51, "nodeType": "725", "messageId": "726", "endLine": 1, "endColumn": 57}, {"ruleId": "723", "severity": 1, "message": "742", "line": 5, "column": 69, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 75}, {"ruleId": "723", "severity": 1, "message": "939", "line": 14, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 14, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "940", "line": 18, "column": 7, "nodeType": "725", "messageId": "726", "endLine": 18, "endColumn": 15}, {"ruleId": "728", "severity": 1, "message": "920", "line": 238, "column": 6, "nodeType": "730", "endLine": 238, "endColumn": 8, "suggestions": "941"}, {"ruleId": "728", "severity": 1, "message": "942", "line": 271, "column": 6, "nodeType": "730", "endLine": 271, "endColumn": 73, "suggestions": "943"}, {"ruleId": "723", "severity": 1, "message": "910", "line": 1, "column": 38, "nodeType": "725", "messageId": "726", "endLine": 1, "endColumn": 49}, {"ruleId": "723", "severity": 1, "message": "944", "line": 9, "column": 38, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 53}, {"ruleId": "723", "severity": 1, "message": "939", "line": 14, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 14, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "940", "line": 18, "column": 7, "nodeType": "725", "messageId": "726", "endLine": 18, "endColumn": 15}, {"ruleId": "728", "severity": 1, "message": "920", "line": 226, "column": 6, "nodeType": "730", "endLine": 226, "endColumn": 8, "suggestions": "945"}, {"ruleId": "728", "severity": 1, "message": "942", "line": 259, "column": 6, "nodeType": "730", "endLine": 259, "endColumn": 73, "suggestions": "946"}, {"ruleId": "723", "severity": 1, "message": "829", "line": 6, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 6, "endColumn": 14}, {"ruleId": "728", "severity": 1, "message": "947", "line": 51, "column": 6, "nodeType": "730", "endLine": 51, "endColumn": 8, "suggestions": "948"}, {"ruleId": "723", "severity": 1, "message": "885", "line": 4, "column": 27, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "949", "line": 4, "column": 62, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 67}, {"ruleId": "723", "severity": 1, "message": "950", "line": 7, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 7, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "755", "line": 7, "column": 37, "nodeType": "725", "messageId": "726", "endLine": 7, "endColumn": 51}, {"ruleId": "723", "severity": 1, "message": "880", "line": 16, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 16, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "884", "line": 16, "column": 16, "nodeType": "725", "messageId": "726", "endLine": 16, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "951", "line": 59, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 59, "endColumn": 27}, {"ruleId": "728", "severity": 1, "message": "920", "line": 104, "column": 6, "nodeType": "730", "endLine": 104, "endColumn": 8, "suggestions": "952"}, {"ruleId": "728", "severity": 1, "message": "953", "line": 114, "column": 6, "nodeType": "730", "endLine": 114, "endColumn": 35, "suggestions": "954"}, {"ruleId": "723", "severity": 1, "message": "733", "line": 11, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 11, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "843", "line": 14, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 14, "endColumn": 8}, {"ruleId": "723", "severity": 1, "message": "882", "line": 2, "column": 31, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 38}, {"ruleId": "728", "severity": 1, "message": "770", "line": 32, "column": 6, "nodeType": "730", "endLine": 32, "endColumn": 12, "suggestions": "955"}, {"ruleId": "723", "severity": 1, "message": "949", "line": 3, "column": 59, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 64}, {"ruleId": "723", "severity": 1, "message": "742", "line": 5, "column": 48, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 54}, {"ruleId": "723", "severity": 1, "message": "935", "line": 8, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 8, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "756", "line": 12, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 12, "endColumn": 19}, {"ruleId": "723", "severity": 1, "message": "880", "line": 21, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 21, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "956", "line": 33, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 33, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "729", "line": 190, "column": 6, "nodeType": "730", "endLine": 190, "endColumn": 8, "suggestions": "957"}, {"ruleId": "723", "severity": 1, "message": "739", "line": 5, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 16}, {"ruleId": "723", "severity": 1, "message": "733", "line": 5, "column": 24, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "755", "line": 9, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "880", "line": 22, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 22, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "958", "line": 127, "column": 27, "nodeType": "725", "messageId": "726", "endLine": 127, "endColumn": 39}, {"ruleId": "728", "severity": 1, "message": "959", "line": 300, "column": 6, "nodeType": "730", "endLine": 300, "endColumn": 34, "suggestions": "960"}, {"ruleId": "723", "severity": 1, "message": "878", "line": 2, "column": 16, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "961", "line": 2, "column": 23, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "732", "line": 2, "column": 60, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 65}, {"ruleId": "723", "severity": 1, "message": "863", "line": 2, "column": 67, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 72}, {"ruleId": "723", "severity": 1, "message": "765", "line": 3, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "779", "line": 3, "column": 24, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 38}, {"ruleId": "723", "severity": 1, "message": "880", "line": 6, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 6, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "884", "line": 6, "column": 16, "nodeType": "725", "messageId": "726", "endLine": 6, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "818", "line": 7, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 7, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "962", "line": 10, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 20}, {"ruleId": "723", "severity": 1, "message": "963", "line": 13, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 13, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "964", "line": 13, "column": 19, "nodeType": "725", "messageId": "726", "endLine": 13, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "733", "line": 4, "column": 34, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 41}, {"ruleId": "723", "severity": 1, "message": "885", "line": 4, "column": 64, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 68}, {"ruleId": "723", "severity": 1, "message": "751", "line": 10, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "965", "line": 49, "column": 6, "nodeType": "730", "endLine": 49, "endColumn": 20, "suggestions": "966"}, {"ruleId": "723", "severity": 1, "message": "871", "line": 3, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 12}, {"ruleId": "723", "severity": 1, "message": "872", "line": 3, "column": 14, "nodeType": "725", "messageId": "726", "endLine": 3, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "904", "line": 4, "column": 19, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 23}, {"ruleId": "723", "severity": 1, "message": "742", "line": 4, "column": 25, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "967", "line": 65, "column": 6, "nodeType": "730", "endLine": 65, "endColumn": 20, "suggestions": "968"}, {"ruleId": "723", "severity": 1, "message": "733", "line": 4, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "740", "line": 4, "column": 29, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 37}, {"ruleId": "723", "severity": 1, "message": "739", "line": 4, "column": 39, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 47}, {"ruleId": "723", "severity": 1, "message": "762", "line": 17, "column": 22, "nodeType": "725", "messageId": "726", "endLine": 17, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "969", "line": 69, "column": 6, "nodeType": "730", "endLine": 69, "endColumn": 20, "suggestions": "970", "suppressions": "971"}, {"ruleId": "723", "severity": 1, "message": "972", "line": 14, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 14, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "973", "line": 16, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 16, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "822", "line": 138, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 138, "endColumn": 18}, {"ruleId": "728", "severity": 1, "message": "974", "line": 208, "column": 6, "nodeType": "730", "endLine": 208, "endColumn": 73, "suggestions": "975", "suppressions": "976"}, {"ruleId": "728", "severity": 1, "message": "977", "line": 214, "column": 6, "nodeType": "730", "endLine": 214, "endColumn": 40, "suggestions": "978", "suppressions": "979"}, {"ruleId": "728", "severity": 1, "message": "974", "line": 87, "column": 6, "nodeType": "730", "endLine": 87, "endColumn": 20, "suggestions": "980"}, {"ruleId": "728", "severity": 1, "message": "981", "line": 93, "column": 6, "nodeType": "730", "endLine": 93, "endColumn": 37, "suggestions": "982"}, {"ruleId": "723", "severity": 1, "message": "983", "line": 16, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 16, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "984", "line": 17, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 17, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "985", "line": 31, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 31, "endColumn": 26}, {"ruleId": "723", "severity": 1, "message": "986", "line": 31, "column": 28, "nodeType": "725", "messageId": "726", "endLine": 31, "endColumn": 47}, {"ruleId": "728", "severity": 1, "message": "987", "line": 38, "column": 6, "nodeType": "730", "endLine": 38, "endColumn": 20, "suggestions": "988"}, {"ruleId": "723", "severity": 1, "message": "989", "line": 41, "column": 12, "nodeType": "725", "messageId": "726", "endLine": 41, "endColumn": 26}, {"ruleId": "728", "severity": 1, "message": "920", "line": 443, "column": 6, "nodeType": "730", "endLine": 443, "endColumn": 8, "suggestions": "990"}, {"ruleId": "723", "severity": 1, "message": "885", "line": 4, "column": 31, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 35}, {"ruleId": "723", "severity": 1, "message": "846", "line": 8, "column": 45, "nodeType": "725", "messageId": "726", "endLine": 8, "endColumn": 67}, {"ruleId": "723", "severity": 1, "message": "880", "line": 14, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 14, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "899", "line": 17, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 17, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "809", "line": 78, "column": 11, "nodeType": "725", "messageId": "726", "endLine": 78, "endColumn": 24}, {"ruleId": "723", "severity": 1, "message": "991", "line": 79, "column": 11, "nodeType": "725", "messageId": "726", "endLine": 79, "endColumn": 24}, {"ruleId": "723", "severity": 1, "message": "822", "line": 290, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 290, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "992", "line": 428, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 428, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "993", "line": 433, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 433, "endColumn": 31}, {"ruleId": "728", "severity": 1, "message": "994", "line": 327, "column": 6, "nodeType": "730", "endLine": 327, "endColumn": 15, "suggestions": "995"}, {"ruleId": "723", "severity": 1, "message": "732", "line": 2, "column": 24, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "996", "line": 22, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 22, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "997", "line": 22, "column": 23, "nodeType": "725", "messageId": "726", "endLine": 22, "endColumn": 37}, {"ruleId": "723", "severity": 1, "message": "750", "line": 8, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 8, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "998", "line": 11, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 11, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "999", "line": 8, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 8, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "1000", "line": 9, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 18}, {"ruleId": "1001", "severity": 1, "message": "1002", "line": 144, "column": 9, "nodeType": "1003", "messageId": "1004", "endLine": 179, "endColumn": 10}, {"ruleId": "1001", "severity": 1, "message": "1002", "line": 268, "column": 9, "nodeType": "1003", "messageId": "1004", "endLine": 303, "endColumn": 10}, {"ruleId": "728", "severity": 1, "message": "1005", "line": 317, "column": 6, "nodeType": "730", "endLine": 317, "endColumn": 99, "suggestions": "1006"}, {"ruleId": "728", "severity": 1, "message": "1007", "line": 212, "column": 6, "nodeType": "730", "endLine": 212, "endColumn": 19, "suggestions": "1008"}, {"ruleId": "728", "severity": 1, "message": "1009", "line": 225, "column": 6, "nodeType": "730", "endLine": 225, "endColumn": 28, "suggestions": "1010"}, {"ruleId": "723", "severity": 1, "message": "895", "line": 2, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 2, "endColumn": 13}, "no-unused-vars", "'Layout' is defined but never used.", "Identifier", "unusedVar", "'Register' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'user'. Either include it or remove the dependency array.", "ArrayExpression", ["1011"], "'Space' is defined but never used.", "'Divider' is defined but never used.", "'EnvironmentOutlined' is defined but never used.", "'preventRedirect' is assigned a value but never used.", "'grades' is assigned a value but never used.", "'setGrades' is assigned a value but never used.", "'hasSubmitted' is assigned a value but never used.", "'Progress' is defined but never used.", "'Timeline' is defined but never used.", "'Descriptions' is defined but never used.", "'Avatar' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchHomework' and 'fetchStatistics'. Either include them or remove the dependency array.", ["1012"], "'Cascader' is defined but never used.", "'apiLogin' is defined but never used.", "'selectedDistrict' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchProvinces'. Either include it or remove the dependency array.", ["1013"], "'BookOutlined' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'SyncOutlined' is defined but never used.", "'ClockCircleOutlined' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'TrophyOutlined' is defined but never used.", "'CalendarOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'calculateStudentStats'. Either include it or remove the dependency array.", ["1014"], "'studentHomework' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchBoundStudents'. Either include it or remove the dependency array.", ["1015"], "'Paragraph' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchStatistics'. Either include it or remove the dependency array.", ["1016"], "'UserOutlined' is defined but never used.", "'TeamOutlined' is defined but never used.", "'getClassesBySchool' is defined but never used.", "'TabPane' is assigned a value but never used.", "'setPageSize' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchClasses'. Either include it or remove the dependency array.", ["1017"], "'handleAddClass' is assigned a value but never used.", "'handleClassSelectionChange' is assigned a value but never used.", "'studentColumns' is assigned a value but never used.", "'showAddStudentModal' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'SearchOutlined' is defined but never used.", "'totalClasses' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchClassesData', 'fetchRegionsData', and 'fetchSubjectsData'. Either include them or remove the dependency array.", ["1018"], "React Hook useEffect has a missing dependency: 'filterData'. Either include it or remove the dependency array.", ["1019"], "React Hook useEffect has a missing dependency: 'generateTreeData'. Either include it or remove the dependency array.", ["1020"], "React Hook useEffect has a missing dependency: 'updateFilteredSubjects'. Either include it or remove the dependency array.", ["1021"], ["1022"], "React Hook useEffect has missing dependencies: 'fetchClassesData' and 'fetchSubjectsData'. Either include them or remove the dependency array.", ["1023"], ["1024"], ["1025"], "'Link' is defined but never used.", "'Breadcrumb' is defined but never used.", "'Dropdown' is defined but never used.", "'MessageOutlined' is defined but never used.", "'BarChartOutlined' is defined but never used.", "'LogoutOutlined' is defined but never used.", "'AppLayout' is defined but never used.", "'Header' is assigned a value but never used.", "'question_content' is assigned a value but never used.", "'correct_answer' is assigned a value but never used.", "'wrong_answer' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'studentAnswer' is assigned a value but never used.", "'markAsCompleted' is defined but never used.", "'ExerciseItem' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'Menu' is defined but never used.", "'DashboardOutlined' is defined but never used.", "'getUsers' is defined but never used.", "'UserManagement' is defined but never used.", "'Sider' is assigned a value but never used.", "'Option' is assigned a value but never used.", "'users' is assigned a value but never used.", "'systemInfo' is assigned a value but never used.", "'studentStats' is assigned a value but never used.", "'token' is assigned a value but never used.", "'handleAddUser' is assigned a value but never used.", "'userColumns' is assigned a value but never used.", "'navigateToSchoolManagement' is assigned a value but never used.", "'renderStatsDashboard' is assigned a value but never used.", "'roleEnabled' is assigned a value but never used.", "'useRef' is defined but never used.", "'moment' is defined but never used.", "'getDashboardStatistics' is defined but never used.", "'fetchSchoolClasses' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isSystemLevel'. Either include it or remove the dependency array.", ["1026"], "'handleExport' is assigned a value but never used.", "'class_count' is assigned a value but never used.", "'student_count' is assigned a value but never used.", "'homework_count' is assigned a value but never used.", "'corrected_count' is assigned a value but never used.", "'filename' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchGrades', 'fetchSchools', and 'onFilterChange'. Either include them or remove the dependency array. If 'onFilterChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1027"], ["1028"], "'Modal' is defined but never used.", "'Popconfirm' is defined but never used.", "'WarningOutlined' is defined but never used.", "'QuestionCircleOutlined' is defined but never used.", "'qs' is defined but never used.", "'selectedTab' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getHeaders'. Either include it or remove the dependency array.", ["1029"], "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", "'Tooltip' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSchoolData'. Either include it or remove the dependency array.", ["1030"], ["1031"], "React Hook useEffect has missing dependencies: 'activeTab' and 'fetchSchoolData'. Either include them or remove the dependency array.", ["1032"], "'targetSchoolId' is assigned a value but never used.", "'useAuth' is defined but never used.", "'Radio' is defined but never used.", "'HomeOutlined' is defined but never used.", "'PlusOutlined' is defined but never used.", "'register' is defined but never used.", "React Hook useEffect has missing dependencies: 'availableRoles', 'isStudent', 'registrationSettings', and 'selectedRole'. Either include them or remove the dependency array.", ["1033"], "React Hook useEffect has a missing dependency: 'getInitialValues'. Either include it or remove the dependency array.", ["1034"], "'Row' is defined but never used.", "'Col' is defined but never used.", "'selectedClassId' is assigned a value but never used.", "'batchCreateStudents' is assigned a value but never used.", "'isMobile' is assigned a value but never used.", "'isFeatureEnabled' is assigned a value but never used.", "'tempParts' is assigned a value but never used.", "'Input' is defined but never used.", "'CloseCircleOutlined' is defined but never used.", "'Title' is assigned a value but never used.", "'currentBinding' is assigned a value but never used.", "'message' is defined but never used.", "'lastUserMessage' is assigned a value but never used.", "'Text' is assigned a value but never used.", "'Spin' is defined but never used.", "'Card' is defined but never used.", "'OrderedListOutlined' is defined but never used.", "'BarsOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSubjects'. Either include it or remove the dependency array.", ["1035"], "'roles' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1036"], "'renderMobileUserCards' is assigned a value but never used.", "'Tag' is defined but never used.", "'DeleteOutlined' is defined but never used.", "'modalVisible' is assigned a value but never used.", "'setModalVisible' is assigned a value but never used.", "'form' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPermissions'. Either include it or remove the dependency array.", ["1037"], "'CloseOutlined' is defined but never used.", "'applications' is assigned a value but never used.", "'List' is defined but never used.", "'FileImageOutlined' is defined but never used.", "'ReactMarkdown' is defined but never used.", "'location' is assigned a value but never used.", "'authUser' is assigned a value but never used.", "'subjectName' is assigned a value but never used.", "'useCallback' is defined but never used.", "'assignmentId' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'form' and 'location.state?.assignmentId'. Either include them or remove the dependency array.", ["1038"], "React Hook useEffect has a missing dependency: 'fetchAssignmentDetails'. Either include it or remove the dependency array.", ["1039"], ["1040"], "'setViewMode' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'handleBatchDelete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAssignments'. Either include it or remove the dependency array.", ["1041"], ["1042"], ["1043"], ["1044"], "'submitSuccess' is assigned a value but never used.", "'students' is assigned a value but never used.", "'selectedStudent' is assigned a value but never used.", "'classes' is assigned a value but never used.", "'defaultSubjectId' is assigned a value but never used.", "'isSystemLevel' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'location.state?.filters' and 'location.state?.isSystemLevel'. Either include them or remove the dependency array.", ["1045"], ["1046"], ["1047"], "'EyeOutlined' is defined but never used.", "'EditOutlined' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchAssignment' and 'fetchHomeworkProgress'. Either include them or remove the dependency array.", ["1048"], "'RangePicker' is assigned a value but never used.", "'debounce' is assigned a value but never used.", ["1049"], "React Hook useEffect has missing dependencies: 'fetchAssignments' and 'fetchHomeworks'. Either include them or remove the dependency array.", ["1050"], "'HistoryOutlined' is defined but never used.", ["1051"], ["1052"], "React Hook useEffect has a missing dependency: 'calculateStats'. Either include it or remove the dependency array.", ["1053"], "'Empty' is defined but never used.", "'UploadOutlined' is defined but never used.", "'isMakeupSubmission' is assigned a value but never used.", ["1054"], "React Hook useEffect has a missing dependency: 'filterAssignments'. Either include it or remove the dependency array.", ["1055"], ["1056"], "'subjects' is assigned a value but never used.", ["1057"], "'correctCount' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'user.id'. Either include it or remove the dependency array.", ["1058"], "'Button' is defined but never used.", "'searchForm' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchOverviewData'. Either include it or remove the dependency array.", ["1059"], "React Hook useEffect has a missing dependency: 'fetchQuestionsData'. Either include it or remove the dependency array.", ["1060"], "React Hook useEffect has a missing dependency: 'fetchSuggestionsData'. Either include it or remove the dependency array.", ["1061"], ["1062"], "'InfoCircleOutlined' is defined but never used.", "'BugOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudentsData'. Either include it or remove the dependency array.", ["1063"], ["1064"], "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["1065"], ["1066"], ["1067"], "React Hook useEffect has a missing dependency: 'fetchParentReport'. Either include it or remove the dependency array.", ["1068"], "'FolderOpenOutlined' is defined but never used.", "'FilterOutlined' is defined but never used.", "'selectedStudents' is assigned a value but never used.", "'setSelectedStudents' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["1069"], "'setAssignments' is assigned a value but never used.", ["1070"], "'correctAnswer' is assigned a value but never used.", "'requestAIErrorAnalysis' is assigned a value but never used.", "'requestAIReinforcement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'startCamera' and 'stopCamera'. Either include them or remove the dependency array.", ["1071"], "'showDetails' is assigned a value but never used.", "'setShowDetails' is assigned a value but never used.", "'StarOutlined' is defined but never used.", "'ZoomInOutlined' is defined but never used.", "'ZoomOutOutlined' is defined but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useCallback has a missing dependency: 'isPinching'. Either include it or remove the dependency array.", ["1072"], "React Hook useEffect has a missing dependency: 'checkReminders'. Either include it or remove the dependency array.", ["1073"], "React Hook useEffect has a missing dependency: 'showNotification'. Either include it or remove the dependency array.", ["1074"], {"desc": "1075", "fix": "1076"}, {"desc": "1077", "fix": "1078"}, {"desc": "1079", "fix": "1080"}, {"desc": "1081", "fix": "1082"}, {"desc": "1083", "fix": "1084"}, {"desc": "1085", "fix": "1086"}, {"desc": "1087", "fix": "1088"}, {"desc": "1089", "fix": "1090"}, {"desc": "1091", "fix": "1092"}, {"desc": "1093", "fix": "1094"}, {"desc": "1095", "fix": "1096"}, {"desc": "1095", "fix": "1097"}, {"desc": "1098", "fix": "1099"}, {"desc": "1100", "fix": "1101"}, {"desc": "1095", "fix": "1102"}, {"desc": "1103", "fix": "1104"}, {"desc": "1105", "fix": "1106"}, {"kind": "1107", "justification": "1108"}, {"desc": "1109", "fix": "1110"}, {"desc": "1111", "fix": "1112"}, {"desc": "1113", "fix": "1114"}, {"desc": "1115", "fix": "1116"}, {"desc": "1117", "fix": "1118"}, {"desc": "1119", "fix": "1120"}, {"desc": "1121", "fix": "1122"}, {"desc": "1123", "fix": "1124"}, {"desc": "1125", "fix": "1126"}, {"desc": "1127", "fix": "1128"}, {"desc": "1129", "fix": "1130"}, {"desc": "1131", "fix": "1132"}, {"desc": "1133", "fix": "1134"}, {"kind": "1107", "justification": "1108"}, {"desc": "1135", "fix": "1136"}, {"kind": "1107", "justification": "1108"}, {"desc": "1137", "fix": "1138"}, {"desc": "1139", "fix": "1140"}, {"desc": "1141", "fix": "1142"}, {"desc": "1143", "fix": "1144"}, {"desc": "1145", "fix": "1146"}, {"desc": "1147", "fix": "1148"}, {"desc": "1145", "fix": "1149"}, {"desc": "1147", "fix": "1150"}, {"desc": "1151", "fix": "1152"}, {"desc": "1145", "fix": "1153"}, {"desc": "1154", "fix": "1155"}, {"desc": "1087", "fix": "1156"}, {"desc": "1157", "fix": "1158"}, {"desc": "1159", "fix": "1160"}, {"desc": "1161", "fix": "1162"}, {"desc": "1163", "fix": "1164"}, {"desc": "1165", "fix": "1166"}, {"kind": "1107", "justification": "1108"}, {"desc": "1167", "fix": "1168"}, {"kind": "1107", "justification": "1108"}, {"desc": "1169", "fix": "1170"}, {"kind": "1107", "justification": "1108"}, {"desc": "1171", "fix": "1172"}, {"desc": "1173", "fix": "1174"}, {"desc": "1175", "fix": "1176"}, {"desc": "1145", "fix": "1177"}, {"desc": "1178", "fix": "1179"}, {"desc": "1180", "fix": "1181"}, {"desc": "1182", "fix": "1183"}, {"desc": "1184", "fix": "1185"}, "Update the dependencies array to be: [initialized, user]", {"range": "1186", "text": "1187"}, "Update the dependencies array to be: [fetchHomework, fetchStatistics, studentId]", {"range": "1188", "text": "1189"}, "Update the dependencies array to be: [fetchProvinces]", {"range": "1190", "text": "1191"}, "Update the dependencies array to be: [calculateStudentStats, user]", {"range": "1192", "text": "1193"}, "Update the dependencies array to be: [fetchBoundStudents]", {"range": "1194", "text": "1195"}, "Update the dependencies array to be: [fetchStatistics, studentId]", {"range": "1196", "text": "1197"}, "Update the dependencies array to be: [fetchClasses, user]", {"range": "1198", "text": "1199"}, "Update the dependencies array to be: [fetchClassesData, fetchRegionsData, fetchSubjectsData]", {"range": "1200", "text": "1201"}, "Update the dependencies array to be: [searchText, gradeFilter, schoolFilter, classesData, regionSchools, provinceFilter, cityFilter, districtFilter, filterData]", {"range": "1202", "text": "1203"}, "Update the dependencies array to be: [filteredData, generateTreeData, showTreeView, treeViewMode]", {"range": "1204", "text": "1205"}, "Update the dependencies array to be: [selectedClass, subjectsData, updateFilteredSubjects]", {"range": "1206", "text": "1207"}, {"range": "1208", "text": "1207"}, "Update the dependencies array to be: [fetchClassesData, fetchSubjectsData, user]", {"range": "1209", "text": "1210"}, "Update the dependencies array to be: [searchText, gradeFilter, classFilter, classesData, filterData]", {"range": "1211", "text": "1212"}, {"range": "1213", "text": "1207"}, "Update the dependencies array to be: [user, filters, isSystemLevel]", {"range": "1214", "text": "1215"}, "Update the dependencies array to be: [user, isSuperAdmin, fetchSchools, fetchGrades, onFilterChange]", {"range": "1216", "text": "1217"}, "directive", "", "Update the dependencies array to be: [getHeaders]", {"range": "1218", "text": "1219"}, "Update the dependencies array to be: [selectedSchoolId, schoolId, user, fetchSchoolData]", {"range": "1220", "text": "1221"}, "Update the dependencies array to be: [fetchSchoolData, schoolId]", {"range": "1222", "text": "1223"}, "Update the dependencies array to be: [activeTab, fetchSchoolData, selectedSchoolId]", {"range": "1224", "text": "1225"}, "Update the dependencies array to be: [navigate, form, isStudent, selectedRole, availableRoles, registrationSettings]", {"range": "1226", "text": "1227"}, "Update the dependencies array to be: [settingsLoading, availableRoles, form, getInitialValues]", {"range": "1228", "text": "1229"}, "Update the dependencies array to be: [fetchSubjects, selectedCategoryId]", {"range": "1230", "text": "1231"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1232", "text": "1233"}, "Update the dependencies array to be: [fetchPermissions, selectedRole, subjects]", {"range": "1234", "text": "1235"}, "Update the dependencies array to be: [form, location.state?.assignmentId, user]", {"range": "1236", "text": "1237"}, "Update the dependencies array to be: [location.state.assignmentId, form, fetchAssignmentDetails]", {"range": "1238", "text": "1239"}, "Update the dependencies array to be: [location.state, form, fetchAssignmentDetails]", {"range": "1240", "text": "1241"}, "Update the dependencies array to be: [fetchAssignments, searchParams]", {"range": "1242", "text": "1243"}, "Update the dependencies array to be: [location.state, navigate, location.pathname, fetchAssignments]", {"range": "1244", "text": "1245"}, "Update the dependencies array to be: [form, location.state?.filters, location.state?.isSystemLevel, user]", {"range": "1246", "text": "1247"}, "Update the dependencies array to be: [fetchStatistics, user]", {"range": "1248", "text": "1249"}, "Update the dependencies array to be: [selectedClass, selectedAssignment, dateRange, fetchStatistics]", {"range": "1250", "text": "1251"}, "Update the dependencies array to be: [assignmentId, fetchAssignment, fetchHomeworkProgress]", {"range": "1252", "text": "1253"}, "Update the dependencies array to be: [fetchAssignments]", {"range": "1254", "text": "1255"}, "Update the dependencies array to be: [location.state, navigate, location.pathname, filters.assignmentId, fetchAssignments, fetchHomeworks]", {"range": "1256", "text": "1257"}, {"range": "1258", "text": "1255"}, {"range": "1259", "text": "1257"}, "Update the dependencies array to be: [calculateStats]", {"range": "1260", "text": "1261"}, {"range": "1262", "text": "1255"}, "Update the dependencies array to be: [assignments, filterAssignments, location.state]", {"range": "1263", "text": "1264"}, {"range": "1265", "text": "1199"}, "Update the dependencies array to be: [user]", {"range": "1266", "text": "1267"}, "Update the dependencies array to be: [homeworkId, location.state, user.id]", {"range": "1268", "text": "1269"}, "Update the dependencies array to be: [assignmentId, fetchOverviewData]", {"range": "1270", "text": "1271"}, "Update the dependencies array to be: [assignmentId, fetchQuestionsData]", {"range": "1272", "text": "1273"}, "Update the dependencies array to be: [assignmentId, fetchSuggestionsData]", {"range": "1274", "text": "1275"}, "Update the dependencies array to be: [assignmentId, filters.sortBy, filters.order, filters.filterStatus, fetchStudentsData]", {"range": "1276", "text": "1277"}, "Update the dependencies array to be: [applyFilters, filters.searchText, studentsData]", {"range": "1278", "text": "1279"}, "Update the dependencies array to be: [assignmentId, fetchStudentsData]", {"range": "1280", "text": "1281"}, "Update the dependencies array to be: [selectedStudent, assignmentId, fetchParentReport]", {"range": "1282", "text": "1283"}, "Update the dependencies array to be: [assignmentId, fetchStudents]", {"range": "1284", "text": "1285"}, {"range": "1286", "text": "1255"}, "Update the dependencies array to be: [startCamera, stopCamera, visible]", {"range": "1287", "text": "1288"}, "Update the dependencies array to be: [isPinching, isDragging, isResizing, dragStart.x, dragStart.y, imageSize.width, imageSize.height, cropArea.width, cropArea.height, resizeHandle]", {"range": "1289", "text": "1290"}, "Update the dependencies array to be: [assignments, checkReminders]", {"range": "1291", "text": "1292"}, "Update the dependencies array to be: [reminders, lastCheck, showNotification]", {"range": "1293", "text": "1294"}, [3289, 3302], "[initialized, user]", [2854, 2865], "[fetchHomework, fetchStatistics, studentId]", [8049, 8051], "[fetchProvinces]", [5043, 5049], "[calculateStudentStats, user]", [4275, 4277], "[fetchBoundStudents]", [1376, 1387], "[fetchStatistics, studentId]", [2613, 2619], "[fetchClasses, user]", [52315, 52317], "[fetchClassesData, fetchRegionsData, fetchSubjectsData]", [52364, 52475], "[searchText, gradeFilter, schoolFilter, classesData, regionSchools, provinceFilter, cityFilter, districtFilter, filterData]", [52540, 52582], "[filteredData, generateTreeData, showTreeView, treeViewMode]", [52668, 52697], "[selectedClass, subjectsData, updateFilteredSubjects]", [52783, 52812], [59431, 59437], "[fetchClassesData, fetchSubjectsData, user]", [59484, 59535], "[searchText, gradeFilter, classFilter, classesData, filterData]", [59621, 59650], [7796, 7811], "[user, filters, isSystemLevel]", [6764, 6784], "[user, isSuperAdmin, fetchSchools, fetchGrades, onFilterChange]", [3014, 3016], "[getHeaders]", [2940, 2974], "[selectedSchoolId, schoolId, user, fetchSchoolData]", [3794, 3804], "[fetchSchoolData, schoolId]", [5266, 5284], "[activeTab, fetchSchoolData, selectedSchoolId]", [8384, 8400], "[navigate, form, isStudent, selectedRole, availableRoles, registrationSettings]", [8792, 8831], "[settingsLoading, availableRoles, form, getInitialValues]", [3573, 3593], "[fetchSubjects, selectedCategoryId]", [3465, 3467], "[fetchUsers]", [3378, 3402], "[fetchPermissions, selectedRole, subjects]", [7589, 7595], "[form, location.state?.assignmentId, user]", [8024, 8060], "[location.state.assignmentId, form, fetchAssignmentDetails]", [8533, 8555], "[location.state, form, fetchAssignmentDetails]", [7034, 7048], "[fetchAssignments, searchParams]", [7388, 7433], "[location.state, navigate, location.pathname, fetchAssignments]", [16240, 16252], "[form, location.state?.filters, location.state?.isSystemLevel, user]", [1938, 1944], "[fetchStatistics, user]", [2632, 2678], "[selectedClass, selectedAssignment, dateRange, fetchStatistics]", [2238, 2252], "[assignmentId, fetchAssignment, fetchHomeworkProgress]", [7266, 7268], "[fetchAssignments]", [8289, 8356], "[location.state, navigate, location.pathname, filters.assignmentId, fetchAssignments, fetchHomeworks]", [6813, 6815], [7836, 7903], [1629, 1631], "[calculateStats]", [3326, 3328], [3650, 3679], "[assignments, filterAssignments, location.state]", [1049, 1055], [6701, 6703], "[user]", [13444, 13472], "[homeworkId, location.state, user.id]", [1278, 1292], "[assignmentId, fetchOverviewData]", [2179, 2193], "[assignmentId, fetchQuestionsData]", [1796, 1810], "[assignmentId, fetchSuggestionsData]", [6102, 6169], "[assignmentId, filters.sortBy, filters.order, filters.filterStatus, fetchStudentsData]", [6322, 6356], "[applyFilters, filters.searchText, studentsData]", [2413, 2427], "[assignmentId, fetchStudentsData]", [2531, 2562], "[selectedStudent, assignmentId, fetchParentReport]", [1106, 1120], "[assignmentId, fetchStudents]", [11519, 11521], [8717, 8726], "[startCamera, stopCamera, visible]", [10893, 10986], "[isPinching, isDragging, isResizing, dragStart.x, dragStart.y, imageSize.width, imageSize.height, cropArea.width, cropArea.height, resizeHandle]", [5890, 5903], "[assignments, checkReminders]", [6246, 6268], "[reminders, lastCheck, showNotification]"]