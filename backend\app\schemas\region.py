# -*- coding: utf-8 -*-
from pydantic import BaseModel
from typing import List, Optional

# 省份相关Schema
class ProvinceBase(BaseModel):
    name: str
    code: Optional[str] = None

class ProvinceCreate(ProvinceBase):
    pass

class ProvinceUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None

class Province(ProvinceBase):
    id: int

    class Config:
        from_attributes = True

# 城市相关Schema
class CityBase(BaseModel):
    name: str
    code: Optional[str] = None
    province_id: int

class CityCreate(CityBase):
    pass

class CityUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    province_id: Optional[int] = None

class City(CityBase):
    id: int

    class Config:
        from_attributes = True

# 区县相关Schema
class DistrictBase(BaseModel):
    name: str
    code: Optional[str] = None
    city_id: int

class DistrictCreate(DistrictBase):
    pass

class DistrictUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    city_id: Optional[int] = None

class District(DistrictBase):
    id: int

    class Config:
        from_attributes = True

# 带关联数据的Schema
class DistrictWithCity(District):
    city: City

class CityWithDistricts(City):
    districts: List[District] = []

class ProvinceWithCities(Province):
    cities: List[City] = []

# 完整层级结构Schema
class RegionTree(BaseModel):
    provinces: List[ProvinceWithCities] = []