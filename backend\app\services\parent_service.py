"""
家长端专用服务层
独立实现，避免与现有服务耦合
"""
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, desc, func, case, text
from datetime import datetime, timedelta
import logging

from ..models.user import User, ParentStudent, Class, ClassStudent
from ..models.homework import Homework, HomeworkAssignment, HomeworkCorrection, HomeworkImage
from ..models.subject import Subject

logger = logging.getLogger(__name__)

class ParentService:
    """家长端专用服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_bound_students(self, parent_id: int) -> List[Dict[str, Any]]:
        """获取家长绑定的学生列表"""
        try:
            # 查询家长绑定的学生（使用学生的主班级）
            parent_students = self.db.query(
                ParentStudent,
                User.id.label('student_id'),
                User.username.label('student_username'),
                User.full_name.label('student_name'),
                Class.name.label('class_name'),
                Class.id.label('class_id')
            ).join(
                User, ParentStudent.student_id == User.id
            ).outerjoin(
                Class, User.class_id == Class.id  # 使用学生的主班级
            ).filter(
                ParentStudent.parent_id == parent_id
            ).all()
            
            students = []
            for ps, student_id, student_username, student_name, class_name, class_id in parent_students:
                # 获取学生最近的作业统计
                recent_stats = await self._get_student_recent_stats(student_id)
                
                students.append({
                    "student_id": student_id,
                    "student_username": student_username,
                    "student_name": student_name or student_username,
                    "class_name": class_name,
                    "class_id": class_id,
                    "relationship": ps.relationship,
                    "is_primary": ps.is_primary,
                    "recent_stats": recent_stats
                })
            
            return students
            
        except Exception as e:
            logger.error(f"获取绑定学生列表失败: {str(e)}")
            raise
    
    async def get_student_homework(
        self,
        student_id: int,
        page: int = 1,
        limit: int = 20,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取学生作业列表"""
        try:
            # 构建SQL查询字符串
            base_sql = """
                SELECT
                    h.id,
                    h.title,
                    h.status,
                    h.score,
                    h.accuracy,
                    h.created_at,
                    h.graded_at,
                    h.homework_comment,
                    ha.title as assignment_title,
                    ha.due_date,
                    s.name as subject_name,
                    c.name as class_name
                FROM homeworks h
                LEFT JOIN homework_assignments ha ON h.assignment_id = ha.id
                LEFT JOIN subjects s ON ha.subject_id = s.id
                LEFT JOIN classes c ON ha.class_id = c.id
                WHERE h.student_id = :student_id
            """

            # 添加状态筛选
            params = {"student_id": student_id}
            if status:
                base_sql += " AND h.status = :status"
                params["status"] = status

            # 添加排序和分页
            offset = (page - 1) * limit
            final_sql = base_sql + f" ORDER BY h.created_at DESC LIMIT {limit} OFFSET {offset}"

            # 执行查询获取总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM homeworks h
                WHERE h.student_id = :student_id
                {" AND h.status = :status" if status else ""}
            """

            total_result = self.db.execute(text(count_sql), params).fetchone()
            total = total_result[0] if total_result else 0

            # 执行查询
            result = self.db.execute(text(final_sql), params).fetchall()

            # 格式化结果
            homeworks = []
            for row in result:
                homeworks.append({
                    "id": row[0],
                    "title": row[1],
                    "assignment_title": row[8],
                    "subject_name": row[10],
                    "class_name": row[11],
                    "status": row[2],
                    "score": row[3],
                    "accuracy": row[4],
                    "homework_comment": row[7],
                    "created_at": row[5],
                    "graded_at": row[6],
                    "due_date": row[9],
                    "is_overdue": False  # 简化处理
                })

            return {
                "homework": homeworks,  # 注意这里改为homework，与前端期望一致
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total,
                    "pages": (total + limit - 1) // limit if limit > 0 else 0
                }
            }

        except Exception as e:
            logger.error(f"获取学生作业列表失败: {str(e)}")
            raise
    
    async def get_student_profile(self, student_id: int) -> Dict[str, Any]:
        """获取学生基本信息"""
        try:
            # 获取学生信息
            student_info = self.db.query(
                User,
                Class.name.label('class_name'),
                Class.id.label('class_id')
            ).outerjoin(
                ClassStudent, ClassStudent.student_id == User.id
            ).outerjoin(
                Class, ClassStudent.class_id == Class.id
            ).filter(
                User.id == student_id
            ).first()
            
            if not student_info:
                raise ValueError("学生不存在")
            
            user, class_name, class_id = student_info
            
            # 获取基础统计
            stats = await self._get_student_recent_stats(student_id)
            
            return {
                "student_id": user.id,
                "username": user.username,
                "full_name": user.full_name or user.username,
                "class_name": class_name,
                "class_id": class_id,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "statistics": stats
            }
            
        except Exception as e:
            logger.error(f"获取学生信息失败: {str(e)}")
            raise
    
    async def get_homework_detail(self, homework_id: int) -> Dict[str, Any]:
        """获取作业详细信息"""
        try:
            # 获取作业基本信息
            homework_info = self.db.query(
                Homework,
                HomeworkAssignment.title.label('assignment_title'),
                HomeworkAssignment.description.label('assignment_description'),
                HomeworkAssignment.due_date.label('due_date'),
                Subject.name.label('subject_name'),
                User.full_name.label('student_name')
            ).join(
                HomeworkAssignment, Homework.assignment_id == HomeworkAssignment.id
            ).outerjoin(
                Subject, HomeworkAssignment.subject_id == Subject.id
            ).join(
                User, Homework.student_id == User.id
            ).filter(
                Homework.id == homework_id
            ).first()
            
            if not homework_info:
                raise ValueError("作业不存在")
            
            hw, assignment_title, assignment_desc, due_date, subject_name, student_name = homework_info
            
            # 获取作业图片
            images = self.db.query(HomeworkImage).filter(
                HomeworkImage.homework_id == homework_id
            ).all()
            
            # 获取批改结果
            corrections = self.db.query(HomeworkCorrection).filter(
                HomeworkCorrection.homework_id == homework_id
            ).order_by(HomeworkCorrection.page_number).all()
            
            return {
                "homework": {
                    "id": hw.id,
                    "title": hw.title,
                    "description": hw.description,
                    "status": hw.status,
                    "score": hw.score,
                    "accuracy": hw.accuracy,
                    "created_at": hw.created_at.isoformat() if hw.created_at else None,
                    "graded_at": hw.graded_at.isoformat() if hw.graded_at else None,
                    "homework_comment": hw.homework_comment
                },
                "assignment": {
                    "title": assignment_title,
                    "description": assignment_desc,
                    "due_date": due_date.isoformat() if due_date else None,
                    "subject_name": subject_name
                },
                "student": {
                    "name": student_name
                },
                "images": [
                    {
                        "id": img.id,
                        "filename": img.filename,
                        "file_path": img.file_path,
                        "page_number": img.page_number
                    } for img in images
                ],
                "corrections": [
                    {
                        "id": corr.id,
                        "page_number": corr.page_number,
                        "correction_data": corr.correction_data,
                        "created_at": corr.created_at.isoformat() if corr.created_at else None
                    } for corr in corrections
                ]
            }
            
        except Exception as e:
            logger.error(f"获取作业详情失败: {str(e)}")
            raise
    
    async def get_student_statistics(self, student_id: int, days: int = 30) -> Dict[str, Any]:
        """获取学生统计信息"""
        try:
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 获取时间范围内的作业统计
            homework_stats = self.db.query(
                func.count(Homework.id).label('total_homework'),
                func.count(case((Homework.status == 'submitted', 1))).label('submitted_homework'),
                func.count(case((Homework.status == 'graded', 1))).label('graded_homework'),
                func.avg(case((Homework.score.isnot(None), Homework.score))).label('avg_score'),
                func.avg(case((Homework.accuracy.isnot(None), Homework.accuracy))).label('avg_accuracy')
            ).filter(
                and_(
                    Homework.student_id == student_id,
                    Homework.created_at >= start_date,
                    Homework.created_at <= end_date
                )
            ).first()
            
            return {
                "period": {
                    "days": days,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "homework_stats": {
                    "total_homework": homework_stats.total_homework or 0,
                    "submitted_homework": homework_stats.submitted_homework or 0,
                    "graded_homework": homework_stats.graded_homework or 0,
                    "completion_rate": round((homework_stats.submitted_homework or 0) / max(homework_stats.total_homework or 1, 1) * 100, 1),
                    "avg_score": round(homework_stats.avg_score or 0, 1),
                    "avg_accuracy": round(homework_stats.avg_accuracy or 0, 1)
                }
            }
            
        except Exception as e:
            logger.error(f"获取学生统计信息失败: {str(e)}")
            raise
    
    async def get_recent_performance(self, student_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """获取学生最近表现"""
        try:
            recent_homework = self.db.query(
                Homework,
                HomeworkAssignment.title.label('assignment_title'),
                Subject.name.label('subject_name')
            ).join(
                HomeworkAssignment, Homework.assignment_id == HomeworkAssignment.id
            ).outerjoin(
                Subject, HomeworkAssignment.subject_id == Subject.id
            ).filter(
                Homework.student_id == student_id,
                Homework.status == 'graded'
            ).order_by(
                desc(Homework.graded_at)
            ).limit(limit).all()
            
            performance = []
            for hw, assignment_title, subject_name in recent_homework:
                performance.append({
                    "homework_id": hw.id,
                    "title": hw.title,
                    "assignment_title": assignment_title,
                    "subject_name": subject_name,
                    "score": hw.score,
                    "accuracy": hw.accuracy,
                    "graded_at": hw.graded_at.isoformat() if hw.graded_at else None
                })
            
            return performance
            
        except Exception as e:
            logger.error(f"获取学生最近表现失败: {str(e)}")
            raise
    
    async def _get_student_recent_stats(self, student_id: int) -> Dict[str, Any]:
        """获取学生最近统计（内部方法）"""
        try:
            # 最近7天的统计
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            stats = self.db.query(
                func.count(Homework.id).label('total'),
                func.count(case((Homework.status == 'graded', 1))).label('graded'),
                func.avg(case((Homework.score.isnot(None), Homework.score))).label('avg_score')
            ).filter(
                and_(
                    Homework.student_id == student_id,
                    Homework.created_at >= start_date
                )
            ).first()
            
            return {
                "recent_homework_count": stats.total or 0,
                "recent_graded_count": stats.graded or 0,
                "recent_avg_score": round(stats.avg_score or 0, 1)
            }
            
        except Exception as e:
            logger.error(f"获取学生最近统计失败: {str(e)}")
            return {
                "recent_homework_count": 0,
                "recent_graded_count": 0,
                "recent_avg_score": 0
            }
