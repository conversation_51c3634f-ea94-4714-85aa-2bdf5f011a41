from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_
from typing import List, Optional
import logging

from ..database import get_db
from ..models.subject import Subject
from ..models.system_settings import SystemSettings
from ..models.user import User, Class
from ..models.role import Role
from ..models.school import School
from ..models.grade import Grade
from ..schemas import subject as subject_schema
from ..schemas import system_settings as system_settings_schema
from ..schemas import registration as registration_schema
from ..schemas import user as user_schema
from ..schemas import role_subject_permission as role_subject_permission_schema
from ..models.user_role import UserRole
from ..models.role_subject_permission import RoleSubjectPermission
from .auth import get_current_user_optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# 完全公开的科目API - 不需要任何认证
@router.get("/subjects", response_model=List[subject_schema.Subject])
async def get_public_subjects(
    skip: int = 0,
    limit: int = 100,
    school_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """获取公开的科目列表，支持按学校筛选，完全不需要认证"""
    try:
        query = db.query(Subject).filter(Subject.is_active == True)

        # 如果提供了学校ID，根据学校配置筛选科目
        if school_id:
            # 方案1：优先根据学校的年级配置筛选科目
            from ..models.grade import Grade
            from ..models.subject import GradeSubject

            school_grades = db.query(Grade).filter(Grade.school_id == school_id).all()

            if school_grades:
                # 学校有年级配置，根据年级筛选科目
                grade_names = [grade.name for grade in school_grades]
                subject_ids = db.query(GradeSubject.subject_id).filter(
                    GradeSubject.grade.in_(grade_names)
                ).distinct().all()

                if subject_ids:
                    subject_id_list = [sid[0] for sid in subject_ids]
                    query = query.filter(Subject.id.in_(subject_id_list))
                    logger.info(f"根据学校年级配置筛选科目: 学校ID={school_id}, 年级={grade_names}, 科目数={len(subject_id_list)}")
            else:
                # 方案2：学校没有年级配置，根据班级配置筛选科目
                from ..models.user import Class

                school_classes = db.query(Class).filter(Class.school_id == school_id).all()

                if school_classes:
                    # 学校有班级配置，根据班级的年级筛选科目
                    class_grades = list(set([cls.grade for cls in school_classes if cls.grade]))

                    if class_grades:
                        subject_ids = db.query(GradeSubject.subject_id).filter(
                            GradeSubject.grade.in_(class_grades)
                        ).distinct().all()

                        if subject_ids:
                            subject_id_list = [sid[0] for sid in subject_ids]
                            query = query.filter(Subject.id.in_(subject_id_list))
                            logger.info(f"根据学校班级配置筛选科目: 学校ID={school_id}, 班级年级={class_grades}, 科目数={len(subject_id_list)}")

                # 方案3：都没有配置，使用系统默认配置（返回所有科目）
                if not school_classes or not any(cls.grade for cls in school_classes):
                    logger.info(f"学校ID={school_id}没有年级和班级配置，使用系统默认配置（所有科目）")

        subjects = query.offset(skip).limit(limit).all()
        logger.info(f"最终返回科目数量: {len(subjects)}")
        return subjects
    except Exception as e:
        logger.error(f"获取科目列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取科目列表失败: {str(e)}")

@router.get("/registration-settings", response_model=system_settings_schema.RegistrationSettings)
async def get_public_registration_settings(db: Session = Depends(get_db)):
    """获取公开的注册设置（简化版本，兼容旧版本）"""
    try:
        logger.info("获取公开注册设置")
        # 获取简化的注册设置
        settings = SystemSettings.get_simple_registration_settings(db)
        
        logger.info(f"返回注册设置: {settings}")
        return settings
    except Exception as e:
        logger.error(f"获取公开注册设置失败: {str(e)}")
        # 默认返回允许注册
        return {
            "allow_student_registration": True,
            "allow_teacher_registration": True
        }

@router.get("/advanced-registration-settings", response_model=system_settings_schema.AdvancedRegistrationSettings)
async def get_advanced_registration_settings(db: Session = Depends(get_db)):
    """获取高级注册设置，包含所有角色配置"""
    try:
        logger.info("获取高级注册设置")
        settings = SystemSettings.get_registration_settings(db)
        logger.info("成功获取高级注册设置")
        return settings
    except Exception as e:
        logger.error(f"获取高级注册设置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取高级注册设置失败: {str(e)}")

@router.get("/available-roles", response_model=registration_schema.AvailableRolesResponse)
async def get_available_roles(db: Session = Depends(get_db)):
    """获取可用于注册的角色列表"""
    try:
        logger.info("获取可用角色")
        settings = SystemSettings.get_registration_settings(db)
        logger.info(f"获取到注册设置: {settings}")
        
        # 提取已启用的角色
        available_roles = []
        for role_name, config in settings["roles"].items():
            logger.info(f"处理角色 {role_name}, 配置: {config}")
            if config["enabled"]:
                role_info = {
                    "name": role_name,
                    "requires_approval": config["requires_approval"],
                    "fields": config["fields"]
                }
                available_roles.append(role_info)
                logger.info(f"添加可用角色: {role_name}")
            else:
                logger.info(f"角色 {role_name} 未启用，跳过")
        
        logger.info(f"找到 {len(available_roles)} 个可用角色: {[role['name'] for role in available_roles]}")
        return {
            "roles": available_roles,
            "allow_school_creation": settings["allow_school_creation"]
        }
    except Exception as e:
        logger.error(f"获取可用角色失败: {str(e)}")
        logger.exception(e)  # 记录完整的异常堆栈
        # 返回默认角色，避免前端崩溃
        return {
            "roles": [
                {"name": "student", "requires_approval": False, "fields": {"school": {"required": True}, "class": {"required": True}, "subject": {"required": False}}},
                {"name": "teacher", "requires_approval": True, "fields": {"school": {"required": True}, "class": {"required": False}, "subject": {"required": True}}}
            ],
            "allow_school_creation": False
        }

@router.get("/student-search", response_model=List[registration_schema.StudentSearchResult])
async def search_students(
    name: Optional[str] = None,
    class_id: Optional[int] = None,
    school_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """
    搜索学生，用于家长-学生绑定
    """
    logger.info(f"搜索学生: name={name}, class_id={class_id}, school_id={school_id}")
    
    # 构建查询 - 统一使用中文角色
    query = db.query(User).filter(User.role == "学生")
    
    # 添加过滤条件
    if name:
        # 判断搜索类型：如果是纯数字且长度>=6，可能是用户名，使用精确匹配
        if name.isdigit() and len(name) >= 6:
            # 对于用户名搜索，使用精确匹配
            query = query.filter(User.username == name)
        else:
            # 对于姓名搜索，使用模糊匹配
            query = query.filter(
                or_(
                    User.full_name.like(f"%{name}%"),
                    User.username.like(f"%{name}%")
                )
            )
    
    if class_id:
        query = query.filter(User.class_id == class_id)
    
    if school_id:
        query = query.filter(User.school_id == school_id)
    
    # 执行查询
    students = query.limit(10).all()
    
    # 转换为响应模型
    results = []
    for student in students:
        # 获取班级名称
        class_name = None
        if student.class_:
            class_name = student.class_.name
        
        # 获取学校名称
        school_name = None
        if student.school:
            school_name = student.school.name
        
        results.append(registration_schema.StudentSearchResult(
            id=student.id,
            full_name=student.full_name,
            username=student.username,
            class_id=student.class_id,
            class_name=class_name,
            school_id=student.school_id,
            school_name=school_name
        ))
    
    logger.info(f"搜索结果: 找到 {len(results)} 个学生")
    return results

# 获取用户的科目权限
@router.get("/user/subject-permissions/{subject_id}", response_model=role_subject_permission_schema.SubjectPermissionCheck)
async def get_user_subject_permission(
    subject_id: int,
    current_user: User = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """获取当前用户对指定科目的权限"""
    try:
        # 检查科目是否存在
        subject = db.query(Subject).filter(Subject.id == subject_id).first()
        if not subject:
            raise HTTPException(status_code=404, detail="科目不存在")
        
        # 默认权限：只读
        permission_check = {
            "subject_id": subject_id,
            "can_view": True,
            "can_edit": False,
            "can_delete": False,
            "can_assign": False
        }
        
        # 未登录用户只有查看权限
        if not current_user:
            return permission_check
        
        # 超级管理员拥有所有权限
        if current_user.is_admin:
            permission_check["can_edit"] = True
            permission_check["can_delete"] = True
            permission_check["can_assign"] = True
            return permission_check
        
        # 获取用户的角色
        user_roles = db.query(UserRole).filter(UserRole.user_id == current_user.id).all()
        if not user_roles:
            return permission_check
        
        # 检查用户角色的科目权限
        for user_role in user_roles:
            role_permission = db.query(RoleSubjectPermission).filter(
                RoleSubjectPermission.role_id == user_role.role_id,
                RoleSubjectPermission.subject_id == subject_id
            ).first()
            
            if role_permission:
                # 合并权限（有一个角色有权限就有权限）
                permission_check["can_view"] = permission_check["can_view"] or role_permission.can_view
                permission_check["can_edit"] = permission_check["can_edit"] or role_permission.can_edit
                permission_check["can_delete"] = permission_check["can_delete"] or role_permission.can_delete
                permission_check["can_assign"] = permission_check["can_assign"] or role_permission.can_assign
        
        return permission_check
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户科目权限失败: {str(e)}")
        return permission_check

# 学校选择相关的公开API - 用于登录和注册页面
@router.get("/provinces")
async def get_public_provinces(db: Session = Depends(get_db)):
    """获取所有省份列表（公开API，用于登录和注册页面）"""
    try:
        provinces = db.query(School.province).filter(
            School.is_active == True,
            School.province.isnot(None),
            School.province != ""
        ).distinct().all()
        return [{"value": p[0], "label": p[0]} for p in provinces if p[0]]
    except Exception as e:
        logger.error(f"获取省份列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取省份列表失败: {str(e)}")

@router.get("/cities")
async def get_public_cities(province: str, db: Session = Depends(get_db)):
    """根据省份获取城市列表（公开API，用于登录和注册页面）"""
    try:
        cities = db.query(School.city).filter(
            School.province == province,
            School.is_active == True,
            School.city.isnot(None),
            School.city != ""
        ).distinct().all()
        return [{"value": c[0], "label": c[0]} for c in cities if c[0]]
    except Exception as e:
        logger.error(f"获取城市列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取城市列表失败: {str(e)}")

@router.get("/districts")
async def get_public_districts(province: str, city: str, db: Session = Depends(get_db)):
    """根据省份和城市获取区县列表（公开API，用于登录和注册页面）"""
    try:
        districts = db.query(School.district).filter(
            School.province == province,
            School.city == city,
            School.is_active == True,
            School.district.isnot(None),
            School.district != ""
        ).distinct().all()
        return [{"value": d[0], "label": d[0]} for d in districts if d[0]]
    except Exception as e:
        logger.error(f"获取区县列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取区县列表失败: {str(e)}")

@router.get("/schools")
async def get_public_schools(
    province: str,
    city: str,
    district: str = None,
    db: Session = Depends(get_db)
):
    """根据地理位置获取学校列表（公开API，用于登录和注册页面）"""
    try:
        query = db.query(School).filter(
            School.province == province,
            School.city == city,
            School.is_active == True
        )
        if district:
            query = query.filter(School.district == district)

        schools = query.all()
        return [{
            "value": s.id,
            "label": s.name,
            "province": s.province,
            "city": s.city,
            "district": s.district,
            "address": s.address
        } for s in schools]
    except Exception as e:
        logger.error(f"获取学校列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取学校列表失败: {str(e)}")

@router.get("/schools/search")
async def search_public_schools(q: str, db: Session = Depends(get_db)):
    """搜索学校（公开API，用于登录和注册页面）"""
    try:
        schools = db.query(School).filter(
            School.name.contains(q),
            School.is_active == True
        ).limit(20).all()
        return [{
            "value": s.id,
            "label": s.name,
            "province": s.province,
            "city": s.city,
            "district": s.district,
            "address": s.address
        } for s in schools]
    except Exception as e:
        logger.error(f"搜索学校失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索学校失败: {str(e)}")

# 家长注册时的临时绑定API（不需要认证）
@router.post("/temp-parent-student-binding", response_model=registration_schema.TempParentStudentVerification)
async def create_temp_parent_student_binding(
    binding: registration_schema.TempParentStudentVerificationCreate,
    db: Session = Depends(get_db)
):
    """创建临时家长学生绑定验证（用于注册流程，不需要认证）"""
    try:
        # 检查学生是否存在
        student = db.query(User).filter(User.id == binding.student_id).first()
        if not student:
            raise HTTPException(status_code=404, detail="学生不存在")

        # 获取验证设置
        from ..models.system_settings import SystemSettings
        settings = SystemSettings.get_registration_settings(db)
        verification_settings = settings.get("student_binding_verification", {})
        expiry_minutes = verification_settings.get("code_expiry_minutes", 30)

        # 创建验证记录（parent_id暂时为0，注册完成后更新）
        from ..models.registration import ParentStudentVerification
        verification = ParentStudentVerification.generate_verification(
            parent_id=0,  # 临时设为0，注册完成后更新
            student_id=binding.student_id,
            relationship_type=binding.relationship,
            is_primary=binding.is_primary,
            expiry_minutes=expiry_minutes
        )

        db.add(verification)
        db.commit()
        db.refresh(verification)

        logger.info(f"创建了临时家长学生绑定验证: 学生ID={binding.student_id}")
        return verification
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建临时家长学生绑定验证失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建临时家长学生绑定验证失败: {str(e)}")

@router.post("/verify-temp-binding-code", response_model=registration_schema.TempParentStudentVerification)
async def verify_temp_binding_code(
    verification_data: registration_schema.ParentStudentVerificationVerify,
    db: Session = Depends(get_db)
):
    """验证临时家长学生绑定验证码（用于注册流程，不需要认证）"""
    try:
        from ..models.registration import ParentStudentVerification

        # 获取验证记录
        verification = db.query(ParentStudentVerification).filter(
            ParentStudentVerification.id == verification_data.verification_id
        ).first()

        if not verification:
            raise HTTPException(status_code=404, detail="验证记录不存在")

        # 验证验证码
        success, message = verification.verify(verification_data.code)

        if not success:
            raise HTTPException(status_code=400, detail=message)

        # 更新状态为已验证（但不创建关联，等注册完成后再创建）
        verification.status = "verified"
        db.commit()
        db.refresh(verification)

        logger.info(f"临时家长学生绑定验证成功: 学生ID={verification.student_id}")
        return verification
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"验证临时家长学生绑定验证码失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"验证临时家长学生绑定验证码失败: {str(e)}")
