
from sqlalchemy import Column, Integer, String, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime

from ..database import Base

class Province(Base):
    """省份模型"""
    __tablename__ = "provinces"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)  # 省份名称
    code = Column(String, unique=True, index=True, nullable=True)   # 省份代码
    
    # 关系
    cities = relationship("City", back_populates="province", cascade="all, delete-orphan")

class City(Base):
    """城市模型"""
    __tablename__ = "cities"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True, nullable=False)  # 城市名称
    code = Column(String, index=True, nullable=True)   # 城市代码
    province_id = Column(Integer, ForeignKey("provinces.id", ondelete="CASCADE"), nullable=False)
    
    # 关系
    province = relationship("Province", back_populates="cities")
    districts = relationship("District", back_populates="city", cascade="all, delete-orphan")

class District(Base):
    """区县模型"""
    __tablename__ = "districts"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True, nullable=False)  # 区县名称
    code = Column(String, index=True, nullable=True)   # 区县代码
    city_id = Column(Integer, ForeignKey("cities.id", ondelete="CASCADE"), nullable=False)
    
    # 关系
    city = relationship("City", back_populates="districts")
