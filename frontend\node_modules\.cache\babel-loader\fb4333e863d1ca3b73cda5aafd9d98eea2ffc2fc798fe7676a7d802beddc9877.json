{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\correcthomework4\\\\frontend\\\\src\\\\pages\\\\ParentDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Typography, Spin, message, Avatar, List, Badge, Statistic, Progress, Empty, Button, Space, Divider } from 'antd';\nimport { UserOutlined, BookOutlined, TrophyOutlined, ClockCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined, BarChartOutlined, EyeOutlined } from '@ant-design/icons';\nimport api from '../utils/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst ParentDashboard = ({\n  user\n}) => {\n  _s();\n  // 添加样式\n  const selectedStudentStyle = {\n    border: '2px solid #1890ff',\n    boxShadow: '0 2px 8px rgba(24, 144, 255, 0.2)'\n  };\n  const [loading, setLoading] = useState(true);\n  const [boundStudents, setBoundStudents] = useState([]);\n  const [selectedStudent, setSelectedStudent] = useState(null);\n  const [studentHomework, setStudentHomework] = useState([]);\n  const [studentStats, setStudentStats] = useState(null);\n\n  // 获取绑定学生列表\n  const fetchBoundStudents = async () => {\n    try {\n      const response = await api.get('/parent/bound-students');\n\n      // 检查响应格式\n      if (response && typeof response === 'object' && response.success) {\n        setBoundStudents(response.data);\n        // 默认选择第一个学生\n        if (response.data && response.data.length > 0) {\n          setSelectedStudent(response.data[0]);\n        }\n      } else if (Array.isArray(response)) {\n        setBoundStudents(response);\n        // 默认选择第一个学生\n        if (response.length > 0) {\n          setSelectedStudent(response[0]);\n        }\n      } else {\n        console.log('❌ 未知响应格式:', response);\n      }\n    } catch (error) {\n      console.error('获取绑定学生失败:', error);\n      message.error('获取绑定学生信息失败');\n    }\n  };\n\n  // 获取学生作业列表\n  const fetchStudentHomework = async studentId => {\n    if (!studentId) return;\n    try {\n      const response = await api.get(`/parent/student/${studentId}/homework?limit=10`);\n      if (response.data.success) {\n        setStudentHomework(response.data.data.homeworks || []);\n      }\n    } catch (error) {\n      console.error('获取学生作业失败:', error);\n      message.error('获取学生作业信息失败');\n    }\n  };\n\n  // 获取学生统计信息\n  const fetchStudentStats = async studentId => {\n    if (!studentId) return;\n    try {\n      const response = await api.get(`/parent/student/${studentId}/statistics`);\n      if (response.data.success) {\n        setStudentStats(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取学生统计失败:', error);\n      // 不显示错误消息，因为这是增强功能\n    }\n  };\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      await fetchBoundStudents();\n      setLoading(false);\n    };\n    loadData();\n  }, []);\n  useEffect(() => {\n    if (selectedStudent) {\n      fetchStudentHomework(selectedStudent.student_id);\n      fetchStudentStats(selectedStudent.student_id);\n    }\n  }, [selectedStudent]);\n\n  // 查看作业详情\n  const handleViewHomework = async student => {\n    try {\n      // 获取学生作业列表\n      const response = await api.get(`/parent/student/${student.student_id}/homework`);\n\n      // 检查响应格式\n      let homeworkList = [];\n      if (response && response.success && response.data && response.data.homework) {\n        homeworkList = response.data.homework;\n      } else if (Array.isArray(response)) {\n        homeworkList = response;\n      } else if (response && response.success && Array.isArray(response.data)) {\n        homeworkList = response.data;\n      }\n      if (homeworkList.length > 0) {\n        // 创建简洁的作业列表显示\n        const homeworkHtml = homeworkList.map(hw => `\n          <div style=\"border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px;\">\n            <h3>${hw.assignment_title || hw.title}</h3>\n            <p><strong>科目:</strong> ${hw.subject_name || '未知'}</p>\n            <p><strong>班级:</strong> ${hw.class_name || '未知'}</p>\n            <p><strong>状态:</strong> ${hw.status === 'graded' ? '已批改' : hw.status === 'submitted' ? '已提交' : '未提交'}</p>\n            <p><strong>分数:</strong> ${hw.score || 0}分</p>\n            <p><strong>正确率:</strong> ${Math.round((hw.accuracy || 0) * 100)}%</p>\n            <p><strong>提交时间:</strong> ${new Date(hw.created_at).toLocaleString()}</p>\n            ${hw.homework_comment ? `<p><strong>老师评语:</strong> ${hw.homework_comment}</p>` : ''}\n          </div>\n        `).join('');\n        const newWindow = window.open('', '_blank');\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${student.student_name} - 作业详情</title>\n              <style>\n                body { font-family: Arial, sans-serif; margin: 20px; }\n                h1 { color: #1890ff; }\n                h3 { color: #333; margin: 0 0 10px 0; }\n                p { margin: 5px 0; }\n              </style>\n            </head>\n            <body>\n              <h1>📚 ${student.student_name} - 作业详情</h1>\n              <p>共找到 ${homeworkList.length} 份作业</p>\n              ${homeworkHtml}\n            </body>\n          </html>\n        `);\n      } else {\n        message.info('暂无作业记录');\n      }\n    } catch (error) {\n      console.error('获取作业详情失败:', error);\n      message.error('获取作业详情失败');\n    }\n  };\n\n  // 查看学习报告（统计信息）\n  const handleViewReport = async student => {\n    try {\n      // 获取学生统计信息\n      const response = await api.get(`/parent/student/${student.student_id}/statistics`);\n      if (response && response.success && response.data) {\n        var _stats$period, _stats$period2, _stats$period3;\n        const stats = response.data;\n        const homeworkStats = stats.homework_stats || {};\n\n        // 创建美观的统计报告\n        const reportHtml = `\n          <div style=\"max-width: 800px; margin: 0 auto;\">\n            <div style=\"background: #f0f8ff; padding: 20px; border-radius: 10px; margin-bottom: 20px;\">\n              <h2>📊 统计周期</h2>\n              <p><strong>统计天数:</strong> ${((_stats$period = stats.period) === null || _stats$period === void 0 ? void 0 : _stats$period.days) || 30} 天</p>\n              <p><strong>开始日期:</strong> ${new Date((_stats$period2 = stats.period) === null || _stats$period2 === void 0 ? void 0 : _stats$period2.start_date).toLocaleDateString()}</p>\n              <p><strong>结束日期:</strong> ${new Date((_stats$period3 = stats.period) === null || _stats$period3 === void 0 ? void 0 : _stats$period3.end_date).toLocaleDateString()}</p>\n            </div>\n\n            <div style=\"background: #f6ffed; padding: 20px; border-radius: 10px; margin-bottom: 20px;\">\n              <h2>📝 作业统计</h2>\n              <div style=\"display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;\">\n                <div style=\"background: white; padding: 15px; border-radius: 5px; text-align: center;\">\n                  <h3 style=\"color: #1890ff; margin: 0;\">${homeworkStats.total_homework || 0}</h3>\n                  <p style=\"margin: 5px 0 0 0;\">总作业数</p>\n                </div>\n                <div style=\"background: white; padding: 15px; border-radius: 5px; text-align: center;\">\n                  <h3 style=\"color: #52c41a; margin: 0;\">${homeworkStats.graded_homework || 0}</h3>\n                  <p style=\"margin: 5px 0 0 0;\">已批改作业</p>\n                </div>\n                <div style=\"background: white; padding: 15px; border-radius: 5px; text-align: center;\">\n                  <h3 style=\"color: #fa8c16; margin: 0;\">${Math.round(homeworkStats.avg_score || 0)}分</h3>\n                  <p style=\"margin: 5px 0 0 0;\">平均分数</p>\n                </div>\n                <div style=\"background: white; padding: 15px; border-radius: 5px; text-align: center;\">\n                  <h3 style=\"color: #722ed1; margin: 0;\">${Math.round((homeworkStats.avg_accuracy || 0) * 100)}%</h3>\n                  <p style=\"margin: 5px 0 0 0;\">平均正确率</p>\n                </div>\n              </div>\n            </div>\n\n            <div style=\"background: #fff7e6; padding: 20px; border-radius: 10px;\">\n              <h2>📈 学习表现</h2>\n              <div style=\"background: white; padding: 15px; border-radius: 5px;\">\n                <p><strong>完成率:</strong> ${Math.round(homeworkStats.completion_rate || 0)}%</p>\n                <p><strong>学习建议:</strong></p>\n                <ul>\n                  ${homeworkStats.avg_score >= 80 ? '<li style=\"color: #52c41a;\">✅ 学习表现优秀，继续保持！</li>' : homeworkStats.avg_score >= 60 ? '<li style=\"color: #fa8c16;\">⚠️ 学习表现良好，还有提升空间</li>' : '<li style=\"color: #ff4d4f;\">❗ 需要加强学习，建议多练习</li>'}\n                  ${homeworkStats.avg_accuracy >= 0.8 ? '<li style=\"color: #52c41a;\">✅ 正确率很高，基础扎实</li>' : '<li style=\"color: #fa8c16;\">📚 建议加强基础知识练习</li>'}\n                  <li style=\"color: #1890ff;\">📅 建议保持每日学习习惯</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        `;\n        const newWindow = window.open('', '_blank');\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${student.student_name} - 学习报告</title>\n              <style>\n                body {\n                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n                  margin: 20px;\n                  background: #fafafa;\n                }\n                h1 { color: #1890ff; text-align: center; }\n                h2 { color: #333; margin: 0 0 15px 0; }\n                h3 { margin: 0; }\n                p { margin: 8px 0; }\n                ul { margin: 10px 0; padding-left: 20px; }\n                li { margin: 5px 0; }\n              </style>\n            </head>\n            <body>\n              <h1>📊 ${student.student_name} - 学习报告</h1>\n              ${reportHtml}\n            </body>\n          </html>\n        `);\n      } else {\n        message.error('获取学习报告数据失败');\n      }\n    } catch (error) {\n      console.error('获取学习报告失败:', error);\n      message.error('获取学习报告失败');\n    }\n  };\n\n  // 渲染学生选择卡片\n  const renderStudentCards = () => {\n    if (boundStudents.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(Empty, {\n          description: \"\\u6682\\u65E0\\u7ED1\\u5B9A\\u5B66\\u751F\",\n          image: Empty.PRESENTED_IMAGE_SIMPLE,\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u8BF7\\u8054\\u7CFB\\u5B66\\u6821\\u7BA1\\u7406\\u5458\\u4E3A\\u60A8\\u7ED1\\u5B9A\\u5B66\\u751F\\u8D26\\u6237\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: boundStudents.map(student => {\n        var _student$recent_stats;\n        return /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            hoverable: true,\n            onClick: () => setSelectedStudent(student),\n            style: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.student_id) === student.student_id ? selectedStudentStyle : {},\n            children: [/*#__PURE__*/_jsxDEV(Card.Meta, {\n              avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n                size: 48,\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 25\n              }, this),\n              title: student.student_name,\n              description: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: student.class_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                  status: student.is_primary ? 'success' : 'default',\n                  text: student.relationship\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: [\"\\u6700\\u8FD1\\u4F5C\\u4E1A: \", ((_student$recent_stats = student.recent_stats) === null || _student$recent_stats === void 0 ? void 0 : _student$recent_stats.recent_homework_count) || 0, \" \\u4EFD\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '16px',\n                display: 'flex',\n                gap: '8px',\n                justifyContent: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                size: \"small\",\n                icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 25\n                }, this),\n                onClick: e => {\n                  e.stopPropagation();\n                  handleViewHomework(student);\n                },\n                children: \"\\u4F5C\\u4E1A\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 25\n                }, this),\n                onClick: e => {\n                  e.stopPropagation();\n                  handleViewReport(student);\n                },\n                children: \"\\u5B66\\u4E60\\u62A5\\u544A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, student.student_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染统计信息\n  const renderStatistics = () => {\n    if (!selectedStudent || !studentStats) {\n      return null;\n    }\n    const stats = studentStats.homework_stats;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5B66\\u4E60\\u7EDF\\u8BA1\",\n      style: {\n        marginTop: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4F5C\\u4E1A\\u6570\",\n            value: stats.total_homework,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u63D0\\u4EA4\",\n            value: stats.submitted_homework,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5B8C\\u6210\\u7387\",\n            value: stats.completion_rate,\n            suffix: \"%\",\n            prefix: /*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u5206\",\n            value: stats.avg_score,\n            precision: 1,\n            prefix: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u5B8C\\u6210\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: stats.completion_rate,\n          status: stats.completion_rate >= 80 ? 'success' : stats.completion_rate >= 60 ? 'normal' : 'exception',\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染最近作业\n  const renderRecentHomework = () => {\n    if (!selectedStudent) {\n      return null;\n    }\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6700\\u8FD1\\u4F5C\\u4E1A\",\n      style: {\n        marginTop: 16\n      },\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 19\n        }, this),\n        onClick: () => {\n          // 这里可以跳转到作业详情页面\n          message.info('作业详情功能开发中');\n        },\n        children: \"\\u67E5\\u770B\\u66F4\\u591A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 11\n      }, this),\n      children: studentHomework.length === 0 ? /*#__PURE__*/_jsxDEV(Empty, {\n        description: \"\\u6682\\u65E0\\u4F5C\\u4E1A\\u8BB0\\u5F55\",\n        image: Empty.PRESENTED_IMAGE_SIMPLE\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(List, {\n        dataSource: studentHomework.slice(0, 5),\n        renderItem: homework => /*#__PURE__*/_jsxDEV(List.Item, {\n          children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n            avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n              icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 29\n              }, this),\n              style: {\n                backgroundColor: homework.status === 'graded' ? '#52c41a' : homework.status === 'submitted' ? '#1890ff' : '#faad14'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 21\n            }, this),\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: homework.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                status: homework.status === 'graded' ? 'success' : homework.status === 'submitted' ? 'processing' : 'warning',\n                text: homework.status === 'graded' ? '已批改' : homework.status === 'submitted' ? '已提交' : '未完成'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 21\n            }, this),\n            description: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: homework.subject_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 23\n              }, this), homework.score && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {\n                  type: \"vertical\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: [\"\\u5F97\\u5206: \", homework.score]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 25\n                }, this), \" \", homework.created_at ? new Date(homework.created_at).toLocaleDateString() : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u5BB6\\u957F\\u7AEF\\u6570\\u636E...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 3,\n        children: [\"\\uD83D\\uDC68\\u200D\\uD83D\\uDC69\\u200D\\uD83D\\uDC67\\u200D\\uD83D\\uDC66 \\u6B22\\u8FCE\\u56DE\\u6765\\uFF0C\", (user === null || user === void 0 ? void 0 : user.full_name) || (user === null || user === void 0 ? void 0 : user.username), \"\\uFF01\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n        type: \"secondary\",\n        children: \"\\u667A\\u6167\\u4E91\\u7AEF\\u5BB6\\u957F\\u7AEF\\u4E3A\\u60A8\\u63D0\\u4F9B\\u5B69\\u5B50\\u7684\\u5B66\\u4E60\\u60C5\\u51B5\\u67E5\\u770B\\u548C\\u5206\\u6790\\u670D\\u52A1\\u3002\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6211\\u7684\\u5B69\\u5B50\",\n      style: {\n        marginBottom: 16\n      },\n      children: renderStudentCards()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 518,\n      columnNumber: 7\n    }, this), selectedStudent && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [renderStatistics(), renderRecentHomework()]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 506,\n    columnNumber: 5\n  }, this);\n};\n_s(ParentDashboard, \"TNT7aZgfaKCQwwe1c+HPXi4fynw=\");\n_c = ParentDashboard;\nexport default ParentDashboard;\nvar _c;\n$RefreshReg$(_c, \"ParentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Typography", "Spin", "message", "Avatar", "List", "Badge", "Statistic", "Progress", "Empty", "<PERSON><PERSON>", "Space", "Divider", "UserOutlined", "BookOutlined", "TrophyOutlined", "ClockCircleOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "BarChartOutlined", "EyeOutlined", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Paragraph", "ParentDashboard", "user", "_s", "selectedStudentStyle", "border", "boxShadow", "loading", "setLoading", "boundStudents", "setBoundStudents", "selectedStudent", "setSelectedStudent", "studentHomework", "setStudentHomework", "studentStats", "setStudentStats", "fetchBoundStudents", "response", "get", "success", "data", "length", "Array", "isArray", "console", "log", "error", "fetchStudentHomework", "studentId", "homeworks", "fetchStudentStats", "loadData", "student_id", "handleViewHomework", "student", "homeworkList", "homework", "homeworkHtml", "map", "hw", "assignment_title", "title", "subject_name", "class_name", "status", "score", "Math", "round", "accuracy", "Date", "created_at", "toLocaleString", "homework_comment", "join", "newWindow", "window", "open", "document", "write", "student_name", "info", "handleViewReport", "_stats$period", "_stats$period2", "_stats$period3", "stats", "homeworkStats", "homework_stats", "reportHtml", "period", "days", "start_date", "toLocaleDateString", "end_date", "total_homework", "graded_homework", "avg_score", "avg_accuracy", "completion_rate", "renderStudentCards", "children", "description", "image", "PRESENTED_IMAGE_SIMPLE", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "_student$recent_stats", "xs", "sm", "md", "lg", "hoverable", "onClick", "style", "Meta", "avatar", "size", "icon", "is_primary", "text", "relationship", "fontSize", "recent_stats", "recent_homework_count", "marginTop", "display", "gap", "justifyContent", "e", "stopPropagation", "renderStatistics", "value", "prefix", "submitted_homework", "suffix", "precision", "strong", "percent", "renderRecentHomework", "extra", "dataSource", "slice", "renderItem", "<PERSON><PERSON>", "backgroundColor", "textAlign", "padding", "marginBottom", "level", "full_name", "username", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/correcthomework4/frontend/src/pages/ParentDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Typography,\n  Spin,\n  message,\n  Avatar,\n  List,\n  Badge,\n  Statistic,\n  Progress,\n  Empty,\n  Button,\n  Space,\n  Divider\n} from 'antd';\nimport {\n  UserOutlined,\n  BookOutlined,\n  TrophyOutlined,\n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  Bar<PERSON>hartOutlined,\n  EyeOutlined\n} from '@ant-design/icons';\nimport api from '../utils/api';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst ParentDashboard = ({ user }) => {\n  // 添加样式\n  const selectedStudentStyle = {\n    border: '2px solid #1890ff',\n    boxShadow: '0 2px 8px rgba(24, 144, 255, 0.2)'\n  };\n  const [loading, setLoading] = useState(true);\n  const [boundStudents, setBoundStudents] = useState([]);\n  const [selectedStudent, setSelectedStudent] = useState(null);\n  const [studentHomework, setStudentHomework] = useState([]);\n  const [studentStats, setStudentStats] = useState(null);\n\n  // 获取绑定学生列表\n  const fetchBoundStudents = async () => {\n    try {\n      const response = await api.get('/parent/bound-students');\n\n      // 检查响应格式\n      if (response && typeof response === 'object' && response.success) {\n        setBoundStudents(response.data);\n        // 默认选择第一个学生\n        if (response.data && response.data.length > 0) {\n          setSelectedStudent(response.data[0]);\n        }\n      } else if (Array.isArray(response)) {\n        setBoundStudents(response);\n        // 默认选择第一个学生\n        if (response.length > 0) {\n          setSelectedStudent(response[0]);\n        }\n      } else {\n        console.log('❌ 未知响应格式:', response);\n      }\n    } catch (error) {\n      console.error('获取绑定学生失败:', error);\n      message.error('获取绑定学生信息失败');\n    }\n  };\n\n  // 获取学生作业列表\n  const fetchStudentHomework = async (studentId) => {\n    if (!studentId) return;\n    \n    try {\n      const response = await api.get(`/parent/student/${studentId}/homework?limit=10`);\n      if (response.data.success) {\n        setStudentHomework(response.data.data.homeworks || []);\n      }\n    } catch (error) {\n      console.error('获取学生作业失败:', error);\n      message.error('获取学生作业信息失败');\n    }\n  };\n\n  // 获取学生统计信息\n  const fetchStudentStats = async (studentId) => {\n    if (!studentId) return;\n    \n    try {\n      const response = await api.get(`/parent/student/${studentId}/statistics`);\n      if (response.data.success) {\n        setStudentStats(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取学生统计失败:', error);\n      // 不显示错误消息，因为这是增强功能\n    }\n  };\n\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      await fetchBoundStudents();\n      setLoading(false);\n    };\n    \n    loadData();\n  }, []);\n\n  useEffect(() => {\n    if (selectedStudent) {\n      fetchStudentHomework(selectedStudent.student_id);\n      fetchStudentStats(selectedStudent.student_id);\n    }\n  }, [selectedStudent]);\n\n  // 查看作业详情\n  const handleViewHomework = async (student) => {\n    try {\n      // 获取学生作业列表\n      const response = await api.get(`/parent/student/${student.student_id}/homework`);\n\n      // 检查响应格式\n      let homeworkList = [];\n      if (response && response.success && response.data && response.data.homework) {\n        homeworkList = response.data.homework;\n      } else if (Array.isArray(response)) {\n        homeworkList = response;\n      } else if (response && response.success && Array.isArray(response.data)) {\n        homeworkList = response.data;\n      }\n\n      if (homeworkList.length > 0) {\n        // 创建简洁的作业列表显示\n        const homeworkHtml = homeworkList.map(hw => `\n          <div style=\"border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px;\">\n            <h3>${hw.assignment_title || hw.title}</h3>\n            <p><strong>科目:</strong> ${hw.subject_name || '未知'}</p>\n            <p><strong>班级:</strong> ${hw.class_name || '未知'}</p>\n            <p><strong>状态:</strong> ${hw.status === 'graded' ? '已批改' : hw.status === 'submitted' ? '已提交' : '未提交'}</p>\n            <p><strong>分数:</strong> ${hw.score || 0}分</p>\n            <p><strong>正确率:</strong> ${Math.round((hw.accuracy || 0) * 100)}%</p>\n            <p><strong>提交时间:</strong> ${new Date(hw.created_at).toLocaleString()}</p>\n            ${hw.homework_comment ? `<p><strong>老师评语:</strong> ${hw.homework_comment}</p>` : ''}\n          </div>\n        `).join('');\n\n        const newWindow = window.open('', '_blank');\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${student.student_name} - 作业详情</title>\n              <style>\n                body { font-family: Arial, sans-serif; margin: 20px; }\n                h1 { color: #1890ff; }\n                h3 { color: #333; margin: 0 0 10px 0; }\n                p { margin: 5px 0; }\n              </style>\n            </head>\n            <body>\n              <h1>📚 ${student.student_name} - 作业详情</h1>\n              <p>共找到 ${homeworkList.length} 份作业</p>\n              ${homeworkHtml}\n            </body>\n          </html>\n        `);\n      } else {\n        message.info('暂无作业记录');\n      }\n    } catch (error) {\n      console.error('获取作业详情失败:', error);\n      message.error('获取作业详情失败');\n    }\n  };\n\n  // 查看学习报告（统计信息）\n  const handleViewReport = async (student) => {\n    try {\n      // 获取学生统计信息\n      const response = await api.get(`/parent/student/${student.student_id}/statistics`);\n\n      if (response && response.success && response.data) {\n        const stats = response.data;\n        const homeworkStats = stats.homework_stats || {};\n\n        // 创建美观的统计报告\n        const reportHtml = `\n          <div style=\"max-width: 800px; margin: 0 auto;\">\n            <div style=\"background: #f0f8ff; padding: 20px; border-radius: 10px; margin-bottom: 20px;\">\n              <h2>📊 统计周期</h2>\n              <p><strong>统计天数:</strong> ${stats.period?.days || 30} 天</p>\n              <p><strong>开始日期:</strong> ${new Date(stats.period?.start_date).toLocaleDateString()}</p>\n              <p><strong>结束日期:</strong> ${new Date(stats.period?.end_date).toLocaleDateString()}</p>\n            </div>\n\n            <div style=\"background: #f6ffed; padding: 20px; border-radius: 10px; margin-bottom: 20px;\">\n              <h2>📝 作业统计</h2>\n              <div style=\"display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;\">\n                <div style=\"background: white; padding: 15px; border-radius: 5px; text-align: center;\">\n                  <h3 style=\"color: #1890ff; margin: 0;\">${homeworkStats.total_homework || 0}</h3>\n                  <p style=\"margin: 5px 0 0 0;\">总作业数</p>\n                </div>\n                <div style=\"background: white; padding: 15px; border-radius: 5px; text-align: center;\">\n                  <h3 style=\"color: #52c41a; margin: 0;\">${homeworkStats.graded_homework || 0}</h3>\n                  <p style=\"margin: 5px 0 0 0;\">已批改作业</p>\n                </div>\n                <div style=\"background: white; padding: 15px; border-radius: 5px; text-align: center;\">\n                  <h3 style=\"color: #fa8c16; margin: 0;\">${Math.round(homeworkStats.avg_score || 0)}分</h3>\n                  <p style=\"margin: 5px 0 0 0;\">平均分数</p>\n                </div>\n                <div style=\"background: white; padding: 15px; border-radius: 5px; text-align: center;\">\n                  <h3 style=\"color: #722ed1; margin: 0;\">${Math.round((homeworkStats.avg_accuracy || 0) * 100)}%</h3>\n                  <p style=\"margin: 5px 0 0 0;\">平均正确率</p>\n                </div>\n              </div>\n            </div>\n\n            <div style=\"background: #fff7e6; padding: 20px; border-radius: 10px;\">\n              <h2>📈 学习表现</h2>\n              <div style=\"background: white; padding: 15px; border-radius: 5px;\">\n                <p><strong>完成率:</strong> ${Math.round(homeworkStats.completion_rate || 0)}%</p>\n                <p><strong>学习建议:</strong></p>\n                <ul>\n                  ${homeworkStats.avg_score >= 80 ?\n                    '<li style=\"color: #52c41a;\">✅ 学习表现优秀，继续保持！</li>' :\n                    homeworkStats.avg_score >= 60 ?\n                    '<li style=\"color: #fa8c16;\">⚠️ 学习表现良好，还有提升空间</li>' :\n                    '<li style=\"color: #ff4d4f;\">❗ 需要加强学习，建议多练习</li>'\n                  }\n                  ${homeworkStats.avg_accuracy >= 0.8 ?\n                    '<li style=\"color: #52c41a;\">✅ 正确率很高，基础扎实</li>' :\n                    '<li style=\"color: #fa8c16;\">📚 建议加强基础知识练习</li>'\n                  }\n                  <li style=\"color: #1890ff;\">📅 建议保持每日学习习惯</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        `;\n\n        const newWindow = window.open('', '_blank');\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${student.student_name} - 学习报告</title>\n              <style>\n                body {\n                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n                  margin: 20px;\n                  background: #fafafa;\n                }\n                h1 { color: #1890ff; text-align: center; }\n                h2 { color: #333; margin: 0 0 15px 0; }\n                h3 { margin: 0; }\n                p { margin: 8px 0; }\n                ul { margin: 10px 0; padding-left: 20px; }\n                li { margin: 5px 0; }\n              </style>\n            </head>\n            <body>\n              <h1>📊 ${student.student_name} - 学习报告</h1>\n              ${reportHtml}\n            </body>\n          </html>\n        `);\n      } else {\n        message.error('获取学习报告数据失败');\n      }\n    } catch (error) {\n      console.error('获取学习报告失败:', error);\n      message.error('获取学习报告失败');\n    }\n  };\n\n  // 渲染学生选择卡片\n  const renderStudentCards = () => {\n    if (boundStudents.length === 0) {\n      return (\n        <Card>\n          <Empty\n            description=\"暂无绑定学生\"\n            image={Empty.PRESENTED_IMAGE_SIMPLE}\n          >\n            <Text type=\"secondary\">\n              请联系学校管理员为您绑定学生账户\n            </Text>\n          </Empty>\n        </Card>\n      );\n    }\n\n    return (\n      <Row gutter={[16, 16]}>\n        {boundStudents.map((student) => (\n          <Col xs={24} sm={12} md={8} lg={6} key={student.student_id}>\n            <Card\n              hoverable\n              onClick={() => setSelectedStudent(student)}\n              style={selectedStudent?.student_id === student.student_id ? selectedStudentStyle : {}}\n            >\n              <Card.Meta\n                avatar={<Avatar size={48} icon={<UserOutlined />} />}\n                title={student.student_name}\n                description={\n                  <div>\n                    <Text type=\"secondary\">{student.class_name}</Text>\n                    <br />\n                    <Badge\n                      status={student.is_primary ? 'success' : 'default'}\n                      text={student.relationship}\n                    />\n                    <br />\n                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                      最近作业: {student.recent_stats?.recent_homework_count || 0} 份\n                    </Text>\n                  </div>\n                }\n              />\n\n              {/* 操作按钮 */}\n              <div style={{\n                marginTop: '16px',\n                display: 'flex',\n                gap: '8px',\n                justifyContent: 'center'\n              }}>\n                <Button\n                  type=\"primary\"\n                  size=\"small\"\n                  icon={<BookOutlined />}\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    handleViewHomework(student);\n                  }}\n                >\n                  作业详情\n                </Button>\n                <Button\n                  size=\"small\"\n                  icon={<BarChartOutlined />}\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    handleViewReport(student);\n                  }}\n                >\n                  学习报告\n                </Button>\n              </div>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n    );\n  };\n\n  // 渲染统计信息\n  const renderStatistics = () => {\n    if (!selectedStudent || !studentStats) {\n      return null;\n    }\n\n    const stats = studentStats.homework_stats;\n    \n    return (\n      <Card title=\"学习统计\" style={{ marginTop: 16 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={12} sm={6}>\n            <Statistic\n              title=\"总作业数\"\n              value={stats.total_homework}\n              prefix={<BookOutlined />}\n            />\n          </Col>\n          <Col xs={12} sm={6}>\n            <Statistic\n              title=\"已提交\"\n              value={stats.submitted_homework}\n              prefix={<CheckCircleOutlined />}\n            />\n          </Col>\n          <Col xs={12} sm={6}>\n            <Statistic\n              title=\"完成率\"\n              value={stats.completion_rate}\n              suffix=\"%\"\n              prefix={<TrophyOutlined />}\n            />\n          </Col>\n          <Col xs={12} sm={6}>\n            <Statistic\n              title=\"平均分\"\n              value={stats.avg_score}\n              precision={1}\n              prefix={<BarChartOutlined />}\n            />\n          </Col>\n        </Row>\n        \n        <Divider />\n        \n        <div>\n          <Text strong>完成进度</Text>\n          <Progress \n            percent={stats.completion_rate} \n            status={stats.completion_rate >= 80 ? 'success' : stats.completion_rate >= 60 ? 'normal' : 'exception'}\n            style={{ marginTop: 8 }}\n          />\n        </div>\n      </Card>\n    );\n  };\n\n  // 渲染最近作业\n  const renderRecentHomework = () => {\n    if (!selectedStudent) {\n      return null;\n    }\n\n    return (\n      <Card \n        title=\"最近作业\" \n        style={{ marginTop: 16 }}\n        extra={\n          <Button \n            type=\"link\" \n            icon={<EyeOutlined />}\n            onClick={() => {\n              // 这里可以跳转到作业详情页面\n              message.info('作业详情功能开发中');\n            }}\n          >\n            查看更多\n          </Button>\n        }\n      >\n        {studentHomework.length === 0 ? (\n          <Empty description=\"暂无作业记录\" image={Empty.PRESENTED_IMAGE_SIMPLE} />\n        ) : (\n          <List\n            dataSource={studentHomework.slice(0, 5)}\n            renderItem={(homework) => (\n              <List.Item>\n                <List.Item.Meta\n                  avatar={\n                    <Avatar \n                      icon={<BookOutlined />}\n                      style={{\n                        backgroundColor: homework.status === 'graded' ? '#52c41a' : \n                                        homework.status === 'submitted' ? '#1890ff' : '#faad14'\n                      }}\n                    />\n                  }\n                  title={\n                    <Space>\n                      <Text strong>{homework.title}</Text>\n                      <Badge \n                        status={\n                          homework.status === 'graded' ? 'success' : \n                          homework.status === 'submitted' ? 'processing' : 'warning'\n                        }\n                        text={\n                          homework.status === 'graded' ? '已批改' : \n                          homework.status === 'submitted' ? '已提交' : '未完成'\n                        }\n                      />\n                    </Space>\n                  }\n                  description={\n                    <div>\n                      <Text type=\"secondary\">{homework.subject_name}</Text>\n                      {homework.score && (\n                        <>\n                          <Divider type=\"vertical\" />\n                          <Text type=\"secondary\">得分: {homework.score}</Text>\n                        </>\n                      )}\n                      <br />\n                      <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                        <ClockCircleOutlined /> {homework.created_at ? new Date(homework.created_at).toLocaleDateString() : ''}\n                      </Text>\n                    </div>\n                  }\n                />\n              </List.Item>\n            )}\n          />\n        )}\n      </Card>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>\n          <Text>正在加载家长端数据...</Text>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ padding: '24px' }}>\n      {/* 欢迎信息 */}\n      <Card style={{ marginBottom: 16 }}>\n        <Title level={3}>\n          👨‍👩‍👧‍👦 欢迎回来，{user?.full_name || user?.username}！\n        </Title>\n        <Paragraph type=\"secondary\">\n          智慧云端家长端为您提供孩子的学习情况查看和分析服务。\n        </Paragraph>\n      </Card>\n\n      {/* 学生选择 */}\n      <Card title=\"我的孩子\" style={{ marginBottom: 16 }}>\n        {renderStudentCards()}\n      </Card>\n\n      {/* 选中学生的详细信息 */}\n      {selectedStudent && (\n        <>\n          {renderStatistics()}\n          {renderRecentHomework()}\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default ParentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,yBAAyB,EACzBC,gBAAgB,EAChBC,WAAW,QACN,mBAAmB;AAC1B,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAG3B,UAAU;AAE7C,MAAM4B,eAAe,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACpC;EACA,MAAMC,oBAAoB,GAAG;IAC3BC,MAAM,EAAE,mBAAmB;IAC3BC,SAAS,EAAE;EACb,CAAC;EACD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAMiD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,wBAAwB,CAAC;;MAExD;MACA,IAAID,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACE,OAAO,EAAE;QAChEV,gBAAgB,CAACQ,QAAQ,CAACG,IAAI,CAAC;QAC/B;QACA,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;UAC7CV,kBAAkB,CAACM,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC;MACF,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACN,QAAQ,CAAC,EAAE;QAClCR,gBAAgB,CAACQ,QAAQ,CAAC;QAC1B;QACA,IAAIA,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;UACvBV,kBAAkB,CAACM,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC;MACF,CAAC,MAAM;QACLO,OAAO,CAACC,GAAG,CAAC,WAAW,EAAER,QAAQ,CAAC;MACpC;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCpD,OAAO,CAACoD,KAAK,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAG,MAAOC,SAAS,IAAK;IAChD,IAAI,CAACA,SAAS,EAAE;IAEhB,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,mBAAmBU,SAAS,oBAAoB,CAAC;MAChF,IAAIX,QAAQ,CAACG,IAAI,CAACD,OAAO,EAAE;QACzBN,kBAAkB,CAACI,QAAQ,CAACG,IAAI,CAACA,IAAI,CAACS,SAAS,IAAI,EAAE,CAAC;MACxD;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCpD,OAAO,CAACoD,KAAK,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAG,MAAOF,SAAS,IAAK;IAC7C,IAAI,CAACA,SAAS,EAAE;IAEhB,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,mBAAmBU,SAAS,aAAa,CAAC;MACzE,IAAIX,QAAQ,CAACG,IAAI,CAACD,OAAO,EAAE;QACzBJ,eAAe,CAACE,QAAQ,CAACG,IAAI,CAACA,IAAI,CAAC;MACrC;IACF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;IACF;EACF,CAAC;EAED1D,SAAS,CAAC,MAAM;IACd,MAAM+D,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3BxB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMS,kBAAkB,CAAC,CAAC;MAC1BT,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDwB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN/D,SAAS,CAAC,MAAM;IACd,IAAI0C,eAAe,EAAE;MACnBiB,oBAAoB,CAACjB,eAAe,CAACsB,UAAU,CAAC;MAChDF,iBAAiB,CAACpB,eAAe,CAACsB,UAAU,CAAC;IAC/C;EACF,CAAC,EAAE,CAACtB,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMuB,kBAAkB,GAAG,MAAOC,OAAO,IAAK;IAC5C,IAAI;MACF;MACA,MAAMjB,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,mBAAmBgB,OAAO,CAACF,UAAU,WAAW,CAAC;;MAEhF;MACA,IAAIG,YAAY,GAAG,EAAE;MACrB,IAAIlB,QAAQ,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACgB,QAAQ,EAAE;QAC3ED,YAAY,GAAGlB,QAAQ,CAACG,IAAI,CAACgB,QAAQ;MACvC,CAAC,MAAM,IAAId,KAAK,CAACC,OAAO,CAACN,QAAQ,CAAC,EAAE;QAClCkB,YAAY,GAAGlB,QAAQ;MACzB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAACE,OAAO,IAAIG,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACG,IAAI,CAAC,EAAE;QACvEe,YAAY,GAAGlB,QAAQ,CAACG,IAAI;MAC9B;MAEA,IAAIe,YAAY,CAACd,MAAM,GAAG,CAAC,EAAE;QAC3B;QACA,MAAMgB,YAAY,GAAGF,YAAY,CAACG,GAAG,CAACC,EAAE,IAAI;AACpD;AACA,kBAAkBA,EAAE,CAACC,gBAAgB,IAAID,EAAE,CAACE,KAAK;AACjD,sCAAsCF,EAAE,CAACG,YAAY,IAAI,IAAI;AAC7D,sCAAsCH,EAAE,CAACI,UAAU,IAAI,IAAI;AAC3D,sCAAsCJ,EAAE,CAACK,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAGL,EAAE,CAACK,MAAM,KAAK,WAAW,GAAG,KAAK,GAAG,KAAK;AAChH,sCAAsCL,EAAE,CAACM,KAAK,IAAI,CAAC;AACnD,uCAAuCC,IAAI,CAACC,KAAK,CAAC,CAACR,EAAE,CAACS,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC;AAC3E,wCAAwC,IAAIC,IAAI,CAACV,EAAE,CAACW,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;AAChF,cAAcZ,EAAE,CAACa,gBAAgB,GAAG,6BAA6Bb,EAAE,CAACa,gBAAgB,MAAM,GAAG,EAAE;AAC/F;AACA,SAAS,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;QAEX,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;QAC3CF,SAAS,CAACG,QAAQ,CAACC,KAAK,CAAC;AACjC;AACA;AACA,uBAAuBxB,OAAO,CAACyB,YAAY;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBzB,OAAO,CAACyB,YAAY;AAC3C,uBAAuBxB,YAAY,CAACd,MAAM;AAC1C,gBAAgBgB,YAAY;AAC5B;AACA;AACA,SAAS,CAAC;MACJ,CAAC,MAAM;QACL/D,OAAO,CAACsF,IAAI,CAAC,QAAQ,CAAC;MACxB;IACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCpD,OAAO,CAACoD,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMmC,gBAAgB,GAAG,MAAO3B,OAAO,IAAK;IAC1C,IAAI;MACF;MACA,MAAMjB,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,mBAAmBgB,OAAO,CAACF,UAAU,aAAa,CAAC;MAElF,IAAIf,QAAQ,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QAAA,IAAA0C,aAAA,EAAAC,cAAA,EAAAC,cAAA;QACjD,MAAMC,KAAK,GAAGhD,QAAQ,CAACG,IAAI;QAC3B,MAAM8C,aAAa,GAAGD,KAAK,CAACE,cAAc,IAAI,CAAC,CAAC;;QAEhD;QACA,MAAMC,UAAU,GAAG;AAC3B;AACA;AACA;AACA,0CAA0C,EAAAN,aAAA,GAAAG,KAAK,CAACI,MAAM,cAAAP,aAAA,uBAAZA,aAAA,CAAcQ,IAAI,KAAI,EAAE;AAClE,0CAA0C,IAAIrB,IAAI,EAAAc,cAAA,GAACE,KAAK,CAACI,MAAM,cAAAN,cAAA,uBAAZA,cAAA,CAAcQ,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;AACjG,0CAA0C,IAAIvB,IAAI,EAAAe,cAAA,GAACC,KAAK,CAACI,MAAM,cAAAL,cAAA,uBAAZA,cAAA,CAAcS,QAAQ,CAAC,CAACD,kBAAkB,CAAC,CAAC;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2DN,aAAa,CAACQ,cAAc,IAAI,CAAC;AAC5F;AACA;AACA;AACA,2DAA2DR,aAAa,CAACS,eAAe,IAAI,CAAC;AAC7F;AACA;AACA;AACA,2DAA2D7B,IAAI,CAACC,KAAK,CAACmB,aAAa,CAACU,SAAS,IAAI,CAAC,CAAC;AACnG;AACA;AACA;AACA,2DAA2D9B,IAAI,CAACC,KAAK,CAAC,CAACmB,aAAa,CAACW,YAAY,IAAI,CAAC,IAAI,GAAG,CAAC;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C/B,IAAI,CAACC,KAAK,CAACmB,aAAa,CAACY,eAAe,IAAI,CAAC,CAAC;AACzF;AACA;AACA,oBAAoBZ,aAAa,CAACU,SAAS,IAAI,EAAE,GAC7B,iDAAiD,GACjDV,aAAa,CAACU,SAAS,IAAI,EAAE,GAC7B,mDAAmD,GACnD,iDAAiD;AACrE,oBACoBV,aAAa,CAACW,YAAY,IAAI,GAAG,GACjC,+CAA+C,GAC/C,gDAAgD;AACpE;AACA;AACA;AACA;AACA;AACA,SACS;QAED,MAAMvB,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;QAC3CF,SAAS,CAACG,QAAQ,CAACC,KAAK,CAAC;AACjC;AACA;AACA,uBAAuBxB,OAAO,CAACyB,YAAY;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBzB,OAAO,CAACyB,YAAY;AAC3C,gBAAgBS,UAAU;AAC1B;AACA;AACA,SAAS,CAAC;MACJ,CAAC,MAAM;QACL9F,OAAO,CAACoD,KAAK,CAAC,YAAY,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCpD,OAAO,CAACoD,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMqD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIvE,aAAa,CAACa,MAAM,KAAK,CAAC,EAAE;MAC9B,oBACE3B,OAAA,CAACzB,IAAI;QAAA+G,QAAA,eACHtF,OAAA,CAACd,KAAK;UACJqG,WAAW,EAAC,sCAAQ;UACpBC,KAAK,EAAEtG,KAAK,CAACuG,sBAAuB;UAAAH,QAAA,eAEpCtF,OAAA,CAACI,IAAI;YAACsF,IAAI,EAAC,WAAW;YAAAJ,QAAA,EAAC;UAEvB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEX;IAEA,oBACE9F,OAAA,CAACxB,GAAG;MAACuH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAT,QAAA,EACnBxE,aAAa,CAAC8B,GAAG,CAAEJ,OAAO;QAAA,IAAAwD,qBAAA;QAAA,oBACzBhG,OAAA,CAACvB,GAAG;UAACwH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eAChCtF,OAAA,CAACzB,IAAI;YACH8H,SAAS;YACTC,OAAO,EAAEA,CAAA,KAAMrF,kBAAkB,CAACuB,OAAO,CAAE;YAC3C+D,KAAK,EAAE,CAAAvF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsB,UAAU,MAAKE,OAAO,CAACF,UAAU,GAAG7B,oBAAoB,GAAG,CAAC,CAAE;YAAA6E,QAAA,gBAEtFtF,OAAA,CAACzB,IAAI,CAACiI,IAAI;cACRC,MAAM,eAAEzG,OAAA,CAACnB,MAAM;gBAAC6H,IAAI,EAAE,EAAG;gBAACC,IAAI,eAAE3G,OAAA,CAACV,YAAY;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrD/C,KAAK,EAAEP,OAAO,CAACyB,YAAa;cAC5BsB,WAAW,eACTvF,OAAA;gBAAAsF,QAAA,gBACEtF,OAAA,CAACI,IAAI;kBAACsF,IAAI,EAAC,WAAW;kBAAAJ,QAAA,EAAE9C,OAAO,CAACS;gBAAU;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClD9F,OAAA;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN9F,OAAA,CAACjB,KAAK;kBACJmE,MAAM,EAAEV,OAAO,CAACoE,UAAU,GAAG,SAAS,GAAG,SAAU;kBACnDC,IAAI,EAAErE,OAAO,CAACsE;gBAAa;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACF9F,OAAA;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN9F,OAAA,CAACI,IAAI;kBAACsF,IAAI,EAAC,WAAW;kBAACa,KAAK,EAAE;oBAAEQ,QAAQ,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,GAAC,4BAC5C,EAAC,EAAAU,qBAAA,GAAAxD,OAAO,CAACwE,YAAY,cAAAhB,qBAAA,uBAApBA,qBAAA,CAAsBiB,qBAAqB,KAAI,CAAC,EAAC,SAC1D;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF9F,OAAA;cAAKuG,KAAK,EAAE;gBACVW,SAAS,EAAE,MAAM;gBACjBC,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,KAAK;gBACVC,cAAc,EAAE;cAClB,CAAE;cAAA/B,QAAA,gBACAtF,OAAA,CAACb,MAAM;gBACLuG,IAAI,EAAC,SAAS;gBACdgB,IAAI,EAAC,OAAO;gBACZC,IAAI,eAAE3G,OAAA,CAACT,YAAY;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBQ,OAAO,EAAGgB,CAAC,IAAK;kBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;kBACnBhF,kBAAkB,CAACC,OAAO,CAAC;gBAC7B,CAAE;gBAAA8C,QAAA,EACH;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9F,OAAA,CAACb,MAAM;gBACLuH,IAAI,EAAC,OAAO;gBACZC,IAAI,eAAE3G,OAAA,CAACJ,gBAAgB;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3BQ,OAAO,EAAGgB,CAAC,IAAK;kBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;kBACnBpD,gBAAgB,CAAC3B,OAAO,CAAC;gBAC3B,CAAE;gBAAA8C,QAAA,EACH;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,GAtD+BtD,OAAO,CAACF,UAAU;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuDrD,CAAC;MAAA,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAM0B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACxG,eAAe,IAAI,CAACI,YAAY,EAAE;MACrC,OAAO,IAAI;IACb;IAEA,MAAMmD,KAAK,GAAGnD,YAAY,CAACqD,cAAc;IAEzC,oBACEzE,OAAA,CAACzB,IAAI;MAACwE,KAAK,EAAC,0BAAM;MAACwD,KAAK,EAAE;QAAEW,SAAS,EAAE;MAAG,CAAE;MAAA5B,QAAA,gBAC1CtF,OAAA,CAACxB,GAAG;QAACuH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAT,QAAA,gBACpBtF,OAAA,CAACvB,GAAG;UAACwH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAZ,QAAA,eACjBtF,OAAA,CAAChB,SAAS;YACR+D,KAAK,EAAC,0BAAM;YACZ0E,KAAK,EAAElD,KAAK,CAACS,cAAe;YAC5B0C,MAAM,eAAE1H,OAAA,CAACT,YAAY;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9F,OAAA,CAACvB,GAAG;UAACwH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAZ,QAAA,eACjBtF,OAAA,CAAChB,SAAS;YACR+D,KAAK,EAAC,oBAAK;YACX0E,KAAK,EAAElD,KAAK,CAACoD,kBAAmB;YAChCD,MAAM,eAAE1H,OAAA,CAACN,mBAAmB;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9F,OAAA,CAACvB,GAAG;UAACwH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAZ,QAAA,eACjBtF,OAAA,CAAChB,SAAS;YACR+D,KAAK,EAAC,oBAAK;YACX0E,KAAK,EAAElD,KAAK,CAACa,eAAgB;YAC7BwC,MAAM,EAAC,GAAG;YACVF,MAAM,eAAE1H,OAAA,CAACR,cAAc;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9F,OAAA,CAACvB,GAAG;UAACwH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAZ,QAAA,eACjBtF,OAAA,CAAChB,SAAS;YACR+D,KAAK,EAAC,oBAAK;YACX0E,KAAK,EAAElD,KAAK,CAACW,SAAU;YACvB2C,SAAS,EAAE,CAAE;YACbH,MAAM,eAAE1H,OAAA,CAACJ,gBAAgB;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9F,OAAA,CAACX,OAAO;QAAAsG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEX9F,OAAA;QAAAsF,QAAA,gBACEtF,OAAA,CAACI,IAAI;UAAC0H,MAAM;UAAAxC,QAAA,EAAC;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxB9F,OAAA,CAACf,QAAQ;UACP8I,OAAO,EAAExD,KAAK,CAACa,eAAgB;UAC/BlC,MAAM,EAAEqB,KAAK,CAACa,eAAe,IAAI,EAAE,GAAG,SAAS,GAAGb,KAAK,CAACa,eAAe,IAAI,EAAE,GAAG,QAAQ,GAAG,WAAY;UACvGmB,KAAK,EAAE;YAAEW,SAAS,EAAE;UAAE;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX,CAAC;;EAED;EACA,MAAMkC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAChH,eAAe,EAAE;MACpB,OAAO,IAAI;IACb;IAEA,oBACEhB,OAAA,CAACzB,IAAI;MACHwE,KAAK,EAAC,0BAAM;MACZwD,KAAK,EAAE;QAAEW,SAAS,EAAE;MAAG,CAAE;MACzBe,KAAK,eACHjI,OAAA,CAACb,MAAM;QACLuG,IAAI,EAAC,MAAM;QACXiB,IAAI,eAAE3G,OAAA,CAACH,WAAW;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBQ,OAAO,EAAEA,CAAA,KAAM;UACb;UACA1H,OAAO,CAACsF,IAAI,CAAC,WAAW,CAAC;QAC3B,CAAE;QAAAoB,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;MAAAR,QAAA,EAEApE,eAAe,CAACS,MAAM,KAAK,CAAC,gBAC3B3B,OAAA,CAACd,KAAK;QAACqG,WAAW,EAAC,sCAAQ;QAACC,KAAK,EAAEtG,KAAK,CAACuG;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEnE9F,OAAA,CAAClB,IAAI;QACHoJ,UAAU,EAAEhH,eAAe,CAACiH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE;QACxCC,UAAU,EAAG1F,QAAQ,iBACnB1C,OAAA,CAAClB,IAAI,CAACuJ,IAAI;UAAA/C,QAAA,eACRtF,OAAA,CAAClB,IAAI,CAACuJ,IAAI,CAAC7B,IAAI;YACbC,MAAM,eACJzG,OAAA,CAACnB,MAAM;cACL8H,IAAI,eAAE3G,OAAA,CAACT,YAAY;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBS,KAAK,EAAE;gBACL+B,eAAe,EAAE5F,QAAQ,CAACQ,MAAM,KAAK,QAAQ,GAAG,SAAS,GACzCR,QAAQ,CAACQ,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG;cAChE;YAAE;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACF;YACD/C,KAAK,eACH/C,OAAA,CAACZ,KAAK;cAAAkG,QAAA,gBACJtF,OAAA,CAACI,IAAI;gBAAC0H,MAAM;gBAAAxC,QAAA,EAAE5C,QAAQ,CAACK;cAAK;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpC9F,OAAA,CAACjB,KAAK;gBACJmE,MAAM,EACJR,QAAQ,CAACQ,MAAM,KAAK,QAAQ,GAAG,SAAS,GACxCR,QAAQ,CAACQ,MAAM,KAAK,WAAW,GAAG,YAAY,GAAG,SAClD;gBACD2D,IAAI,EACFnE,QAAQ,CAACQ,MAAM,KAAK,QAAQ,GAAG,KAAK,GACpCR,QAAQ,CAACQ,MAAM,KAAK,WAAW,GAAG,KAAK,GAAG;cAC3C;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CACR;YACDP,WAAW,eACTvF,OAAA;cAAAsF,QAAA,gBACEtF,OAAA,CAACI,IAAI;gBAACsF,IAAI,EAAC,WAAW;gBAAAJ,QAAA,EAAE5C,QAAQ,CAACM;cAAY;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACpDpD,QAAQ,CAACS,KAAK,iBACbnD,OAAA,CAAAE,SAAA;gBAAAoF,QAAA,gBACEtF,OAAA,CAACX,OAAO;kBAACqG,IAAI,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3B9F,OAAA,CAACI,IAAI;kBAACsF,IAAI,EAAC,WAAW;kBAAAJ,QAAA,GAAC,gBAAI,EAAC5C,QAAQ,CAACS,KAAK;gBAAA;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,eAClD,CACH,eACD9F,OAAA;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN9F,OAAA,CAACI,IAAI;gBAACsF,IAAI,EAAC,WAAW;gBAACa,KAAK,EAAE;kBAAEQ,QAAQ,EAAE;gBAAO,CAAE;gBAAAzB,QAAA,gBACjDtF,OAAA,CAACP,mBAAmB;kBAAAkG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACpD,QAAQ,CAACc,UAAU,GAAG,IAAID,IAAI,CAACb,QAAQ,CAACc,UAAU,CAAC,CAACsB,kBAAkB,CAAC,CAAC,GAAG,EAAE;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEX,CAAC;EAED,IAAIlF,OAAO,EAAE;IACX,oBACEZ,OAAA;MAAKuG,KAAK,EAAE;QAAEgC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAlD,QAAA,gBACnDtF,OAAA,CAACrB,IAAI;QAAC+H,IAAI,EAAC;MAAO;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB9F,OAAA;QAAKuG,KAAK,EAAE;UAAEW,SAAS,EAAE;QAAG,CAAE;QAAA5B,QAAA,eAC5BtF,OAAA,CAACI,IAAI;UAAAkF,QAAA,EAAC;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9F,OAAA;IAAKuG,KAAK,EAAE;MAAEiC,OAAO,EAAE;IAAO,CAAE;IAAAlD,QAAA,gBAE9BtF,OAAA,CAACzB,IAAI;MAACgI,KAAK,EAAE;QAAEkC,YAAY,EAAE;MAAG,CAAE;MAAAnD,QAAA,gBAChCtF,OAAA,CAACG,KAAK;QAACuI,KAAK,EAAE,CAAE;QAAApD,QAAA,GAAC,mGACE,EAAC,CAAA/E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoI,SAAS,MAAIpI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqI,QAAQ,GAAC,QACtD;MAAA;QAAAjD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR9F,OAAA,CAACK,SAAS;QAACqF,IAAI,EAAC,WAAW;QAAAJ,QAAA,EAAC;MAE5B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGP9F,OAAA,CAACzB,IAAI;MAACwE,KAAK,EAAC,0BAAM;MAACwD,KAAK,EAAE;QAAEkC,YAAY,EAAE;MAAG,CAAE;MAAAnD,QAAA,EAC5CD,kBAAkB,CAAC;IAAC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,EAGN9E,eAAe,iBACdhB,OAAA,CAAAE,SAAA;MAAAoF,QAAA,GACGkC,gBAAgB,CAAC,CAAC,EAClBQ,oBAAoB,CAAC,CAAC;IAAA,eACvB,CACH;EAAA;IAAArC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtF,EAAA,CAlfIF,eAAe;AAAAuI,EAAA,GAAfvI,eAAe;AAofrB,eAAeA,eAAe;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}