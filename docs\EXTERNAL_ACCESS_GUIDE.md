# 🌐 外网访问配置指南

## 📋 问题描述

当通过外网IP访问智教云端平台时，可能会遇到以下问题：
- 学生登录后显示"无效凭证"错误
- AI助手聊天显示认证失败
- 拍照解题功能无法使用
- 所有需要认证的API都返回401错误

## 🔍 问题原因

1. **API地址配置问题**：前端配置的API地址使用localhost或127.0.0.1，外网无法访问
2. **代理配置问题**：开发环境的proxy配置不适用于外网访问
3. **CORS跨域问题**：后端CORS配置可能不支持外网域名

## 🛠️ 解决方案

### 方案一：修改环境变量（推荐）

1. **修改前端环境变量**
   ```bash
   # 编辑 frontend/.env 文件
   REACT_APP_API_URL=http://YOUR_EXTERNAL_IP:8083/api
   ```
   
   将 `YOUR_EXTERNAL_IP` 替换为您的实际外网IP地址，例如：
   ```bash
   REACT_APP_API_URL=http://*************:8083/api
   ```

2. **重启前端服务**
   ```bash
   cd frontend
   npm start
   ```

### 方案二：使用动态配置（已实现）

系统已经实现了动态API地址配置：
- 本地访问时自动使用 `localhost:8083`
- 外网访问时自动使用当前域名的8083端口

### 方案三：Nginx反向代理（生产环境推荐）

1. **安装Nginx**
   ```bash
   # Ubuntu/Debian
   sudo apt install nginx
   
   # CentOS/RHEL
   sudo yum install nginx
   ```

2. **配置Nginx**
   ```nginx
   # /etc/nginx/sites-available/smartedu
   server {
       listen 80;
       server_name your-domain.com;  # 替换为您的域名或IP
       
       # 前端静态文件
       location / {
           proxy_pass http://localhost:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
       
       # 后端API
       location /api/ {
           proxy_pass http://localhost:8083;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           
           # 文件上传配置
           client_max_body_size 10M;
           proxy_read_timeout 60s;
           proxy_connect_timeout 60s;
       }
   }
   ```

3. **启用配置**
   ```bash
   sudo ln -s /etc/nginx/sites-available/smartedu /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl reload nginx
   ```

## 🔧 后端配置检查

### 1. 确保后端监听所有接口

检查后端启动配置：
```python
# backend/main.py
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8083)  # 确保是0.0.0.0而不是127.0.0.1
```

### 2. 检查防火墙设置

```bash
# Ubuntu/Debian
sudo ufw allow 8083
sudo ufw allow 3000

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8083/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

### 3. 检查CORS配置

后端已配置允许所有来源：
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH", "HEAD"],
    allow_headers=["*"],
)
```

## 🐛 调试步骤

### 1. 检查网络连通性

```bash
# 测试后端API是否可访问
curl http://YOUR_EXTERNAL_IP:8083/api/health

# 测试前端是否可访问
curl http://YOUR_EXTERNAL_IP:3000
```

### 2. 检查浏览器控制台

打开浏览器开发者工具，查看：
- Network标签页中的API请求
- Console标签页中的错误信息
- 注意API请求的完整URL是否正确

### 3. 检查后端日志

查看后端控制台输出，确认：
- 是否收到API请求
- 认证token是否正确
- 是否有CORS错误

## 📱 移动端访问

### 微信浏览器访问

1. **确保使用HTTPS**（微信要求）
   ```nginx
   server {
       listen 443 ssl;
       server_name your-domain.com;
       
       ssl_certificate /path/to/certificate.crt;
       ssl_certificate_key /path/to/private.key;
       
       # 其他配置...
   }
   ```

2. **配置域名解析**
   - 将域名解析到您的服务器IP
   - 更新环境变量使用域名而不是IP

### 手机浏览器访问

确保手机和服务器在同一网络，或者服务器有公网IP。

## ⚡ 快速解决方案

如果您需要立即解决外网访问问题：

1. **获取服务器外网IP**
   ```bash
   curl ifconfig.me
   ```

2. **修改前端配置**
   ```bash
   # 编辑 frontend/.env
   REACT_APP_API_URL=http://YOUR_IP:8083/api
   ```

3. **重启服务**
   ```bash
   # 重启前端
   cd frontend && npm start
   
   # 确保后端正在运行
   cd backend && python main.py
   ```

4. **测试访问**
   - 浏览器访问：`http://YOUR_IP:3000`
   - 使用学生账号登录测试

## 📞 技术支持

如果仍然遇到问题，请检查：
- [ ] 服务器防火墙设置
- [ ] 网络运营商是否屏蔽端口
- [ ] 域名解析是否正确
- [ ] SSL证书是否有效（HTTPS访问）

---

*最后更新时间：2025年8月1日*
