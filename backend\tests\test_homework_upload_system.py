#!/usr/bin/env python3
"""
作业上传系统综合测试
测试新的文件管理器、命名规范、权限导出等功能
"""

import pytest
import os
import tempfile
import shutil
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch
import sqlite3

# 导入要测试的模块
from app.services.file_manager import FileManager, HomeworkConfig
from app.services.export_service import ExportService
from app.models.file_metadata import FileMetadata, FileType

class TestFileManager:
    """测试文件管理器"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.file_manager = FileManager(base_upload_dir=self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_directory_creation(self):
        """测试目录创建"""
        # 测试基础目录创建
        assert os.path.exists(os.path.join(self.temp_dir, "schools"))
        assert os.path.exists(os.path.join(self.temp_dir, "system/super_admin_exports"))
        assert os.path.exists(os.path.join(self.temp_dir, "temp"))
        
        # 测试学校目录创建
        self.file_manager.ensure_school_directories(1)
        school_dir = os.path.join(self.temp_dir, "schools/school_001")
        assert os.path.exists(school_dir)
        assert os.path.exists(os.path.join(school_dir, "exports/teacher_exports"))
        assert os.path.exists(os.path.join(school_dir, "exports/admin_exports"))
        
        # 测试年级班级目录创建
        self.file_manager.ensure_grade_class_directories(1, "701")
        grade_class_dir = os.path.join(school_dir, "grade_701")
        assert os.path.exists(grade_class_dir)
        assert os.path.exists(os.path.join(grade_class_dir, "subjects/math/assignments"))
        assert os.path.exists(os.path.join(grade_class_dir, "subjects/english/assignments"))
    
    def test_grade_class_code_generation(self):
        """测试年级班级代码生成"""
        # 测试正常情况
        assert self.file_manager.get_grade_class_code("7年级", "1班") == "701"
        assert self.file_manager.get_grade_class_code("8年级", "2班") == "802"
        assert self.file_manager.get_grade_class_code("9年级", "10班") == "910"
        
        # 测试边界情况
        assert self.file_manager.get_grade_class_code("七年级", "一班") == "七年级_一班"
    
    def test_homework_filename_generation(self):
        """测试作业文件名生成"""
        filename = self.file_manager.generate_homework_filename(
            user_id=12345,
            homework_id=67890,
            page_number=1,
            file_extension=".jpg"
        )
        
        # 检查文件名格式
        assert filename.startswith("user_12345_")
        assert "_hw67890_p1.jpg" in filename
        assert len(filename.split("_")) >= 5  # user_id_timestamp_hw_id_page
    
    def test_homework_file_path_generation(self):
        """测试作业文件路径生成"""
        file_info = self.file_manager.generate_homework_file_path(
            school_id=1,
            grade_class_code="701",
            subject="math",
            assignment_id=123,
            assignment_name="期中考试",
            user_id=12345,
            homework_id=67890,
            page_number=1,
            file_extension=".jpg",
            is_annotated=False
        )
        
        # 检查路径结构（使用os.path.normpath处理路径分隔符）
        normalized_path = os.path.normpath(file_info["relative_path"]).replace("\\", "/")
        assert "schools/school_001/grade_701/subjects/math/assignments" in normalized_path
        assert "assignment_123_期中考试" in normalized_path
        assert "original" in normalized_path
        assert file_info["filename"].startswith("user_12345_")
        assert file_info["url_path"].startswith("/uploads/")
    
    def test_annotated_filename_generation(self):
        """测试批注文件名生成"""
        original_filename = "user_12345_20241204_120000_hw67890_p1.jpg"
        annotated_filename = self.file_manager.generate_annotated_filename(original_filename)
        
        assert annotated_filename == "user_12345_20241204_120000_hw67890_p1_annotated.jpg"
    
    def test_file_validation(self):
        """测试文件验证"""
        # 测试有效文件
        is_valid, message = self.file_manager.validate_homework_file(
            file_size=5 * 1024 * 1024,  # 5MB
            file_extension=".jpg"
        )
        assert is_valid
        assert message == ""
        
        # 测试无效格式
        is_valid, message = self.file_manager.validate_homework_file(
            file_size=5 * 1024 * 1024,
            file_extension=".pdf"
        )
        assert not is_valid
        assert "不支持的文件格式" in message
        
        # 测试文件过大
        is_valid, message = self.file_manager.validate_homework_file(
            file_size=15 * 1024 * 1024,  # 15MB
            file_extension=".jpg"
        )
        assert not is_valid
        assert "文件大小超过限制" in message
    
    def test_page_labels(self):
        """测试页面标签"""
        math_labels = self.file_manager.get_page_labels("math")
        assert math_labels == ['第1页', '第2页', '第3页', '第4页']
        
        english_labels = self.file_manager.get_page_labels("english")
        assert english_labels == ['听力部分', '阅读部分', '写作部分', '第4页']
        
        physics_labels = self.file_manager.get_page_labels("physics")
        assert physics_labels == ['实验步骤', '实验数据', '实验结果', '实验总结']
        
        # 测试未知科目
        unknown_labels = self.file_manager.get_page_labels("unknown")
        assert unknown_labels == ['第1页', '第2页', '第3页', '第4页']
    
    def test_export_filename_generation(self):
        """测试导出文件名生成"""
        filename = self.file_manager.generate_export_filename(
            export_type="教师导出",
            school_name="测试学校",
            grade_class="701",
            subject="数学",
            assignment_name="期中考试"
        )
        
        assert filename.startswith("教师导出_测试学校_701_数学_期中考试_")
        assert filename.endswith(".zip")
    
    def test_file_operations(self):
        """测试文件操作"""
        # 创建测试文件
        test_content = b"test image content"
        file_info = self.file_manager.generate_homework_file_path(
            school_id=1,
            grade_class_code="701",
            subject="math",
            assignment_id=123,
            assignment_name="测试作业",
            user_id=12345,
            homework_id=67890,
            page_number=1
        )
        
        # 测试保存文件
        success = self.file_manager.save_file(test_content, file_info)
        assert success
        assert os.path.exists(file_info["full_path"])
        
        # 测试文件信息获取
        file_info_result = self.file_manager.get_file_info(file_info["full_path"])
        assert file_info_result is not None
        assert file_info_result["size"] == len(test_content)
        assert file_info_result["exists"] is True
        
        # 测试文件删除
        success = self.file_manager.delete_file(file_info["full_path"])
        assert success
        assert not os.path.exists(file_info["full_path"])

class TestHomeworkConfig:
    """测试作业配置"""
    
    def test_config_values(self):
        """测试配置值"""
        assert HomeworkConfig.MAX_PAGES_PER_HOMEWORK == 4
        assert HomeworkConfig.MIN_PAGES_PER_HOMEWORK == 1
        assert HomeworkConfig.MAX_FILE_SIZE_MB == 10
        assert HomeworkConfig.MAX_TOTAL_SIZE_MB == 40
        assert '.jpg' in HomeworkConfig.SUPPORTED_IMAGE_FORMATS
        assert '.png' in HomeworkConfig.SUPPORTED_IMAGE_FORMATS

class TestFileMetadata:
    """测试文件元数据模型"""
    
    def test_file_metadata_creation(self):
        """测试文件元数据创建"""
        metadata = FileMetadata(
            file_type=FileType.ORIGINAL,
            file_path="schools/school_001/grade_701/subjects/math/assignments/assignment_123_测试/original/user_12345_20241204_120000_hw67890_p1.jpg",
            url_path="/uploads/schools/school_001/grade_701/subjects/math/assignments/assignment_123_测试/original/user_12345_20241204_120000_hw67890_p1.jpg",
            stored_filename="user_12345_20241204_120000_hw67890_p1.jpg",
            original_filename="homework_page1.jpg",
            file_size=1024000,
            mime_type="image/jpeg",
            school_id=1,
            grade_class_code="701",
            subject_id=1,
            assignment_id=123,
            student_id=12345,
            homework_id=67890,
            page_number=1,
            total_pages=3
        )
        
        # 测试字典转换
        data = metadata.to_dict()
        assert data["file_type"] == "original"
        assert data["page_number"] == 1
        assert data["grade_class_code"] == "701"
        
        # 测试页面标签获取
        page_label = metadata.get_page_label("math")
        assert page_label == "第1页"
        
        page_label = metadata.get_page_label("english")
        assert page_label == "听力部分"

class TestIntegration:
    """集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.file_manager = FileManager(base_upload_dir=self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_complete_homework_upload_workflow(self):
        """测试完整的作业上传工作流"""
        # 1. 创建学校和班级目录
        school_id = 1
        grade_class_code = "701"
        subject = "math"
        assignment_id = 123
        assignment_name = "期中考试"
        user_id = 12345
        homework_id = 67890
        
        self.file_manager.ensure_school_directories(school_id)
        self.file_manager.ensure_grade_class_directories(school_id, grade_class_code)
        
        # 2. 生成多页作业文件路径
        pages = []
        for page_num in range(1, 5):  # 4页作业
            file_info = self.file_manager.generate_homework_file_path(
                school_id=school_id,
                grade_class_code=grade_class_code,
                subject=subject,
                assignment_id=assignment_id,
                assignment_name=assignment_name,
                user_id=user_id,
                homework_id=homework_id,
                page_number=page_num
            )
            pages.append(file_info)
        
        # 3. 保存作业文件
        for i, page_info in enumerate(pages):
            test_content = f"page {i+1} content".encode()
            success = self.file_manager.save_file(test_content, page_info)
            assert success
            assert os.path.exists(page_info["full_path"])
        
        # 4. 生成批注文件
        for i, page_info in enumerate(pages):
            annotated_info = self.file_manager.generate_homework_file_path(
                school_id=school_id,
                grade_class_code=grade_class_code,
                subject=subject,
                assignment_id=assignment_id,
                assignment_name=assignment_name,
                user_id=user_id,
                homework_id=homework_id,
                page_number=i+1,
                is_annotated=True
            )
            
            annotated_content = f"annotated page {i+1} content".encode()
            success = self.file_manager.save_file(annotated_content, annotated_info)
            assert success
            assert os.path.exists(annotated_info["full_path"])
        
        # 5. 验证文件结构
        assignment_dir = self.file_manager.ensure_assignment_directories(
            school_id, grade_class_code, subject, assignment_id, assignment_name
        )
        
        original_files = self.file_manager.get_homework_files_by_assignment(
            school_id, grade_class_code, subject, assignment_id, assignment_name, "original"
        )
        annotated_files = self.file_manager.get_homework_files_by_assignment(
            school_id, grade_class_code, subject, assignment_id, assignment_name, "annotated"
        )
        
        assert len(original_files) == 4
        assert len(annotated_files) == 4

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
