#!/usr/bin/env python3
"""
数据库迁移脚本 - 创建文件元数据表
支持新的作业图片上传命名规范
"""

import sqlite3
import os
import sys
from datetime import datetime

def create_file_metadata_tables(db_path="backend/homework_system.db"):
    """创建文件元数据相关表"""
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 开始创建文件元数据表...")
        
        # 1. 创建file_metadata表
        create_file_metadata_sql = """
        CREATE TABLE IF NOT EXISTS file_metadata (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            
            -- 文件基本信息
            file_type VARCHAR(20) NOT NULL CHECK (file_type IN ('original', 'annotated', 'export', 'temp')),
            file_path VARCHAR(500) NOT NULL,
            url_path VARCHAR(500) NOT NULL,
            stored_filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255),
            
            -- 文件属性
            file_size INTEGER,
            mime_type VARCHAR(100),
            
            -- 层级结构信息
            school_id INTEGER NOT NULL,
            grade_class_code VARCHAR(10) NOT NULL,
            subject_id INTEGER,
            assignment_id INTEGER,
            
            -- 学生和作业信息
            student_id INTEGER NOT NULL,
            homework_id INTEGER,
            
            -- 页面信息
            page_number INTEGER NOT NULL CHECK (page_number >= 1 AND page_number <= 4),
            total_pages INTEGER,
            sequence_in_homework INTEGER,
            
            -- 时间信息
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            
            -- 状态信息
            is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
            
            -- 外键约束
            FOREIGN KEY (school_id) REFERENCES schools(id),
            FOREIGN KEY (subject_id) REFERENCES subjects(id),
            FOREIGN KEY (assignment_id) REFERENCES homework_assignments(id),
            FOREIGN KEY (student_id) REFERENCES users(id),
            FOREIGN KEY (homework_id) REFERENCES homeworks(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        );
        """
        
        cursor.execute(create_file_metadata_sql)
        print("✅ file_metadata表创建成功")
        
        # 2. 创建索引
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_school_grade_class ON file_metadata(school_id, grade_class_code);",
            "CREATE INDEX IF NOT EXISTS idx_assignment_student ON file_metadata(assignment_id, student_id);",
            "CREATE INDEX IF NOT EXISTS idx_homework_page ON file_metadata(homework_id, page_number);",
            "CREATE INDEX IF NOT EXISTS idx_file_type_created ON file_metadata(file_type, created_at);",
            "CREATE INDEX IF NOT EXISTS idx_student_homework ON file_metadata(student_id, homework_id);",
            "CREATE INDEX IF NOT EXISTS idx_active_files ON file_metadata(is_active, file_type);"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("✅ 文件元数据表索引创建成功")
        
        # 3. 创建file_access_logs表
        create_access_log_sql = """
        CREATE TABLE IF NOT EXISTS file_access_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            access_type VARCHAR(50),
            ip_address VARCHAR(45),
            user_agent VARCHAR(500),
            
            FOREIGN KEY (file_id) REFERENCES file_metadata(id),
            FOREIGN KEY (user_id) REFERENCES users(id)
        );
        """
        
        cursor.execute(create_access_log_sql)
        print("✅ file_access_logs表创建成功")
        
        # 4. 创建访问日志索引
        access_log_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_file_access ON file_access_logs(file_id, access_time);",
            "CREATE INDEX IF NOT EXISTS idx_user_access ON file_access_logs(user_id, access_time);"
        ]
        
        for index_sql in access_log_indexes:
            cursor.execute(index_sql)
        
        print("✅ 访问日志表索引创建成功")
        
        # 5. 提交事务
        conn.commit()
        
        # 6. 验证表创建
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('file_metadata', 'file_access_logs');")
        tables = cursor.fetchall()
        
        if len(tables) == 2:
            print("✅ 所有表创建验证成功")
            
            # 显示表结构
            print("\n📋 file_metadata表结构:")
            cursor.execute("PRAGMA table_info(file_metadata);")
            columns = cursor.fetchall()
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            return True
        else:
            print("❌ 表创建验证失败")
            return False
            
    except sqlite3.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

def check_existing_tables(db_path="backend/homework_system.db"):
    """检查现有表结构"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 检查现有表结构...")
        
        # 检查是否已存在file_metadata表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='file_metadata';")
        if cursor.fetchone():
            print("⚠️  file_metadata表已存在")
            
            # 显示现有表结构
            cursor.execute("PRAGMA table_info(file_metadata);")
            columns = cursor.fetchall()
            print("   现有表结构:")
            for col in columns:
                print(f"     {col[1]} ({col[2]})")
            return True
        else:
            print("ℹ️  file_metadata表不存在，可以创建")
            return False
            
    except sqlite3.Error as e:
        print(f"❌ 检查表结构失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """主函数"""
    print("🚀 开始文件元数据表迁移...")
    print(f"⏰ 迁移时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查现有表
    if check_existing_tables():
        response = input("表已存在，是否继续创建？(y/N): ")
        if response.lower() != 'y':
            print("❌ 迁移已取消")
            return
    
    # 创建表
    if create_file_metadata_tables():
        print("✅ 文件元数据表迁移完成！")
        print("\n📝 迁移说明:")
        print("   - 创建了file_metadata表，支持新的文件命名规范")
        print("   - 创建了file_access_logs表，用于记录文件访问日志")
        print("   - 添加了必要的索引以优化查询性能")
        print("   - 支持1-4页作业上传和科目特定的页面标签")
    else:
        print("❌ 文件元数据表迁移失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
