# 📸 拍照解题 API 文档

## 📋 接口概述

拍照解题API为学生用户提供图片上传和AI智能解题服务。通过上传题目图片，系统会调用火山引擎AI模型进行题目识别和解答。

## 🔗 接口信息

### 基本信息
- **接口路径**：`POST /api/students/photo-solve`
- **请求方式**：POST
- **内容类型**：multipart/form-data
- **权限要求**：学生用户登录

### 🔐 权限验证
- 需要有效的JWT Token
- 仅限学生角色用户访问
- 非学生用户访问将返回403错误

## 📤 请求参数

### 请求头
```http
Authorization: Bearer <JWT_TOKEN>
Content-Type: multipart/form-data
```

### 请求体参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image | File | 是 | 题目图片文件 |

### 文件要求
- **支持格式**：JPG、PNG、GIF等常见图片格式
- **文件大小**：建议不超过10MB
- **图片质量**：建议分辨率不低于720p
- **内容要求**：图片应包含清晰的题目内容

## 📥 响应格式

### 成功响应 (200)
```json
{
  "question": "题目内容",
  "answer": "最终答案",
  "steps": [
    "解题步骤1",
    "解题步骤2",
    "解题步骤3"
  ],
  "knowledge_points": [
    "相关知识点1",
    "相关知识点2"
  ],
  "difficulty": "简单/中等/困难",
  "subject": "数学/语文/英语/物理/化学/生物/历史/地理/政治",
  "user_id": 123,
  "username": "student001"
}
```

### 错误响应

#### 权限错误 (403)
```json
{
  "detail": "只有学生用户可以使用拍照解题功能"
}
```

#### 配置错误 (404)
```json
{
  "detail": "AI服务配置不可用"
}
```

#### 图片处理错误 (500)
```json
{
  "detail": "图片处理失败: 具体错误信息"
}
```

#### AI服务错误 (500)
```json
{
  "detail": "AI解题失败: 具体错误信息"
}
```

## 🔧 技术实现

### 图片处理流程
1. **接收上传**：接收multipart/form-data格式的图片文件
2. **格式转换**：使用PIL库转换为RGB模式
3. **尺寸优化**：调整为最佳分辨率（最大1024x1024）
4. **格式统一**：转换为JPEG格式，85%质量压缩
5. **Base64编码**：转换为Base64格式用于API调用

### AI解题流程
1. **配置获取**：从数据库获取火山引擎AI配置
2. **API调用**：调用火山引擎GPT-4V视觉模型
3. **结果解析**：解析AI返回的JSON格式结果
4. **数据补充**：添加用户信息到响应结果

### 提示词模板
```text
你是一位专业的数学和学科解题助手，具有强大的OCR图像识别能力。请完成以下任务:

1. 识别图片中的题目内容
2. 提供详细的解题步骤和答案
3. 给出相关知识点解释

**重要**: 你必须以下面的JSON格式返回结果，不要添加任何额外文字、标记或注释:
```json
{
  "question": "题目内容",
  "answer": "最终答案",
  "steps": [
    "解题步骤1",
    "解题步骤2",
    "解题步骤3"
  ],
  "knowledge_points": [
    "相关知识点1",
    "相关知识点2"
  ],
  "difficulty": "简单/中等/困难",
  "subject": "数学/语文/英语/物理/化学/生物/历史/地理/政治"
}
```

请严格按照上述JSON格式输出，确保:
1. 所有字符串使用双引号而不是单引号
2. 不要在JSON前后添加任何额外文字
3. 确保输出是有效的JSON格式
```

## 📊 使用示例

### cURL 示例
```bash
curl -X POST "http://localhost:8083/api/students/photo-solve" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "image=@/path/to/question.jpg"
```

### JavaScript 示例
```javascript
const formData = new FormData();
formData.append('image', imageFile);

const response = await fetch('/api/students/photo-solve', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

const result = await response.json();
console.log(result);
```

### Python 示例
```python
import requests

url = "http://localhost:8083/api/students/photo-solve"
headers = {
    "Authorization": f"Bearer {token}"
}
files = {
    "image": open("question.jpg", "rb")
}

response = requests.post(url, headers=headers, files=files)
result = response.json()
print(result)
```

## ⚠️ 注意事项

### 性能考虑
- AI解题通常需要5-15秒处理时间
- 建议前端显示加载状态
- 图片大小影响处理速度，建议适当压缩

### 错误处理
- 网络超时：建议设置30秒超时时间
- 图片格式：不支持的格式会自动转换
- AI解析失败：返回原始错误信息供调试

### 安全考虑
- 临时文件自动清理
- 不存储用户上传的图片
- API调用日志记录

## 🔮 扩展功能

### 计划中的功能
- **批量解题**：支持一次上传多张图片
- **解题历史**：记录用户解题历史
- **错题收藏**：将解题结果添加到错题本
- **语音解答**：支持语音播报解题过程

### API版本控制
- 当前版本：v1.0
- 向后兼容：保证API向后兼容性
- 版本升级：通过URL路径区分版本

## 📞 技术支持

### 常见问题
1. **Q: 为什么解题失败？**
   A: 检查图片清晰度、网络连接和AI服务配置

2. **Q: 支持哪些题目类型？**
   A: 支持大部分文字题目，复杂图形题识别有限

3. **Q: 如何提高识别准确率？**
   A: 确保图片清晰、光线充足、题目完整

### 联系方式
- 📧 技术支持：<EMAIL>
- 📱 开发团队：<EMAIL>
- 🐛 问题反馈：GitHub Issues

---

*最后更新时间：2025年8月1日*
*API版本：v1.0*
