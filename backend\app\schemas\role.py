from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from .role_subject_permission import RoleSubjectPermission

# 角色模型
class RoleBase(BaseModel):
    name: str
    description: Optional[str] = None
    is_system: bool = False

class RoleCreate(RoleBase):
    pass

class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_system: Optional[bool] = None

class Role(RoleBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

class RoleWithPermissions(Role):
    subject_permissions: List[RoleSubjectPermission] = []

    model_config = {
        "from_attributes": True
    } 