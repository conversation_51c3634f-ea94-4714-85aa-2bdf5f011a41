{"ast": null, "code": "import * as React from 'react';\nvar AddButton = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    editable = props.editable,\n    locale = props.locale,\n    style = props.style;\n  if (!editable || editable.showAdd === false) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"button\", {\n    ref: ref,\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-add\"),\n    style: style,\n    \"aria-label\": (locale === null || locale === void 0 ? void 0 : locale.addAriaLabel) || 'Add tab',\n    onClick: function onClick(event) {\n      editable.onEdit('add', {\n        event: event\n      });\n    }\n  }, editable.addIcon || '+');\n});\nexport default AddButton;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}