import React, { useState, useEffect } from 'react';
import {
  Card,
  Switch,
  Typography,
  Spin,
  message,
  Row,
  Col,
  Divider,
  Button,
  Modal,
  Alert,
  Badge,
  Space,
  Tooltip
} from 'antd';
import {
  SettingOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import api from '../utils/api';

const { Title, Text, Paragraph } = Typography;
const { confirm } = Modal;

const ParentFeatureManager = ({ isMobile = false }) => {
  const [loading, setLoading] = useState(true);
  const [features, setFeatures] = useState({});
  const [updating, setUpdating] = useState({});

  // 获取家长端特性状态
  const fetchFeatures = async () => {
    console.log('🚀 开始获取家长端特性状态...');
    try {
      setLoading(true);
      console.log('📡 发送API请求: /admin/parent-features');

      const response = await api.get('/admin/parent-features');
      console.log('📨 收到API响应:', response);
      console.log('📋 响应数据:', response);

      // 处理两种可能的响应格式
      let featuresData = null;

      if (response && response.success) {
        // 标准格式: {success: true, data: {...}}
        featuresData = response.data;
        console.log('✅ 标准格式响应，特性数据:', featuresData);
      } else if (response && response.categories) {
        // 直接格式: {total_categories: 3, categories: {...}}
        featuresData = response;
        console.log('✅ 直接格式响应，特性数据:', featuresData);
      } else {
        console.error('❌ 无法识别的响应格式:', response);
        message.error('获取家长端特性状态失败：响应格式错误');
        return;
      }

      if (featuresData) {
        console.log('🎯 设置特性数据到状态:', featuresData);
        setFeatures(featuresData);
        message.success('家长端特性状态加载成功');
      }
    } catch (error) {
      console.error('💥 API调用异常:', error);
      console.error('错误详情:', error);
      message.error(`获取家长端特性状态失败: ${error.detail || error.message}`);
    } finally {
      console.log('🏁 API调用完成，设置loading为false');
      setLoading(false);
    }
  };

  // 切换特性状态
  const toggleFeature = async (featureName) => {
    const isMainCategory = !featureName.includes('.');
    const categories = features.categories || {};

    const currentStatus = isMainCategory
      ? categories[featureName]?.enabled
      : categories[featureName.split('.')[0]]?.features?.[featureName.split('.')[1]];

    console.log('🔄 切换特性:', featureName, '当前状态:', currentStatus);

    confirm({
      title: '确认操作',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>您确定要{currentStatus ? '禁用' : '启用'}功能 <strong>{featureName}</strong> 吗？</p>
          {isMainCategory && (
            <Alert
              message="注意"
              description="切换主类别将影响该类别下的所有子功能"
              type="warning"
              showIcon
              style={{ marginTop: 8 }}
            />
          )}
        </div>
      ),
      onOk: async () => {
        try {
          setUpdating(prev => ({ ...prev, [featureName]: true }));
          const response = await api.post(`/admin/parent-features/${featureName}/toggle`);

          console.log('🔍 API响应数据:', response);
          console.log('🔍 response.success:', response.success);

          if (response.success) {
            message.success(response.message);
            await fetchFeatures(); // 重新获取状态
          } else {
            console.error('❌ API返回success=false:', response);
            message.error('切换特性状态失败');
          }
        } catch (error) {
          console.error('💥 切换特性状态异常:', error);
          console.error('💥 错误响应:', error);
          message.error(`切换特性状态失败: ${error.detail || error.message}`);
        } finally {
          setUpdating(prev => ({ ...prev, [featureName]: false }));
        }
      }
    });
  };

  // 重置所有特性
  const resetFeatures = () => {
    confirm({
      title: '重置确认',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>您确定要重置所有家长端特性配置为默认值吗？</p>
          <Alert
            message="警告"
            description="此操作将重置所有特性开关状态，无法撤销！"
            type="error"
            showIcon
            style={{ marginTop: 8 }}
          />
        </div>
      ),
      onOk: async () => {
        try {
          setLoading(true);
          const response = await api.post('/admin/parent-features/reset');
          
          if (response.success) {
            message.success('特性配置已重置为默认值');
            await fetchFeatures();
          } else {
            message.error('重置特性配置失败');
          }
        } catch (error) {
          console.error('重置特性配置失败:', error);
          message.error(`重置特性配置失败: ${error.detail || error.message}`);
        } finally {
          setLoading(false);
        }
      }
    });
  };

  useEffect(() => {
    fetchFeatures();
  }, []);

  // 渲染特性开关
  const renderFeatureSwitch = (featureName, enabled, description, isSubFeature = false) => {
    const isUpdating = updating[featureName];
    
    return (
      <div 
        key={featureName}
        style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          padding: isSubFeature ? '8px 0 8px 24px' : '12px 0',
          borderLeft: isSubFeature ? '3px solid #f0f0f0' : 'none',
          marginLeft: isSubFeature ? '12px' : '0'
        }}
      >
        <div style={{ flex: 1 }}>
          <Space>
            <Text strong={!isSubFeature} style={{ 
              fontSize: isSubFeature ? '13px' : '14px',
              color: isSubFeature ? '#666' : '#333'
            }}>
              {featureName}
            </Text>
            <Badge 
              status={enabled ? 'success' : 'default'} 
              text={enabled ? '已启用' : '已禁用'}
            />
          </Space>
          {description && (
            <div style={{ marginTop: 4 }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {description}
              </Text>
            </div>
          )}
        </div>
        <Tooltip title={enabled ? '点击禁用' : '点击启用'}>
          <Switch
            checked={enabled}
            loading={isUpdating}
            onChange={() => toggleFeature(featureName)}
            size={isMobile ? 'small' : 'default'}
          />
        </Tooltip>
      </div>
    );
  };

  // 渲染特性类别
  const renderFeatureCategory = (categoryName, categoryData) => {
    const { enabled, description, features: subFeatures } = categoryData;
    
    return (
      <Card 
        key={categoryName}
        size="small"
        style={{ marginBottom: 16 }}
        title={
          <Space>
            {enabled ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : <CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
            <span>{categoryName}</span>
          </Space>
        }
        extra={
          <Switch
            checked={enabled}
            loading={updating[categoryName]}
            onChange={() => toggleFeature(categoryName)}
            size="small"
          />
        }
      >
        <div style={{ marginBottom: 12 }}>
          <Text type="secondary">{description}</Text>
        </div>
        
        {subFeatures && Object.keys(subFeatures).length > 0 && (
          <div>
            <Divider style={{ margin: '12px 0' }} />
            <Text strong style={{ fontSize: '13px', color: '#666' }}>子功能：</Text>
            <div style={{ marginTop: 8 }}>
              {Object.entries(subFeatures).map(([featureName, featureEnabled]) => 
                renderFeatureSwitch(
                  `${categoryName}.${featureName}`, 
                  featureEnabled, 
                  null, 
                  true
                )
              )}
            </div>
          </div>
        )}
      </Card>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>正在加载家长端特性配置...</Text>
        </div>
      </div>
    );
  }

  const { categories = {} } = features;
  console.log('🔍 渲染时的features状态:', features);
  console.log('🔍 渲染时的categories:', categories);
  console.log('🔍 categories键数量:', Object.keys(categories).length);

  return (
    <div style={{ padding: isMobile ? '8px' : '16px' }}>
      {/* 头部信息 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col flex="auto">
            <Title level={4} style={{ margin: 0 }}>
              <SettingOutlined style={{ marginRight: 8 }} />
              家长端功能管理
            </Title>
            <Paragraph style={{ margin: '8px 0 0 0', color: '#666' }}>
              管理家长端各项功能的启用状态，支持分阶段控制功能开放
            </Paragraph>
          </Col>
          <Col>
            <Space>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchFeatures}
                loading={loading}
              >
                刷新
              </Button>
              <Button 
                danger 
                icon={<ExclamationCircleOutlined />} 
                onClick={resetFeatures}
              >
                重置配置
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 功能状态概览 */}
      <Card title="功能状态概览" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
                {features.total_categories || 0}
              </Title>
              <Text type="secondary">总功能类别</Text>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ margin: 0, color: '#52c41a' }}>
                {features.enabled_categories || 0}
              </Title>
              <Text type="secondary">已启用类别</Text>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ margin: 0, color: '#faad14' }}>
                {Object.values(categories).reduce((total, cat) => 
                  total + Object.keys(cat.features || {}).length, 0
                )}
              </Title>
              <Text type="secondary">总子功能数</Text>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 特性开关列表 */}
      <div>
        <Title level={5} style={{ marginBottom: 16 }}>
          <InfoCircleOutlined style={{ marginRight: 8 }} />
          功能开关控制
        </Title>
        
        {Object.keys(categories).length === 0 ? (
          <Card>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <Text type="secondary">暂无家长端特性配置</Text>
            </div>
          </Card>
        ) : (
          Object.entries(categories).map(([categoryName, categoryData]) =>
            renderFeatureCategory(categoryName, categoryData)
          )
        )}
      </div>

      {/* 使用说明 */}
      <Card title="使用说明" style={{ marginTop: 16 }}>
        <ul style={{ paddingLeft: '20px', margin: 0 }}>
          <li>主类别开关控制整个功能模块的启用状态</li>
          <li>子功能开关可以精确控制具体功能的启用</li>
          <li>建议按阶段逐步启用功能，确保系统稳定性</li>
          <li>第三阶段高级功能建议在充分测试后再启用</li>
          <li>如遇问题可使用"重置配置"恢复默认状态</li>
        </ul>
      </Card>
    </div>
  );
};

export default ParentFeatureManager;
