# -*- coding: utf-8 -*-
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
import logging

from ..database import get_db
from ..models.region import Province, City, District
from ..models.user import User
from ..schemas import region as region_schema
from ..services.auth_service import get_current_user

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# 检查是否为超级管理员的依赖函数
async def get_super_admin_user(current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要超级管理员权限"
        )
    return current_user

# 省份管理API
@router.get("/provinces", response_model=List[region_schema.Province])
async def get_all_provinces(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取所有省份列表"""
    provinces = db.query(Province).offset(skip).limit(limit).all()
    return provinces

@router.get("/provinces/{province_id}", response_model=region_schema.ProvinceWithCities)
async def get_province(
    province_id: int,
    db: Session = Depends(get_db)
):
    """获取省份详情，包含所有城市"""
    province = db.query(Province).filter(Province.id == province_id).first()
    if not province:
        raise HTTPException(status_code=404, detail="省份不存在")
    return province

@router.post("/provinces", response_model=region_schema.Province)
async def create_province(
    province: region_schema.ProvinceCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_super_admin_user)
):
    """创建新省份（需要超级管理员权限）"""
    # 检查是否已存在同名省份
    db_province = db.query(Province).filter(Province.name == province.name).first()
    if db_province:
        raise HTTPException(status_code=400, detail="省份名称已存在")
    
    # 创建新省份
    new_province = Province(name=province.name, code=province.code)
    db.add(new_province)
    db.commit()
    db.refresh(new_province)
    return new_province

@router.put("/provinces/{province_id}", response_model=region_schema.Province)
async def update_province(
    province_id: int,
    province: region_schema.ProvinceUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_super_admin_user)
):
    """更新省份信息（需要超级管理员权限）"""
    db_province = db.query(Province).filter(Province.id == province_id).first()
    if not db_province:
        raise HTTPException(status_code=404, detail="省份不存在")
    
    # 如果更新名称，检查是否与其他省份冲突
    if province.name and province.name != db_province.name:
        existing = db.query(Province).filter(Province.name == province.name).first()
        if existing and existing.id != province_id:
            raise HTTPException(status_code=400, detail="省份名称已存在")
    
    # 更新省份信息
    if province.name:
        db_province.name = province.name
    if province.code:
        db_province.code = province.code
    
    db.commit()
    db.refresh(db_province)
    return db_province

@router.delete("/provinces/{province_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_province(
    province_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_super_admin_user)
):
    """删除省份（需要超级管理员权限）"""
    db_province = db.query(Province).filter(Province.id == province_id).first()
    if not db_province:
        raise HTTPException(status_code=404, detail="省份不存在")
    
    # 删除省份（级联删除所有关联的城市和区县）
    db.delete(db_province)
    db.commit()
    return {"detail": "省份已删除"}

# 城市管理API
@router.get("/cities", response_model=List[region_schema.City])
async def get_all_cities(
    province_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取城市列表，可按省份筛选"""
    query = db.query(City)
    if province_id:
        query = query.filter(City.province_id == province_id)
    cities = query.offset(skip).limit(limit).all()
    return cities

@router.get("/cities/{city_id}", response_model=region_schema.CityWithDistricts)
async def get_city(
    city_id: int,
    db: Session = Depends(get_db)
):
    """获取城市详情，包含所有区县"""
    city = db.query(City).filter(City.id == city_id).first()
    if not city:
        raise HTTPException(status_code=404, detail="城市不存在")
    return city

@router.post("/cities", response_model=region_schema.City)
async def create_city(
    city: region_schema.CityCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_super_admin_user)
):
    """创建新城市（需要超级管理员权限）"""
    # 检查省份是否存在
    province = db.query(Province).filter(Province.id == city.province_id).first()
    if not province:
        raise HTTPException(status_code=404, detail="省份不存在")
    
    # 检查是否已存在同名城市（在同一省份内）
    db_city = db.query(City).filter(
        City.name == city.name,
        City.province_id == city.province_id
    ).first()
    if db_city:
        raise HTTPException(status_code=400, detail="同一省份内已存在同名城市")
    
    # 创建新城市
    new_city = City(
        name=city.name,
        code=city.code,
        province_id=city.province_id
    )
    db.add(new_city)
    db.commit()
    db.refresh(new_city)
    return new_city

@router.put("/cities/{city_id}", response_model=region_schema.City)
async def update_city(
    city_id: int,
    city: region_schema.CityUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_super_admin_user)
):
    """更新城市信息（需要超级管理员权限）"""
    db_city = db.query(City).filter(City.id == city_id).first()
    if not db_city:
        raise HTTPException(status_code=404, detail="城市不存在")
    
    # 如果更新省份，检查新省份是否存在
    if city.province_id and city.province_id != db_city.province_id:
        province = db.query(Province).filter(Province.id == city.province_id).first()
        if not province:
            raise HTTPException(status_code=404, detail="省份不存在")
    
    # 如果更新名称或省份，检查是否与其他城市冲突
    if (city.name and city.name != db_city.name) or (city.province_id and city.province_id != db_city.province_id):
        province_id = city.province_id if city.province_id else db_city.province_id
        name = city.name if city.name else db_city.name
        
        existing = db.query(City).filter(
            City.name == name,
            City.province_id == province_id,
            City.id != city_id
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="同一省份内已存在同名城市")
    
    # 更新城市信息
    if city.name:
        db_city.name = city.name
    if city.code:
        db_city.code = city.code
    if city.province_id:
        db_city.province_id = city.province_id
    
    db.commit()
    db.refresh(db_city)
    return db_city

@router.delete("/cities/{city_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_city(
    city_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_super_admin_user)
):
    """删除城市（需要超级管理员权限）"""
    db_city = db.query(City).filter(City.id == city_id).first()
    if not db_city:
        raise HTTPException(status_code=404, detail="城市不存在")
    
    # 删除城市（级联删除所有关联的区县）
    db.delete(db_city)
    db.commit()
    return {"detail": "城市已删除"}

# 区县管理API
@router.get("/districts", response_model=List[region_schema.District])
async def get_all_districts(
    city_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取区县列表，可按城市筛选"""
    query = db.query(District)
    if city_id:
        query = query.filter(District.city_id == city_id)
    districts = query.offset(skip).limit(limit).all()
    return districts

@router.get("/districts/{district_id}", response_model=region_schema.DistrictWithCity)
async def get_district(
    district_id: int,
    db: Session = Depends(get_db)
):
    """获取区县详情"""
    district = db.query(District).filter(District.id == district_id).first()
    if not district:
        raise HTTPException(status_code=404, detail="区县不存在")
    return district

@router.post("/districts", response_model=region_schema.District)
async def create_district(
    district: region_schema.DistrictCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_super_admin_user)
):
    """创建新区县（需要超级管理员权限）"""
    # 检查城市是否存在
    city = db.query(City).filter(City.id == district.city_id).first()
    if not city:
        raise HTTPException(status_code=404, detail="城市不存在")
    
    # 检查是否已存在同名区县（在同一城市内）
    db_district = db.query(District).filter(
        District.name == district.name,
        District.city_id == district.city_id
    ).first()
    if db_district:
        raise HTTPException(status_code=400, detail="同一城市内已存在同名区县")
    
    # 创建新区县
    new_district = District(
        name=district.name,
        code=district.code,
        city_id=district.city_id
    )
    db.add(new_district)
    db.commit()
    db.refresh(new_district)
    return new_district

@router.put("/districts/{district_id}", response_model=region_schema.District)
async def update_district(
    district_id: int,
    district: region_schema.DistrictUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_super_admin_user)
):
    """更新区县信息（需要超级管理员权限）"""
    db_district = db.query(District).filter(District.id == district_id).first()
    if not db_district:
        raise HTTPException(status_code=404, detail="区县不存在")
    
    # 如果更新城市，检查新城市是否存在
    if district.city_id and district.city_id != db_district.city_id:
        city = db.query(City).filter(City.id == district.city_id).first()
        if not city:
            raise HTTPException(status_code=404, detail="城市不存在")
    
    # 如果更新名称或城市，检查是否与其他区县冲突
    if (district.name and district.name != db_district.name) or (district.city_id and district.city_id != db_district.city_id):
        city_id = district.city_id if district.city_id else db_district.city_id
        name = district.name if district.name else db_district.name
        
        existing = db.query(District).filter(
            District.name == name,
            District.city_id == city_id,
            District.id != district_id
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="同一城市内已存在同名区县")
    
    # 更新区县信息
    if district.name:
        db_district.name = district.name
    if district.code:
        db_district.code = district.code
    if district.city_id:
        db_district.city_id = district.city_id
    
    db.commit()
    db.refresh(db_district)
    return db_district

@router.delete("/districts/{district_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_district(
    district_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_super_admin_user)
):
    """删除区县（需要超级管理员权限）"""
    db_district = db.query(District).filter(District.id == district_id).first()
    if not db_district:
        raise HTTPException(status_code=404, detail="区县不存在")
    
    # 删除区县
    db.delete(db_district)
    db.commit()
    return {"detail": "区县已删除"}

# 获取层级区域结构
@router.get("/regions", response_model=Dict)
async def get_regions(
    province_id: Optional[int] = None,
    city_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """获取区域层级结构"""
    result = {}
    
    # 获取省份列表
    province_query = db.query(Province)
    if province_id:
        province_query = province_query.filter(Province.id == province_id)
    provinces = province_query.all()
    
    for province in provinces:
        province_data = {
            "id": province.id,
            "name": province.name,
            "code": province.code,
            "cities": {}
        }
        
        # 获取城市列表
        city_query = db.query(City).filter(City.province_id == province.id)
        if city_id:
            city_query = city_query.filter(City.id == city_id)
        cities = city_query.all()
        
        for city in cities:
            city_data = {
                "id": city.id,
                "name": city.name,
                "code": city.code,
                "districts": []
            }
            
            # 获取区县列表
            districts = db.query(District).filter(District.city_id == city.id).all()
            for district in districts:
                district_data = {
                    "id": district.id,
                    "name": district.name,
                    "code": district.code
                }
                city_data["districts"].append(district_data)
            
            province_data["cities"][city.id] = city_data
        
        result[province.id] = province_data
    
    return result 

# 公开API：注册页等无需登录即可获取地区数据
@router.get("/public/provinces")
def public_get_provinces(db: Session = Depends(get_db)):
    provinces = db.query(Province).all()
    return {"provinces": [p.name for p in provinces]}

@router.get("/public/cities")
def public_get_cities(province: str = Query(...), db: Session = Depends(get_db)):
    province_obj = db.query(Province).filter_by(name=province).first()
    if not province_obj:
        return {"cities": []}
    cities = db.query(City).filter_by(province_id=province_obj.id).all()
    return {"cities": [c.name for c in cities]}

@router.get("/public/districts")
def public_get_districts(city: str = Query(...), db: Session = Depends(get_db)):
    city_obj = db.query(City).filter_by(name=city).first()
    if not city_obj:
        return {"districts": []}
    districts = db.query(District).filter_by(city_id=city_obj.id).all()
    return {"districts": [d.name for d in districts]} 