from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, func, desc, and_, distinct, text
import traceback
from typing import List, Optional
from enum import Enum
import logging
import json
import re
from pydantic import BaseModel
from datetime import datetime, timedelta
from jose import JWTError, jwt

class AIUsageType(str, Enum):
    HOMEWORK_GRADING = "homework_grading"
    ERROR_ANALYSIS = "error_analysis"
    REINFORCEMENT_EXERCISE = "reinforcement_exercise"
    AI_ASSISTANT = "ai_assistant"
    HOMEWORK_COMMENT = "homework_comment"

from ..database import get_db
from ..models.user import User
from ..models.user import Class, ClassStudent, ClassTeacher
from ..models.user_role import UserRole
from ..models.school import School
from ..models.subject import Subject, SubjectCategory, GradeSubject
from ..models.ai_config import AIModelConfig
from ..models.system_settings import SystemSettings
from ..models.role import Role, Permission, role_permission_association
from ..models.role_subject_permission import RoleSubjectPermission
from ..schemas import user as user_schema
from ..schemas import class_schema
from ..schemas import school as school_schema
from ..schemas import subject as subject_schema
from ..schemas import ai_config as ai_config_schema
from ..schemas import system_settings as system_settings_schema
from ..schemas import registration as registration_schema
from ..utils.auth import get_password_hash, SECRET_KEY, ALGORITHM
from ..schemas import role as role_schema
from ..schemas import role_subject_permission as role_subject_permission_schema
from app.models.user import ParentStudent
from app.models.role import Role
from ..utils.auth import get_password_hash

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

def generate_safe_email(username: str, domain: str = "school.local") -> str:
    """
    生成安全的邮箱地址，确保符合邮箱格式验证规则

    Args:
        username: 用户名（可能包含中文字符）
        domain: 邮箱域名

    Returns:
        符合格式的邮箱地址
    """
    # 移除或替换非ASCII字符
    safe_username = re.sub(r'[^\w.-]', '', username.encode('ascii', 'ignore').decode('ascii'))

    # 如果处理后的用户名为空，使用默认前缀
    if not safe_username:
        safe_username = "user"

    # 确保用户名不以点开头或结尾
    safe_username = safe_username.strip('.')

    # 如果用户名仍然为空，使用默认值
    if not safe_username:
        safe_username = "user"

    # 生成邮箱
    email = f"{safe_username}@{domain}"

    # 验证生成的邮箱格式
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        # 如果仍然不符合格式，使用更安全的生成方式
        import hashlib
        hash_suffix = hashlib.md5(username.encode()).hexdigest()[:8]
        email = f"user_{hash_suffix}@{domain}"

    return email

# 定义OAuth2PasswordBearer
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

# 定义获取当前用户的函数
async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效凭证",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise credentials_exception
    return user

# 定义获取管理员用户的函数
async def get_admin_user(current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="权限不足，需要管理员权限")
    return current_user

# 定义权限检查依赖函数工厂
def require_permission(permission_code: str):
    async def check_permission(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
        """检查当前用户是否有指定权限"""
        # 获取用户的所有角色
        user_roles = db.query(UserRole).filter(UserRole.user_id == current_user.id).all()
        if not user_roles:
            raise HTTPException(status_code=403, detail="用户没有任何角色")

        # 检查是否有指定权限
        permission = db.query(Permission).filter(Permission.code == permission_code).first()
        if not permission:
            return False

        # 检查每个角色是否有所需权限
        for user_role in user_roles:
            role = db.query(Role).filter(Role.id == user_role.role_id).first()
            if role and permission in role.permissions:
                return current_user
            
        raise HTTPException(status_code=403, detail="权限不足")
    return check_permission

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# 添加用户列表响应模型，包含总数和分页数据
class UserListResponse(BaseModel):
    total: int
    items: List[user_schema.User]

# 检查是否为管理员的依赖函数
async def get_admin_or_school_admin_user(current_user: User = Depends(get_current_user)):
    # 超级管理员有所有权限
    if current_user.is_admin:
        return current_user

    # 检查是否为学校管理员或校长
    if current_user.role in ["学校管理员", "principal", "校长"]:
        return current_user

    # 如果用户有学校管理员相关的角色，也允许访问
    # 这里可以进一步检查用户的角色分配
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="权限不足，需要管理员或学校管理员权限"
    )

# 用户管理API
@router.get("/users", response_model=UserListResponse)
async def get_all_users(
    skip: int = 0, 
    limit: int = 100,
    search: str = None,
    role: str = None,
    school_id: int = None,
    is_active: bool = None,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取用户列表，支持搜索和筛选"""
    try:
        logger.info(f"获取用户列表: search={search}, role={role}, school_id={school_id}, skip={skip}, limit={limit}")
        query = db.query(User)
        
        # 添加搜索功能，支持按用户名、姓名或邮箱搜索
        if search:
            search_term = f"%{search}%"
            logger.info(f"搜索用户: term='{search_term}'")
            query = query.filter(
                or_(
                    User.username.ilike(search_term),
                    User.full_name.ilike(search_term),
                    User.email.ilike(search_term)
                )
            )
        
        # 添加角色筛选功能
        if role:
            if role == "admin" or role == "管理员":
                query = query.filter(User.is_admin == True)
            elif role == "teacher" or role == "教师":
                query = query.filter(User.is_teacher == True)
            elif role == "student" or role == "学生":
                query = query.filter(User.role == "学生")
        
        # 添加学校筛选功能
        if school_id:
            query = query.filter(User.school_id == school_id)
        
        # 添加状态筛选功能
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        # 计算总数 - 在应用分页前获取总数
        total = query.count()
        logger.info(f"用户总数: {total}")
        
        # 分页处理
        users = query.offset(skip).limit(limit).all()
        logger.info(f"返回用户数: {len(users)}")

        # 特别处理搜索结果，打印详细日志
        if search:
            # 记录搜索结果
            user_names = [user.username for user in users]
            logger.info(f"搜索'{search}'的结果: {user_names}")

            # 如果搜索包含 'danphy' 或 'Danphy'，特殊记录日志
            if 'danphy' in search.lower():
                danphy_users = [user for user in users if 'danphy' in user.username.lower()]
                logger.info(f"Danphy用户搜索结果: {[user.username for user in danphy_users]}")

                # 特别记录所有用户，帮助调试
                all_danphy = db.query(User).filter(User.username.ilike('%danphy%')).all()
                logger.info(f"数据库中所有Danphy用户: {[user.username for user in all_danphy]}")

        # 为每个用户添加学校名称和角色信息
        users_with_enhanced_info = []
        for user in users:
            school_name = None
            if user.school_id:
                try:
                    from sqlalchemy import text
                    result = db.execute(text("SELECT name FROM schools WHERE id = :id"), {"id": user.school_id})
                    row = result.fetchone()
                    if row:
                        school_name = row[0]
                except Exception as e:
                    logger.error(f"获取学校名称失败: {str(e)}")

            # 使用角色服务获取正确的中文角色名称
            from ..services.role_service import RoleService
            primary_role_name = RoleService.get_user_primary_role_name(user, db)

            # 构建用户响应数据，包含学校名称和角色信息
            user_response = user_schema.User(
                id=user.id,
                username=user.username,
                email=user.email,
                full_name=user.full_name,
                phone=user.phone,
                is_teacher=user.is_teacher,
                is_admin=user.is_admin,
                role=primary_role_name,  # 使用新角色系统的角色名称
                school_id=user.school_id,
                is_active=user.is_active,
                created_at=user.created_at,
                school_name=school_name
            )
            users_with_enhanced_info.append(user_response)

        # 始终返回格式化的分页响应，确保包含正确的总数
        logger.info(f"返回用户列表响应: 总数={total}, 当前页用户数={len(users_with_enhanced_info)}")
        return {"total": total, "items": users_with_enhanced_info}
    except Exception as e:
        logger.error(f"获取用户列表时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")

@router.post("/batch-students", response_model=List[user_schema.User])
async def batch_create_students(
    students_data: List[dict], 
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """批量创建学生账户"""
    created_users = []
    existing_users = []
    
    logger.info(f"接收到批量创建学生请求，数据数量: {len(students_data)}")
    logger.info(f"请求数据详情: {students_data}")
    
    try:
        for index, student_data_dict in enumerate(students_data):
            try:
                logger.info(f"处理第 {index + 1} 个学生数据: {student_data_dict}")
                
                # 手动处理验证和默认值
                username = str(student_data_dict.get("username", "")).strip()  # 确保username是字符串并去除空格
                email = student_data_dict.get("email", "").strip()
                full_name = student_data_dict.get("full_name", "").strip()
                phone = student_data_dict.get("phone", "").strip()
                password = student_data_dict.get("password", "123456")  # 默认密码
                
                logger.info(f"处理后的学生数据: username='{username}', email='{email}', full_name='{full_name}'")
                
                # 基本验证 - 只要求用户名和姓名
                if not username:
                    logger.warning(f"跳过第 {index + 1} 个学生: 用户名为空")
                    continue
                    
                if not full_name:
                    logger.warning(f"跳过第 {index + 1} 个学生: 姓名为空")
                    continue
                
                # 如果邮箱为空，生成一个默认邮箱
                if not email:
                    email = generate_safe_email(username, "example.com")
                    logger.info(f"为学生 {username} 生成默认邮箱: {email}")

                # 简单的邮箱格式验证 - 更宽松的验证
                if email and '@' not in email:
                    # 如果邮箱格式不正确，生成一个默认邮箱
                    email = generate_safe_email(username, "example.com")
                    logger.info(f"为学生 {username} 修正邮箱格式: {email}")
                
                # 检查用户名是否已存在
                existing_user = db.query(User).filter(User.username == username).first()
                if existing_user:
                    # 如果用户已存在，添加到已存在用户列表并记录日志
                    logger.info(f"用户名 '{username}' 已存在，ID: {existing_user.id}")
                    existing_users.append(existing_user)
                    continue
                    
                # 检查邮箱是否已存在
                existing_email = db.query(User).filter(User.email == email).first()
                if existing_email:
                    # 如果邮箱已被使用，生成一个新的邮箱
                    email = generate_safe_email(f"{username}_{existing_email.id}", "example.com")
                    logger.info(f"邮箱已被使用，生成新邮箱: {email}")
                    
                    # 再次检查新邮箱是否已存在
                    existing_email = db.query(User).filter(User.email == email).first()
                    if existing_email:
                        logger.warning(f"生成的新邮箱 '{email}' 也已存在，跳过此学生")
                        continue
                
                # 创建新用户
                hashed_password = get_password_hash(password)
                new_user = User(
                    username=username,
                    email=email,
                    hashed_password=hashed_password,
                    full_name=full_name,
                    phone=phone,
                    is_teacher=False,
                    is_admin=False
                )
                
                logger.info(f"准备创建新用户: {username}")
                db.add(new_user)
                # 单独提交每个用户，确保部分失败不影响其他用户
                db.commit()
                db.refresh(new_user)
                created_users.append(new_user)
                logger.info(f"成功创建学生: {username} (ID: {new_user.id})")
            
            except Exception as e:
                # 如果处理单个学生时出错，记录错误但继续处理其他学生
                db.rollback()  # 回滚当前事务
                logger.error(f"创建第 {index + 1} 个学生账户失败 (用户名: {username if 'username' in locals() else 'unknown'}): {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                # 继续处理下一个学生
                continue
        
        # 合并新创建的用户和已存在的用户
        all_users = created_users + existing_users
        
        logger.info(f"批量处理学生完成，新创建: {len(created_users)}，已存在: {len(existing_users)}，总计返回: {len(all_users)}")
        for user in all_users:
            logger.info(f"返回用户: ID={user.id}, 用户名={user.username}, 姓名={user.full_name}")
        
        # 如果没有处理任何用户，尝试重新查找这些用户名
        if not all_users:
            logger.warning("没有成功创建或找到任何学生，尝试重新查找所有用户名")
            for student_data in students_data:
                username = str(student_data.get("username", "")).strip()
                if username:
                    user = db.query(User).filter(User.username == username).first()
                    if user:
                        logger.info(f"重新查找到用户: ID={user.id}, 用户名={user.username}")
                        all_users.append(user)
            
            logger.info(f"重新查找后找到 {len(all_users)} 个用户")
        
        # 返回所有用户（包括新创建和已存在的）
        return all_users
    
    except Exception as e:
        # 捕获整体处理过程中的异常
        db.rollback()
        logger.error(f"批量创建学生账户时发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
        # 合并已成功创建的用户和已存在的用户
        all_users = created_users + existing_users
        logger.info(f"尽管发生错误，仍返回 {len(all_users)} 个用户")
        
        # 如果没有处理任何用户，尝试重新查找这些用户名
        if not all_users:
            logger.warning("发生错误且没有成功创建或找到任何学生，尝试重新查找所有用户名")
            for student_data in students_data:
                username = str(student_data.get("username", "")).strip()
                if username:
                    user = db.query(User).filter(User.username == username).first()
                    if user:
                        logger.info(f"重新查找到用户: ID={user.id}, 用户名={user.username}")
                        all_users.append(user)
            
            logger.info(f"重新查找后找到 {len(all_users)} 个用户")
        
        return all_users

@router.post("/users", response_model=user_schema.User)
async def create_user(
    user_data: user_schema.UserCreate, 
    current_user: User = Depends(get_admin_or_school_admin_user),
    db: Session = Depends(get_db)
):
    """创建用户"""
    # 在多学校系统中，用户名允许重复，但邮箱必须唯一
    # 检查邮箱是否已存在（全系统唯一）
    if user_data.email:
        existing_email = db.query(User).filter(User.email == user_data.email).first()
        if existing_email:
            raise HTTPException(status_code=400, detail="邮箱已被使用")

    # 检查同一学校内用户名是否重复（学校内唯一）
    existing_user_in_school = db.query(User).filter(
        User.username == user_data.username,
        User.school_id == user_data.school_id
    ).first()
    if existing_user_in_school:
        role_name = existing_user_in_school.role or "用户"
        raise HTTPException(status_code=400, detail=f"用户名 '{user_data.username}' 在该学校内已被{role_name}使用，请使用其他用户名")
    
    # 检查权限：非超级管理员只能创建自己学校的用户
    if not current_user.is_admin and user_data.school_id != current_user.school_id:
        raise HTTPException(status_code=403, detail="没有权限创建其他学校的用户")
    
    # 使用UserService创建用户（自动同步到user_roles表）
    from ..services.user_service import UserService

    hashed_password = get_password_hash(user_data.password)

    # 确定角色名称
    role_name = None
    if user_data.is_admin and user_data.is_teacher:
        role_name = "超级管理员"
    elif user_data.is_admin:
        role_name = "学校管理员"
    elif user_data.is_teacher:
        role_name = "教师"
    else:
        role_name = "学生"

    logger.info(f"管理员创建用户: {user_data.username}, 角色: {role_name}")
    new_user = UserService.create_user_with_role(
        user_data=user_data,
        db=db,
        hashed_password=hashed_password,
        role_name=role_name
    )

    return new_user

@router.get("/users/{user_id}", response_model=user_schema.User)
async def get_user(
    user_id: int, 
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="用户不存在")
    return user

@router.put("/users/{user_id}", response_model=user_schema.User)
async def update_user(
    user_id: int,
    user_data: user_schema.UserCreate,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="用户不存在")

    # 更新用户信息
    db_user.username = user_data.username
    db_user.email = user_data.email
    db_user.full_name = user_data.full_name
    db_user.phone = user_data.phone
    db_user.is_teacher = user_data.is_teacher
    db_user.is_admin = user_data.is_admin

    # 根据角色标志更新角色字段，确保一致性
    if db_user.is_admin and db_user.is_teacher:
        db_user.role = "超级管理员"  # 既是管理员又是教师，权限最高
    elif db_user.is_admin:
        db_user.role = "学校管理员"  # 只是管理员，权限限制在学校内
    elif db_user.is_teacher:
        db_user.role = "教师"
    else:
        db_user.role = "学生"

    # 更新学校ID
    if user_data.school_id is not None:
        db_user.school_id = user_data.school_id

    # 如果提供了密码，则更新密码
    if user_data.password:
        db_user.hashed_password = get_password_hash(user_data.password)

    db.commit()
    db.refresh(db_user)

    # 获取学校名称
    school_name = None
    if db_user.school_id:
        try:
            from sqlalchemy import text
            result = db.execute(text("SELECT name FROM schools WHERE id = :id"), {"id": db_user.school_id})
            row = result.fetchone()
            if row:
                school_name = row[0]
        except Exception as e:
            logger.error(f"获取学校名称失败: {str(e)}")

    # 获取用户的主要角色名称
    primary_role_name = None
    if db_user.primary_role_id:
        try:
            from sqlalchemy import text
            result = db.execute(text("SELECT name FROM roles WHERE id = :id"), {"id": db_user.primary_role_id})
            row = result.fetchone()
            if row:
                primary_role_name = row[0]
        except Exception as e:
            logger.error(f"获取主要角色失败: {str(e)}")

    # 如果没有主要角色，使用角色服务获取
    if not primary_role_name:
        from ..services.role_service import RoleService
        primary_role_name = RoleService.get_user_primary_role_name(db_user, db)

    # 构建返回数据，包含学校名称和角色信息
    user_response = user_schema.User(
        id=db_user.id,
        username=db_user.username,
        email=db_user.email,
        full_name=db_user.full_name,
        phone=db_user.phone,
        is_teacher=db_user.is_teacher,
        is_admin=db_user.is_admin,
        role=primary_role_name,  # 使用新角色系统的角色名称
        school_id=db_user.school_id,
        is_active=db_user.is_active,
        created_at=db_user.created_at,
        school_name=school_name
    )

    return user_response

@router.delete("/users/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int, 
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    db.delete(db_user)
    db.commit()
    return {"detail": "用户已删除"}

# AI模型配置API（管理员专用）
@router.get("/ai-configs", response_model=List[ai_config_schema.AIModelConfig])
async def get_all_ai_configs(
    skip: int = 0,
    limit: int = 100,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    try:
        logger.info(f"获取所有AI配置，用户: {admin.username}")
        configs = db.query(AIModelConfig).offset(skip).limit(limit).all()
        logger.info(f"成功获取 {len(configs)} 个AI配置")
        result = []
        for config in configs:
            result.append({
                "id": config.id,
                "model_name": config.model_name,
                "model_id": config.model_id,
                "provider": config.provider,
                "usage_type": config.usage_type,
                "api_key": config.api_key,
                "api_endpoint": config.api_endpoint,
                "is_active": config.is_active,
                "created_at": config.created_at,
                "updated_at": config.updated_at
            })
        return result
    except Exception as e:
        logger.error(f"获取AI配置出错: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")

# AI模型配置API（教师可用，不包含敏感信息）
@router.get("/ai-configs/available")
async def get_available_ai_configs(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取可用的AI配置列表（教师可访问，不包含敏感信息）"""
    try:
        logger.info(f"获取可用AI配置，用户: {current_user.username}, 角色: {current_user.role}")

        # 只获取激活的配置
        configs = db.query(AIModelConfig).filter(
            AIModelConfig.is_active == True
        ).offset(skip).limit(limit).all()

        logger.info(f"成功获取 {len(configs)} 个可用AI配置")
        result = []
        for config in configs:
            # 不包含敏感信息（API密钥、端点等）
            result.append({
                "id": config.id,
                "model_name": config.model_name,
                "provider": config.provider,
                "usage_type": config.usage_type,
                "is_active": config.is_active,
                "created_at": config.created_at
            })
        return result
    except Exception as e:
        logger.error(f"获取可用AI配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取可用AI配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI配置时出错: {str(e)}"
        )

@router.post("/ai-configs", response_model=ai_config_schema.AIModelConfig)
async def create_ai_config(
    config: ai_config_schema.AIModelConfigCreate, 
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    # 创建新配置，允许多个配置同时激活
    db_config = AIModelConfig(
        model_name=config.model_name,
        model_id=config.model_id,
        provider=config.provider,
        usage_type=config.usage_type,
        api_key=config.api_key,
        api_endpoint=config.api_endpoint,
        is_active=config.is_active
    )
    db.add(db_config)
    db.commit()
    db.refresh(db_config)
    
    logger.info(f"创建AI配置: ID {db_config.id}, {db_config.provider}/{db_config.model_name}, 可用状态: {db_config.is_active}")
    return db_config

@router.get("/ai-configs/{config_id}", response_model=ai_config_schema.AIModelConfig)
async def get_ai_config(
    config_id: int, 
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    config = db.query(AIModelConfig).filter(AIModelConfig.id == config_id).first()
    if config is None:
        raise HTTPException(status_code=404, detail="配置不存在")
    return config

@router.put("/ai-configs/{config_id}", response_model=ai_config_schema.AIModelConfig)
async def update_ai_config(
    config_id: int, 
    config_data: ai_config_schema.AIModelConfigUpdate, 
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    db_config = db.query(AIModelConfig).filter(AIModelConfig.id == config_id).first()
    if db_config is None:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    # 更新配置信息
    for key, value in config_data.dict(exclude_unset=True).items():
        setattr(db_config, key, value)
    
    db.commit()
    db.refresh(db_config)
    
    # 记录日志
    logger.info(f"更新AI配置 ID {config_id}: {db_config.provider}/{db_config.model_name}, 可用状态: {db_config.is_active}")
    
    return db_config

@router.delete("/ai-configs/{config_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_ai_config(
    config_id: int, 
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    db_config = db.query(AIModelConfig).filter(AIModelConfig.id == config_id).first()
    if db_config is None:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    db.delete(db_config)
    db.commit()
    return {"detail": "配置已删除"}

@router.get("/ai-usage-types")
async def get_ai_usage_types(admin: User = Depends(get_admin_user)):
    """获取AI使用类型选项"""
    usage_types = []
    for usage_type in AIUsageType:
        usage_types.append({
            "value": usage_type.value,
            "label": {
                "homework_grading": "作业批改",
                "error_analysis": "错题分析",
                "reinforcement_exercise": "强化练习生成",
                "ai_assistant": "AI助手问答",
                "homework_comment": "作业点评"  # 新增作业点评类型
            }.get(usage_type.value, usage_type.value)
        })
    return {"usage_types": usage_types}

# 获取所有可用年级API
@router.get("/classes/grades", response_model=List[str])
async def get_available_grades(
    school_id: Optional[int] = None,
    current_user: User = Depends(get_admin_or_school_admin_user),
    db: Session = Depends(get_db)
):
    """获取所有可用的年级列表"""
    # 查询条件
    query = db.query(distinct(Class.grade)).filter(Class.grade.isnot(None))

    # 如果是学校管理员，只能查看自己学校的年级
    if current_user.role == "principal" and not current_user.is_admin:
        if current_user.school_id is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="学校管理员必须关联到一个学校"
            )
        query = query.filter(Class.school_id == current_user.school_id)
    # 如果是超级管理员且指定了学校ID，按学校ID筛选
    elif school_id:
        query = query.filter(Class.school_id == school_id)

    # 执行查询
    grades = query.all()

    # 处理结果
    result = [grade[0] for grade in grades if grade[0]]

    # 如果没有结果，返回默认年级列表
    if not result:
        result = [
            "小学一年级", "小学二年级", "小学三年级",
            "小学四年级", "小学五年级", "小学六年级",
            "初中一年级", "初中二年级", "初中三年级",
            "高中一年级", "高中二年级", "高中三年级"
        ]

    # 对年级进行排序
    def sort_grades(grade_list):
        grade_order = {
            # 小学
            '小学一年级': 1, '小学二年级': 2, '小学三年级': 3,
            '小学四年级': 4, '小学五年级': 5, '小学六年级': 6,
            # 初中
            '初中一年级': 7, '初中二年级': 8, '初中三年级': 9,
            '初一': 7, '初二': 8, '初三': 9,
            # 高中
            '高中一年级': 10, '高中二年级': 11, '高中三年级': 12,
            '高一': 10, '高二': 11, '高三': 12,
            # 其他常见格式
            '一年级': 1, '二年级': 2, '三年级': 3, '四年级': 4, '五年级': 5, '六年级': 6,
            '七年级': 7, '八年级': 8, '九年级': 9,
            '十年级': 10, '十一年级': 11, '十二年级': 12
        }
        return sorted(grade_list, key=lambda x: grade_order.get(x, 999))

    result = sort_grades(result)

    logger.info(f"获取年级列表 - 学校ID: {school_id}, 用户: {current_user.username}, 结果: {result}")
    return result

# 班级管理API
@router.get("/classes", response_model=List[class_schema.Class])
async def get_all_classes(
    school_id: Optional[int] = None,
    grade_id: Optional[int] = None,
    grade: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(require_permission(Permission.VIEW_CLASS)),
    db: Session = Depends(get_db)
):
    """获取所有班级"""
    query = db.query(Class)

    # 如果是学校管理员，只能查看自己学校的班级
    if current_user.role == "principal" and not current_user.is_admin:
        if current_user.school_id is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="学校管理员必须关联到一个学校"
            )
        query = query.filter(Class.school_id == current_user.school_id)
    # 如果是超级管理员且指定了学校ID，按学校ID筛选
    elif school_id:
        query = query.filter(Class.school_id == school_id)

    # 按年级筛选
    if grade:
        query = query.filter(Class.grade == grade)
    elif grade_id:
        # 如果传入的是grade_id，需要先获取对应的年级名称
        # 这里假设grade_id对应年级名称的映射关系
        grade_mapping = {
            1: "初一", 2: "初二", 3: "初三",
            4: "高一", 5: "高二", 6: "高三"
        }
        if grade_id in grade_mapping:
            query = query.filter(Class.grade == grade_mapping[grade_id])
    
    classes = query.offset(skip).limit(limit).all()
    
    # 手动构建响应，包含学生数量
    result = []
    for class_ in classes:
        class_dict = {
            "id": class_.id,
            "name": class_.name,
            "description": class_.description,
            "created_at": class_.created_at,
            "student_count": len(class_.students),
            "grade": class_.grade,
            "school_id": class_.school_id
        }
        result.append(class_dict)
    
    return result

@router.get("/classes/all")
async def get_all_classes_with_school_info(
    skip: int = 0,
    limit: int = 5000,  # 增加默认限制，确保能获取到所有班级
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取班级列表（包含学校信息）- 根据用户角色返回相应的班级"""
    try:
        # 联合查询班级和学校信息
        from ..models.school import School

        # 构建基础查询
        query = db.query(Class, School.name.label('school_name')).join(
            School, Class.school_id == School.id
        )

        # 根据用户角色过滤班级
        if current_user.is_admin:
            # 管理员可以看到所有班级
            pass
        elif current_user.is_teacher:
            # 教师只能看到与自己关联的班级
            query = query.join(
                ClassTeacher,
                and_(
                    ClassTeacher.class_id == Class.id,
                    ClassTeacher.teacher_id == current_user.id
                )
            )
        else:
            # 其他用户（如学生）只能看到自己所在的班级
            query = query.join(
                ClassStudent,
                and_(
                    ClassStudent.class_id == Class.id,
                    ClassStudent.student_id == current_user.id
                )
            )

        classes_with_schools = query.offset(skip).limit(limit).all()

        # 构建响应数据
        result = []
        for class_, school_name in classes_with_schools:
            # 计算学生数量
            student_count = len(class_.students) if class_.students else 0
            
            # 获取班级的教师信息
            teachers = []
            class_teachers = db.query(User).join(
                ClassTeacher,
                and_(
                    ClassTeacher.teacher_id == User.id,
                    ClassTeacher.class_id == class_.id
                )
            ).all()
            
            for teacher in class_teachers:
                teachers.append({
                    "id": teacher.id,
                    "username": teacher.username,
                    "full_name": teacher.full_name,
                    "email": teacher.email
                })

            class_dict = {
                "id": class_.id,
                "name": class_.name,
                "description": class_.description,
                "created_at": class_.created_at,
                "student_count": student_count,
                "grade": class_.grade,
                "school_id": class_.school_id,
                "school_name": school_name,  # 添加学校名称
                "teachers": teachers  # 添加教师列表
            }
            result.append(class_dict)

        logger.info(f"超级管理员 {current_user.username} 获取了 {len(result)} 个班级的信息")
        return result

    except Exception as e:
        logger.error(f"获取所有班级信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取班级信息失败: {str(e)}")

@router.get("/regions")
async def get_regions(
    current_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取地区数据（省份、城市、区县）- 仅限超级管理员"""
    try:
        from ..models.region import Province, City, District
        from ..models.school import School
        from sqlalchemy import distinct

        # 优先使用专门的地区表，如果没有数据则回退到学校表
        provinces_from_regions = db.query(Province).all()

        if provinces_from_regions:
            # 使用专门的地区表数据
            result = []
            for province in provinces_from_regions:
                province_data = {
                    "province": province.name,
                    "cities": []
                }

                for city in province.cities:
                    city_data = {
                        "city": city.name,
                        "districts": [district.name for district in city.districts]
                    }
                    province_data["cities"].append(city_data)

                # 按城市名称排序
                province_data["cities"].sort(key=lambda x: x["city"])
                result.append(province_data)

            # 按省份名称排序
            result.sort(key=lambda x: x["province"])

            logger.info(f"超级管理员 {current_user.username} 获取了地区数据(来自regions表): {len(result)} 个省份")
            return result

        else:
            # 回退到从学校表获取地区数据
            provinces_query = db.query(distinct(School.province)).filter(
                School.is_active == True,
                School.province.isnot(None),
                School.province != ""
            ).all()

            result = []

            for (province,) in provinces_query:
                # 获取该省份下的所有城市
                cities_query = db.query(distinct(School.city)).filter(
                    School.is_active == True,
                    School.province == province,
                    School.city.isnot(None),
                    School.city != ""
                ).all()

                province_data = {
                    "province": province,
                    "cities": []
                }

                for (city,) in cities_query:
                    # 获取该城市下的所有区县
                    districts_query = db.query(distinct(School.district)).filter(
                        School.is_active == True,
                        School.province == province,
                        School.city == city,
                        School.district.isnot(None),
                        School.district != ""
                    ).all()

                    city_data = {
                        "city": city,
                        "districts": sorted([district for (district,) in districts_query])
                    }
                    province_data["cities"].append(city_data)

                # 按城市名称排序
                province_data["cities"].sort(key=lambda x: x["city"])
                result.append(province_data)

            # 按省份名称排序
            result.sort(key=lambda x: x["province"])

            logger.info(f"超级管理员 {current_user.username} 获取了地区数据(来自schools表): {len(result)} 个省份")
            return result

    except Exception as e:
        logger.error(f"获取地区数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取地区数据失败: {str(e)}")

@router.get("/schools/by-region")
async def get_schools_by_region(
    province: str = None,
    city: str = None,
    district: str = None,
    current_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """根据地区筛选学校 - 仅限超级管理员"""
    try:
        from ..models.school import School

        query = db.query(School).filter(School.is_active == True)

        # 根据参数筛选
        if province and province != "all":
            query = query.filter(School.province == province)
        if city and city != "all":
            query = query.filter(School.city == city)
        if district and district != "all":
            query = query.filter(School.district == district)

        schools = query.all()

        result = []
        for school in schools:
            result.append({
                "id": school.id,
                "name": school.name,
                "province": school.province,
                "city": school.city,
                "district": school.district,
                "address": school.address
            })

        logger.info(f"超级管理员 {current_user.username} 根据地区筛选获取了 {len(result)} 个学校")
        return result

    except Exception as e:
        logger.error(f"根据地区获取学校失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"根据地区获取学校失败: {str(e)}")

@router.post("/classes", response_model=class_schema.Class)
async def create_class(
    class_data: class_schema.ClassCreate, 
    current_user: User = Depends(require_permission(Permission.MANAGE_CLASS)),
    db: Session = Depends(get_db)
):
    """创建班级"""
    # 如果是学校管理员，只能为自己的学校创建班级
    if current_user.role == "principal" and not current_user.is_admin:
        if current_user.school_id is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="学校管理员必须关联到一个学校"
            )
        # 强制使用学校管理员的学校ID
        class_data.school_id = current_user.school_id
    
    # 创建班级
    db_class = Class(
        name=class_data.name,
        description=class_data.description,
        grade=class_data.grade,
        school_id=class_data.school_id
    )
    
    db.add(db_class)
    db.commit()
    db.refresh(db_class)
    
    # 手动构建响应，包含学生数量
    return {
        "id": db_class.id,
        "name": db_class.name,
        "description": db_class.description,
        "created_at": db_class.created_at,
        "student_count": 0,
        "grade": db_class.grade,
        "school_id": db_class.school_id
    }

@router.get("/classes/{class_id}", response_model=class_schema.ClassDetail)
async def get_class(
    class_id: int, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取班级详情"""
    db_class = db.query(Class).filter(Class.id == class_id).first()
    if db_class is None:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 权限检查：
    # 1. 超级管理员可以查看所有班级
    # 2. 教师可以查看关联的班级
    # 3. 学生可以查看自己所在的班级
    if not current_user.is_admin:
        # 检查是否为该班级的教师
        teacher_association = db.query(ClassTeacher).filter(
            ClassTeacher.class_id == class_id,
            ClassTeacher.teacher_id == current_user.id
        ).first()
        
        # 检查是否为该班级的学生
        student_association = db.query(ClassStudent).filter(
            ClassStudent.class_id == class_id,
            ClassStudent.student_id == current_user.id
        ).first()
        
        if not teacher_association and not student_association:
            raise HTTPException(
                status_code=403,
                detail="您没有权限访问此班级信息"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限查看其他学校的班级"
            )
    
    # 获取班级学生
    students = []
    
    # 记录调试信息
    logger.info(f"获取班级 {class_id} 的学生列表")
    logger.info(f"班级学生关联数量: {len(db_class.students) if db_class.students else 0}")
    
    # 使用JOIN查询一次性获取所有学生
    try:
        # 先检查是否有student_id为NULL的记录
        null_students = db.query(ClassStudent).filter(
            ClassStudent.class_id == class_id,
            ClassStudent.student_id.is_(None)
        ).count()
        if null_students > 0:
            logger.warning(f"发现 {null_students} 条student_id为NULL的记录，这些记录将被忽略")
        
        # 只JOIN那些student_id不为NULL的记录
        class_students = db.query(ClassStudent, User).join(
            User, ClassStudent.student_id == User.id
        ).filter(
            ClassStudent.class_id == class_id,
            ClassStudent.student_id.isnot(None)  # 确保student_id不为NULL
        ).all()
        
        logger.info(f"JOIN查询结果数量: {len(class_students)}")
    except Exception as e:
        logger.error(f"JOIN查询失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        class_students = []
    
    for class_student, student in class_students:
        students.append({
            "id": student.id,
            "username": student.username,
            "full_name": student.full_name,
            "email": student.email
            # 移除对不存在的phone属性的访问
        })
    
    # 手动构建响应
    return {
        "id": db_class.id,
        "name": db_class.name,
        "description": db_class.description,
        "created_at": db_class.created_at,
        "students": students,
        "student_count": len(students),
        "grade": db_class.grade,
        "school_id": db_class.school_id
    }

@router.put("/classes/{class_id}", response_model=class_schema.Class)
async def update_class(
    class_id: int, 
    class_data: class_schema.ClassUpdate, 
    current_user: User = Depends(require_permission(Permission.MANAGE_CLASS)),
    db: Session = Depends(get_db)
):
    """更新班级信息"""
    db_class = db.query(Class).filter(Class.id == class_id).first()
    if db_class is None:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 如果是学校管理员，只能更新自己学校的班级
    if current_user.role == "principal" and not current_user.is_admin:
        if db_class.school_id != current_user.school_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限更新其他学校的班级"
            )
        # 不允许学校管理员更改班级所属学校
        if class_data.school_id is not None and class_data.school_id != current_user.school_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="不允许更改班级所属学校"
            )
    
    # 更新班级信息
    if class_data.name:
        db_class.name = class_data.name
    if class_data.description is not None:
        db_class.description = class_data.description
    if class_data.grade is not None:
        db_class.grade = class_data.grade
    if class_data.school_id is not None:
        db_class.school_id = class_data.school_id
    
    db.commit()
    db.refresh(db_class)
    
    # 手动构建响应，包含学生数量
    return {
        "id": db_class.id,
        "name": db_class.name,
        "description": db_class.description,
        "created_at": db_class.created_at,
        "student_count": len(db_class.students),
        "grade": db_class.grade,
        "school_id": db_class.school_id
    }

# 批量更新班级API
@router.put("/classes/batch", response_model=dict)
async def batch_update_classes(
    batch_data: dict, 
    current_user: User = Depends(get_admin_or_school_admin_user),
    db: Session = Depends(get_db)
):
    """批量更新班级信息"""
    # 验证请求数据
    if "class_ids" not in batch_data or not isinstance(batch_data["class_ids"], list):
        raise HTTPException(status_code=400, detail="请提供有效的班级ID列表")
    
    class_ids = batch_data.pop("class_ids")
    if not class_ids:
        raise HTTPException(status_code=400, detail="班级ID列表不能为空")
    
    # 检查是否有可更新的字段
    update_fields = {}
    if "name" in batch_data and batch_data["name"]:
        update_fields["name"] = batch_data["name"]
    if "description" in batch_data and batch_data["description"] is not None:
        update_fields["description"] = batch_data["description"]
    if "grade" in batch_data and batch_data["grade"] is not None:
        update_fields["grade"] = batch_data["grade"]
    
    if not update_fields:
        raise HTTPException(status_code=400, detail="没有提供可更新的字段")
    
    # 查询所有指定的班级
    classes = db.query(Class).filter(Class.id.in_(class_ids)).all()
    if not classes:
        raise HTTPException(status_code=404, detail="未找到指定的班级")
    
    # 检查权限
    if current_user.role == "principal" and not current_user.is_admin:
        # 学校管理员只能更新自己学校的班级
        for cls in classes:
            if cls.school_id != current_user.school_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"没有权限更新班级ID: {cls.id}，该班级不属于您的学校"
                )
    
    # 批量更新班级
    updated_count = 0
    for i, cls in enumerate(classes):
        # 如果提供了班级名称前缀，为每个班级添加编号
        if "name" in update_fields:
            if len(classes) > 1:
                cls.name = f"{update_fields['name']}({i+1})班"
            else:
                cls.name = update_fields['name']
        
        # 更新其他字段
        if "description" in update_fields:
            cls.description = update_fields["description"]
        if "grade" in update_fields:
            cls.grade = update_fields["grade"]
        
        updated_count += 1
    
    # 提交更改
    db.commit()
    
    return {
        "success": True,
        "message": f"成功更新 {updated_count} 个班级",
        "updated_count": updated_count
    }

@router.delete("/classes/{class_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_class(
    class_id: int, 
    current_user: User = Depends(require_permission(Permission.MANAGE_CLASS)),
    db: Session = Depends(get_db)
):
    """删除班级"""
    db_class = db.query(Class).filter(Class.id == class_id).first()
    if db_class is None:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 如果是学校管理员，只能删除自己学校的班级
    if current_user.role == "principal" and not current_user.is_admin:
        if db_class.school_id != current_user.school_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限删除其他学校的班级"
            )
    
    # 删除班级
    db.delete(db_class)
    db.commit()
    
    return

@router.post("/classes/{class_id}/students", response_model=class_schema.StudentBase)
async def add_student_to_class(
    class_id: int, 
    student_data: class_schema.AddStudentToClass, 
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """向班级添加学生"""
    try:
        # 检查班级是否存在
        db_class = db.query(Class).filter(Class.id == class_id).first()
        if db_class is None:
            raise HTTPException(status_code=404, detail="班级不存在")
        
        # 检查学生是否存在
        student = db.query(User).filter(User.id == student_data.student_id).first()
        if student is None:
            raise HTTPException(status_code=404, detail="学生不存在")
        
        # 检查学生是否已在班级中
        class_student = db.query(ClassStudent).filter(
            ClassStudent.class_id == class_id,
            ClassStudent.student_id == student_data.student_id
        ).first()
        
        if class_student:
            raise HTTPException(status_code=400, detail="该学生已在班级中")
        
        # 更新学生的class_id
        student.class_id = class_id

        # 创建班级学生关联
        new_class_student = ClassStudent(
            class_id=class_id,
            student_id=student_data.student_id
        )
        db.add(new_class_student)
        db.commit()
        
        logger.info(f"成功添加学生(ID:{student_data.student_id})到班级(ID:{class_id})")
        
        return {
            "id": student.id,
            "username": student.username,
            "full_name": student.full_name,
            "email": student.email
            # 移除对不存在的phone属性的访问
        }
    except HTTPException as http_ex:
        # 重新抛出HTTP异常
        raise http_ex
    except Exception as e:
        # 记录错误
        logger.error(f"添加学生到班级时发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        db.rollback()
        raise HTTPException(status_code=500, detail=f"添加学生失败: {str(e)}")

@router.post("/classes/{class_id}/students/create", response_model=class_schema.StudentBase)
async def create_student_for_class(
    class_id: int,
    student_data: class_schema.CreateStudentForClass,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """创建新学生并添加到班级"""
    try:
        # 检查班级是否存在
        db_class = db.query(Class).filter(Class.id == class_id).first()
        if db_class is None:
            raise HTTPException(status_code=404, detail="班级不存在")

        # 在多学校系统中，用户名允许重复，但邮箱必须唯一
        # 检查邮箱是否已存在（全系统唯一）
        if student_data.email:
            existing_email = db.query(User).filter(User.email == student_data.email).first()
            if existing_email:
                raise HTTPException(status_code=400, detail="邮箱已存在")

        # 检查同一学校内用户名是否重复（学校内唯一）
        existing_user_in_school = db.query(User).filter(
            User.username == student_data.username,
            User.school_id == db_class.school_id
        ).first()
        if existing_user_in_school:
            raise HTTPException(status_code=400, detail=f"学号'{student_data.username}'在该学校内已存在，请使用其他学号")

        # 创建新学生用户
        from ..utils.auth import get_password_hash
        hashed_password = get_password_hash(student_data.password or "123456")  # 默认密码

        new_student = User(
            username=student_data.username,
            full_name=student_data.full_name,
            email=student_data.email,
            hashed_password=hashed_password,
            role="学生",  # 使用中文角色
            is_active=True,
            school_id=db_class.school_id,  # 设置学生的学校ID为班级所属学校
            class_id=class_id  # 同时设置class_id
        )
        db.add(new_student)
        db.flush()  # 获取新创建用户的ID

        # 创建班级学生关联
        new_class_student = ClassStudent(
            class_id=class_id,
            student_id=new_student.id
        )
        db.add(new_class_student)
        db.commit()

        logger.info(f"成功创建学生({student_data.username})并添加到班级(ID:{class_id})")

        return {
            "id": new_student.id,
            "username": new_student.username,
            "full_name": new_student.full_name,
            "email": new_student.email
        }

    except HTTPException as http_ex:
        raise http_ex
    except Exception as e:
        logger.error(f"创建学生并添加到班级失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建学生并添加到班级失败: {str(e)}")

@router.delete("/classes/{class_id}/students/{student_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_student_from_class(
    class_id: int, 
    student_id: int,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """从班级中移除学生"""
    # 检查班级是否存在
    db_class = db.query(Class).filter(Class.id == class_id).first()
    if db_class is None:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 检查学生是否存在
    db_student = db.query(User).filter(User.id == student_id).first()
    if db_student is None:
        raise HTTPException(status_code=404, detail="学生不存在")
    
    # 检查学生是否在班级中
    class_student = db.query(ClassStudent).filter(
        ClassStudent.class_id == class_id,
        ClassStudent.student_id == student_id
    ).first()
    
    if not class_student:
        raise HTTPException(status_code=404, detail="该学生不在班级中")
    
    # 删除班级学生关联
    db.delete(class_student)
    db.commit()
    return {"detail": "已从班级移除学生"}

@router.put("/classes/{class_id}/students/{student_id}", response_model=user_schema.User)
async def update_student_in_class(
    class_id: int, 
    student_id: int,
    student_data: user_schema.UserUpdate,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """更新班级中学生的信息"""
    # 验证班级是否存在
    db_class = db.query(Class).filter(Class.id == class_id).first()
    if db_class is None:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 验证学生是否存在
    db_student = db.query(User).filter(User.id == student_id).first()
    if db_student is None:
        raise HTTPException(status_code=404, detail="学生不存在")
    
    # 检查学生是否在班级中
    class_student_relation = db.query(ClassStudent).filter(
        ClassStudent.class_id == class_id,
        ClassStudent.student_id == student_id
    ).first()
    
    if not class_student_relation:
        raise HTTPException(status_code=400, detail="该学生不在班级中")
    
    # 更新学生信息
    if student_data.username:
        # 检查用户名是否已存在
        existing_user = db.query(User).filter(User.username == student_data.username).first()
        if existing_user and existing_user.id != student_id:
            raise HTTPException(status_code=400, detail="用户名已存在")
        db_student.username = student_data.username
    
    if student_data.email:
        # 检查邮箱是否已存在
        existing_user = db.query(User).filter(User.email == student_data.email).first()
        if existing_user and existing_user.id != student_id:
            raise HTTPException(status_code=400, detail="邮箱已存在")
        db_student.email = student_data.email
        
    if student_data.full_name is not None:
        db_student.full_name = student_data.full_name
        
    if student_data.phone is not None:
        db_student.phone = student_data.phone
        
    # 如果提供了密码，则更新密码
    if student_data.password:
        db_student.hashed_password = get_password_hash(student_data.password)
    
    db.commit()
    db.refresh(db_student)
    return db_student

# 班级教师API
@router.get("/classes/{class_id}/teachers")
async def get_class_teachers(
    class_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取班级的教师列表"""
    # 检查班级是否存在
    db_class = db.query(Class).filter(Class.id == class_id).first()
    if not db_class:
        raise HTTPException(status_code=404, detail="班级不存在")

    # 权限检查
    if not current_user.is_admin:
        # 检查是否为该班级的教师
        teacher_association = db.query(ClassTeacher).filter(
            ClassTeacher.class_id == class_id,
            ClassTeacher.teacher_id == current_user.id
        ).first()
        
        # 检查是否为该班级的学生
        student_association = db.query(ClassStudent).filter(
            ClassStudent.class_id == class_id,
            ClassStudent.student_id == current_user.id
        ).first()
        
        if not teacher_association and not student_association:
            raise HTTPException(
                status_code=403,
                detail="您没有权限访问此班级的教师信息"
            )

    # 获取班级的所有教师
    teachers = db.query(User).join(
        ClassTeacher,
        and_(
            ClassTeacher.teacher_id == User.id,
            ClassTeacher.class_id == class_id
        )
    ).all()

    result = []
    for teacher in teachers:
        result.append({
            "id": teacher.id,
            "username": teacher.username,
            "email": teacher.email,
            "full_name": teacher.full_name
        })

    return result

# 班级家长API
@router.get("/classes/{class_id}/parents")
async def get_class_parents(
    class_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取班级的家长列表"""
    # 检查班级是否存在
    db_class = db.query(Class).filter(Class.id == class_id).first()
    if not db_class:
        raise HTTPException(status_code=404, detail="班级不存在")

    # 权限检查
    if not current_user.is_admin:
        # 检查是否为该班级的教师
        teacher_association = db.query(ClassTeacher).filter(
            ClassTeacher.class_id == class_id,
            ClassTeacher.teacher_id == current_user.id
        ).first()
        
        if not teacher_association:
            raise HTTPException(
                status_code=403,
                detail="您没有权限访问此班级的家长信息"
            )

    # 获取班级学生的所有家长
    parents = db.query(User).join(
        ParentStudent,
        ParentStudent.parent_id == User.id
    ).join(
        ClassStudent,
        and_(
            ClassStudent.student_id == ParentStudent.student_id,
            ClassStudent.class_id == class_id
        )
    ).all()

    result = []
    for parent in parents:
        result.append({
            "id": parent.id,
            "username": parent.username,
            "email": parent.email,
            "full_name": parent.full_name
        })

    return result

# 系统信息API
@router.get("/system-info")
async def get_system_info(admin: User = Depends(get_admin_user), db: Session = Depends(get_db)):
    user_count = db.query(User).count()
    student_count = db.query(User).filter(User.is_teacher == False, User.is_admin == False).count()
    teacher_count = db.query(User).filter(User.is_teacher == True).count()
    admin_count = db.query(User).filter(User.is_admin == True).count()
    ai_config_count = db.query(AIModelConfig).count()
    class_count = db.query(Class).count()
    
    return {
        "user_count": user_count,
        "student_count": student_count,
        "teacher_count": teacher_count,
        "admin_count": admin_count,
        "ai_config_count": ai_config_count,
        "class_count": class_count
    }

@router.get("/test", status_code=status.HTTP_200_OK)
async def test_endpoint():
    """测试API是否正常工作的简单端点"""
    return {"message": "API测试成功", "status": "success"}

# 科目分类管理API
@router.get("/subject-categories", response_model=subject_schema.SubjectCategoryListResponse)
async def get_subject_categories(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """获取所有科目分类"""
    try:
        logger.info("获取科目分类列表")
        query = db.query(SubjectCategory)
        total = query.count()
        categories = query.offset(skip).limit(limit).all()
        
        return {
            "total": total,
            "items": categories
        }
    except Exception as e:
        logger.error(f"获取科目分类列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取科目分类列表失败: {str(e)}")

@router.post("/subject-categories", response_model=subject_schema.SubjectCategory)
async def create_subject_category(
    category: subject_schema.SubjectCategoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """创建科目分类"""
    try:
        # 检查分类名称是否已存在
        existing_category = db.query(SubjectCategory).filter(SubjectCategory.name == category.name).first()
        if existing_category:
            raise HTTPException(status_code=400, detail="分类名称已存在")
        
        # 创建新分类
        db_category = SubjectCategory(
            name=category.name,
            description=category.description,
            order=category.order,
            is_active=category.is_active
        )
        db.add(db_category)
        db.commit()
        db.refresh(db_category)
        logger.info(f"创建科目分类成功: {category.name}")
        return db_category
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建科目分类失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建科目分类失败: {str(e)}")

@router.get("/subject-categories/{category_id}", response_model=subject_schema.SubjectCategory)
async def get_subject_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """获取指定科目分类"""
    category = db.query(SubjectCategory).filter(SubjectCategory.id == category_id).first()
    if category is None:
        raise HTTPException(status_code=404, detail="科目分类不存在")
    return category

@router.put("/subject-categories/{category_id}", response_model=subject_schema.SubjectCategory)
async def update_subject_category(
    category_id: int,
    category_data: subject_schema.SubjectCategoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """更新科目分类"""
    try:
        # 获取要更新的分类
        db_category = db.query(SubjectCategory).filter(SubjectCategory.id == category_id).first()
        if db_category is None:
            raise HTTPException(status_code=404, detail="科目分类不存在")
        
        # 如果更新名称，检查是否与其他分类重名
        if category_data.name is not None and category_data.name != db_category.name:
            existing_category = db.query(SubjectCategory).filter(SubjectCategory.name == category_data.name).first()
            if existing_category and existing_category.id != category_id:
                raise HTTPException(status_code=400, detail="分类名称已存在")
            db_category.name = category_data.name
        
        # 更新其他字段
        if category_data.description is not None:
            db_category.description = category_data.description
        if category_data.order is not None:
            db_category.order = category_data.order
        if category_data.is_active is not None:
            db_category.is_active = category_data.is_active
        
        db.commit()
        db.refresh(db_category)
        logger.info(f"更新科目分类成功: ID={category_id}")
        return db_category
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新科目分类失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新科目分类失败: {str(e)}")

@router.delete("/subject-categories/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_subject_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """删除科目分类"""
    try:
        # 获取要删除的分类
        db_category = db.query(SubjectCategory).filter(SubjectCategory.id == category_id).first()
        if db_category is None:
            raise HTTPException(status_code=404, detail="科目分类不存在")
        
        # 检查分类下是否有科目
        subjects_count = db.query(Subject).filter(Subject.category_id == category_id).count()
        if subjects_count > 0:
            raise HTTPException(status_code=400, detail=f"无法删除该分类，该分类下有 {subjects_count} 个科目")
        
        # 删除分类
        db.delete(db_category)
        db.commit()
        logger.info(f"删除科目分类成功: ID={category_id}")
        return {"detail": "科目分类已删除"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除科目分类失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除科目分类失败: {str(e)}")

# 更新科目管理API，支持分类和年级关联
@router.get("/subjects", response_model=subject_schema.SubjectListResponse)
async def get_all_subjects(
    skip: int = 0,
    limit: int = 100,
    category_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有科目，可按分类筛选"""
    try:
        logger.info("获取科目列表")
        query = db.query(Subject).options(joinedload(Subject.category))

        # 按分类筛选
        if category_id is not None:
            query = query.filter(Subject.category_id == category_id)

        total = query.count()
        subjects = query.offset(skip).limit(limit).all()
        
        return {
            "total": total,
            "items": subjects
        }
    except Exception as e:
        logger.error(f"获取科目列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取科目列表失败: {str(e)}")

@router.post("/subjects", response_model=subject_schema.SubjectDetail)
async def create_subject(
    subject: subject_schema.SubjectCreate, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """创建科目，支持同时关联年级"""
    try:
        # 检查科目名称是否已存在
        existing_subject = db.query(Subject).filter(Subject.name == subject.name).first()
        if existing_subject:
            raise HTTPException(status_code=400, detail="科目名称已存在")
        
        # 如果指定了分类，检查分类是否存在
        if subject.category_id:
            category = db.query(SubjectCategory).filter(SubjectCategory.id == subject.category_id).first()
            if not category:
                raise HTTPException(status_code=404, detail="指定的科目分类不存在")
        
        # 创建新科目
        db_subject = Subject(
            name=subject.name,
            pattern=subject.pattern,
            description=subject.description,
            icon=subject.icon,
            color=subject.color,
            order=subject.order,
            is_active=subject.is_active,
            category_id=subject.category_id
        )
        db.add(db_subject)
        db.flush()  # 获取科目ID，但不提交事务
        
        # 添加年级关联
        if subject.grades:
            for grade_data in subject.grades:
                grade_subject = GradeSubject(
                    grade=grade_data.grade,
                    subject_id=db_subject.id,
                    is_required=grade_data.is_required,
                    order=grade_data.order
                )
                db.add(grade_subject)
        
        db.commit()
        db.refresh(db_subject)
        logger.info(f"创建科目成功: {subject.name}")
        return db_subject
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建科目失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建科目失败: {str(e)}")

@router.get("/subjects/{subject_id}", response_model=subject_schema.SubjectDetail)
async def get_subject(
    subject_id: int, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_or_school_admin_user)
):
    """获取科目详情，包括分类和年级关联"""
    subject = db.query(Subject).filter(Subject.id == subject_id).first()
    if subject is None:
        raise HTTPException(status_code=404, detail="科目不存在")
    return subject

@router.put("/subjects/{subject_id}", response_model=subject_schema.SubjectDetail)
async def update_subject(
    subject_id: int, 
    subject_data: subject_schema.SubjectUpdate, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """更新科目信息，支持更新年级关联"""
    try:
        # 获取要更新的科目
        db_subject = db.query(Subject).filter(Subject.id == subject_id).first()
        if db_subject is None:
            raise HTTPException(status_code=404, detail="科目不存在")
        
        # 如果更新名称，检查是否与其他科目重名
        if subject_data.name is not None and subject_data.name != db_subject.name:
            existing_subject = db.query(Subject).filter(Subject.name == subject_data.name).first()
            if existing_subject and existing_subject.id != subject_id:
                raise HTTPException(status_code=400, detail="科目名称已存在")
            db_subject.name = subject_data.name
        
        # 如果更新分类，检查分类是否存在
        if subject_data.category_id is not None:
            if subject_data.category_id > 0:
                category = db.query(SubjectCategory).filter(SubjectCategory.id == subject_data.category_id).first()
                if not category:
                    raise HTTPException(status_code=404, detail="指定的科目分类不存在")
            db_subject.category_id = subject_data.category_id
        
        # 更新其他字段
        if subject_data.pattern is not None:
            db_subject.pattern = subject_data.pattern
        if subject_data.description is not None:
            db_subject.description = subject_data.description
        if subject_data.icon is not None:
            db_subject.icon = subject_data.icon
        if subject_data.color is not None:
            db_subject.color = subject_data.color
        if subject_data.order is not None:
            db_subject.order = subject_data.order
        if subject_data.is_active is not None:
            db_subject.is_active = subject_data.is_active
        
        # 更新年级关联
        if subject_data.grades is not None:
            # 删除现有的年级关联
            db.query(GradeSubject).filter(GradeSubject.subject_id == subject_id).delete()
            
            # 添加新的年级关联
            for grade_data in subject_data.grades:
                grade_subject = GradeSubject(
                    grade=grade_data.grade,
                    subject_id=subject_id,
                    is_required=grade_data.is_required,
                    order=grade_data.order
                )
                db.add(grade_subject)
        
        db.commit()
        db.refresh(db_subject)
        logger.info(f"更新科目成功: ID={subject_id}")
        return db_subject
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新科目失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新科目失败: {str(e)}")

@router.get("/subjects/by-grade/{grade}", response_model=List[subject_schema.Subject])
async def get_subjects_by_grade(
    grade: str,
    db: Session = Depends(get_db)
):
    """获取指定年级的科目列表"""
    try:
        # 查询与指定年级关联的所有科目
        subjects = db.query(Subject).join(
            GradeSubject, Subject.id == GradeSubject.subject_id
        ).filter(
            GradeSubject.grade == grade,
            Subject.is_active == True
        ).order_by(GradeSubject.order).all()
        
        return subjects
    except Exception as e:
        logger.error(f"获取年级科目列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取年级科目列表失败: {str(e)}")

@router.get("/grades", response_model=List[str])
async def get_all_grades(
    db: Session = Depends(get_db)
):
    """获取系统中所有的年级列表"""
    try:
        # 查询所有不重复的年级
        grades = db.query(GradeSubject.grade).distinct().all()
        return [grade[0] for grade in grades]
    except Exception as e:
        logger.error(f"获取年级列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取年级列表失败: {str(e)}")

# 学校详情API
@router.get("/schools/{school_id}", response_model=school_schema.SchoolDetail)
async def get_school_detail(
    school_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_or_school_admin_user)
):
    """获取学校详情"""
    # 检查权限
    if not current_user.is_admin and current_user.school_id != school_id:
        raise HTTPException(status_code=403, detail="没有权限访问该学校信息")
    
    # 修正：从School模型获取学校数据
    from ..models.school import School
    school = db.query(School).filter(School.id == school_id).first()
    if school is None:
        raise HTTPException(status_code=404, detail="学校不存在")
    
    return school

# 更新学校信息API
@router.put("/schools/{school_id}", response_model=school_schema.SchoolDetail)
async def update_school_info(
    school_id: int,
    school_data: school_schema.SchoolUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_or_school_admin_user)
):
    """更新学校信息"""
    # 检查权限
    if not current_user.is_admin and current_user.school_id != school_id:
        raise HTTPException(status_code=403, detail="没有权限更新该学校信息")
    
    # 检查是否有权限编辑学校
    if not current_user.is_admin:
        # 检查用户是否有编辑学校的权限
        has_permission = check_user_permission(db, current_user.id, "edit_school", school_id)
        if not has_permission:
            raise HTTPException(status_code=403, detail="没有编辑学校的权限")
    
    # 修正：从School模型获取学校数据
    from ..models.school import School
    school = db.query(School).filter(School.id == school_id).first()
    if school is None:
        raise HTTPException(status_code=404, detail="学校不存在")
    
    # 更新学校信息
    for key, value in school_data.dict(exclude_unset=True).items():
        setattr(school, key, value)
    
    db.commit()
    db.refresh(school)
    return school

# 获取学校班级列表API
@router.get("/schools/{school_id}/classes", response_model=List[school_schema.ClassWithStudentCount])
async def get_school_classes(
    school_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_or_school_admin_user)
):
    """获取学校班级列表"""
    # 检查权限
    if not current_user.is_admin and current_user.school_id != school_id:
        raise HTTPException(status_code=403, detail="没有权限访问该学校班级")
    
    # 查询学校班级
    classes = db.query(Class).filter(Class.school_id == school_id).all()
    
    # 计算每个班级的学生数量
    result = []
    for cls in classes:
        try:
            # 使用ClassStudent关联表查询学生数量
            student_count = db.query(ClassStudent).filter(
                ClassStudent.class_id == cls.id
            ).count()
            
            class_data = school_schema.ClassWithStudentCount(
                id=cls.id,
                name=cls.name,
                grade=cls.grade or "",  # 确保grade不为None
                school_id=cls.school_id,
                student_count=student_count,
                created_at=cls.created_at
            )
            result.append(class_data)
        except Exception as e:
            # 记录错误但继续处理其他班级
            print(f"处理班级 {cls.id} 时出错: {str(e)}")
            continue
    
    return result

# 获取学校用户列表API
@router.get("/schools/{school_id}/users", response_model=List[school_schema.UserWithRoles])
async def get_school_users(
    school_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_or_school_admin_user)
):
    """获取学校用户列表"""
    # 检查权限
    if not current_user.is_admin and current_user.school_id != school_id:
        raise HTTPException(status_code=403, detail="没有权限访问该学校用户")
    

    
    # 查询学校用户
    users = db.query(User).filter(User.school_id == school_id).all()
    
    # 获取用户角色信息
    result = []
    for user in users:
        # 获取主要角色
        primary_role = None
        if hasattr(user, 'primary_role_id') and user.primary_role_id:
            primary_role = db.query(User).filter(User.id == user.primary_role_id).first() # Assuming User model is used for role
        
        # 获取所有角色
        roles = []
        if hasattr(user, 'role') and user.role:
            role = db.query(User).filter(User.id == user.role).first()
            if role:
                roles.append(role)
        
        # 创建用户数据对象
        user_data = school_schema.UserWithRoles(
            id=user.id,
            username=user.username,
            email=user.email,
            full_name=user.full_name if hasattr(user, 'full_name') else None,
            is_active=user.is_active,
            is_admin=user.is_admin,
            is_teacher=hasattr(user, 'is_teacher') and user.is_teacher,
            school_id=user.school_id,
            class_id=getattr(user, 'class_id', None),
            primary_role=primary_role,
            roles=roles
        )
        result.append(user_data)
    
    return result

# 获取角色列表API
@router.get("/roles", response_model=List[school_schema.Role])
async def get_roles(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_or_school_admin_user)
):
    """获取角色列表"""
    # 超级管理员可以看到所有角色
    if current_user.is_admin:
        roles = db.query(Role).all()
    else:
        # 非超级管理员只能看到学校级别及以下的角色
        roles = db.query(Role).filter(Role.level < 90).all()

    return roles

# 分配角色API
@router.post("/roles/assign", response_model=school_schema.UserRole)
async def assign_user_role(
    role_data: school_schema.UserRoleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_or_school_admin_user)
):
    """分配用户角色"""
    # 检查权限
    if not current_user.is_admin and (current_user.school_id != role_data.school_id):
        raise HTTPException(status_code=403, detail="没有权限分配该学校的角色")
    
    # 检查是否有分配角色的权限
    if not current_user.is_admin:
        has_permission = check_user_permission(db, current_user.id, "assign_role", role_data.school_id)
        if not has_permission:
            raise HTTPException(status_code=403, detail="没有分配角色的权限")
    
    # 检查用户是否存在
    user = db.query(User).filter(User.id == role_data.user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 检查角色是否存在
    role = db.query(User).filter(User.id == role_data.role_id).first() # Assuming User model is used for role
    if role is None:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    # 检查是否已经分配了该角色
    existing_role = db.query(UserRole).filter(
        UserRole.user_id == role_data.user_id,
        UserRole.role_id == role_data.role_id,
        UserRole.school_id == role_data.school_id
    ).first()
    
    if existing_role:
        raise HTTPException(status_code=400, detail="用户已经拥有该角色")
    
    # 创建用户角色关联
    user_role = UserRole(
        user_id=role_data.user_id,
        role_id=role_data.role_id,
        school_id=role_data.school_id,
        grade_id=role_data.grade_id,
        subject_id=role_data.subject_id,
        class_id=role_data.class_id
    )
    
    db.add(user_role)
    db.commit()
    db.refresh(user_role)
    
    # 如果用户没有主要角色，将此角色设为主要角色
    if not user.primary_role_id:
        user.primary_role_id = role_data.role_id
        db.commit()
    
    return user_role

# 撤销用户角色API
@router.post("/roles/revoke", response_model=school_schema.Message)
async def revoke_user_role(
    role_data: school_schema.UserRoleDelete,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_or_school_admin_user)
):
    """撤销用户角色"""
    # 检查权限
    if not current_user.is_admin and (current_user.school_id != role_data.school_id):
        raise HTTPException(status_code=403, detail="没有权限撤销该学校的角色")
    
    # 检查是否有撤销角色的权限
    if not current_user.is_admin:
        has_permission = check_user_permission(db, current_user.id, "revoke_role", role_data.school_id)
        if not has_permission:
            raise HTTPException(status_code=403, detail="没有撤销角色的权限")
    
    # 查找用户角色关联
    user_role = db.query(UserRole).filter(
        UserRole.user_id == role_data.user_id,
        UserRole.role_id == role_data.role_id,
        UserRole.school_id == role_data.school_id
    ).first()
    
    if user_role is None:
        raise HTTPException(status_code=404, detail="用户没有该角色")
    
    # 删除用户角色关联
    db.delete(user_role)
    
    # 如果撤销的是用户的主要角色，需要重新设置主要角色
    user = db.query(User).filter(User.id == role_data.user_id).first()
    if user and user.primary_role_id == role_data.role_id:
        # 查找用户的其他角色
        other_role = db.query(UserRole).filter(
            UserRole.user_id == role_data.user_id
        ).first()
        
        if other_role:
            user.primary_role_id = other_role.role_id
        else:
            user.primary_role_id = None
    
    db.commit()
    
    return {"message": "角色撤销成功"}

# 检查用户权限的辅助函数
def check_user_permission(db: Session, user_id: int, permission_code: str, school_id: int = None):
    """检查用户是否有指定权限"""
    # 查询用户角色
    user_roles = db.query(UserRole).filter(UserRole.user_id == user_id)
    if school_id:
        user_roles = user_roles.filter(UserRole.school_id == school_id)
    
    user_roles = user_roles.all()
    
    # 查询权限ID
    permission = db.query(Permission).filter(Permission.code == permission_code).first()
    if permission is None:
        return False
    
    # 检查角色是否有该权限
    for user_role in user_roles:
        role = db.query(Role).filter(Role.id == user_role.role_id).first()
        if role and permission in role.permissions:
            return True
    
    return False

# 处理OPTIONS预检请求
@router.options("/schools/{school_id}/classes", include_in_schema=False)
async def options_school_classes(school_id: int):
    return {"detail": "OK"}

@router.options("/roles", include_in_schema=False)
async def options_roles():
    return {"detail": "OK"}

@router.options("/schools/{school_id}/users", include_in_schema=False)
async def options_school_users(school_id: int):
    return {"detail": "OK"}

# 添加获取所有学校列表的API
@router.get("/schools", response_model=List[school_schema.SchoolResponse])
async def get_all_schools(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_admin_or_school_admin_user),
    db: Session = Depends(get_db)
):
    """获取所有学校列表"""
    # 超级管理员可以看到所有学校
    if current_user.is_admin:
        schools = db.query(School).offset(skip).limit(limit).all()
    # 学校管理员只能看到自己的学校
    else:
        # 确保用户有关联的学校
        if not current_user.school_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="学校管理员必须关联到一个学校"
            )
        schools = db.query(School).filter(School.id == current_user.school_id).all()

    # 构建响应数据，包含统计信息
    result = []
    for school in schools:
        # 查询统计数据
        class_count = db.query(func.count(Class.id)).filter(Class.school_id == school.id).scalar()
        teacher_count = db.query(func.count(User.id)).filter(
            User.school_id == school.id,
            User.is_teacher == True
        ).scalar()
        student_count = db.query(func.count(User.id)).filter(
            User.school_id == school.id,
            User.is_teacher == False,
            User.is_admin == False
        ).scalar()

        # 构建响应数据
        school_response = school_schema.SchoolResponse(
            id=school.id,
            name=school.name,
            province=school.province,
            city=school.city,
            district=school.district,
            address=school.address,
            contact_info=school.contact_info,
            is_active=school.is_active,
            created_at=school.created_at,
            class_count=class_count or 0,
            teacher_count=teacher_count or 0,
            student_count=student_count or 0
        )
        result.append(school_response)

    return result

# 分级选择相关API（公开访问，用于登录和注册页面）
@router.get("/schools/provinces")
async def get_provinces(db: Session = Depends(get_db)):
    """获取所有省份列表（公开API，用于登录和注册页面）"""
    try:
        provinces = db.query(School.province).distinct().all()
        return [{"value": p[0], "label": p[0]} for p in provinces if p[0]]
    except Exception as e:
        logger.error(f"获取省份列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取省份列表失败: {str(e)}")

@router.get("/schools/cities")
async def get_cities(province: str, db: Session = Depends(get_db)):
    """根据省份获取城市列表（用于登录页面）"""
    try:
        cities = db.query(School.city).filter(School.province == province).distinct().all()
        return [{"value": c[0], "label": c[0]} for c in cities if c[0]]
    except Exception as e:
        logger.error(f"获取城市列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取城市列表失败: {str(e)}")

@router.get("/schools/districts")
async def get_districts(province: str, city: str, db: Session = Depends(get_db)):
    """根据省份和城市获取区县列表（用于登录页面）"""
    try:
        districts = db.query(School.district).filter(
            School.province == province,
            School.city == city
        ).distinct().all()
        return [{"value": d[0], "label": d[0]} for d in districts if d[0]]
    except Exception as e:
        logger.error(f"获取区县列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取区县列表失败: {str(e)}")

@router.get("/schools/by-location")
async def get_schools_by_location(
    province: str,
    city: str,
    district: str = None,
    db: Session = Depends(get_db)
):
    """根据地理位置获取学校列表（用于登录页面）"""
    try:
        query = db.query(School).filter(
            School.province == province,
            School.city == city,
            School.is_active == True
        )
        if district:
            query = query.filter(School.district == district)

        schools = query.all()
        return [{"value": s.id, "label": s.name, "address": s.address} for s in schools]
    except Exception as e:
        logger.error(f"获取学校列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取学校列表失败: {str(e)}")

@router.get("/schools/search")
async def search_schools(q: str, db: Session = Depends(get_db)):
    """搜索学校（用于登录页面）"""
    try:
        schools = db.query(School).filter(
            School.name.contains(q),
            School.is_active == True
        ).limit(20).all()
        return [{
            "value": s.id,
            "label": s.name,
            "province": s.province,
            "city": s.city,
            "district": s.district,
            "address": s.address
        } for s in schools]
    except Exception as e:
        logger.error(f"搜索学校失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索学校失败: {str(e)}")

@router.get("/query")
async def execute_query(
    query: str,
    current_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """执行SQL查询（仅限管理员使用）"""
    try:
        logger.info(f"管理员 {current_user.username} 执行查询: {query}")
        
        # 检查是否为SELECT查询以防止修改数据
        if not query.strip().lower().startswith('select'):
            logger.warning(f"尝试执行非SELECT查询: {query}")
            raise HTTPException(status_code=403, detail="只允许执行SELECT查询")

        # 添加缓存，如果是查询班级数量，直接返回缓存的结果
        if "count" in query.lower() and "classes" in query.lower() and "school_id" in query.lower():
            school_id_match = re.search(r'school_id\s*=\s*(\d+)', query)
            if school_id_match:
                school_id = int(school_id_match.group(1))
                logger.info(f"检测到班级计数查询，学校ID: {school_id}")
                
                # 使用ORM查询获取班级计数
                class_count = db.query(Class).filter(Class.school_id == school_id).count()
                logger.info(f"学校ID={school_id}的班级总数: {class_count}")
                
                # 返回查询结果，确保结果格式一致
                return {"result": [{"count": class_count}]}
        
        # 执行原始SQL查询
        result = db.execute(query)
        
        # 处理查询结果
        if result.returns_rows:
            columns = result.keys()
            rows = []
            for row in result:
                # 将每行转换为字典
                row_dict = {}
                for i, column in enumerate(columns):
                    row_dict[column] = row[i]
                rows.append(row_dict)
            
            return {"result": rows}
        else:
            return {"message": "查询执行成功，但没有返回数据"}
    except Exception as e:
        logger.error(f"执行查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询执行失败: {str(e)}")

@router.options("/schools", include_in_schema=False)
async def options_schools():
    return {"detail": "OK"}

# 添加班级详情API的OPTIONS路由处理
@router.options("/classes/{class_id}", include_in_schema=False)
async def options_class_detail(class_id: int):
    """
    处理班级详情API的OPTIONS预检请求
    """
    return {"detail": "OK"}

# 添加班级学生API的OPTIONS路由处理
@router.options("/classes/{class_id}/students", include_in_schema=False)
async def options_class_students(class_id: int):
    """
    处理班级学生API的OPTIONS预检请求
    """
    return {"detail": "OK"}

# 添加更多班级相关API的OPTIONS路由处理
@router.options("/classes", include_in_schema=False)
async def options_classes():
    """
    处理班级列表API的OPTIONS预检请求
    """
    return {"detail": "OK"}

# 处理OPTIONS预检请求
@router.options("/schools/{school_id}/classes", include_in_schema=False)
async def options_school_classes(school_id: int):
    return {"detail": "OK"}

@router.options("/roles", include_in_schema=False)
async def options_roles():
    return {"detail": "OK"}

@router.options("/schools/{school_id}/users", include_in_schema=False)
async def options_school_users(school_id: int):
    return {"detail": "OK"}

# 班级教师管理API
@router.get("/classes/{class_id}/teachers")
async def get_class_teachers(
    class_id: int,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取班级教师列表"""
    try:
        # 检查班级是否存在
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail="班级不存在")

        # 权限检查：如果不是超级管理员，只能访问自己学校的班级
        if not (admin.is_admin or admin.role in ['超级管理员', 'admin']) and admin.school_id != class_obj.school_id:
            raise HTTPException(status_code=403, detail="无权访问其他学校的班级数据")

        # 获取班级教师关联
        class_teachers = db.query(ClassTeacher).filter(ClassTeacher.class_id == class_id).all()

        # 构建返回数据
        teachers_data = []
        for ct in class_teachers:
            teacher = db.query(User).filter(User.id == ct.teacher_id).first()
            if teacher:
                # 获取教师的科目信息
                subject_name = None
                role_id = None
                
                # 从教师的基本信息获取科目
                if teacher.subject:
                    subject_name = teacher.subject
                
                # 从教师的基本信息获取角色
                role_name = teacher.role or "subject_teacher"

                teachers_data.append({
                    "id": teacher.id,
                    "username": teacher.username,
                    "name": teacher.full_name or teacher.username,
                    "subject": subject_name,
                    "phone": teacher.phone,
                    "email": teacher.email,
                    "role": role_name or "subject_teacher"  # 默认角色
                })

        return teachers_data
    except Exception as e:
        logger.error(f"获取班级教师列表失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取班级教师列表失败: {str(e)}")

@router.post("/classes/{class_id}/teachers")
async def create_class_teacher(
    class_id: int,
    teacher_data: dict,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """为班级添加教师"""
    try:
        logger.info(f"📝 添加教师到班级 {class_id}, 数据: {teacher_data}")
        logger.info(f"👤 当前用户: {admin.username}, 角色: {admin.role}, is_admin: {admin.is_admin}")
        # 检查班级是否存在
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail="班级不存在")

        # 创建或获取教师用户
        teacher_name = teacher_data.get('teacher_name') or teacher_data.get('name')
        username = teacher_data.get('username')
        password = teacher_data.get('password')
        teacher_email = teacher_data.get('teacher_email') or teacher_data.get('email')
        teacher_phone = teacher_data.get('teacher_phone') or teacher_data.get('phone')
        subject_name = teacher_data.get('subject')

        # 查找科目
        subject = None
        if subject_name:
            subject = db.query(Subject).filter(Subject.name == subject_name).first()
            if not subject:
                raise HTTPException(status_code=400, detail=f"科目 '{subject_name}' 不存在")

        # 检查是否已存在该教师（通过用户名或邮箱）
        existing_teacher = None
        if username:
            # 多学校系统规则：同一学校内用户名不能重复，不同学校可以重复
            same_school_user = db.query(User).filter(
                User.username == username,
                User.school_id == class_obj.school_id
            ).first()

            if same_school_user:
                # 同一学校内所有角色的用户名都必须唯一，不允许重复
                role_name = same_school_user.role or "用户"
                raise HTTPException(status_code=400, detail=f"用户名 '{username}' 在本校已被{role_name}使用，请使用其他用户名")
            # 如果没有同校用户，允许创建（即使其他学校有相同用户名）

        # 如果通过用户名没找到，再通过邮箱查找
        if not existing_teacher and teacher_email:
            existing_email_user = db.query(User).filter(User.email == teacher_email).first()
            if existing_email_user:
                if existing_email_user.is_teacher:
                    existing_teacher = existing_email_user  # 如果是教师，可以使用
                else:
                    raise HTTPException(status_code=400, detail=f"邮箱 '{teacher_email}' 已被非教师用户使用")

        if not existing_teacher and teacher_name:
            # 使用提供的用户名或生成用户名
            if not username:
                import random
                username = f"{teacher_name}_{random.randint(1000, 9999)}"
                while db.query(User).filter(User.username == username).first():
                    username = f"{teacher_name}_{random.randint(1000, 9999)}"
            # 在多学校系统中，用户名允许重复，不需要检查用户名冲突
            # else:
            #     # 如果提供了用户名，再次检查是否与现有用户冲突
            #     existing_username_user = db.query(User).filter(User.username == username).first()
            #     if existing_username_user:
            #         raise HTTPException(status_code=400, detail=f"用户名 '{username}' 已存在")

            # 如果没有提供邮箱，生成一个默认邮箱
            if not teacher_email:
                teacher_email = generate_safe_email(username, "school.local")
            else:
                # 邮箱必须全系统唯一
                existing_email_user = db.query(User).filter(User.email == teacher_email).first()
                if existing_email_user:
                    raise HTTPException(status_code=400, detail=f"邮箱 '{teacher_email}' 已存在")

            # 使用提供的密码或默认密码
            if not password:
                password = "123456"  # 默认密码
            hashed_password = get_password_hash(password)
            new_teacher = User(
                username=username,
                email=teacher_email,
                hashed_password=hashed_password,
                full_name=teacher_name,
                phone=teacher_phone,
                is_teacher=True,
                is_admin=False,
                school_id=class_obj.school_id,
                subject=subject_name,  # 设置科目
                role=teacher_data.get('role', 'subject_teacher')  # 设置角色
            )
            db.add(new_teacher)
            db.commit()
            db.refresh(new_teacher)
            teacher = new_teacher
        else:
            teacher = existing_teacher

        if not teacher:
            raise HTTPException(status_code=400, detail="无法创建或找到教师用户")

        # 检查是否已经是该班级的教师
        existing_class_teacher = db.query(ClassTeacher).filter(
            ClassTeacher.class_id == class_id,
            ClassTeacher.teacher_id == teacher.id
        ).first()

        if not existing_class_teacher:
            # 添加班级教师关联
            class_teacher = ClassTeacher(
                class_id=class_id,
                teacher_id=teacher.id
            )
            db.add(class_teacher)

        # 创建或更新用户角色，包含科目信息
        role_name = teacher_data.get('role', 'subject_teacher')

        # 查找或创建角色
        role = db.query(Role).filter(Role.code == role_name).first()
        if not role:
            # 创建默认角色
            role = Role(
                name="任课教师" if role_name == "subject_teacher" else "班主任",
                code=role_name,
                description=f"系统自动创建的{role_name}角色"
            )
            db.add(role)
            db.commit()
            db.refresh(role)

        # 检查是否已有该角色
        existing_user_role = db.execute(
            text("SELECT * FROM user_roles WHERE user_id = :user_id AND role_id = :role_id AND class_id = :class_id"),
            {"user_id": teacher.id, "role_id": role.id, "class_id": class_id}
        ).fetchone()

        if not existing_user_role:
            # 插入新的用户角色记录
            db.execute(
                text("INSERT INTO user_roles (user_id, role_id, school_id, subject_id, class_id) VALUES (:user_id, :role_id, :school_id, :subject_id, :class_id)"),
                {"user_id": teacher.id, "role_id": role.id, "school_id": class_obj.school_id, "subject_id": subject.id if subject else None, "class_id": class_id}
            )
        else:
            # 更新科目信息
            db.execute(
                text("UPDATE user_roles SET subject_id = :subject_id WHERE user_id = :user_id AND role_id = :role_id AND class_id = :class_id"),
                {"subject_id": subject.id if subject else None, "user_id": teacher.id, "role_id": role.id, "class_id": class_id}
            )

        db.commit()

        logger.info(f"成功为班级 {class_id} 添加教师 {teacher.full_name}")
        return {"message": "教师添加成功", "teacher_id": teacher.id}

    except HTTPException as he:
        logger.error(f"❌ HTTP异常: {he.status_code} - {he.detail}")
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ 添加班级教师失败: {str(e)}")
        import traceback
        logger.error(f"❌ 详细错误信息: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"添加班级教师失败: {str(e)}")

@router.put("/classes/{class_id}/teachers/{teacher_id}")
async def update_class_teacher(
    class_id: int,
    teacher_id: int,
    teacher_data: dict,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """更新班级教师信息"""
    try:
        # 检查班级是否存在
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail="班级不存在")

        # 检查教师是否存在
        teacher = db.query(User).filter(User.id == teacher_id).first()
        if not teacher:
            raise HTTPException(status_code=404, detail="教师不存在")

        # 检查教师是否属于该班级
        class_teacher = db.query(ClassTeacher).filter(
            ClassTeacher.class_id == class_id,
            ClassTeacher.teacher_id == teacher_id
        ).first()
        if not class_teacher:
            raise HTTPException(status_code=404, detail="该教师不属于此班级")

        # 更新教师基本信息
        if 'teacher_name' in teacher_data or 'name' in teacher_data:
            teacher.full_name = teacher_data.get('teacher_name') or teacher_data.get('name')

        # 更新用户名（需要检查唯一性）
        if 'username' in teacher_data:
            new_username = teacher_data.get('username')
            if new_username and new_username != teacher.username:
                existing_user = db.query(User).filter(User.username == new_username, User.id != teacher_id).first()
                if existing_user:
                    raise HTTPException(status_code=400, detail=f"用户名 '{new_username}' 已存在")
                teacher.username = new_username

        # 更新密码
        if 'password' in teacher_data:
            new_password = teacher_data.get('password')
            if new_password:
                teacher.hashed_password = get_password_hash(new_password)

        if 'teacher_email' in teacher_data or 'email' in teacher_data:
            new_email = teacher_data.get('teacher_email') or teacher_data.get('email')
            if new_email and new_email != teacher.email:
                existing_email = db.query(User).filter(User.email == new_email, User.id != teacher_id).first()
                if existing_email:
                    raise HTTPException(status_code=400, detail=f"邮箱 '{new_email}' 已存在")
                teacher.email = new_email

        if 'teacher_phone' in teacher_data or 'phone' in teacher_data:
            teacher.phone = teacher_data.get('teacher_phone') or teacher_data.get('phone')

        # 更新科目信息
        if 'subject' in teacher_data:
            subject_name = teacher_data.get('subject')
            if subject_name:
                subject = db.query(Subject).filter(Subject.name == subject_name).first()
                if subject:
                    teacher.subject = subject_name
                else:
                    raise HTTPException(status_code=400, detail=f"科目 '{subject_name}' 不存在")

        # 更新角色信息
        if 'role' in teacher_data:
            teacher.role = teacher_data.get('role')

        db.commit()
        logger.info(f"成功更新班级 {class_id} 的教师 {teacher.full_name}")
        return {"message": "教师信息更新成功"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新班级教师失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新班级教师失败: {str(e)}")

@router.delete("/classes/{class_id}/teachers/{teacher_id}")
async def remove_class_teacher(
    class_id: int,
    teacher_id: int,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """从班级中移除教师"""
    try:
        # 检查班级是否存在
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail="班级不存在")

        # 检查教师是否存在
        teacher = db.query(User).filter(User.id == teacher_id).first()
        if not teacher:
            raise HTTPException(status_code=404, detail="教师不存在")

        # 检查教师是否属于该班级
        class_teacher = db.query(ClassTeacher).filter(
            ClassTeacher.class_id == class_id,
            ClassTeacher.teacher_id == teacher_id
        ).first()
        if not class_teacher:
            raise HTTPException(status_code=404, detail="该教师不属于此班级")

        # 删除班级教师关联
        db.delete(class_teacher)

        # 删除相关的用户角色记录
        db.execute(
            text("DELETE FROM user_roles WHERE user_id = :user_id AND class_id = :class_id"),
            {"user_id": teacher_id, "class_id": class_id}
        )

        db.commit()
        logger.info(f"成功从班级 {class_id} 移除教师 {teacher.full_name}")
        return {"message": "教师移除成功"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"移除班级教师失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"移除班级教师失败: {str(e)}")

@router.put("/classes/{class_id}/head-teacher")
async def assign_head_teacher(
    class_id: int,
    teacher_data: dict,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """指定班主任"""
    try:
        # 检查班级是否存在
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail="班级不存在")

        teacher_id = teacher_data.get('teacher_id')
        if not teacher_id:
            raise HTTPException(status_code=400, detail="缺少教师ID")

        # 检查教师是否存在
        teacher = db.query(User).filter(User.id == teacher_id).first()
        if not teacher:
            raise HTTPException(status_code=404, detail="教师不存在")

        # 检查教师是否属于该班级
        class_teacher = db.query(ClassTeacher).filter(
            ClassTeacher.class_id == class_id,
            ClassTeacher.teacher_id == teacher_id
        ).first()
        if not class_teacher:
            raise HTTPException(status_code=404, detail="该教师不属于此班级")

        # 将当前班主任改为任课教师
        db.execute(
            text("UPDATE user_roles SET role_id = (SELECT id FROM roles WHERE code = 'subject_teacher' LIMIT 1) WHERE class_id = :class_id AND role_id = (SELECT id FROM roles WHERE code = 'head_teacher' LIMIT 1)"),
            {"class_id": class_id}
        )

        # 将指定教师设为班主任
        head_teacher_role = db.query(Role).filter(Role.code == 'head_teacher').first()
        if not head_teacher_role:
            # 创建班主任角色
            head_teacher_role = Role(
                name="班主任",
                code="head_teacher",
                description="班级班主任角色"
            )
            db.add(head_teacher_role)
            db.commit()
            db.refresh(head_teacher_role)

        # 更新教师角色为班主任
        db.execute(
            text("UPDATE user_roles SET role_id = :role_id WHERE user_id = :user_id AND class_id = :class_id"),
            {"role_id": head_teacher_role.id, "user_id": teacher_id, "class_id": class_id}
        )

        # 更新教师的基本角色信息
        teacher.role = 'head_teacher'

        db.commit()
        logger.info(f"成功指定教师 {teacher.full_name} 为班级 {class_id} 的班主任")
        return {"message": "班主任指定成功"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"指定班主任失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"指定班主任失败: {str(e)}")

# 系统设置API
@router.get("/system-settings/registration", response_model=system_settings_schema.RegistrationSettings)
async def get_registration_settings(
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取注册设置"""
    try:
        # 获取简化的注册设置
        settings = SystemSettings.get_simple_registration_settings(db)
        return settings
    except Exception as e:
        logger.error(f"获取注册设置失败: {str(e)}")
        # 如果出错，返回默认设置
        return {
            "allow_student_registration": True,
            "allow_teacher_registration": True
        }

@router.put("/system-settings/registration", response_model=system_settings_schema.RegistrationSettings)
async def update_registration_settings(
    settings: system_settings_schema.RegistrationSettings,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """更新注册设置"""
    try:
        # 获取完整的注册设置
        full_settings = SystemSettings.get_registration_settings(db)
        
        # 更新学生和教师的启用状态
        full_settings["roles"]["student"]["enabled"] = settings.allow_student_registration
        full_settings["roles"]["teacher"]["enabled"] = settings.allow_teacher_registration
        
        # 保存更新后的设置
        settings_obj = db.query(SystemSettings).filter(
            SystemSettings.key == "registration_settings"
        ).first()
        
        if not settings_obj:
            settings_obj = SystemSettings(
                key="registration_settings",
                description="用户注册相关设置，包括角色配置、学校创建和验证方法"
            )
            db.add(settings_obj)
        
        settings_obj.set_json_value(full_settings)
        db.commit()
        
        logger.info(f"更新注册设置成功: {settings}")
        return settings
    except Exception as e:
        logger.error(f"更新注册设置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新注册设置失败: {str(e)}")

# 高级注册设置API
@router.get("/system-settings/advanced-registration", response_model=system_settings_schema.AdvancedRegistrationSettings)
async def get_advanced_registration_settings(
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取高级注册设置"""
    try:
        settings = SystemSettings.get_registration_settings(db)
        return settings
    except Exception as e:
        logger.error(f"获取高级注册设置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取高级注册设置失败: {str(e)}")

@router.put("/system-settings/advanced-registration", response_model=system_settings_schema.AdvancedRegistrationSettings)
async def update_advanced_registration_settings(
    settings: system_settings_schema.AdvancedRegistrationSettings,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """更新高级注册设置"""
    try:
        # 保存更新后的设置
        settings_obj = db.query(SystemSettings).filter(
            SystemSettings.key == "registration_settings"
        ).first()
        
        if not settings_obj:
            settings_obj = SystemSettings(
                key="registration_settings",
                description="用户注册相关设置，包括角色配置、学校创建和验证方法"
            )
            db.add(settings_obj)
        
        settings_obj.set_json_value(settings.dict())
        db.commit()
        
        logger.info("更新高级注册设置成功")
        return settings
    except Exception as e:
        logger.error(f"更新高级注册设置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新高级注册设置失败: {str(e)}")

@router.put("/system-settings/global-registration-switch")
async def update_global_registration_switch(
    request: dict,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """更新总注册开关"""
    try:
        enabled = request.get("enabled", True)
        result = SystemSettings.update_global_registration_switch(db, enabled)

        if result is None:
            raise HTTPException(status_code=404, detail="注册设置不存在")

        return {
            "success": True,
            "message": f"总注册开关已{'开启' if enabled else '关闭'}",
            "global_registration_enabled": enabled
        }
    except Exception as e:
        logger.error(f"更新总注册开关失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新总注册开关失败: {str(e)}")

@router.put("/system-settings/role-config/{role_name}", response_model=system_settings_schema.RoleConfig)
async def update_role_config(
    role_name: str,
    config: system_settings_schema.RoleConfigUpdate,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """更新特定角色的配置"""
    try:
        # 获取完整的注册设置
        full_settings = SystemSettings.get_registration_settings(db)
        
        # 检查角色是否存在
        if role_name not in full_settings["roles"]:
            raise HTTPException(status_code=404, detail=f"角色 '{role_name}' 不存在")
        
        # 更新角色配置
        role_config = full_settings["roles"][role_name]
        
        if config.enabled is not None:
            role_config["enabled"] = config.enabled
        if config.requires_approval is not None:
            role_config["requires_approval"] = config.requires_approval
        if config.approval_level is not None:
            role_config["approval_level"] = config.approval_level
        if config.fields is not None:
            role_config["fields"] = config.fields
        
        # 保存更新后的设置
        settings_obj = db.query(SystemSettings).filter(
            SystemSettings.key == "registration_settings"
        ).first()
        
        settings_obj.set_json_value(full_settings)
        db.commit()
        
        logger.info(f"更新角色 '{role_name}' 配置成功")
        return role_config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新角色配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新角色配置失败: {str(e)}")

# 学校申请审核API
@router.get("/school-applications", response_model=List[registration_schema.SchoolApplication])
async def get_school_applications(
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取所有学校申请
    """
    try:
        from ..models.registration import SchoolApplication

        # 构建查询
        query = db.query(SchoolApplication)

        # 按状态筛选
        if status:
            query = query.filter(SchoolApplication.status == status)

        # 执行查询
        applications = query.order_by(SchoolApplication.created_at.desc()).offset(skip).limit(limit).all()

        return applications
    except Exception as e:
        logger.error(f"获取学校申请失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取学校申请失败: {str(e)}")

# ==================== 系统级作业分析API ====================

@router.get("/system-homework-assignments")
async def get_system_homework_assignments(
    class_id: Optional[int] = None,
    school_id: Optional[int] = None,
    subject_id: Optional[int] = None,
    page: int = 1,
    limit: int = 100,
    search: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取系统级作业任务列表，基于管理员角色权限控制"""
    # 应用统一的权限控制（管理员身份）
    if not (current_user.is_admin or current_user.is_teacher):
        raise HTTPException(status_code=403, detail="没有权限查看系统作业任务列表")

    try:
        # 查询真实的homeworks数据
        from sqlalchemy import text

        # 构建动态WHERE条件
        where_conditions = []
        params = {}

        # 应用筛选条件
        if class_id:
            where_conditions.append("h.class_id = :class_id")
            params["class_id"] = class_id

        if school_id:
            where_conditions.append("h.school_id = :school_id")
            params["school_id"] = school_id

        if subject_id:
            where_conditions.append("h.subject_id = :subject_id")
            params["subject_id"] = subject_id

        # 添加搜索过滤
        if search:
            where_conditions.append("h.title LIKE :search")
            params["search"] = f"%{search}%"

        # 构建WHERE子句
        if where_conditions:
            where_clause = " AND ".join(where_conditions)
        else:
            where_clause = "1=1"

        # 构建SQL查询
        sql_query = f"""
        SELECT
            h.id, h.title, h.description, h.class_id, h.student_id,
            h.created_at, h.status, h.school_id, h.subject_id, h.assignment_id,
            h.score, h.accuracy, h.graded_at
        FROM
            homeworks h
        WHERE {where_clause}
        ORDER BY h.created_at DESC
        LIMIT :limit OFFSET :offset
        """

        params["limit"] = limit
        params["offset"] = (page - 1) * limit

        # 执行查询
        result = db.execute(text(sql_query), params)
        assignments = result.fetchall()

        # 转换为字典列表
        results = []
        for row in assignments:
            result_dict = {
                "id": row.id,
                "title": row.title,
                "description": row.description,
                "class_id": row.class_id,
                "student_id": row.student_id,
                "assignment_id": row.assignment_id,
                "created_at": str(row.created_at) if row.created_at else None,
                "status": row.status or "submitted",
                "school_id": row.school_id,
                "subject_id": row.subject_id,
                "score": row.score,
                "accuracy": row.accuracy,
                "graded_at": str(row.graded_at) if row.graded_at else None,
                "class_name": f"班级{row.class_id}" if row.class_id else "未分配班级",
                "student_name": f"学生{row.student_id}" if row.student_id else "未知学生",
                "teacher_name": "未知教师",
                "school_name": "四川省双流中学"
            }
            results.append(result_dict)

        logger.info(f"返回 {len(results)} 个系统作业管理记录")
        return results

    except Exception as e:
        logger.error(f"获取系统作业任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统作业任务列表失败: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取学校申请列表失败: {str(e)}")

@router.put("/school-applications/{application_id}/review", response_model=registration_schema.SchoolApplication)
async def review_school_application(
    application_id: int,
    data: registration_schema.SchoolApplicationUpdate,
    current_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    审批学校申请（批准或拒绝）
    """
    try:
        from ..models.registration import SchoolApplication
        from ..models.school import School
        
        # 查询申请
        application = db.query(SchoolApplication).filter(
            SchoolApplication.id == application_id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="学校申请不存在")
        
        if application.status != "pending":
            raise HTTPException(status_code=400, detail="只能审批待处理的申请")
        
        # 更新申请状态
        application.status = data.status
        application.reviewer_id = current_user.id
        application.updated_at = datetime.utcnow()
        
        # 如果是拒绝，记录拒绝原因
        if data.status == "rejected":
            if not data.rejection_reason:
                raise HTTPException(status_code=400, detail="拒绝申请时必须提供拒绝原因")
            application.rejection_reason = data.rejection_reason
        
        # 如果是批准，创建新学校
        if data.status == "approved":
            # 检查学校名称是否已存在
            existing_school = db.query(School).filter(School.name == application.name).first()
            if existing_school:
                raise HTTPException(status_code=400, detail=f"学校名称 '{application.name}' 已存在")
            
            # 创建新学校
            new_school = School(
                name=application.name,
                province=application.province,
                city=application.city,
                district=application.district,
                address=application.address,
                description=application.description
            )
            
            db.add(new_school)
        
        # 提交更改
        db.commit()
        db.refresh(application)
        
        return application
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"审批学校申请失败: {str(e)}")

@router.put("/school-applications/{application_id}/approve", response_model=registration_schema.SchoolApplication)
async def approve_school_application(
    application_id: int,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """批准学校申请"""
    try:
        from ..models.registration import SchoolApplication
        from ..models.school import School
        
        # 获取申请
        application = db.query(SchoolApplication).filter(
            SchoolApplication.id == application_id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="申请不存在")
        
        if application.status != "pending":
            raise HTTPException(status_code=400, detail=f"申请状态为 '{application.status}'，无法批准")
        
        # 创建新学校
        school = School(
            name=application.name,
            province=application.province,
            city=application.city,
            district=application.district,
            address=application.address
        )
        
        db.add(school)
        db.flush()  # 获取学校ID
        
        # 更新申请状态
        application.status = "approved"
        application.reviewer_id = admin.id
        application.updated_at = datetime.utcnow()
        
        # 将申请人设为学校管理员
        if application.applicant_id:
            applicant = db.query(User).get(application.applicant_id)
            if applicant:
                applicant.school_id = school.id
                applicant.role = "school_admin"
                
                # 创建注册状态记录
                from ..models.registration import UserRegistrationStatus
                
                reg_status = UserRegistrationStatus(
                    user_id=applicant.id,
                    role_name="school_admin",
                    status="approved",
                    first_reviewer_id=admin.id,
                    final_reviewer_id=admin.id
                )
                db.add(reg_status)
        
        db.commit()
        
        logger.info(f"学校申请 {application_id} 已批准，创建了新学校 {school.id}")
        return application
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"批准学校申请失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批准学校申请失败: {str(e)}")

@router.put("/school-applications/{application_id}/reject", response_model=registration_schema.SchoolApplication)
async def reject_school_application(
    application_id: int,
    data: registration_schema.SchoolApplicationUpdate,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """拒绝学校申请"""
    try:
        from ..models.registration import SchoolApplication
        
        # 获取申请
        application = db.query(SchoolApplication).filter(
            SchoolApplication.id == application_id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="申请不存在")
        
        if application.status != "pending":
            raise HTTPException(status_code=400, detail=f"申请状态为 '{application.status}'，无法拒绝")
        
        # 更新申请状态
        application.status = "rejected"
        application.reviewer_id = admin.id
        application.rejection_reason = data.rejection_reason
        application.updated_at = datetime.utcnow()
        
        db.commit()
        
        logger.info(f"学校申请 {application_id} 已拒绝")
        return application
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"拒绝学校申请失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"拒绝学校申请失败: {str(e)}")

# 用户注册审核API
@router.get("/registration-approvals", response_model=List[registration_schema.UserRegistrationStatus])
async def get_registration_approvals(
    status: Optional[str] = None,
    role_name: Optional[str] = None,
    school_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取用户注册审核列表"""
    try:
        from ..models.registration import UserRegistrationStatus
        from ..models.school import School

        query = db.query(UserRegistrationStatus).join(
            User, UserRegistrationStatus.user_id == User.id
        )

        if status:
            query = query.filter(UserRegistrationStatus.status == status)
        if role_name:
            query = query.filter(UserRegistrationStatus.role_name == role_name)
        if school_id:
            query = query.filter(User.school_id == school_id)

        approvals = query.order_by(UserRegistrationStatus.created_at.desc()).offset(skip).limit(limit).all()

        # 为每个审批记录添加详细的用户信息
        result = []
        for approval in approvals:
            # 转换为字典
            approval_dict = {
                "id": approval.id,
                "user_id": approval.user_id,
                "role_name": approval.role_name,
                "status": approval.status,
                "role_id": approval.role_id,
                "first_reviewer_id": approval.first_reviewer_id,
                "final_reviewer_id": approval.final_reviewer_id,
                "rejection_reason": approval.rejection_reason,
                "created_at": approval.created_at,
                "updated_at": approval.updated_at,
                "additional_info": approval.get_additional_info()
            }

            # 获取用户详细信息
            user = approval.user
            user_details = {
                "username": user.username,
                "full_name": user.full_name,
                "email": user.email,
                "phone": getattr(user, 'phone', None),
                "role": user.role,
                "is_teacher": user.is_teacher,
                "is_admin": user.is_admin,
                "is_active": user.is_active,
                "created_at": user.created_at.isoformat() if user.created_at else None
            }

            # 获取学校信息
            if user.school_id:
                school = db.query(School).filter(School.id == user.school_id).first()
                if school:
                    user_details["school"] = {
                        "id": school.id,
                        "name": school.name,
                        "province": school.province,
                        "city": school.city,
                        "district": school.district
                    }

            approval_dict["user_details"] = user_details
            result.append(approval_dict)

        return result
    except Exception as e:
        logger.error(f"获取用户注册审核列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取用户注册审核列表失败: {str(e)}")

@router.put("/registration-approvals/{approval_id}/first-review", response_model=registration_schema.UserRegistrationStatus)
async def first_review_registration(
    approval_id: int,
    data: registration_schema.UserRegistrationStatusUpdate,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """一级审核用户注册申请"""
    try:
        from ..models.registration import UserRegistrationStatus
        
        # 获取审核记录
        approval = db.query(UserRegistrationStatus).filter(
            UserRegistrationStatus.id == approval_id
        ).first()
        
        if not approval:
            raise HTTPException(status_code=404, detail="审核记录不存在")
        
        if approval.status != "pending":
            raise HTTPException(status_code=400, detail=f"审核状态为 '{approval.status}'，无法进行一级审核")
        
        # 检查权限
        settings = SystemSettings.get_registration_settings(db)
        role_config = settings["roles"].get(approval.role_name, {})
        approval_level = role_config.get("approval_level")
        
        # 如果需要二级审核，则标记为needs_verification
        if approval_level in ["principal", "super_admin"] and admin.role != "super_admin":
            approval.status = "needs_verification"
        else:
            # 否则直接批准或拒绝
            approval.status = data.status or "approved"
            approval.final_reviewer_id = admin.id
            
            # 如果是直接批准，更新用户状态
            if approval.status == "approved":
                user = db.query(User).get(approval.user_id)
                if user:
                    # 更新用户角色
                    user.role = approval.role_name
                    # 激活用户
                    user.is_active = True
                    
                    # 获取额外信息
                    additional_info = approval.get_additional_info()
                    
                    # 更新用户的其他信息
                    if additional_info:
                        # 更新学校ID
                        if 'school_id' in additional_info and additional_info['school_id']:
                            user.school_id = additional_info['school_id']
                        
                        # 更新班级ID
                        if 'class_id' in additional_info and additional_info['class_id']:
                            user.class_id = additional_info['class_id']
                        
                        # 更新科目
                        if 'subject' in additional_info and additional_info['subject']:
                            user.subject = additional_info['subject']
                    
                    logger.info(f"用户 {user.username} (ID: {user.id}) 注册审核已批准，角色: {user.role}")
            elif approval.status == "rejected":
                # 如果拒绝，可以选择禁用用户
                user = db.query(User).get(approval.user_id)
                if user:
                    user.is_active = False
                    logger.info(f"用户 {user.username} (ID: {user.id}) 注册审核已拒绝")
        
        approval.first_reviewer_id = admin.id
        
        if data.rejection_reason:
            approval.rejection_reason = data.rejection_reason
        
        if data.additional_info:
            approval.set_additional_info(data.additional_info)
        
        approval.updated_at = datetime.utcnow()
        
        db.commit()
        
        logger.info(f"用户注册审核 {approval_id} 已完成一级审核，状态为 {approval.status}")
        return approval
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"一级审核用户注册申请失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"一级审核用户注册申请失败: {str(e)}")

@router.put("/registration-approvals/{approval_id}/final-review", response_model=registration_schema.UserRegistrationStatus)
async def final_review_registration(
    approval_id: int,
    data: registration_schema.UserRegistrationStatusUpdate,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """终审用户注册申请"""
    try:
        from ..models.registration import UserRegistrationStatus
        
        # 获取审核记录
        approval = db.query(UserRegistrationStatus).filter(
            UserRegistrationStatus.id == approval_id
        ).first()
        
        if not approval:
            raise HTTPException(status_code=404, detail="审核记录不存在")
        
        if approval.status != "needs_verification":
            raise HTTPException(status_code=400, detail=f"审核状态为 '{approval.status}'，无法进行终审")
        
        # 检查权限
        settings = SystemSettings.get_registration_settings(db)
        role_config = settings["roles"].get(approval.role_name, {})
        approval_level = role_config.get("approval_level")
        
        if approval_level == "super_admin" and not admin.is_admin:
            raise HTTPException(status_code=403, detail="只有超级管理员可以进行此角色的终审")
        
        # 更新审核状态
        approval.status = data.status or "approved"
        approval.final_reviewer_id = admin.id
        
        if data.rejection_reason:
            approval.rejection_reason = data.rejection_reason
        
        if data.additional_info:
            approval.set_additional_info(data.additional_info)
        
        approval.updated_at = datetime.utcnow()
        
        # 如果批准，更新用户状态
        if approval.status == "approved":
            user = db.query(User).get(approval.user_id)
            if user:
                # 更新用户角色
                user.role = approval.role_name
                # 激活用户
                user.is_active = True
                
                # 获取额外信息
                additional_info = approval.get_additional_info()
                
                # 更新用户的其他信息
                if additional_info:
                    # 更新学校ID
                    if 'school_id' in additional_info and additional_info['school_id']:
                        user.school_id = additional_info['school_id']
                    
                    # 更新班级ID
                    if 'class_id' in additional_info and additional_info['class_id']:
                        user.class_id = additional_info['class_id']
                    
                    # 更新科目
                    if 'subject' in additional_info and additional_info['subject']:
                        user.subject = additional_info['subject']
                
                logger.info(f"用户 {user.username} (ID: {user.id}) 注册审核已批准，角色: {user.role}")
        elif approval.status == "rejected":
            # 如果拒绝，可以选择禁用用户
            user = db.query(User).get(approval.user_id)
            if user:
                user.is_active = False
                logger.info(f"用户 {user.username} (ID: {user.id}) 注册审核已拒绝")
        
        db.commit()
        
        logger.info(f"用户注册审核 {approval_id} 已完成终审，状态为 {approval.status}")
        return approval
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"终审用户注册申请失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"终审用户注册申请失败: {str(e)}")

# 角色-科目权限管理API
@router.get("/roles/{role_id}/subject-permissions", response_model=List[role_subject_permission_schema.RoleSubjectPermission])
async def get_role_subject_permissions(
    role_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """获取角色的科目权限"""
    try:
        # 检查角色是否存在
        role = db.query(Role).filter(Role.id == role_id).first()
        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")
        
        # 获取角色的科目权限
        permissions = db.query(RoleSubjectPermission).filter(
            RoleSubjectPermission.role_id == role_id
        ).all()
        
        return permissions
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色科目权限失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取角色科目权限失败: {str(e)}")

@router.post("/roles/{role_id}/subject-permissions", response_model=role_subject_permission_schema.RoleSubjectPermission)
async def create_role_subject_permission(
    role_id: int,
    permission: role_subject_permission_schema.RoleSubjectPermissionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """创建角色的科目权限"""
    try:
        # 检查角色是否存在
        role = db.query(Role).filter(Role.id == role_id).first()
        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")
        
        # 检查科目是否存在
        subject = db.query(Subject).filter(Subject.id == permission.subject_id).first()
        if not subject:
            raise HTTPException(status_code=404, detail="科目不存在")
        
        # 检查权限是否已存在
        existing_permission = db.query(RoleSubjectPermission).filter(
            RoleSubjectPermission.role_id == role_id,
            RoleSubjectPermission.subject_id == permission.subject_id
        ).first()
        if existing_permission:
            raise HTTPException(status_code=400, detail="该角色已有此科目的权限设置")
        
        # 创建新权限
        new_permission = RoleSubjectPermission(
            role_id=role_id,
            subject_id=permission.subject_id,
            can_view=permission.can_view,
            can_edit=permission.can_edit,
            can_delete=permission.can_delete,
            can_assign=permission.can_assign
        )
        db.add(new_permission)
        db.commit()
        db.refresh(new_permission)
        
        return new_permission
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建角色科目权限失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建角色科目权限失败: {str(e)}")

@router.put("/roles/{role_id}/subject-permissions/{permission_id}", response_model=role_subject_permission_schema.RoleSubjectPermission)
async def update_role_subject_permission(
    role_id: int,
    permission_id: int,
    permission_data: role_subject_permission_schema.RoleSubjectPermissionUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """更新角色的科目权限"""
    try:
        # 检查权限是否存在
        permission = db.query(RoleSubjectPermission).filter(
            RoleSubjectPermission.id == permission_id,
            RoleSubjectPermission.role_id == role_id
        ).first()
        if not permission:
            raise HTTPException(status_code=404, detail="角色科目权限不存在")
        
        # 更新权限
        if permission_data.can_view is not None:
            permission.can_view = permission_data.can_view
        if permission_data.can_edit is not None:
            permission.can_edit = permission_data.can_edit
        if permission_data.can_delete is not None:
            permission.can_delete = permission_data.can_delete
        if permission_data.can_assign is not None:
            permission.can_assign = permission_data.can_assign
        
        db.commit()
        db.refresh(permission)
        
        return permission
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新角色科目权限失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新角色科目权限失败: {str(e)}")

@router.delete("/roles/{role_id}/subject-permissions/{permission_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role_subject_permission(
    role_id: int,
    permission_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """删除角色的科目权限"""
    try:
        # 检查权限是否存在
        permission = db.query(RoleSubjectPermission).filter(
            RoleSubjectPermission.id == permission_id,
            RoleSubjectPermission.role_id == role_id
        ).first()
        if not permission:
            raise HTTPException(status_code=404, detail="角色科目权限不存在")
        
        # 删除权限
        db.delete(permission)
        db.commit()
        
        return {"detail": "角色科目权限已删除"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除角色科目权限失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除角色科目权限失败: {str(e)}")

@router.post("/roles/{role_id}/batch-subject-permissions", response_model=List[role_subject_permission_schema.RoleSubjectPermission])
async def batch_set_role_subject_permissions(
    role_id: int,
    data: role_subject_permission_schema.BatchSetRoleSubjectPermissions,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """批量设置角色的科目权限"""
    try:
        # 检查角色是否存在
        role = db.query(Role).filter(Role.id == role_id).first()
        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")
        
        # 验证role_id是否匹配
        if data.role_id != role_id:
            raise HTTPException(status_code=400, detail="请求中的角色ID与路径中的角色ID不匹配")
        
        # 删除该角色的所有现有权限
        db.query(RoleSubjectPermission).filter(RoleSubjectPermission.role_id == role_id).delete()
        
        # 批量创建新权限
        new_permissions = []
        for perm in data.permissions:
            # 检查科目是否存在
            subject = db.query(Subject).filter(Subject.id == perm.subject_id).first()
            if not subject:
                continue  # 跳过不存在的科目
            
            new_permission = RoleSubjectPermission(
                role_id=role_id,
                subject_id=perm.subject_id,
                can_view=perm.can_view,
                can_edit=perm.can_edit,
                can_delete=perm.can_delete,
                can_assign=perm.can_assign
            )
            db.add(new_permission)
            new_permissions.append(new_permission)
        
        db.commit()
        
        # 刷新所有新权限
        for perm in new_permissions:
            db.refresh(perm)
        
        return new_permissions
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量设置角色科目权限失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量设置角色科目权限失败: {str(e)}")

# 班级家长管理API
@router.get("/classes/{class_id}/parents")
async def get_class_parents(
    class_id: int,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取班级家长列表"""
    try:
        # 检查班级是否存在
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail="班级不存在")

        # 权限检查：如果不是超级管理员，只能访问自己学校的班级数据
        if not (admin.is_admin or admin.role in ['超级管理员', 'admin']) and admin.school_id != class_obj.school_id:
            raise HTTPException(status_code=403, detail="无权访问其他学校的班级数据")

        # 获取班级学生
        class_students = db.query(ClassStudent).filter(ClassStudent.class_id == class_id).all()
        student_ids = [cs.student_id for cs in class_students]

        # 获取这些学生的家长
        parent_students = db.query(ParentStudent).filter(ParentStudent.student_id.in_(student_ids)).all()
        
        # 构建返回数据
        parents_data = []
        for ps in parent_students:
            parent = db.query(User).filter(User.id == ps.parent_id).first()
            student = db.query(User).filter(User.id == ps.student_id).first()
            
            if parent and student:
                parents_data.append({
                    "id": parent.id,
                    "username": parent.username,
                    "name": parent.full_name or parent.username,
                    "relationship": ps.relationship,
                    "phone": parent.phone,
                    "email": parent.email,
                    "student_name": student.full_name or student.username,
                    "student_id": student.id,
                    "is_primary": ps.is_primary
                })

        return parents_data
    except Exception as e:
        logger.error(f"获取班级家长列表失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取班级家长列表失败: {str(e)}")

@router.post("/classes/{class_id}/parents")
async def create_class_parent(
    class_id: int,
    parent_data: dict,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """为班级添加家长"""
    try:
        # 检查班级是否存在
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail="班级不存在")

        # 获取学生信息
        student_id = parent_data.get('student_id')
        if not student_id:
            raise HTTPException(status_code=400, detail="学生ID不能为空")

        # 检查学生是否在该班级
        class_student = db.query(ClassStudent).filter(
            ClassStudent.class_id == class_id,
            ClassStudent.student_id == student_id
        ).first()
        if not class_student:
            raise HTTPException(status_code=400, detail="该学生不在指定班级中")

        # 创建或获取家长用户
        parent_name = parent_data.get('name')
        username = parent_data.get('username')
        password = parent_data.get('password')
        parent_email = parent_data.get('email')
        parent_phone = parent_data.get('phone')
        relationship = parent_data.get('relationship', 'parent')

        # 在多学校系统中，用户名允许重复，但邮箱必须唯一
        existing_parent = None

        # 邮箱必须全系统唯一
        if parent_email:
            existing_email = db.query(User).filter(User.email == parent_email).first()
            if existing_email:
                raise HTTPException(status_code=400, detail=f"邮箱 '{parent_email}' 已存在")

        # 检查同一学校内用户名是否重复（学校内唯一）
        if username:
            existing_user_in_school = db.query(User).filter(
                User.username == username,
                User.school_id == class_obj.school_id
            ).first()
            if existing_user_in_school:
                # 同一学校内所有角色的用户名都必须唯一，不允许重复
                role_name = existing_user_in_school.role or "用户"
                raise HTTPException(status_code=400, detail=f"用户名 '{username}' 在本校已被{role_name}使用，请使用其他用户名")
            else:
                existing_parent = None

        if not existing_parent and parent_name:
            # 使用提供的用户名或生成用户名
            if not username:
                import random
                username = f"parent_{parent_name}_{random.randint(1000, 9999)}"
                while db.query(User).filter(User.username == username).first():
                    username = f"parent_{parent_name}_{random.randint(1000, 9999)}"

            # 如果没有提供邮箱，生成一个默认邮箱
            if not parent_email:
                parent_email = generate_safe_email(username, "school.local")

            # 使用提供的密码或默认密码
            if not password:
                password = "123456"  # 默认密码
            hashed_password = get_password_hash(password)
            new_parent = User(
                username=username,
                email=parent_email,
                hashed_password=hashed_password,
                full_name=parent_name,
                phone=parent_phone,
                is_admin=False,
                school_id=class_obj.school_id,
                role='parent'  # 设置角色为家长
            )
            db.add(new_parent)
            db.commit()
            db.refresh(new_parent)
            parent = new_parent
        else:
            parent = existing_parent

        if not parent:
            raise HTTPException(status_code=400, detail="无法创建或找到家长用户")

        # 检查是否已经是该学生的家长
        existing_parent_student = db.query(ParentStudent).filter(
            ParentStudent.parent_id == parent.id,
            ParentStudent.student_id == student_id
        ).first()

        if not existing_parent_student:
            # 添加家长学生关联
            parent_student = ParentStudent(
                parent_id=parent.id,
                student_id=student_id,
                relationship=relationship,
                is_primary=parent_data.get('is_primary', False)
            )
            db.add(parent_student)
            db.commit()

        return {
            "id": parent.id,
            "name": parent.full_name or parent.username,
            "relationship": relationship,
            "phone": parent.phone,
            "email": parent.email,
            "student_id": student_id
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加班级家长失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加班级家长失败: {str(e)}")

@router.put("/parents/{parent_id}")
async def update_parent(
    parent_id: int,
    parent_data: dict,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """更新家长信息"""
    try:
        parent = db.query(User).filter(User.id == parent_id).first()
        if not parent:
            raise HTTPException(status_code=404, detail="家长不存在")

        # 更新家长信息
        if 'name' in parent_data:
            parent.full_name = parent_data['name']
        if 'phone' in parent_data:
            parent.phone = parent_data['phone']
        if 'email' in parent_data:
            parent.email = parent_data['email']

        db.commit()
        db.refresh(parent)

        return {
            "id": parent.id,
            "name": parent.full_name or parent.username,
            "phone": parent.phone,
            "email": parent.email
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新家长信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新家长信息失败: {str(e)}")

@router.delete("/classes/{class_id}/parents/{parent_id}")
async def remove_parent_from_class(
    class_id: int,
    parent_id: int,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """从班级移除家长"""
    try:
        # 检查班级是否存在
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail="班级不存在")

        # 检查家长是否存在
        parent = db.query(User).filter(User.id == parent_id).first()
        if not parent:
            raise HTTPException(status_code=404, detail="家长不存在")

        # 获取班级学生ID列表
        class_students = db.query(ClassStudent).filter(ClassStudent.class_id == class_id).all()
        student_ids = [cs.student_id for cs in class_students]

        # 删除该家长与班级学生的关联
        parent_students = db.query(ParentStudent).filter(
            ParentStudent.parent_id == parent_id,
            ParentStudent.student_id.in_(student_ids)
        ).all()

        for ps in parent_students:
            db.delete(ps)

        db.commit()
        return {"message": "家长移除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移除家长失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"移除家长失败: {str(e)}")

# ==================== 家长端特性管理 ====================

@router.get("/parent-features")
async def get_parent_features(
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取家长端特性配置"""
    try:
        from ..services.feature_service import FeatureService
        feature_service = FeatureService(db)
        status = feature_service.get_feature_status()

        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"获取家长端特性配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取家长端特性配置失败: {str(e)}")

@router.post("/parent-features/{feature_name}/toggle")
async def toggle_parent_feature(
    feature_name: str,
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """切换家长端特性状态"""
    try:
        from ..services.feature_service import FeatureService
        feature_service = FeatureService(db)

        success = feature_service.toggle_feature(feature_name)
        if success:
            new_status = feature_service.is_feature_enabled(feature_name)
            return {
                "success": True,
                "message": f"特性 '{feature_name}' 已{'启用' if new_status else '禁用'}",
                "data": {
                    "feature_name": feature_name,
                    "enabled": new_status
                }
            }
        else:
            raise HTTPException(status_code=500, detail="切换特性状态失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"切换家长端特性状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"切换家长端特性状态失败: {str(e)}")

@router.post("/parent-features/reset")
async def reset_parent_features(
    admin: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """重置家长端特性配置为默认值"""
    try:
        from ..services.feature_service import FeatureService
        feature_service = FeatureService(db)

        success = feature_service.reset_features()
        if success:
            return {
                "success": True,
                "message": "家长端特性配置已重置为默认值"
            }
        else:
            raise HTTPException(status_code=500, detail="重置特性配置失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置家长端特性配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重置家长端特性配置失败: {str(e)}")
