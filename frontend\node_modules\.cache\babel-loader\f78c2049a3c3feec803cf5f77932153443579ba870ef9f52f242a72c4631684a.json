{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { devUseWarning } from '../../_util/warning';\nfunction filter(items) {\n  return items.filter(item => item);\n}\nexport default function useLegacyItems(items, children) {\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Tabs');\n    warning.deprecated(!children, 'Tabs.TabPane', 'items');\n  }\n  if (items) {\n    return items.map(item => {\n      var _a;\n      const mergedDestroyOnHidden = (_a = item.destroyOnHidden) !== null && _a !== void 0 ? _a : item.destroyInactiveTabPane;\n      return Object.assign(Object.assign({}, item), {\n        // TODO: In the future, destroyInactiveTabPane in rc-tabs needs to be upgrade to destroyOnHidden\n        destroyInactiveTabPane: mergedDestroyOnHidden\n      });\n    });\n  }\n  const childrenItems = toArray(children).map(node => {\n    if (/*#__PURE__*/React.isValidElement(node)) {\n      const {\n        key,\n        props\n      } = node;\n      const _a = props || {},\n        {\n          tab\n        } = _a,\n        restProps = __rest(_a, [\"tab\"]);\n      const item = Object.assign(Object.assign({\n        key: String(key)\n      }, restProps), {\n        label: tab\n      });\n      return item;\n    }\n    return null;\n  });\n  return filter(childrenItems);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}