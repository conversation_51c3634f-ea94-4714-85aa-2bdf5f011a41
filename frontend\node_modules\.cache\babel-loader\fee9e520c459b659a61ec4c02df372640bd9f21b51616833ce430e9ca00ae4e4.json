{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport QqSquareFilledSvg from \"@ant-design/icons-svg/es/asn/QqSquareFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar QqSquareFilled = function QqSquareFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: QqSquareFilledSvg\n  }));\n};\n\n/**![qq-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzIyLjUgNjc2LjRjLTExLjUgMS40LTQ0LjktNTIuNy00NC45LTUyLjcgMCAzMS4zLTE2LjIgNzIuMi01MS4xIDEwMS44IDE2LjkgNS4yIDU0LjkgMTkuMiA0NS45IDM0LjQtNy4zIDEyLjMtMTI1LjYgNy45LTE1OS44IDQtMzQuMiAzLjgtMTUyLjUgOC4zLTE1OS44LTQtOS4xLTE1LjIgMjguOS0yOS4yIDQ1LjgtMzQuNC0zNS0yOS41LTUxLjEtNzAuNC01MS4xLTEwMS44IDAgMC0zMy40IDU0LjEtNDQuOSA1Mi43LTUuNC0uNy0xMi40LTI5LjYgOS40LTk5LjcgMTAuMy0zMyAyMi02MC41IDQwLjItMTA1LjgtMy4xLTExNi45IDQ1LjMtMjE1IDE2MC40LTIxNSAxMTMuOSAwIDE2My4zIDk2LjEgMTYwLjQgMjE1IDE4LjEgNDUuMiAyOS45IDcyLjggNDAuMiAxMDUuOCAyMS43IDcwLjEgMTQuNiA5OS4xIDkuMyA5OS43eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(QqSquareFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'QqSquareFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}