# 将database.py的内容移动到这里，使database成为一个包

import os
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

logger = logging.getLogger(__name__)

# 数据库配置 - 确保数据库文件在backend目录下
# 直接使用相对于当前工作目录的路径
db_file_path = "smart_edu.db"  # 在backend目录下运行时，这会创建在backend目录
DATABASE_URL = f"sqlite:///{db_file_path}"

# 获取数据库文件的绝对路径
db_path = os.path.abspath(db_file_path)
print(f"数据库路径: {db_path}")
print(f"数据库文件存在: {os.path.exists(db_path)}")

# 创建数据库引擎
engine = create_engine(
    DATABASE_URL, 
    connect_args={"check_same_thread": False},
    echo=False  # 设置为True可以看到SQL语句
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基类
Base = declarative_base()

# 依赖项：获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """初始化数据库，创建必要的表和数据"""
    try:
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        # 检查并初始化基础数据
        with SessionLocal() as db:
            # 检查是否需要初始化角色数据
            result = db.execute(text("SELECT COUNT(*) FROM roles")).fetchone()
            if result[0] == 0:
                logger.info("初始化角色数据...")
                init_roles(db)
            
            # 检查是否需要初始化学校数据
            result = db.execute(text("SELECT COUNT(*) FROM schools")).fetchone()
            if result[0] == 0:
                logger.info("初始化学校数据...")
                init_schools(db)
                
            db.commit()
            
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")

def init_roles(db):
    """初始化角色数据"""
    roles_data = [
        {"name": "超级管理员", "code": "super_admin", "description": "系统最高管理者，可管理所有学校", "level": 100},
        {"name": "学校管理员", "code": "school_admin", "description": "拥有所属学校最高管理权限", "level": 80},
        {"name": "校长", "code": "principal", "description": "管理所属学校所有科目的作业", "level": 90},
        {"name": "副校长", "code": "vice_principal", "description": "管理所属学校所有科目的作业", "level": 85},
        {"name": "教务处主任", "code": "academic_director", "description": "管理所属学校所有科目的作业", "level": 75},
        {"name": "年级组长", "code": "grade_director", "description": "管理所属学校指定年级所有科目的作业", "level": 50},
        {"name": "教研组长", "code": "subject_leader", "description": "管理所属学校指定科目的作业", "level": 55},
        {"name": "备课组长", "code": "lesson_planner", "description": "管理所属学校指定年级指定科目的作业", "level": 30},
        {"name": "班主任", "code": "class_teacher", "description": "管理指定班级所有角色即所有科目作业", "level": 40},
        {"name": "教师", "code": "teacher", "description": "管理所属学校所担任的班级的指定科目的作业", "level": 30},
        {"name": "学生", "code": "student", "description": "管理个人学习数据", "level": 10},
        {"name": "家长", "code": "parent", "description": "查看学生学习情况", "level": 20}
    ]
    
    for role_data in roles_data:
        db.execute(text("""
            INSERT OR IGNORE INTO roles (name, code, description, level, created_at, updated_at)
            VALUES (:name, :code, :description, :level, datetime('now'), datetime('now'))
        """), role_data)

def init_schools(db):
    """初始化学校数据"""
    schools_data = [
        {"name": "四川省双流中学", "address": "四川省成都市双流区", "phone": "028-12345678"}
    ]
    
    for school_data in schools_data:
        db.execute(text("""
            INSERT OR IGNORE INTO schools (name, address, phone, created_at, updated_at)
            VALUES (:name, :address, :phone, datetime('now'), datetime('now'))
        """), school_data)
