{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys\\\\frontend\\\\src\\\\components\\\\ParentFeatureManager.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Switch, Typography, Spin, message, Row, Col, Divider, Button, Modal, Alert, Badge, Space, Tooltip } from 'antd';\nimport { SettingOutlined, ReloadOutlined, ExclamationCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';\nimport api from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  confirm\n} = Modal;\nconst ParentFeatureManager = ({\n  isMobile = false\n}) => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [features, setFeatures] = useState({});\n  const [updating, setUpdating] = useState({});\n\n  // 获取家长端特性状态\n  const fetchFeatures = async () => {\n    console.log('🚀 开始获取家长端特性状态...');\n    try {\n      setLoading(true);\n      console.log('📡 发送API请求: /admin/parent-features');\n      const response = await api.get('/admin/parent-features');\n      console.log('📨 收到API响应:', response);\n      console.log('📋 响应数据:', response);\n\n      // 处理两种可能的响应格式\n      let featuresData = null;\n      if (response && response.success) {\n        // 标准格式: {success: true, data: {...}}\n        featuresData = response.data;\n        console.log('✅ 标准格式响应，特性数据:', featuresData);\n      } else if (response && response.categories) {\n        // 直接格式: {total_categories: 3, categories: {...}}\n        featuresData = response;\n        console.log('✅ 直接格式响应，特性数据:', featuresData);\n      } else {\n        console.error('❌ 无法识别的响应格式:', response);\n        message.error('获取家长端特性状态失败：响应格式错误');\n        return;\n      }\n      if (featuresData) {\n        console.log('🎯 设置特性数据到状态:', featuresData);\n        setFeatures(featuresData);\n        message.success('家长端特性状态加载成功');\n      }\n    } catch (error) {\n      console.error('💥 API调用异常:', error);\n      console.error('错误详情:', error);\n      message.error(`获取家长端特性状态失败: ${error.detail || error.message}`);\n    } finally {\n      console.log('🏁 API调用完成，设置loading为false');\n      setLoading(false);\n    }\n  };\n\n  // 切换特性状态\n  const toggleFeature = async featureName => {\n    var _categories$featureNa, _categories$featureNa2, _categories$featureNa3;\n    const isMainCategory = !featureName.includes('.');\n    const categories = features.categories || {};\n    const currentStatus = isMainCategory ? (_categories$featureNa = categories[featureName]) === null || _categories$featureNa === void 0 ? void 0 : _categories$featureNa.enabled : (_categories$featureNa2 = categories[featureName.split('.')[0]]) === null || _categories$featureNa2 === void 0 ? void 0 : (_categories$featureNa3 = _categories$featureNa2.features) === null || _categories$featureNa3 === void 0 ? void 0 : _categories$featureNa3[featureName.split('.')[1]];\n    console.log('🔄 切换特性:', featureName, '当前状态:', currentStatus);\n    confirm({\n      title: '确认操作',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 13\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u60A8\\u786E\\u5B9A\\u8981\", currentStatus ? '禁用' : '启用', \"\\u529F\\u80FD \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: featureName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 50\n          }, this), \" \\u5417\\uFF1F\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), isMainCategory && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u6CE8\\u610F\",\n          description: \"\\u5207\\u6362\\u4E3B\\u7C7B\\u522B\\u5C06\\u5F71\\u54CD\\u8BE5\\u7C7B\\u522B\\u4E0B\\u7684\\u6240\\u6709\\u5B50\\u529F\\u80FD\",\n          type: \"warning\",\n          showIcon: true,\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this),\n      onOk: async () => {\n        try {\n          setUpdating(prev => ({\n            ...prev,\n            [featureName]: true\n          }));\n          const response = await api.post(`/admin/parent-features/${featureName}/toggle`);\n          console.log('🔍 API响应数据:', response);\n          console.log('🔍 response.success:', response.success);\n          if (response.success) {\n            message.success(response.message);\n            await fetchFeatures(); // 重新获取状态\n          } else {\n            console.error('❌ API返回success=false:', response);\n            message.error('切换特性状态失败');\n          }\n        } catch (error) {\n          console.error('💥 切换特性状态异常:', error);\n          console.error('💥 错误响应:', error);\n          message.error(`切换特性状态失败: ${error.detail || error.message}`);\n        } finally {\n          setUpdating(prev => ({\n            ...prev,\n            [featureName]: false\n          }));\n        }\n      }\n    });\n  };\n\n  // 重置所有特性\n  const resetFeatures = () => {\n    confirm({\n      title: '重置确认',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 13\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u60A8\\u786E\\u5B9A\\u8981\\u91CD\\u7F6E\\u6240\\u6709\\u5BB6\\u957F\\u7AEF\\u7279\\u6027\\u914D\\u7F6E\\u4E3A\\u9ED8\\u8BA4\\u503C\\u5417\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u8B66\\u544A\",\n          description: \"\\u6B64\\u64CD\\u4F5C\\u5C06\\u91CD\\u7F6E\\u6240\\u6709\\u7279\\u6027\\u5F00\\u5173\\u72B6\\u6001\\uFF0C\\u65E0\\u6CD5\\u64A4\\u9500\\uFF01\",\n          type: \"error\",\n          showIcon: true,\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this),\n      onOk: async () => {\n        try {\n          setLoading(true);\n          const response = await api.post('/admin/parent-features/reset');\n          if (response.data.success) {\n            message.success('特性配置已重置为默认值');\n            await fetchFeatures();\n          } else {\n            message.error('重置特性配置失败');\n          }\n        } catch (error) {\n          console.error('重置特性配置失败:', error);\n          message.error('重置特性配置失败');\n        } finally {\n          setLoading(false);\n        }\n      }\n    });\n  };\n  useEffect(() => {\n    fetchFeatures();\n  }, []);\n\n  // 渲染特性开关\n  const renderFeatureSwitch = (featureName, enabled, description, isSubFeature = false) => {\n    const isUpdating = updating[featureName];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        padding: isSubFeature ? '8px 0 8px 24px' : '12px 0',\n        borderLeft: isSubFeature ? '3px solid #f0f0f0' : 'none',\n        marginLeft: isSubFeature ? '12px' : '0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: !isSubFeature,\n            style: {\n              fontSize: isSubFeature ? '13px' : '14px',\n              color: isSubFeature ? '#666' : '#333'\n            },\n            children: featureName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n            status: enabled ? 'success' : 'default',\n            text: enabled ? '已启用' : '已禁用'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), description && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            style: {\n              fontSize: '12px'\n            },\n            children: description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: enabled ? '点击禁用' : '点击启用',\n        children: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: enabled,\n          loading: isUpdating,\n          onChange: () => toggleFeature(featureName),\n          size: isMobile ? 'small' : 'default'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, featureName, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染特性类别\n  const renderFeatureCategory = (categoryName, categoryData) => {\n    const {\n      enabled,\n      description,\n      features: subFeatures\n    } = categoryData;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [enabled ? /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 24\n        }, this) : /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 79\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: categoryName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this),\n      extra: /*#__PURE__*/_jsxDEV(Switch, {\n        checked: enabled,\n        loading: updating[categoryName],\n        onChange: () => toggleFeature(categoryName),\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 12\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), subFeatures && Object.keys(subFeatures).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Divider, {\n          style: {\n            margin: '12px 0'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          style: {\n            fontSize: '13px',\n            color: '#666'\n          },\n          children: \"\\u5B50\\u529F\\u80FD\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: Object.entries(subFeatures).map(([featureName, featureEnabled]) => renderFeatureSwitch(`${categoryName}.${featureName}`, featureEnabled, null, true))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 11\n      }, this)]\n    }, categoryName, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u5BB6\\u957F\\u7AEF\\u7279\\u6027\\u914D\\u7F6E...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this);\n  }\n  const {\n    categories = {}\n  } = features;\n  console.log('🔍 渲染时的features状态:', features);\n  console.log('🔍 渲染时的categories:', categories);\n  console.log('🔍 categories键数量:', Object.keys(categories).length);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: isMobile ? '8px' : '16px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {\n              style: {\n                marginRight: 8\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), \"\\u5BB6\\u957F\\u7AEF\\u529F\\u80FD\\u7BA1\\u7406\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              margin: '8px 0 0 0',\n              color: '#666'\n            },\n            children: \"\\u7BA1\\u7406\\u5BB6\\u957F\\u7AEF\\u5404\\u9879\\u529F\\u80FD\\u7684\\u542F\\u7528\\u72B6\\u6001\\uFF0C\\u652F\\u6301\\u5206\\u9636\\u6BB5\\u63A7\\u5236\\u529F\\u80FD\\u5F00\\u653E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 23\n              }, this),\n              onClick: fetchFeatures,\n              loading: loading,\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              danger: true,\n              icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 23\n              }, this),\n              onClick: resetFeatures,\n              children: \"\\u91CD\\u7F6E\\u914D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u529F\\u80FD\\u72B6\\u6001\\u6982\\u89C8\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 2,\n              style: {\n                margin: 0,\n                color: '#1890ff'\n              },\n              children: features.total_categories || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u603B\\u529F\\u80FD\\u7C7B\\u522B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 2,\n              style: {\n                margin: 0,\n                color: '#52c41a'\n              },\n              children: features.enabled_categories || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u5DF2\\u542F\\u7528\\u7C7B\\u522B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 2,\n              style: {\n                margin: 0,\n                color: '#faad14'\n              },\n              children: Object.values(categories).reduce((total, cat) => total + Object.keys(cat.features || {}).length, 0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u603B\\u5B50\\u529F\\u80FD\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 5,\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(InfoCircleOutlined, {\n          style: {\n            marginRight: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), \"\\u529F\\u80FD\\u5F00\\u5173\\u63A7\\u5236\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), Object.keys(categories).length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '40px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u6682\\u65E0\\u5BB6\\u957F\\u7AEF\\u7279\\u6027\\u914D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 11\n      }, this) : Object.entries(categories).map(([categoryName, categoryData]) => renderFeatureCategory(categoryName, categoryData))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4F7F\\u7528\\u8BF4\\u660E\",\n      style: {\n        marginTop: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          paddingLeft: '20px',\n          margin: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u4E3B\\u7C7B\\u522B\\u5F00\\u5173\\u63A7\\u5236\\u6574\\u4E2A\\u529F\\u80FD\\u6A21\\u5757\\u7684\\u542F\\u7528\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u5B50\\u529F\\u80FD\\u5F00\\u5173\\u53EF\\u4EE5\\u7CBE\\u786E\\u63A7\\u5236\\u5177\\u4F53\\u529F\\u80FD\\u7684\\u542F\\u7528\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u5EFA\\u8BAE\\u6309\\u9636\\u6BB5\\u9010\\u6B65\\u542F\\u7528\\u529F\\u80FD\\uFF0C\\u786E\\u4FDD\\u7CFB\\u7EDF\\u7A33\\u5B9A\\u6027\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u7B2C\\u4E09\\u9636\\u6BB5\\u9AD8\\u7EA7\\u529F\\u80FD\\u5EFA\\u8BAE\\u5728\\u5145\\u5206\\u6D4B\\u8BD5\\u540E\\u518D\\u542F\\u7528\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u5982\\u9047\\u95EE\\u9898\\u53EF\\u4F7F\\u7528\\\"\\u91CD\\u7F6E\\u914D\\u7F6E\\\"\\u6062\\u590D\\u9ED8\\u8BA4\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 289,\n    columnNumber: 5\n  }, this);\n};\n_s(ParentFeatureManager, \"0StpxOLZPmqnh1zxgns2bB+/JuI=\");\n_c = ParentFeatureManager;\nexport default ParentFeatureManager;\nvar _c;\n$RefreshReg$(_c, \"ParentFeatureManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Switch", "Typography", "Spin", "message", "Row", "Col", "Divider", "<PERSON><PERSON>", "Modal", "<PERSON><PERSON>", "Badge", "Space", "<PERSON><PERSON><PERSON>", "SettingOutlined", "ReloadOutlined", "ExclamationCircleOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "InfoCircleOutlined", "api", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "confirm", "ParentFeatureManager", "isMobile", "_s", "loading", "setLoading", "features", "setFeatures", "updating", "setUpdating", "fetchFeatures", "console", "log", "response", "get", "featuresData", "success", "data", "categories", "error", "detail", "toggleFeature", "featureName", "_categories$featureNa", "_categories$featureNa2", "_categories$featureNa3", "isMainCategory", "includes", "currentStatus", "enabled", "split", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "children", "description", "type", "showIcon", "style", "marginTop", "onOk", "prev", "post", "resetFeatures", "renderFeatureSwitch", "isSubFeature", "isUpdating", "display", "justifyContent", "alignItems", "padding", "borderLeft", "marginLeft", "flex", "strong", "fontSize", "color", "status", "text", "checked", "onChange", "size", "renderFeatureCategory", "categoryName", "categoryData", "subFeatures", "marginBottom", "extra", "Object", "keys", "length", "margin", "entries", "map", "featureEnabled", "textAlign", "gutter", "align", "level", "marginRight", "onClick", "danger", "xs", "sm", "total_categories", "enabled_categories", "values", "reduce", "total", "cat", "paddingLeft", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys/frontend/src/components/ParentFeatureManager.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Switch,\n  Typography,\n  Spin,\n  message,\n  Row,\n  Col,\n  Divider,\n  Button,\n  Modal,\n  Alert,\n  Badge,\n  Space,\n  Tooltip\n} from 'antd';\nimport {\n  SettingOutlined,\n  ReloadOutlined,\n  ExclamationCircleOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  InfoCircleOutlined\n} from '@ant-design/icons';\nimport api from '../utils/api';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { confirm } = Modal;\n\nconst ParentFeatureManager = ({ isMobile = false }) => {\n  const [loading, setLoading] = useState(true);\n  const [features, setFeatures] = useState({});\n  const [updating, setUpdating] = useState({});\n\n  // 获取家长端特性状态\n  const fetchFeatures = async () => {\n    console.log('🚀 开始获取家长端特性状态...');\n    try {\n      setLoading(true);\n      console.log('📡 发送API请求: /admin/parent-features');\n\n      const response = await api.get('/admin/parent-features');\n      console.log('📨 收到API响应:', response);\n      console.log('📋 响应数据:', response);\n\n      // 处理两种可能的响应格式\n      let featuresData = null;\n\n      if (response && response.success) {\n        // 标准格式: {success: true, data: {...}}\n        featuresData = response.data;\n        console.log('✅ 标准格式响应，特性数据:', featuresData);\n      } else if (response && response.categories) {\n        // 直接格式: {total_categories: 3, categories: {...}}\n        featuresData = response;\n        console.log('✅ 直接格式响应，特性数据:', featuresData);\n      } else {\n        console.error('❌ 无法识别的响应格式:', response);\n        message.error('获取家长端特性状态失败：响应格式错误');\n        return;\n      }\n\n      if (featuresData) {\n        console.log('🎯 设置特性数据到状态:', featuresData);\n        setFeatures(featuresData);\n        message.success('家长端特性状态加载成功');\n      }\n    } catch (error) {\n      console.error('💥 API调用异常:', error);\n      console.error('错误详情:', error);\n      message.error(`获取家长端特性状态失败: ${error.detail || error.message}`);\n    } finally {\n      console.log('🏁 API调用完成，设置loading为false');\n      setLoading(false);\n    }\n  };\n\n  // 切换特性状态\n  const toggleFeature = async (featureName) => {\n    const isMainCategory = !featureName.includes('.');\n    const categories = features.categories || {};\n\n    const currentStatus = isMainCategory\n      ? categories[featureName]?.enabled\n      : categories[featureName.split('.')[0]]?.features?.[featureName.split('.')[1]];\n\n    console.log('🔄 切换特性:', featureName, '当前状态:', currentStatus);\n\n    confirm({\n      title: '确认操作',\n      icon: <ExclamationCircleOutlined />,\n      content: (\n        <div>\n          <p>您确定要{currentStatus ? '禁用' : '启用'}功能 <strong>{featureName}</strong> 吗？</p>\n          {isMainCategory && (\n            <Alert\n              message=\"注意\"\n              description=\"切换主类别将影响该类别下的所有子功能\"\n              type=\"warning\"\n              showIcon\n              style={{ marginTop: 8 }}\n            />\n          )}\n        </div>\n      ),\n      onOk: async () => {\n        try {\n          setUpdating(prev => ({ ...prev, [featureName]: true }));\n          const response = await api.post(`/admin/parent-features/${featureName}/toggle`);\n\n          console.log('🔍 API响应数据:', response);\n          console.log('🔍 response.success:', response.success);\n\n          if (response.success) {\n            message.success(response.message);\n            await fetchFeatures(); // 重新获取状态\n          } else {\n            console.error('❌ API返回success=false:', response);\n            message.error('切换特性状态失败');\n          }\n        } catch (error) {\n          console.error('💥 切换特性状态异常:', error);\n          console.error('💥 错误响应:', error);\n          message.error(`切换特性状态失败: ${error.detail || error.message}`);\n        } finally {\n          setUpdating(prev => ({ ...prev, [featureName]: false }));\n        }\n      }\n    });\n  };\n\n  // 重置所有特性\n  const resetFeatures = () => {\n    confirm({\n      title: '重置确认',\n      icon: <ExclamationCircleOutlined />,\n      content: (\n        <div>\n          <p>您确定要重置所有家长端特性配置为默认值吗？</p>\n          <Alert\n            message=\"警告\"\n            description=\"此操作将重置所有特性开关状态，无法撤销！\"\n            type=\"error\"\n            showIcon\n            style={{ marginTop: 8 }}\n          />\n        </div>\n      ),\n      onOk: async () => {\n        try {\n          setLoading(true);\n          const response = await api.post('/admin/parent-features/reset');\n          \n          if (response.data.success) {\n            message.success('特性配置已重置为默认值');\n            await fetchFeatures();\n          } else {\n            message.error('重置特性配置失败');\n          }\n        } catch (error) {\n          console.error('重置特性配置失败:', error);\n          message.error('重置特性配置失败');\n        } finally {\n          setLoading(false);\n        }\n      }\n    });\n  };\n\n  useEffect(() => {\n    fetchFeatures();\n  }, []);\n\n  // 渲染特性开关\n  const renderFeatureSwitch = (featureName, enabled, description, isSubFeature = false) => {\n    const isUpdating = updating[featureName];\n    \n    return (\n      <div \n        key={featureName}\n        style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center',\n          padding: isSubFeature ? '8px 0 8px 24px' : '12px 0',\n          borderLeft: isSubFeature ? '3px solid #f0f0f0' : 'none',\n          marginLeft: isSubFeature ? '12px' : '0'\n        }}\n      >\n        <div style={{ flex: 1 }}>\n          <Space>\n            <Text strong={!isSubFeature} style={{ \n              fontSize: isSubFeature ? '13px' : '14px',\n              color: isSubFeature ? '#666' : '#333'\n            }}>\n              {featureName}\n            </Text>\n            <Badge \n              status={enabled ? 'success' : 'default'} \n              text={enabled ? '已启用' : '已禁用'}\n            />\n          </Space>\n          {description && (\n            <div style={{ marginTop: 4 }}>\n              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                {description}\n              </Text>\n            </div>\n          )}\n        </div>\n        <Tooltip title={enabled ? '点击禁用' : '点击启用'}>\n          <Switch\n            checked={enabled}\n            loading={isUpdating}\n            onChange={() => toggleFeature(featureName)}\n            size={isMobile ? 'small' : 'default'}\n          />\n        </Tooltip>\n      </div>\n    );\n  };\n\n  // 渲染特性类别\n  const renderFeatureCategory = (categoryName, categoryData) => {\n    const { enabled, description, features: subFeatures } = categoryData;\n    \n    return (\n      <Card \n        key={categoryName}\n        size=\"small\"\n        style={{ marginBottom: 16 }}\n        title={\n          <Space>\n            {enabled ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : <CloseCircleOutlined style={{ color: '#ff4d4f' }} />}\n            <span>{categoryName}</span>\n          </Space>\n        }\n        extra={\n          <Switch\n            checked={enabled}\n            loading={updating[categoryName]}\n            onChange={() => toggleFeature(categoryName)}\n            size=\"small\"\n          />\n        }\n      >\n        <div style={{ marginBottom: 12 }}>\n          <Text type=\"secondary\">{description}</Text>\n        </div>\n        \n        {subFeatures && Object.keys(subFeatures).length > 0 && (\n          <div>\n            <Divider style={{ margin: '12px 0' }} />\n            <Text strong style={{ fontSize: '13px', color: '#666' }}>子功能：</Text>\n            <div style={{ marginTop: 8 }}>\n              {Object.entries(subFeatures).map(([featureName, featureEnabled]) => \n                renderFeatureSwitch(\n                  `${categoryName}.${featureName}`, \n                  featureEnabled, \n                  null, \n                  true\n                )\n              )}\n            </div>\n          </div>\n        )}\n      </Card>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>\n          <Text>正在加载家长端特性配置...</Text>\n        </div>\n      </div>\n    );\n  }\n\n  const { categories = {} } = features;\n  console.log('🔍 渲染时的features状态:', features);\n  console.log('🔍 渲染时的categories:', categories);\n  console.log('🔍 categories键数量:', Object.keys(categories).length);\n\n  return (\n    <div style={{ padding: isMobile ? '8px' : '16px' }}>\n      {/* 头部信息 */}\n      <Card style={{ marginBottom: 16 }}>\n        <Row gutter={[16, 16]} align=\"middle\">\n          <Col flex=\"auto\">\n            <Title level={4} style={{ margin: 0 }}>\n              <SettingOutlined style={{ marginRight: 8 }} />\n              家长端功能管理\n            </Title>\n            <Paragraph style={{ margin: '8px 0 0 0', color: '#666' }}>\n              管理家长端各项功能的启用状态，支持分阶段控制功能开放\n            </Paragraph>\n          </Col>\n          <Col>\n            <Space>\n              <Button \n                icon={<ReloadOutlined />} \n                onClick={fetchFeatures}\n                loading={loading}\n              >\n                刷新\n              </Button>\n              <Button \n                danger \n                icon={<ExclamationCircleOutlined />} \n                onClick={resetFeatures}\n              >\n                重置配置\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 功能状态概览 */}\n      <Card title=\"功能状态概览\" style={{ marginBottom: 16 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={8}>\n            <div style={{ textAlign: 'center' }}>\n              <Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n                {features.total_categories || 0}\n              </Title>\n              <Text type=\"secondary\">总功能类别</Text>\n            </div>\n          </Col>\n          <Col xs={24} sm={8}>\n            <div style={{ textAlign: 'center' }}>\n              <Title level={2} style={{ margin: 0, color: '#52c41a' }}>\n                {features.enabled_categories || 0}\n              </Title>\n              <Text type=\"secondary\">已启用类别</Text>\n            </div>\n          </Col>\n          <Col xs={24} sm={8}>\n            <div style={{ textAlign: 'center' }}>\n              <Title level={2} style={{ margin: 0, color: '#faad14' }}>\n                {Object.values(categories).reduce((total, cat) => \n                  total + Object.keys(cat.features || {}).length, 0\n                )}\n              </Title>\n              <Text type=\"secondary\">总子功能数</Text>\n            </div>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 特性开关列表 */}\n      <div>\n        <Title level={5} style={{ marginBottom: 16 }}>\n          <InfoCircleOutlined style={{ marginRight: 8 }} />\n          功能开关控制\n        </Title>\n        \n        {Object.keys(categories).length === 0 ? (\n          <Card>\n            <div style={{ textAlign: 'center', padding: '40px' }}>\n              <Text type=\"secondary\">暂无家长端特性配置</Text>\n            </div>\n          </Card>\n        ) : (\n          Object.entries(categories).map(([categoryName, categoryData]) =>\n            renderFeatureCategory(categoryName, categoryData)\n          )\n        )}\n      </div>\n\n      {/* 使用说明 */}\n      <Card title=\"使用说明\" style={{ marginTop: 16 }}>\n        <ul style={{ paddingLeft: '20px', margin: 0 }}>\n          <li>主类别开关控制整个功能模块的启用状态</li>\n          <li>子功能开关可以精确控制具体功能的启用</li>\n          <li>建议按阶段逐步启用功能，确保系统稳定性</li>\n          <li>第三阶段高级功能建议在充分测试后再启用</li>\n          <li>如遇问题可使用\"重置配置\"恢复默认状态</li>\n        </ul>\n      </Card>\n    </div>\n  );\n};\n\nexport default ParentFeatureManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SACEC,eAAe,EACfC,cAAc,EACdC,yBAAyB,EACzBC,mBAAmB,EACnBC,mBAAmB,EACnBC,kBAAkB,QACb,mBAAmB;AAC1B,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGvB,UAAU;AAC7C,MAAM;EAAEwB;AAAQ,CAAC,GAAGjB,KAAK;AAEzB,MAAMkB,oBAAoB,GAAGA,CAAC;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAMsC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBM,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MAEjD,MAAMC,QAAQ,GAAG,MAAMnB,GAAG,CAACoB,GAAG,CAAC,wBAAwB,CAAC;MACxDH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,QAAQ,CAAC;MACpCF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,QAAQ,CAAC;;MAEjC;MACA,IAAIE,YAAY,GAAG,IAAI;MAEvB,IAAIF,QAAQ,IAAIA,QAAQ,CAACG,OAAO,EAAE;QAChC;QACAD,YAAY,GAAGF,QAAQ,CAACI,IAAI;QAC5BN,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEG,YAAY,CAAC;MAC7C,CAAC,MAAM,IAAIF,QAAQ,IAAIA,QAAQ,CAACK,UAAU,EAAE;QAC1C;QACAH,YAAY,GAAGF,QAAQ;QACvBF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEG,YAAY,CAAC;MAC7C,CAAC,MAAM;QACLJ,OAAO,CAACQ,KAAK,CAAC,cAAc,EAAEN,QAAQ,CAAC;QACvCnC,OAAO,CAACyC,KAAK,CAAC,oBAAoB,CAAC;QACnC;MACF;MAEA,IAAIJ,YAAY,EAAE;QAChBJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEG,YAAY,CAAC;QAC1CR,WAAW,CAACQ,YAAY,CAAC;QACzBrC,OAAO,CAACsC,OAAO,CAAC,aAAa,CAAC;MAChC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCR,OAAO,CAACQ,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BzC,OAAO,CAACyC,KAAK,CAAC,gBAAgBA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACzC,OAAO,EAAE,CAAC;IAChE,CAAC,SAAS;MACRiC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzCP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgB,aAAa,GAAG,MAAOC,WAAW,IAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAC3C,MAAMC,cAAc,GAAG,CAACJ,WAAW,CAACK,QAAQ,CAAC,GAAG,CAAC;IACjD,MAAMT,UAAU,GAAGZ,QAAQ,CAACY,UAAU,IAAI,CAAC,CAAC;IAE5C,MAAMU,aAAa,GAAGF,cAAc,IAAAH,qBAAA,GAChCL,UAAU,CAACI,WAAW,CAAC,cAAAC,qBAAA,uBAAvBA,qBAAA,CAAyBM,OAAO,IAAAL,sBAAA,GAChCN,UAAU,CAACI,WAAW,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAAN,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuClB,QAAQ,cAAAmB,sBAAA,uBAA/CA,sBAAA,CAAkDH,WAAW,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEhFnB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEU,WAAW,EAAE,OAAO,EAAEM,aAAa,CAAC;IAE5D5B,OAAO,CAAC;MACN+B,KAAK,EAAE,MAAM;MACbC,IAAI,eAAEpC,OAAA,CAACN,yBAAyB;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,OAAO,eACLzC,OAAA;QAAA0C,QAAA,gBACE1C,OAAA;UAAA0C,QAAA,GAAG,0BAAI,EAACV,aAAa,GAAG,IAAI,GAAG,IAAI,EAAC,eAAG,eAAAhC,OAAA;YAAA0C,QAAA,EAAShB;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,iBAAG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAC3EV,cAAc,iBACb9B,OAAA,CAACZ,KAAK;UACJN,OAAO,EAAC,cAAI;UACZ6D,WAAW,EAAC,8GAAoB;UAChCC,IAAI,EAAC,SAAS;UACdC,QAAQ;UACRC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAE;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;MACDQ,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACFnC,WAAW,CAACoC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACvB,WAAW,GAAG;UAAK,CAAC,CAAC,CAAC;UACvD,MAAMT,QAAQ,GAAG,MAAMnB,GAAG,CAACoD,IAAI,CAAC,0BAA0BxB,WAAW,SAAS,CAAC;UAE/EX,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,QAAQ,CAAC;UACpCF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACG,OAAO,CAAC;UAErD,IAAIH,QAAQ,CAACG,OAAO,EAAE;YACpBtC,OAAO,CAACsC,OAAO,CAACH,QAAQ,CAACnC,OAAO,CAAC;YACjC,MAAMgC,aAAa,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,MAAM;YACLC,OAAO,CAACQ,KAAK,CAAC,uBAAuB,EAAEN,QAAQ,CAAC;YAChDnC,OAAO,CAACyC,KAAK,CAAC,UAAU,CAAC;UAC3B;QACF,CAAC,CAAC,OAAOA,KAAK,EAAE;UACdR,OAAO,CAACQ,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;UACpCR,OAAO,CAACQ,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;UAChCzC,OAAO,CAACyC,KAAK,CAAC,aAAaA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACzC,OAAO,EAAE,CAAC;QAC7D,CAAC,SAAS;UACR+B,WAAW,CAACoC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACvB,WAAW,GAAG;UAAM,CAAC,CAAC,CAAC;QAC1D;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyB,aAAa,GAAGA,CAAA,KAAM;IAC1B/C,OAAO,CAAC;MACN+B,KAAK,EAAE,MAAM;MACbC,IAAI,eAAEpC,OAAA,CAACN,yBAAyB;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,OAAO,eACLzC,OAAA;QAAA0C,QAAA,gBACE1C,OAAA;UAAA0C,QAAA,EAAG;QAAqB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5BxC,OAAA,CAACZ,KAAK;UACJN,OAAO,EAAC,cAAI;UACZ6D,WAAW,EAAC,0HAAsB;UAClCC,IAAI,EAAC,OAAO;UACZC,QAAQ;UACRC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAE;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;MACDQ,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACFvC,UAAU,CAAC,IAAI,CAAC;UAChB,MAAMQ,QAAQ,GAAG,MAAMnB,GAAG,CAACoD,IAAI,CAAC,8BAA8B,CAAC;UAE/D,IAAIjC,QAAQ,CAACI,IAAI,CAACD,OAAO,EAAE;YACzBtC,OAAO,CAACsC,OAAO,CAAC,aAAa,CAAC;YAC9B,MAAMN,aAAa,CAAC,CAAC;UACvB,CAAC,MAAM;YACLhC,OAAO,CAACyC,KAAK,CAAC,UAAU,CAAC;UAC3B;QACF,CAAC,CAAC,OAAOA,KAAK,EAAE;UACdR,OAAO,CAACQ,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;UACjCzC,OAAO,CAACyC,KAAK,CAAC,UAAU,CAAC;QAC3B,CAAC,SAAS;UACRd,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAEDhC,SAAS,CAAC,MAAM;IACdqC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMsC,mBAAmB,GAAGA,CAAC1B,WAAW,EAAEO,OAAO,EAAEU,WAAW,EAAEU,YAAY,GAAG,KAAK,KAAK;IACvF,MAAMC,UAAU,GAAG1C,QAAQ,CAACc,WAAW,CAAC;IAExC,oBACE1B,OAAA;MAEE8C,KAAK,EAAE;QACLS,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAEL,YAAY,GAAG,gBAAgB,GAAG,QAAQ;QACnDM,UAAU,EAAEN,YAAY,GAAG,mBAAmB,GAAG,MAAM;QACvDO,UAAU,EAAEP,YAAY,GAAG,MAAM,GAAG;MACtC,CAAE;MAAAX,QAAA,gBAEF1C,OAAA;QAAK8C,KAAK,EAAE;UAAEe,IAAI,EAAE;QAAE,CAAE;QAAAnB,QAAA,gBACtB1C,OAAA,CAACV,KAAK;UAAAoD,QAAA,gBACJ1C,OAAA,CAACE,IAAI;YAAC4D,MAAM,EAAE,CAACT,YAAa;YAACP,KAAK,EAAE;cAClCiB,QAAQ,EAAEV,YAAY,GAAG,MAAM,GAAG,MAAM;cACxCW,KAAK,EAAEX,YAAY,GAAG,MAAM,GAAG;YACjC,CAAE;YAAAX,QAAA,EACChB;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACPxC,OAAA,CAACX,KAAK;YACJ4E,MAAM,EAAEhC,OAAO,GAAG,SAAS,GAAG,SAAU;YACxCiC,IAAI,EAAEjC,OAAO,GAAG,KAAK,GAAG;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EACPG,WAAW,iBACV3C,OAAA;UAAK8C,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAE,CAAE;UAAAL,QAAA,eAC3B1C,OAAA,CAACE,IAAI;YAAC0C,IAAI,EAAC,WAAW;YAACE,KAAK,EAAE;cAAEiB,QAAQ,EAAE;YAAO,CAAE;YAAArB,QAAA,EAChDC;UAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNxC,OAAA,CAACT,OAAO;QAAC4C,KAAK,EAAEF,OAAO,GAAG,MAAM,GAAG,MAAO;QAAAS,QAAA,eACxC1C,OAAA,CAACrB,MAAM;UACLwF,OAAO,EAAElC,OAAQ;UACjBzB,OAAO,EAAE8C,UAAW;UACpBc,QAAQ,EAAEA,CAAA,KAAM3C,aAAa,CAACC,WAAW,CAAE;UAC3C2C,IAAI,EAAE/D,QAAQ,GAAG,OAAO,GAAG;QAAU;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA,GAtCLd,WAAW;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAuCb,CAAC;EAEV,CAAC;;EAED;EACA,MAAM8B,qBAAqB,GAAGA,CAACC,YAAY,EAAEC,YAAY,KAAK;IAC5D,MAAM;MAAEvC,OAAO;MAAEU,WAAW;MAAEjC,QAAQ,EAAE+D;IAAY,CAAC,GAAGD,YAAY;IAEpE,oBACExE,OAAA,CAACtB,IAAI;MAEH2F,IAAI,EAAC,OAAO;MACZvB,KAAK,EAAE;QAAE4B,YAAY,EAAE;MAAG,CAAE;MAC5BvC,KAAK,eACHnC,OAAA,CAACV,KAAK;QAAAoD,QAAA,GACHT,OAAO,gBAAGjC,OAAA,CAACL,mBAAmB;UAACmD,KAAK,EAAE;YAAEkB,KAAK,EAAE;UAAU;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGxC,OAAA,CAACJ,mBAAmB;UAACkD,KAAK,EAAE;YAAEkB,KAAK,EAAE;UAAU;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtHxC,OAAA;UAAA0C,QAAA,EAAO6B;QAAY;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CACR;MACDmC,KAAK,eACH3E,OAAA,CAACrB,MAAM;QACLwF,OAAO,EAAElC,OAAQ;QACjBzB,OAAO,EAAEI,QAAQ,CAAC2D,YAAY,CAAE;QAChCH,QAAQ,EAAEA,CAAA,KAAM3C,aAAa,CAAC8C,YAAY,CAAE;QAC5CF,IAAI,EAAC;MAAO;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CACF;MAAAE,QAAA,gBAED1C,OAAA;QAAK8C,KAAK,EAAE;UAAE4B,YAAY,EAAE;QAAG,CAAE;QAAAhC,QAAA,eAC/B1C,OAAA,CAACE,IAAI;UAAC0C,IAAI,EAAC,WAAW;UAAAF,QAAA,EAAEC;QAAW;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,EAELiC,WAAW,IAAIG,MAAM,CAACC,IAAI,CAACJ,WAAW,CAAC,CAACK,MAAM,GAAG,CAAC,iBACjD9E,OAAA;QAAA0C,QAAA,gBACE1C,OAAA,CAACf,OAAO;UAAC6D,KAAK,EAAE;YAAEiC,MAAM,EAAE;UAAS;QAAE;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxCxC,OAAA,CAACE,IAAI;UAAC4D,MAAM;UAAChB,KAAK,EAAE;YAAEiB,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAtB,QAAA,EAAC;QAAI;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpExC,OAAA;UAAK8C,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAE,CAAE;UAAAL,QAAA,EAC1BkC,MAAM,CAACI,OAAO,CAACP,WAAW,CAAC,CAACQ,GAAG,CAAC,CAAC,CAACvD,WAAW,EAAEwD,cAAc,CAAC,KAC7D9B,mBAAmB,CACjB,GAAGmB,YAAY,IAAI7C,WAAW,EAAE,EAChCwD,cAAc,EACd,IAAI,EACJ,IACF,CACF;QAAC;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA,GArCI+B,YAAY;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsCb,CAAC;EAEX,CAAC;EAED,IAAIhC,OAAO,EAAE;IACX,oBACER,OAAA;MAAK8C,KAAK,EAAE;QAAEqC,SAAS,EAAE,QAAQ;QAAEzB,OAAO,EAAE;MAAO,CAAE;MAAAhB,QAAA,gBACnD1C,OAAA,CAACnB,IAAI;QAACwF,IAAI,EAAC;MAAO;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBxC,OAAA;QAAK8C,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAG,CAAE;QAAAL,QAAA,eAC5B1C,OAAA,CAACE,IAAI;UAAAwC,QAAA,EAAC;QAAc;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAM;IAAElB,UAAU,GAAG,CAAC;EAAE,CAAC,GAAGZ,QAAQ;EACpCK,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEN,QAAQ,CAAC;EAC3CK,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,UAAU,CAAC;EAC7CP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE4D,MAAM,CAACC,IAAI,CAACvD,UAAU,CAAC,CAACwD,MAAM,CAAC;EAEhE,oBACE9E,OAAA;IAAK8C,KAAK,EAAE;MAAEY,OAAO,EAAEpD,QAAQ,GAAG,KAAK,GAAG;IAAO,CAAE;IAAAoC,QAAA,gBAEjD1C,OAAA,CAACtB,IAAI;MAACoE,KAAK,EAAE;QAAE4B,YAAY,EAAE;MAAG,CAAE;MAAAhC,QAAA,eAChC1C,OAAA,CAACjB,GAAG;QAACqG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACC,KAAK,EAAC,QAAQ;QAAA3C,QAAA,gBACnC1C,OAAA,CAAChB,GAAG;UAAC6E,IAAI,EAAC,MAAM;UAAAnB,QAAA,gBACd1C,OAAA,CAACC,KAAK;YAACqF,KAAK,EAAE,CAAE;YAACxC,KAAK,EAAE;cAAEiC,MAAM,EAAE;YAAE,CAAE;YAAArC,QAAA,gBACpC1C,OAAA,CAACR,eAAe;cAACsD,KAAK,EAAE;gBAAEyC,WAAW,EAAE;cAAE;YAAE;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8CAEhD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxC,OAAA,CAACG,SAAS;YAAC2C,KAAK,EAAE;cAAEiC,MAAM,EAAE,WAAW;cAAEf,KAAK,EAAE;YAAO,CAAE;YAAAtB,QAAA,EAAC;UAE1D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNxC,OAAA,CAAChB,GAAG;UAAA0D,QAAA,eACF1C,OAAA,CAACV,KAAK;YAAAoD,QAAA,gBACJ1C,OAAA,CAACd,MAAM;cACLkD,IAAI,eAAEpC,OAAA,CAACP,cAAc;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBgD,OAAO,EAAE1E,aAAc;cACvBN,OAAO,EAAEA,OAAQ;cAAAkC,QAAA,EAClB;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxC,OAAA,CAACd,MAAM;cACLuG,MAAM;cACNrD,IAAI,eAAEpC,OAAA,CAACN,yBAAyB;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACpCgD,OAAO,EAAErC,aAAc;cAAAT,QAAA,EACxB;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPxC,OAAA,CAACtB,IAAI;MAACyD,KAAK,EAAC,sCAAQ;MAACW,KAAK,EAAE;QAAE4B,YAAY,EAAE;MAAG,CAAE;MAAAhC,QAAA,eAC/C1C,OAAA,CAACjB,GAAG;QAACqG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAA1C,QAAA,gBACpB1C,OAAA,CAAChB,GAAG;UAAC0G,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAjD,QAAA,eACjB1C,OAAA;YAAK8C,KAAK,EAAE;cAAEqC,SAAS,EAAE;YAAS,CAAE;YAAAzC,QAAA,gBAClC1C,OAAA,CAACC,KAAK;cAACqF,KAAK,EAAE,CAAE;cAACxC,KAAK,EAAE;gBAAEiC,MAAM,EAAE,CAAC;gBAAEf,KAAK,EAAE;cAAU,CAAE;cAAAtB,QAAA,EACrDhC,QAAQ,CAACkF,gBAAgB,IAAI;YAAC;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACRxC,OAAA,CAACE,IAAI;cAAC0C,IAAI,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxC,OAAA,CAAChB,GAAG;UAAC0G,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAjD,QAAA,eACjB1C,OAAA;YAAK8C,KAAK,EAAE;cAAEqC,SAAS,EAAE;YAAS,CAAE;YAAAzC,QAAA,gBAClC1C,OAAA,CAACC,KAAK;cAACqF,KAAK,EAAE,CAAE;cAACxC,KAAK,EAAE;gBAAEiC,MAAM,EAAE,CAAC;gBAAEf,KAAK,EAAE;cAAU,CAAE;cAAAtB,QAAA,EACrDhC,QAAQ,CAACmF,kBAAkB,IAAI;YAAC;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACRxC,OAAA,CAACE,IAAI;cAAC0C,IAAI,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxC,OAAA,CAAChB,GAAG;UAAC0G,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAjD,QAAA,eACjB1C,OAAA;YAAK8C,KAAK,EAAE;cAAEqC,SAAS,EAAE;YAAS,CAAE;YAAAzC,QAAA,gBAClC1C,OAAA,CAACC,KAAK;cAACqF,KAAK,EAAE,CAAE;cAACxC,KAAK,EAAE;gBAAEiC,MAAM,EAAE,CAAC;gBAAEf,KAAK,EAAE;cAAU,CAAE;cAAAtB,QAAA,EACrDkC,MAAM,CAACkB,MAAM,CAACxE,UAAU,CAAC,CAACyE,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAC3CD,KAAK,GAAGpB,MAAM,CAACC,IAAI,CAACoB,GAAG,CAACvF,QAAQ,IAAI,CAAC,CAAC,CAAC,CAACoE,MAAM,EAAE,CAClD;YAAC;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACRxC,OAAA,CAACE,IAAI;cAAC0C,IAAI,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPxC,OAAA;MAAA0C,QAAA,gBACE1C,OAAA,CAACC,KAAK;QAACqF,KAAK,EAAE,CAAE;QAACxC,KAAK,EAAE;UAAE4B,YAAY,EAAE;QAAG,CAAE;QAAAhC,QAAA,gBAC3C1C,OAAA,CAACH,kBAAkB;UAACiD,KAAK,EAAE;YAAEyC,WAAW,EAAE;UAAE;QAAE;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,wCAEnD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAEPoC,MAAM,CAACC,IAAI,CAACvD,UAAU,CAAC,CAACwD,MAAM,KAAK,CAAC,gBACnC9E,OAAA,CAACtB,IAAI;QAAAgE,QAAA,eACH1C,OAAA;UAAK8C,KAAK,EAAE;YAAEqC,SAAS,EAAE,QAAQ;YAAEzB,OAAO,EAAE;UAAO,CAAE;UAAAhB,QAAA,eACnD1C,OAAA,CAACE,IAAI;YAAC0C,IAAI,EAAC,WAAW;YAAAF,QAAA,EAAC;UAAS;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,GAEPoC,MAAM,CAACI,OAAO,CAAC1D,UAAU,CAAC,CAAC2D,GAAG,CAAC,CAAC,CAACV,YAAY,EAAEC,YAAY,CAAC,KAC1DF,qBAAqB,CAACC,YAAY,EAAEC,YAAY,CAClD,CACD;IAAA;MAAAnC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNxC,OAAA,CAACtB,IAAI;MAACyD,KAAK,EAAC,0BAAM;MAACW,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAAAL,QAAA,eAC1C1C,OAAA;QAAI8C,KAAK,EAAE;UAAEoD,WAAW,EAAE,MAAM;UAAEnB,MAAM,EAAE;QAAE,CAAE;QAAArC,QAAA,gBAC5C1C,OAAA;UAAA0C,QAAA,EAAI;QAAkB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BxC,OAAA;UAAA0C,QAAA,EAAI;QAAkB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BxC,OAAA;UAAA0C,QAAA,EAAI;QAAmB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BxC,OAAA;UAAA0C,QAAA,EAAI;QAAmB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BxC,OAAA;UAAA0C,QAAA,EAAI;QAAmB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjC,EAAA,CApWIF,oBAAoB;AAAA8F,EAAA,GAApB9F,oBAAoB;AAsW1B,eAAeA,oBAAoB;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}