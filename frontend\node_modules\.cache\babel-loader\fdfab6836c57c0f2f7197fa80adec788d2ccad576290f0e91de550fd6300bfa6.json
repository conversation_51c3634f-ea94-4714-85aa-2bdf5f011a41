{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport DescriptionsContext from './DescriptionsContext';\nfunction notEmpty(val) {\n  return val !== undefined && val !== null;\n}\nconst Cell = props => {\n  const {\n    itemPrefixCls,\n    component,\n    span,\n    className,\n    style,\n    labelStyle,\n    contentStyle,\n    bordered,\n    label,\n    content,\n    colon,\n    type,\n    styles\n  } = props;\n  const Component = component;\n  const descContext = React.useContext(DescriptionsContext);\n  const {\n    classNames: descriptionsClassNames\n  } = descContext;\n  if (bordered) {\n    return /*#__PURE__*/React.createElement(Component, {\n      className: classNames({\n        [`${itemPrefixCls}-item-label`]: type === 'label',\n        [`${itemPrefixCls}-item-content`]: type === 'content',\n        [`${descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.label}`]: type === 'label',\n        [`${descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.content}`]: type === 'content'\n      }, className),\n      style: style,\n      colSpan: span\n    }, notEmpty(label) && /*#__PURE__*/React.createElement(\"span\", {\n      style: Object.assign(Object.assign({}, labelStyle), styles === null || styles === void 0 ? void 0 : styles.label)\n    }, label), notEmpty(content) && /*#__PURE__*/React.createElement(\"span\", {\n      style: Object.assign(Object.assign({}, labelStyle), styles === null || styles === void 0 ? void 0 : styles.content)\n    }, content));\n  }\n  return /*#__PURE__*/React.createElement(Component, {\n    className: classNames(`${itemPrefixCls}-item`, className),\n    style: style,\n    colSpan: span\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${itemPrefixCls}-item-container`\n  }, (label || label === 0) && (/*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(`${itemPrefixCls}-item-label`, descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.label, {\n      [`${itemPrefixCls}-item-no-colon`]: !colon\n    }),\n    style: Object.assign(Object.assign({}, labelStyle), styles === null || styles === void 0 ? void 0 : styles.label)\n  }, label)), (content || content === 0) && (/*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(`${itemPrefixCls}-item-content`, descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.content),\n    style: Object.assign(Object.assign({}, contentStyle), styles === null || styles === void 0 ? void 0 : styles.content)\n  }, content))));\n};\nexport default Cell;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}