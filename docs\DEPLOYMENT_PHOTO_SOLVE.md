# 🚀 拍照解题功能部署指南

## 📋 部署概述

本指南详细说明如何部署和配置拍照解题功能，包括前端组件、后端API和AI服务配置。

## 🔧 环境要求

### 系统要求
- **操作系统**：Linux/Windows/macOS
- **Python版本**：3.8+
- **Node.js版本**：16+
- **数据库**：SQLite/PostgreSQL/MySQL

### 依赖包要求
```bash
# Python后端依赖
pip install pillow>=9.0.0
pip install requests>=2.28.0
pip install fastapi>=0.68.0
pip install python-multipart>=0.0.5

# Node.js前端依赖
npm install antd>=4.24.0
npm install axios>=0.27.0
```

## 🏗️ 后端部署

### 1. 安装Python依赖
```bash
cd backend
pip install -r requirements.txt

# 如果requirements.txt中没有，手动安装
pip install pillow requests python-multipart
```

### 2. 数据库配置
确保AI配置表存在并配置火山引擎信息：

```sql
-- 检查ai_model_configs表
SELECT * FROM ai_model_configs WHERE provider = 'volcano';

-- 如果不存在，插入配置
INSERT INTO ai_model_configs (
    provider, 
    model_name, 
    model_id, 
    api_endpoint, 
    api_key, 
    is_active
) VALUES (
    'volcano',
    'Doubao-Seed-1.6-flash',
    'ep-20250726110928-qzhhc',
    'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
    'YOUR_VOLCANO_API_KEY',
    1
);
```

### 3. 环境变量配置
创建 `.env` 文件：
```bash
# AI服务配置
VOLCANO_API_KEY=your_actual_api_key_here
VOLCANO_ENDPOINT=https://ark.cn-beijing.volces.com/api/v3/chat/completions
VOLCANO_MODEL_ID=ep-20250726110928-qzhhc

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
TEMP_DIR=/tmp/photo_solve

# 安全配置
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif
```

### 4. 启动后端服务
```bash
cd backend
python main.py
```

验证后端服务：
```bash
curl -X GET http://localhost:8083/health
```

## 🎨 前端部署

### 1. 安装Node.js依赖
```bash
cd frontend
npm install

# 如果需要，安装特定依赖
npm install antd axios
```

### 2. 环境配置
创建 `.env` 文件：
```bash
REACT_APP_API_BASE_URL=http://localhost:8083
REACT_APP_PHOTO_SOLVE_ENABLED=true
```

### 3. 构建前端
```bash
# 开发环境
npm start

# 生产环境
npm run build
```

### 4. 验证前端功能
- 访问 `http://localhost:3000`
- 学生用户登录
- 检查左侧菜单是否有"拍照解题"选项
- 测试拍照功能是否正常

## 🔐 AI服务配置

### 1. 火山引擎账号配置
1. 注册火山引擎账号
2. 开通豆包大模型服务
3. 创建推理接入点
4. 获取API Key和模型ID

### 2. 模型配置验证
```python
# 测试脚本 test_volcano.py
import requests
import base64

api_key = "YOUR_API_KEY"
model_id = "YOUR_MODEL_ID"
endpoint = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {api_key}"
}

# 测试文本请求
payload = {
    "model": model_id,
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 100
}

response = requests.post(endpoint, headers=headers, json=payload)
print(f"Status: {response.status_code}")
print(f"Response: {response.text}")
```

### 3. 数据库配置更新
```python
# 更新数据库配置脚本
from app.database import SessionLocal
from app.models.ai_config import AIModelConfig

db = SessionLocal()

# 更新或创建配置
config = db.query(AIModelConfig).filter(
    AIModelConfig.provider == "volcano"
).first()

if config:
    config.api_key = "YOUR_NEW_API_KEY"
    config.model_id = "YOUR_NEW_MODEL_ID"
else:
    config = AIModelConfig(
        provider="volcano",
        model_name="Doubao-Seed-1.6-flash",
        model_id="YOUR_MODEL_ID",
        api_endpoint="https://ark.cn-beijing.volces.com/api/v3/chat/completions",
        api_key="YOUR_API_KEY",
        is_active=True
    )
    db.add(config)

db.commit()
db.close()
```

## 🌐 Nginx配置

### 反向代理配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/frontend/build;
        try_files $uri $uri/ /index.html;
    }

    # 后端API
    location /api/ {
        proxy_pass http://localhost:8083;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 文件上传配置
        client_max_body_size 10M;
        proxy_read_timeout 60s;
        proxy_connect_timeout 60s;
    }
}
```

### HTTPS配置
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # 相机功能需要HTTPS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 其他配置同上...
}
```

## 🐳 Docker部署

### Dockerfile配置
```dockerfile
# 后端Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8083

CMD ["python", "main.py"]
```

```dockerfile
# 前端Dockerfile
FROM node:16-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

### Docker Compose配置
```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8083:8083"
    environment:
      - VOLCANO_API_KEY=${VOLCANO_API_KEY}
      - DATABASE_URL=${DATABASE_URL}
    volumes:
      - ./backend:/app
      - /tmp/photo_solve:/tmp/photo_solve

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

  database:
    image: postgres:13
    environment:
      - POSTGRES_DB=smartedu
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 🔍 部署验证

### 1. 功能测试清单
- [ ] 后端API健康检查
- [ ] 数据库连接正常
- [ ] AI服务配置正确
- [ ] 前端页面加载正常
- [ ] 学生用户可以登录
- [ ] 拍照解题菜单显示
- [ ] 相机权限请求正常
- [ ] 文件上传功能正常
- [ ] AI解题返回结果
- [ ] 错误处理正常

### 2. 性能测试
```bash
# API响应时间测试
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8083/api/students/photo-solve"

# 并发测试
ab -n 100 -c 10 http://localhost:8083/api/health
```

### 3. 监控配置
```python
# 添加监控日志
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('photo_solve.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
```

## ⚠️ 常见问题

### 1. 相机权限问题
**问题**：浏览器无法访问相机
**解决**：
- 确保使用HTTPS协议
- 检查浏览器权限设置
- 提供文件上传降级方案

### 2. AI服务调用失败
**问题**：API返回错误
**解决**：
- 验证API Key有效性
- 检查模型ID正确性
- 确认网络连接正常

### 3. 图片处理失败
**问题**：PIL库相关错误
**解决**：
```bash
# 安装完整的PIL依赖
pip install pillow[imaging]

# 或者安装系统依赖
apt-get install libjpeg-dev libpng-dev
```

### 4. 文件上传大小限制
**问题**：大文件上传失败
**解决**：
```python
# FastAPI配置
from fastapi import FastAPI
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 📊 监控与维护

### 日志监控
```bash
# 查看解题日志
tail -f photo_solve.log | grep "photo-solve"

# 错误日志统计
grep "ERROR" photo_solve.log | wc -l
```

### 性能监控
- API响应时间
- 文件处理时间
- AI服务调用成功率
- 用户使用频率

### 定期维护
- 清理临时文件
- 更新AI模型配置
- 检查API配额使用情况
- 备份用户数据

---

*最后更新时间：2025年8月1日*
*部署版本：v1.0*
