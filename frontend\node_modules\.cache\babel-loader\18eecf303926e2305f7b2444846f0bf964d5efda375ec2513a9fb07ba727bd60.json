{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys\\\\frontend\\\\src\\\\components\\\\ParentFeatureManager.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Switch, Typography, Spin, message, Row, Col, Divider, Button, Modal, Alert, Badge, Space, Tooltip } from 'antd';\nimport { SettingOutlined, ReloadOutlined, ExclamationCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';\nimport api from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  confirm\n} = Modal;\nconst ParentFeatureManager = ({\n  isMobile = false\n}) => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [features, setFeatures] = useState({});\n  const [updating, setUpdating] = useState({});\n\n  // 获取家长端特性状态\n  const fetchFeatures = async () => {\n    console.log('🚀 开始获取家长端特性状态...');\n    try {\n      setLoading(true);\n      console.log('📡 发送API请求: /admin/parent-features');\n      const response = await api.get('/admin/parent-features');\n      console.log('📨 收到API响应:', response);\n      console.log('📊 响应状态码:', response.status);\n      console.log('📋 响应数据:', response.data);\n\n      // 处理两种可能的响应格式\n      let featuresData = null;\n      if (response.data && response.data.success) {\n        // 标准格式: {success: true, data: {...}}\n        featuresData = response.data.data;\n        console.log('✅ 标准格式响应，特性数据:', featuresData);\n      } else if (response.data && response.data.categories) {\n        // 直接格式: {total_categories: 3, categories: {...}}\n        featuresData = response.data;\n        console.log('✅ 直接格式响应，特性数据:', featuresData);\n      } else {\n        console.error('❌ 无法识别的响应格式:', response.data);\n        message.error('获取家长端特性状态失败：响应格式错误');\n        return;\n      }\n      if (featuresData) {\n        console.log('🎯 设置特性数据到状态:', featuresData);\n        setFeatures(featuresData);\n        message.success('家长端特性状态加载成功');\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data;\n      console.error('💥 API调用异常:', error);\n      console.error('错误详情:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n      message.error(`获取家长端特性状态失败: ${((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message}`);\n    } finally {\n      console.log('🏁 API调用完成，设置loading为false');\n      setLoading(false);\n    }\n  };\n\n  // 切换特性状态\n  const toggleFeature = async featureName => {\n    var _categories$featureNa, _categories$featureNa2, _categories$featureNa3;\n    const isMainCategory = !featureName.includes('.');\n    const categories = features.categories || {};\n    const currentStatus = isMainCategory ? (_categories$featureNa = categories[featureName]) === null || _categories$featureNa === void 0 ? void 0 : _categories$featureNa.enabled : (_categories$featureNa2 = categories[featureName.split('.')[0]]) === null || _categories$featureNa2 === void 0 ? void 0 : (_categories$featureNa3 = _categories$featureNa2.features) === null || _categories$featureNa3 === void 0 ? void 0 : _categories$featureNa3[featureName.split('.')[1]];\n    console.log('🔄 切换特性:', featureName, '当前状态:', currentStatus);\n    confirm({\n      title: '确认操作',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 13\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u60A8\\u786E\\u5B9A\\u8981\", currentStatus ? '禁用' : '启用', \"\\u529F\\u80FD \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: featureName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 50\n          }, this), \" \\u5417\\uFF1F\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), isMainCategory && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u6CE8\\u610F\",\n          description: \"\\u5207\\u6362\\u4E3B\\u7C7B\\u522B\\u5C06\\u5F71\\u54CD\\u8BE5\\u7C7B\\u522B\\u4E0B\\u7684\\u6240\\u6709\\u5B50\\u529F\\u80FD\",\n          type: \"warning\",\n          showIcon: true,\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this),\n      onOk: async () => {\n        try {\n          setUpdating(prev => ({\n            ...prev,\n            [featureName]: true\n          }));\n          const response = await api.post(`/admin/parent-features/${featureName}/toggle`);\n          console.log('🔍 API响应数据:', response.data);\n          console.log('🔍 response.data.success:', response.data.success);\n          if (response.data.success) {\n            message.success(response.data.message);\n            await fetchFeatures(); // 重新获取状态\n          } else {\n            console.error('❌ API返回success=false:', response.data);\n            message.error('切换特性状态失败');\n          }\n        } catch (error) {\n          var _error$response3, _error$response4, _error$response4$data;\n          console.error('💥 切换特性状态异常:', error);\n          console.error('💥 错误响应:', (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data);\n          message.error(`切换特性状态失败: ${((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message}`);\n        } finally {\n          setUpdating(prev => ({\n            ...prev,\n            [featureName]: false\n          }));\n        }\n      }\n    });\n  };\n\n  // 重置所有特性\n  const resetFeatures = () => {\n    confirm({\n      title: '重置确认',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 13\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u60A8\\u786E\\u5B9A\\u8981\\u91CD\\u7F6E\\u6240\\u6709\\u5BB6\\u957F\\u7AEF\\u7279\\u6027\\u914D\\u7F6E\\u4E3A\\u9ED8\\u8BA4\\u503C\\u5417\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u8B66\\u544A\",\n          description: \"\\u6B64\\u64CD\\u4F5C\\u5C06\\u91CD\\u7F6E\\u6240\\u6709\\u7279\\u6027\\u5F00\\u5173\\u72B6\\u6001\\uFF0C\\u65E0\\u6CD5\\u64A4\\u9500\\uFF01\",\n          type: \"error\",\n          showIcon: true,\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this),\n      onOk: async () => {\n        try {\n          setLoading(true);\n          const response = await api.post('/admin/parent-features/reset');\n          if (response.data.success) {\n            message.success('特性配置已重置为默认值');\n            await fetchFeatures();\n          } else {\n            message.error('重置特性配置失败');\n          }\n        } catch (error) {\n          console.error('重置特性配置失败:', error);\n          message.error('重置特性配置失败');\n        } finally {\n          setLoading(false);\n        }\n      }\n    });\n  };\n  useEffect(() => {\n    fetchFeatures();\n  }, []);\n\n  // 渲染特性开关\n  const renderFeatureSwitch = (featureName, enabled, description, isSubFeature = false) => {\n    const isUpdating = updating[featureName];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        padding: isSubFeature ? '8px 0 8px 24px' : '12px 0',\n        borderLeft: isSubFeature ? '3px solid #f0f0f0' : 'none',\n        marginLeft: isSubFeature ? '12px' : '0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: !isSubFeature,\n            style: {\n              fontSize: isSubFeature ? '13px' : '14px',\n              color: isSubFeature ? '#666' : '#333'\n            },\n            children: featureName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n            status: enabled ? 'success' : 'default',\n            text: enabled ? '已启用' : '已禁用'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), description && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            style: {\n              fontSize: '12px'\n            },\n            children: description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: enabled ? '点击禁用' : '点击启用',\n        children: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: enabled,\n          loading: isUpdating,\n          onChange: () => toggleFeature(featureName),\n          size: isMobile ? 'small' : 'default'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)]\n    }, featureName, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染特性类别\n  const renderFeatureCategory = (categoryName, categoryData) => {\n    const {\n      enabled,\n      description,\n      features: subFeatures\n    } = categoryData;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [enabled ? /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 24\n        }, this) : /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 79\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: categoryName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this),\n      extra: /*#__PURE__*/_jsxDEV(Switch, {\n        checked: enabled,\n        loading: updating[categoryName],\n        onChange: () => toggleFeature(categoryName),\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 12\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), subFeatures && Object.keys(subFeatures).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Divider, {\n          style: {\n            margin: '12px 0'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          style: {\n            fontSize: '13px',\n            color: '#666'\n          },\n          children: \"\\u5B50\\u529F\\u80FD\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: Object.entries(subFeatures).map(([featureName, featureEnabled]) => renderFeatureSwitch(`${categoryName}.${featureName}`, featureEnabled, null, true))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this)]\n    }, categoryName, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u5BB6\\u957F\\u7AEF\\u7279\\u6027\\u914D\\u7F6E...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this);\n  }\n  const {\n    categories = {}\n  } = features;\n  console.log('🔍 渲染时的features状态:', features);\n  console.log('🔍 渲染时的categories:', categories);\n  console.log('🔍 categories键数量:', Object.keys(categories).length);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: isMobile ? '8px' : '16px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {\n              style: {\n                marginRight: 8\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), \"\\u5BB6\\u957F\\u7AEF\\u529F\\u80FD\\u7BA1\\u7406\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              margin: '8px 0 0 0',\n              color: '#666'\n            },\n            children: \"\\u7BA1\\u7406\\u5BB6\\u957F\\u7AEF\\u5404\\u9879\\u529F\\u80FD\\u7684\\u542F\\u7528\\u72B6\\u6001\\uFF0C\\u652F\\u6301\\u5206\\u9636\\u6BB5\\u63A7\\u5236\\u529F\\u80FD\\u5F00\\u653E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 23\n              }, this),\n              onClick: fetchFeatures,\n              loading: loading,\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              danger: true,\n              icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 23\n              }, this),\n              onClick: resetFeatures,\n              children: \"\\u91CD\\u7F6E\\u914D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u529F\\u80FD\\u72B6\\u6001\\u6982\\u89C8\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 2,\n              style: {\n                margin: 0,\n                color: '#1890ff'\n              },\n              children: features.total_categories || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u603B\\u529F\\u80FD\\u7C7B\\u522B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 2,\n              style: {\n                margin: 0,\n                color: '#52c41a'\n              },\n              children: features.enabled_categories || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u5DF2\\u542F\\u7528\\u7C7B\\u522B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 2,\n              style: {\n                margin: 0,\n                color: '#faad14'\n              },\n              children: Object.values(categories).reduce((total, cat) => total + Object.keys(cat.features || {}).length, 0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u603B\\u5B50\\u529F\\u80FD\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 5,\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(InfoCircleOutlined, {\n          style: {\n            marginRight: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), \"\\u529F\\u80FD\\u5F00\\u5173\\u63A7\\u5236\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), Object.keys(categories).length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '40px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u6682\\u65E0\\u5BB6\\u957F\\u7AEF\\u7279\\u6027\\u914D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 11\n      }, this) : Object.entries(categories).map(([categoryName, categoryData]) => renderFeatureCategory(categoryName, categoryData))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4F7F\\u7528\\u8BF4\\u660E\",\n      style: {\n        marginTop: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          paddingLeft: '20px',\n          margin: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u4E3B\\u7C7B\\u522B\\u5F00\\u5173\\u63A7\\u5236\\u6574\\u4E2A\\u529F\\u80FD\\u6A21\\u5757\\u7684\\u542F\\u7528\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u5B50\\u529F\\u80FD\\u5F00\\u5173\\u53EF\\u4EE5\\u7CBE\\u786E\\u63A7\\u5236\\u5177\\u4F53\\u529F\\u80FD\\u7684\\u542F\\u7528\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u5EFA\\u8BAE\\u6309\\u9636\\u6BB5\\u9010\\u6B65\\u542F\\u7528\\u529F\\u80FD\\uFF0C\\u786E\\u4FDD\\u7CFB\\u7EDF\\u7A33\\u5B9A\\u6027\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u7B2C\\u4E09\\u9636\\u6BB5\\u9AD8\\u7EA7\\u529F\\u80FD\\u5EFA\\u8BAE\\u5728\\u5145\\u5206\\u6D4B\\u8BD5\\u540E\\u518D\\u542F\\u7528\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\u5982\\u9047\\u95EE\\u9898\\u53EF\\u4F7F\\u7528\\\"\\u91CD\\u7F6E\\u914D\\u7F6E\\\"\\u6062\\u590D\\u9ED8\\u8BA4\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 290,\n    columnNumber: 5\n  }, this);\n};\n_s(ParentFeatureManager, \"0StpxOLZPmqnh1zxgns2bB+/JuI=\");\n_c = ParentFeatureManager;\nexport default ParentFeatureManager;\nvar _c;\n$RefreshReg$(_c, \"ParentFeatureManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Switch", "Typography", "Spin", "message", "Row", "Col", "Divider", "<PERSON><PERSON>", "Modal", "<PERSON><PERSON>", "Badge", "Space", "<PERSON><PERSON><PERSON>", "SettingOutlined", "ReloadOutlined", "ExclamationCircleOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "InfoCircleOutlined", "api", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "confirm", "ParentFeatureManager", "isMobile", "_s", "loading", "setLoading", "features", "setFeatures", "updating", "setUpdating", "fetchFeatures", "console", "log", "response", "get", "status", "data", "featuresData", "success", "categories", "error", "_error$response", "_error$response2", "_error$response2$data", "detail", "toggleFeature", "featureName", "_categories$featureNa", "_categories$featureNa2", "_categories$featureNa3", "isMainCategory", "includes", "currentStatus", "enabled", "split", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "children", "description", "type", "showIcon", "style", "marginTop", "onOk", "prev", "post", "_error$response3", "_error$response4", "_error$response4$data", "resetFeatures", "renderFeatureSwitch", "isSubFeature", "isUpdating", "display", "justifyContent", "alignItems", "padding", "borderLeft", "marginLeft", "flex", "strong", "fontSize", "color", "text", "checked", "onChange", "size", "renderFeatureCategory", "categoryName", "categoryData", "subFeatures", "marginBottom", "extra", "Object", "keys", "length", "margin", "entries", "map", "featureEnabled", "textAlign", "gutter", "align", "level", "marginRight", "onClick", "danger", "xs", "sm", "total_categories", "enabled_categories", "values", "reduce", "total", "cat", "paddingLeft", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys/frontend/src/components/ParentFeatureManager.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Switch,\n  Typography,\n  Spin,\n  message,\n  Row,\n  Col,\n  Divider,\n  Button,\n  Modal,\n  Alert,\n  Badge,\n  Space,\n  Tooltip\n} from 'antd';\nimport {\n  SettingOutlined,\n  ReloadOutlined,\n  ExclamationCircleOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  InfoCircleOutlined\n} from '@ant-design/icons';\nimport api from '../utils/api';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { confirm } = Modal;\n\nconst ParentFeatureManager = ({ isMobile = false }) => {\n  const [loading, setLoading] = useState(true);\n  const [features, setFeatures] = useState({});\n  const [updating, setUpdating] = useState({});\n\n  // 获取家长端特性状态\n  const fetchFeatures = async () => {\n    console.log('🚀 开始获取家长端特性状态...');\n    try {\n      setLoading(true);\n      console.log('📡 发送API请求: /admin/parent-features');\n\n      const response = await api.get('/admin/parent-features');\n      console.log('📨 收到API响应:', response);\n      console.log('📊 响应状态码:', response.status);\n      console.log('📋 响应数据:', response.data);\n\n      // 处理两种可能的响应格式\n      let featuresData = null;\n\n      if (response.data && response.data.success) {\n        // 标准格式: {success: true, data: {...}}\n        featuresData = response.data.data;\n        console.log('✅ 标准格式响应，特性数据:', featuresData);\n      } else if (response.data && response.data.categories) {\n        // 直接格式: {total_categories: 3, categories: {...}}\n        featuresData = response.data;\n        console.log('✅ 直接格式响应，特性数据:', featuresData);\n      } else {\n        console.error('❌ 无法识别的响应格式:', response.data);\n        message.error('获取家长端特性状态失败：响应格式错误');\n        return;\n      }\n\n      if (featuresData) {\n        console.log('🎯 设置特性数据到状态:', featuresData);\n        setFeatures(featuresData);\n        message.success('家长端特性状态加载成功');\n      }\n    } catch (error) {\n      console.error('💥 API调用异常:', error);\n      console.error('错误详情:', error.response?.data || error.message);\n      message.error(`获取家长端特性状态失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      console.log('🏁 API调用完成，设置loading为false');\n      setLoading(false);\n    }\n  };\n\n  // 切换特性状态\n  const toggleFeature = async (featureName) => {\n    const isMainCategory = !featureName.includes('.');\n    const categories = features.categories || {};\n\n    const currentStatus = isMainCategory\n      ? categories[featureName]?.enabled\n      : categories[featureName.split('.')[0]]?.features?.[featureName.split('.')[1]];\n\n    console.log('🔄 切换特性:', featureName, '当前状态:', currentStatus);\n\n    confirm({\n      title: '确认操作',\n      icon: <ExclamationCircleOutlined />,\n      content: (\n        <div>\n          <p>您确定要{currentStatus ? '禁用' : '启用'}功能 <strong>{featureName}</strong> 吗？</p>\n          {isMainCategory && (\n            <Alert\n              message=\"注意\"\n              description=\"切换主类别将影响该类别下的所有子功能\"\n              type=\"warning\"\n              showIcon\n              style={{ marginTop: 8 }}\n            />\n          )}\n        </div>\n      ),\n      onOk: async () => {\n        try {\n          setUpdating(prev => ({ ...prev, [featureName]: true }));\n          const response = await api.post(`/admin/parent-features/${featureName}/toggle`);\n\n          console.log('🔍 API响应数据:', response.data);\n          console.log('🔍 response.data.success:', response.data.success);\n\n          if (response.data.success) {\n            message.success(response.data.message);\n            await fetchFeatures(); // 重新获取状态\n          } else {\n            console.error('❌ API返回success=false:', response.data);\n            message.error('切换特性状态失败');\n          }\n        } catch (error) {\n          console.error('💥 切换特性状态异常:', error);\n          console.error('💥 错误响应:', error.response?.data);\n          message.error(`切换特性状态失败: ${error.response?.data?.detail || error.message}`);\n        } finally {\n          setUpdating(prev => ({ ...prev, [featureName]: false }));\n        }\n      }\n    });\n  };\n\n  // 重置所有特性\n  const resetFeatures = () => {\n    confirm({\n      title: '重置确认',\n      icon: <ExclamationCircleOutlined />,\n      content: (\n        <div>\n          <p>您确定要重置所有家长端特性配置为默认值吗？</p>\n          <Alert\n            message=\"警告\"\n            description=\"此操作将重置所有特性开关状态，无法撤销！\"\n            type=\"error\"\n            showIcon\n            style={{ marginTop: 8 }}\n          />\n        </div>\n      ),\n      onOk: async () => {\n        try {\n          setLoading(true);\n          const response = await api.post('/admin/parent-features/reset');\n          \n          if (response.data.success) {\n            message.success('特性配置已重置为默认值');\n            await fetchFeatures();\n          } else {\n            message.error('重置特性配置失败');\n          }\n        } catch (error) {\n          console.error('重置特性配置失败:', error);\n          message.error('重置特性配置失败');\n        } finally {\n          setLoading(false);\n        }\n      }\n    });\n  };\n\n  useEffect(() => {\n    fetchFeatures();\n  }, []);\n\n  // 渲染特性开关\n  const renderFeatureSwitch = (featureName, enabled, description, isSubFeature = false) => {\n    const isUpdating = updating[featureName];\n    \n    return (\n      <div \n        key={featureName}\n        style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center',\n          padding: isSubFeature ? '8px 0 8px 24px' : '12px 0',\n          borderLeft: isSubFeature ? '3px solid #f0f0f0' : 'none',\n          marginLeft: isSubFeature ? '12px' : '0'\n        }}\n      >\n        <div style={{ flex: 1 }}>\n          <Space>\n            <Text strong={!isSubFeature} style={{ \n              fontSize: isSubFeature ? '13px' : '14px',\n              color: isSubFeature ? '#666' : '#333'\n            }}>\n              {featureName}\n            </Text>\n            <Badge \n              status={enabled ? 'success' : 'default'} \n              text={enabled ? '已启用' : '已禁用'}\n            />\n          </Space>\n          {description && (\n            <div style={{ marginTop: 4 }}>\n              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                {description}\n              </Text>\n            </div>\n          )}\n        </div>\n        <Tooltip title={enabled ? '点击禁用' : '点击启用'}>\n          <Switch\n            checked={enabled}\n            loading={isUpdating}\n            onChange={() => toggleFeature(featureName)}\n            size={isMobile ? 'small' : 'default'}\n          />\n        </Tooltip>\n      </div>\n    );\n  };\n\n  // 渲染特性类别\n  const renderFeatureCategory = (categoryName, categoryData) => {\n    const { enabled, description, features: subFeatures } = categoryData;\n    \n    return (\n      <Card \n        key={categoryName}\n        size=\"small\"\n        style={{ marginBottom: 16 }}\n        title={\n          <Space>\n            {enabled ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : <CloseCircleOutlined style={{ color: '#ff4d4f' }} />}\n            <span>{categoryName}</span>\n          </Space>\n        }\n        extra={\n          <Switch\n            checked={enabled}\n            loading={updating[categoryName]}\n            onChange={() => toggleFeature(categoryName)}\n            size=\"small\"\n          />\n        }\n      >\n        <div style={{ marginBottom: 12 }}>\n          <Text type=\"secondary\">{description}</Text>\n        </div>\n        \n        {subFeatures && Object.keys(subFeatures).length > 0 && (\n          <div>\n            <Divider style={{ margin: '12px 0' }} />\n            <Text strong style={{ fontSize: '13px', color: '#666' }}>子功能：</Text>\n            <div style={{ marginTop: 8 }}>\n              {Object.entries(subFeatures).map(([featureName, featureEnabled]) => \n                renderFeatureSwitch(\n                  `${categoryName}.${featureName}`, \n                  featureEnabled, \n                  null, \n                  true\n                )\n              )}\n            </div>\n          </div>\n        )}\n      </Card>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>\n          <Text>正在加载家长端特性配置...</Text>\n        </div>\n      </div>\n    );\n  }\n\n  const { categories = {} } = features;\n  console.log('🔍 渲染时的features状态:', features);\n  console.log('🔍 渲染时的categories:', categories);\n  console.log('🔍 categories键数量:', Object.keys(categories).length);\n\n  return (\n    <div style={{ padding: isMobile ? '8px' : '16px' }}>\n      {/* 头部信息 */}\n      <Card style={{ marginBottom: 16 }}>\n        <Row gutter={[16, 16]} align=\"middle\">\n          <Col flex=\"auto\">\n            <Title level={4} style={{ margin: 0 }}>\n              <SettingOutlined style={{ marginRight: 8 }} />\n              家长端功能管理\n            </Title>\n            <Paragraph style={{ margin: '8px 0 0 0', color: '#666' }}>\n              管理家长端各项功能的启用状态，支持分阶段控制功能开放\n            </Paragraph>\n          </Col>\n          <Col>\n            <Space>\n              <Button \n                icon={<ReloadOutlined />} \n                onClick={fetchFeatures}\n                loading={loading}\n              >\n                刷新\n              </Button>\n              <Button \n                danger \n                icon={<ExclamationCircleOutlined />} \n                onClick={resetFeatures}\n              >\n                重置配置\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 功能状态概览 */}\n      <Card title=\"功能状态概览\" style={{ marginBottom: 16 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={8}>\n            <div style={{ textAlign: 'center' }}>\n              <Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n                {features.total_categories || 0}\n              </Title>\n              <Text type=\"secondary\">总功能类别</Text>\n            </div>\n          </Col>\n          <Col xs={24} sm={8}>\n            <div style={{ textAlign: 'center' }}>\n              <Title level={2} style={{ margin: 0, color: '#52c41a' }}>\n                {features.enabled_categories || 0}\n              </Title>\n              <Text type=\"secondary\">已启用类别</Text>\n            </div>\n          </Col>\n          <Col xs={24} sm={8}>\n            <div style={{ textAlign: 'center' }}>\n              <Title level={2} style={{ margin: 0, color: '#faad14' }}>\n                {Object.values(categories).reduce((total, cat) => \n                  total + Object.keys(cat.features || {}).length, 0\n                )}\n              </Title>\n              <Text type=\"secondary\">总子功能数</Text>\n            </div>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 特性开关列表 */}\n      <div>\n        <Title level={5} style={{ marginBottom: 16 }}>\n          <InfoCircleOutlined style={{ marginRight: 8 }} />\n          功能开关控制\n        </Title>\n        \n        {Object.keys(categories).length === 0 ? (\n          <Card>\n            <div style={{ textAlign: 'center', padding: '40px' }}>\n              <Text type=\"secondary\">暂无家长端特性配置</Text>\n            </div>\n          </Card>\n        ) : (\n          Object.entries(categories).map(([categoryName, categoryData]) =>\n            renderFeatureCategory(categoryName, categoryData)\n          )\n        )}\n      </div>\n\n      {/* 使用说明 */}\n      <Card title=\"使用说明\" style={{ marginTop: 16 }}>\n        <ul style={{ paddingLeft: '20px', margin: 0 }}>\n          <li>主类别开关控制整个功能模块的启用状态</li>\n          <li>子功能开关可以精确控制具体功能的启用</li>\n          <li>建议按阶段逐步启用功能，确保系统稳定性</li>\n          <li>第三阶段高级功能建议在充分测试后再启用</li>\n          <li>如遇问题可使用\"重置配置\"恢复默认状态</li>\n        </ul>\n      </Card>\n    </div>\n  );\n};\n\nexport default ParentFeatureManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SACEC,eAAe,EACfC,cAAc,EACdC,yBAAyB,EACzBC,mBAAmB,EACnBC,mBAAmB,EACnBC,kBAAkB,QACb,mBAAmB;AAC1B,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGvB,UAAU;AAC7C,MAAM;EAAEwB;AAAQ,CAAC,GAAGjB,KAAK;AAEzB,MAAMkB,oBAAoB,GAAGA,CAAC;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAMsC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBM,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MAEjD,MAAMC,QAAQ,GAAG,MAAMnB,GAAG,CAACoB,GAAG,CAAC,wBAAwB,CAAC;MACxDH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,QAAQ,CAAC;MACpCF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,QAAQ,CAACE,MAAM,CAAC;MACzCJ,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,QAAQ,CAACG,IAAI,CAAC;;MAEtC;MACA,IAAIC,YAAY,GAAG,IAAI;MAEvB,IAAIJ,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACE,OAAO,EAAE;QAC1C;QACAD,YAAY,GAAGJ,QAAQ,CAACG,IAAI,CAACA,IAAI;QACjCL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEK,YAAY,CAAC;MAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACG,UAAU,EAAE;QACpD;QACAF,YAAY,GAAGJ,QAAQ,CAACG,IAAI;QAC5BL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEK,YAAY,CAAC;MAC7C,CAAC,MAAM;QACLN,OAAO,CAACS,KAAK,CAAC,cAAc,EAAEP,QAAQ,CAACG,IAAI,CAAC;QAC5CtC,OAAO,CAAC0C,KAAK,CAAC,oBAAoB,CAAC;QACnC;MACF;MAEA,IAAIH,YAAY,EAAE;QAChBN,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEK,YAAY,CAAC;QAC1CV,WAAW,CAACU,YAAY,CAAC;QACzBvC,OAAO,CAACwC,OAAO,CAAC,aAAa,CAAC;MAChC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdZ,OAAO,CAACS,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCT,OAAO,CAACS,KAAK,CAAC,OAAO,EAAE,EAAAC,eAAA,GAAAD,KAAK,CAACP,QAAQ,cAAAQ,eAAA,uBAAdA,eAAA,CAAgBL,IAAI,KAAII,KAAK,CAAC1C,OAAO,CAAC;MAC7DA,OAAO,CAAC0C,KAAK,CAAC,gBAAgB,EAAAE,gBAAA,GAAAF,KAAK,CAACP,QAAQ,cAAAS,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBN,IAAI,cAAAO,qBAAA,uBAApBA,qBAAA,CAAsBC,MAAM,KAAIJ,KAAK,CAAC1C,OAAO,EAAE,CAAC;IAChF,CAAC,SAAS;MACRiC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzCP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoB,aAAa,GAAG,MAAOC,WAAW,IAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAC3C,MAAMC,cAAc,GAAG,CAACJ,WAAW,CAACK,QAAQ,CAAC,GAAG,CAAC;IACjD,MAAMZ,UAAU,GAAGb,QAAQ,CAACa,UAAU,IAAI,CAAC,CAAC;IAE5C,MAAMa,aAAa,GAAGF,cAAc,IAAAH,qBAAA,GAChCR,UAAU,CAACO,WAAW,CAAC,cAAAC,qBAAA,uBAAvBA,qBAAA,CAAyBM,OAAO,IAAAL,sBAAA,GAChCT,UAAU,CAACO,WAAW,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAAN,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCtB,QAAQ,cAAAuB,sBAAA,uBAA/CA,sBAAA,CAAkDH,WAAW,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEhFvB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEc,WAAW,EAAE,OAAO,EAAEM,aAAa,CAAC;IAE5DhC,OAAO,CAAC;MACNmC,KAAK,EAAE,MAAM;MACbC,IAAI,eAAExC,OAAA,CAACN,yBAAyB;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,OAAO,eACL7C,OAAA;QAAA8C,QAAA,gBACE9C,OAAA;UAAA8C,QAAA,GAAG,0BAAI,EAACV,aAAa,GAAG,IAAI,GAAG,IAAI,EAAC,eAAG,eAAApC,OAAA;YAAA8C,QAAA,EAAShB;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,iBAAG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAC3EV,cAAc,iBACblC,OAAA,CAACZ,KAAK;UACJN,OAAO,EAAC,cAAI;UACZiE,WAAW,EAAC,8GAAoB;UAChCC,IAAI,EAAC,SAAS;UACdC,QAAQ;UACRC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAE;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;MACDQ,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACFvC,WAAW,CAACwC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACvB,WAAW,GAAG;UAAK,CAAC,CAAC,CAAC;UACvD,MAAMb,QAAQ,GAAG,MAAMnB,GAAG,CAACwD,IAAI,CAAC,0BAA0BxB,WAAW,SAAS,CAAC;UAE/Ef,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,QAAQ,CAACG,IAAI,CAAC;UACzCL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,QAAQ,CAACG,IAAI,CAACE,OAAO,CAAC;UAE/D,IAAIL,QAAQ,CAACG,IAAI,CAACE,OAAO,EAAE;YACzBxC,OAAO,CAACwC,OAAO,CAACL,QAAQ,CAACG,IAAI,CAACtC,OAAO,CAAC;YACtC,MAAMgC,aAAa,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,MAAM;YACLC,OAAO,CAACS,KAAK,CAAC,uBAAuB,EAAEP,QAAQ,CAACG,IAAI,CAAC;YACrDtC,OAAO,CAAC0C,KAAK,CAAC,UAAU,CAAC;UAC3B;QACF,CAAC,CAAC,OAAOA,KAAK,EAAE;UAAA,IAAA+B,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;UACd1C,OAAO,CAACS,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;UACpCT,OAAO,CAACS,KAAK,CAAC,UAAU,GAAA+B,gBAAA,GAAE/B,KAAK,CAACP,QAAQ,cAAAsC,gBAAA,uBAAdA,gBAAA,CAAgBnC,IAAI,CAAC;UAC/CtC,OAAO,CAAC0C,KAAK,CAAC,aAAa,EAAAgC,gBAAA,GAAAhC,KAAK,CAACP,QAAQ,cAAAuC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpC,IAAI,cAAAqC,qBAAA,uBAApBA,qBAAA,CAAsB7B,MAAM,KAAIJ,KAAK,CAAC1C,OAAO,EAAE,CAAC;QAC7E,CAAC,SAAS;UACR+B,WAAW,CAACwC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACvB,WAAW,GAAG;UAAM,CAAC,CAAC,CAAC;QAC1D;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM4B,aAAa,GAAGA,CAAA,KAAM;IAC1BtD,OAAO,CAAC;MACNmC,KAAK,EAAE,MAAM;MACbC,IAAI,eAAExC,OAAA,CAACN,yBAAyB;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,OAAO,eACL7C,OAAA;QAAA8C,QAAA,gBACE9C,OAAA;UAAA8C,QAAA,EAAG;QAAqB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5B5C,OAAA,CAACZ,KAAK;UACJN,OAAO,EAAC,cAAI;UACZiE,WAAW,EAAC,0HAAsB;UAClCC,IAAI,EAAC,OAAO;UACZC,QAAQ;UACRC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAE;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;MACDQ,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF3C,UAAU,CAAC,IAAI,CAAC;UAChB,MAAMQ,QAAQ,GAAG,MAAMnB,GAAG,CAACwD,IAAI,CAAC,8BAA8B,CAAC;UAE/D,IAAIrC,QAAQ,CAACG,IAAI,CAACE,OAAO,EAAE;YACzBxC,OAAO,CAACwC,OAAO,CAAC,aAAa,CAAC;YAC9B,MAAMR,aAAa,CAAC,CAAC;UACvB,CAAC,MAAM;YACLhC,OAAO,CAAC0C,KAAK,CAAC,UAAU,CAAC;UAC3B;QACF,CAAC,CAAC,OAAOA,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;UACjC1C,OAAO,CAAC0C,KAAK,CAAC,UAAU,CAAC;QAC3B,CAAC,SAAS;UACRf,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAEDhC,SAAS,CAAC,MAAM;IACdqC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM6C,mBAAmB,GAAGA,CAAC7B,WAAW,EAAEO,OAAO,EAAEU,WAAW,EAAEa,YAAY,GAAG,KAAK,KAAK;IACvF,MAAMC,UAAU,GAAGjD,QAAQ,CAACkB,WAAW,CAAC;IAExC,oBACE9B,OAAA;MAEEkD,KAAK,EAAE;QACLY,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAEL,YAAY,GAAG,gBAAgB,GAAG,QAAQ;QACnDM,UAAU,EAAEN,YAAY,GAAG,mBAAmB,GAAG,MAAM;QACvDO,UAAU,EAAEP,YAAY,GAAG,MAAM,GAAG;MACtC,CAAE;MAAAd,QAAA,gBAEF9C,OAAA;QAAKkD,KAAK,EAAE;UAAEkB,IAAI,EAAE;QAAE,CAAE;QAAAtB,QAAA,gBACtB9C,OAAA,CAACV,KAAK;UAAAwD,QAAA,gBACJ9C,OAAA,CAACE,IAAI;YAACmE,MAAM,EAAE,CAACT,YAAa;YAACV,KAAK,EAAE;cAClCoB,QAAQ,EAAEV,YAAY,GAAG,MAAM,GAAG,MAAM;cACxCW,KAAK,EAAEX,YAAY,GAAG,MAAM,GAAG;YACjC,CAAE;YAAAd,QAAA,EACChB;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACP5C,OAAA,CAACX,KAAK;YACJ8B,MAAM,EAAEkB,OAAO,GAAG,SAAS,GAAG,SAAU;YACxCmC,IAAI,EAAEnC,OAAO,GAAG,KAAK,GAAG;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EACPG,WAAW,iBACV/C,OAAA;UAAKkD,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAE,CAAE;UAAAL,QAAA,eAC3B9C,OAAA,CAACE,IAAI;YAAC8C,IAAI,EAAC,WAAW;YAACE,KAAK,EAAE;cAAEoB,QAAQ,EAAE;YAAO,CAAE;YAAAxB,QAAA,EAChDC;UAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACN5C,OAAA,CAACT,OAAO;QAACgD,KAAK,EAAEF,OAAO,GAAG,MAAM,GAAG,MAAO;QAAAS,QAAA,eACxC9C,OAAA,CAACrB,MAAM;UACL8F,OAAO,EAAEpC,OAAQ;UACjB7B,OAAO,EAAEqD,UAAW;UACpBa,QAAQ,EAAEA,CAAA,KAAM7C,aAAa,CAACC,WAAW,CAAE;UAC3C6C,IAAI,EAAErE,QAAQ,GAAG,OAAO,GAAG;QAAU;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA,GAtCLd,WAAW;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAuCb,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgC,qBAAqB,GAAGA,CAACC,YAAY,EAAEC,YAAY,KAAK;IAC5D,MAAM;MAAEzC,OAAO;MAAEU,WAAW;MAAErC,QAAQ,EAAEqE;IAAY,CAAC,GAAGD,YAAY;IAEpE,oBACE9E,OAAA,CAACtB,IAAI;MAEHiG,IAAI,EAAC,OAAO;MACZzB,KAAK,EAAE;QAAE8B,YAAY,EAAE;MAAG,CAAE;MAC5BzC,KAAK,eACHvC,OAAA,CAACV,KAAK;QAAAwD,QAAA,GACHT,OAAO,gBAAGrC,OAAA,CAACL,mBAAmB;UAACuD,KAAK,EAAE;YAAEqB,KAAK,EAAE;UAAU;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACJ,mBAAmB;UAACsD,KAAK,EAAE;YAAEqB,KAAK,EAAE;UAAU;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtH5C,OAAA;UAAA8C,QAAA,EAAO+B;QAAY;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CACR;MACDqC,KAAK,eACHjF,OAAA,CAACrB,MAAM;QACL8F,OAAO,EAAEpC,OAAQ;QACjB7B,OAAO,EAAEI,QAAQ,CAACiE,YAAY,CAAE;QAChCH,QAAQ,EAAEA,CAAA,KAAM7C,aAAa,CAACgD,YAAY,CAAE;QAC5CF,IAAI,EAAC;MAAO;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CACF;MAAAE,QAAA,gBAED9C,OAAA;QAAKkD,KAAK,EAAE;UAAE8B,YAAY,EAAE;QAAG,CAAE;QAAAlC,QAAA,eAC/B9C,OAAA,CAACE,IAAI;UAAC8C,IAAI,EAAC,WAAW;UAAAF,QAAA,EAAEC;QAAW;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,EAELmC,WAAW,IAAIG,MAAM,CAACC,IAAI,CAACJ,WAAW,CAAC,CAACK,MAAM,GAAG,CAAC,iBACjDpF,OAAA;QAAA8C,QAAA,gBACE9C,OAAA,CAACf,OAAO;UAACiE,KAAK,EAAE;YAAEmC,MAAM,EAAE;UAAS;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxC5C,OAAA,CAACE,IAAI;UAACmE,MAAM;UAACnB,KAAK,EAAE;YAAEoB,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAAC;QAAI;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpE5C,OAAA;UAAKkD,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAE,CAAE;UAAAL,QAAA,EAC1BoC,MAAM,CAACI,OAAO,CAACP,WAAW,CAAC,CAACQ,GAAG,CAAC,CAAC,CAACzD,WAAW,EAAE0D,cAAc,CAAC,KAC7D7B,mBAAmB,CACjB,GAAGkB,YAAY,IAAI/C,WAAW,EAAE,EAChC0D,cAAc,EACd,IAAI,EACJ,IACF,CACF;QAAC;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA,GArCIiC,YAAY;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsCb,CAAC;EAEX,CAAC;EAED,IAAIpC,OAAO,EAAE;IACX,oBACER,OAAA;MAAKkD,KAAK,EAAE;QAAEuC,SAAS,EAAE,QAAQ;QAAExB,OAAO,EAAE;MAAO,CAAE;MAAAnB,QAAA,gBACnD9C,OAAA,CAACnB,IAAI;QAAC8F,IAAI,EAAC;MAAO;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB5C,OAAA;QAAKkD,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAG,CAAE;QAAAL,QAAA,eAC5B9C,OAAA,CAACE,IAAI;UAAA4C,QAAA,EAAC;QAAc;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAM;IAAErB,UAAU,GAAG,CAAC;EAAE,CAAC,GAAGb,QAAQ;EACpCK,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEN,QAAQ,CAAC;EAC3CK,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,UAAU,CAAC;EAC7CR,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkE,MAAM,CAACC,IAAI,CAAC5D,UAAU,CAAC,CAAC6D,MAAM,CAAC;EAEhE,oBACEpF,OAAA;IAAKkD,KAAK,EAAE;MAAEe,OAAO,EAAE3D,QAAQ,GAAG,KAAK,GAAG;IAAO,CAAE;IAAAwC,QAAA,gBAEjD9C,OAAA,CAACtB,IAAI;MAACwE,KAAK,EAAE;QAAE8B,YAAY,EAAE;MAAG,CAAE;MAAAlC,QAAA,eAChC9C,OAAA,CAACjB,GAAG;QAAC2G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACC,KAAK,EAAC,QAAQ;QAAA7C,QAAA,gBACnC9C,OAAA,CAAChB,GAAG;UAACoF,IAAI,EAAC,MAAM;UAAAtB,QAAA,gBACd9C,OAAA,CAACC,KAAK;YAAC2F,KAAK,EAAE,CAAE;YAAC1C,KAAK,EAAE;cAAEmC,MAAM,EAAE;YAAE,CAAE;YAAAvC,QAAA,gBACpC9C,OAAA,CAACR,eAAe;cAAC0D,KAAK,EAAE;gBAAE2C,WAAW,EAAE;cAAE;YAAE;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8CAEhD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5C,OAAA,CAACG,SAAS;YAAC+C,KAAK,EAAE;cAAEmC,MAAM,EAAE,WAAW;cAAEd,KAAK,EAAE;YAAO,CAAE;YAAAzB,QAAA,EAAC;UAE1D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACN5C,OAAA,CAAChB,GAAG;UAAA8D,QAAA,eACF9C,OAAA,CAACV,KAAK;YAAAwD,QAAA,gBACJ9C,OAAA,CAACd,MAAM;cACLsD,IAAI,eAAExC,OAAA,CAACP,cAAc;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBkD,OAAO,EAAEhF,aAAc;cACvBN,OAAO,EAAEA,OAAQ;cAAAsC,QAAA,EAClB;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT5C,OAAA,CAACd,MAAM;cACL6G,MAAM;cACNvD,IAAI,eAAExC,OAAA,CAACN,yBAAyB;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACpCkD,OAAO,EAAEpC,aAAc;cAAAZ,QAAA,EACxB;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP5C,OAAA,CAACtB,IAAI;MAAC6D,KAAK,EAAC,sCAAQ;MAACW,KAAK,EAAE;QAAE8B,YAAY,EAAE;MAAG,CAAE;MAAAlC,QAAA,eAC/C9C,OAAA,CAACjB,GAAG;QAAC2G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAA5C,QAAA,gBACpB9C,OAAA,CAAChB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnD,QAAA,eACjB9C,OAAA;YAAKkD,KAAK,EAAE;cAAEuC,SAAS,EAAE;YAAS,CAAE;YAAA3C,QAAA,gBAClC9C,OAAA,CAACC,KAAK;cAAC2F,KAAK,EAAE,CAAE;cAAC1C,KAAK,EAAE;gBAAEmC,MAAM,EAAE,CAAC;gBAAEd,KAAK,EAAE;cAAU,CAAE;cAAAzB,QAAA,EACrDpC,QAAQ,CAACwF,gBAAgB,IAAI;YAAC;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACR5C,OAAA,CAACE,IAAI;cAAC8C,IAAI,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5C,OAAA,CAAChB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnD,QAAA,eACjB9C,OAAA;YAAKkD,KAAK,EAAE;cAAEuC,SAAS,EAAE;YAAS,CAAE;YAAA3C,QAAA,gBAClC9C,OAAA,CAACC,KAAK;cAAC2F,KAAK,EAAE,CAAE;cAAC1C,KAAK,EAAE;gBAAEmC,MAAM,EAAE,CAAC;gBAAEd,KAAK,EAAE;cAAU,CAAE;cAAAzB,QAAA,EACrDpC,QAAQ,CAACyF,kBAAkB,IAAI;YAAC;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACR5C,OAAA,CAACE,IAAI;cAAC8C,IAAI,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5C,OAAA,CAAChB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnD,QAAA,eACjB9C,OAAA;YAAKkD,KAAK,EAAE;cAAEuC,SAAS,EAAE;YAAS,CAAE;YAAA3C,QAAA,gBAClC9C,OAAA,CAACC,KAAK;cAAC2F,KAAK,EAAE,CAAE;cAAC1C,KAAK,EAAE;gBAAEmC,MAAM,EAAE,CAAC;gBAAEd,KAAK,EAAE;cAAU,CAAE;cAAAzB,QAAA,EACrDoC,MAAM,CAACkB,MAAM,CAAC7E,UAAU,CAAC,CAAC8E,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAC3CD,KAAK,GAAGpB,MAAM,CAACC,IAAI,CAACoB,GAAG,CAAC7F,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC0E,MAAM,EAAE,CAClD;YAAC;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACR5C,OAAA,CAACE,IAAI;cAAC8C,IAAI,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP5C,OAAA;MAAA8C,QAAA,gBACE9C,OAAA,CAACC,KAAK;QAAC2F,KAAK,EAAE,CAAE;QAAC1C,KAAK,EAAE;UAAE8B,YAAY,EAAE;QAAG,CAAE;QAAAlC,QAAA,gBAC3C9C,OAAA,CAACH,kBAAkB;UAACqD,KAAK,EAAE;YAAE2C,WAAW,EAAE;UAAE;QAAE;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,wCAEnD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAEPsC,MAAM,CAACC,IAAI,CAAC5D,UAAU,CAAC,CAAC6D,MAAM,KAAK,CAAC,gBACnCpF,OAAA,CAACtB,IAAI;QAAAoE,QAAA,eACH9C,OAAA;UAAKkD,KAAK,EAAE;YAAEuC,SAAS,EAAE,QAAQ;YAAExB,OAAO,EAAE;UAAO,CAAE;UAAAnB,QAAA,eACnD9C,OAAA,CAACE,IAAI;YAAC8C,IAAI,EAAC,WAAW;YAAAF,QAAA,EAAC;UAAS;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,GAEPsC,MAAM,CAACI,OAAO,CAAC/D,UAAU,CAAC,CAACgE,GAAG,CAAC,CAAC,CAACV,YAAY,EAAEC,YAAY,CAAC,KAC1DF,qBAAqB,CAACC,YAAY,EAAEC,YAAY,CAClD,CACD;IAAA;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN5C,OAAA,CAACtB,IAAI;MAAC6D,KAAK,EAAC,0BAAM;MAACW,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAAAL,QAAA,eAC1C9C,OAAA;QAAIkD,KAAK,EAAE;UAAEsD,WAAW,EAAE,MAAM;UAAEnB,MAAM,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBAC5C9C,OAAA;UAAA8C,QAAA,EAAI;QAAkB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B5C,OAAA;UAAA8C,QAAA,EAAI;QAAkB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B5C,OAAA;UAAA8C,QAAA,EAAI;QAAmB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5B5C,OAAA;UAAA8C,QAAA,EAAI;QAAmB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5B5C,OAAA;UAAA8C,QAAA,EAAI;QAAmB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACrC,EAAA,CArWIF,oBAAoB;AAAAoG,EAAA,GAApBpG,oBAAoB;AAuW1B,eAAeA,oBAAoB;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}