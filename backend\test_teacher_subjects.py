#!/usr/bin/env python3
"""
测试教师任教科目显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json

def test_teacher_login_and_subjects():
    """测试教师登录并获取任教科目"""
    base_url = "http://localhost:8083/api"
    
    # 教师登录信息
    teacher_email = "<EMAIL>"
    teacher_password = "123456"
    
    try:
        # 1. 教师登录
        login_data = {
            "username": teacher_email,
            "password": teacher_password
        }

        print(f"正在登录教师: {teacher_email}")
        login_response = requests.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code != 200:
            print(f"登录失败: {login_response.status_code} - {login_response.text}")
            return
        
        login_result = login_response.json()
        token = login_result["access_token"]
        print(f"登录成功，获取到token")
        
        # 2. 获取用户信息
        headers = {"Authorization": f"Bearer {token}"}
        me_response = requests.get(f"{base_url}/me", headers=headers)
        
        if me_response.status_code != 200:
            print(f"获取用户信息失败: {me_response.status_code} - {me_response.text}")
            return
        
        user_info = me_response.json()
        print(f"用户信息获取成功:")
        print(f"  用户名: {user_info.get('username')}")
        print(f"  姓名: {user_info.get('full_name')}")
        print(f"  邮箱: {user_info.get('email')}")
        print(f"  是否教师: {user_info.get('is_teacher')}")
        print(f"  角色: {user_info.get('role')}")
        print(f"  学校: {user_info.get('school_name')}")
        print(f"  任教科目: {user_info.get('teaching_subjects')}")
        
        # 检查任教科目是否正确显示
        teaching_subjects = user_info.get('teaching_subjects', [])
        if teaching_subjects:
            print(f"✅ 任教科目显示正常: {', '.join(teaching_subjects)}")
        else:
            print("❌ 任教科目显示为空或未设置")
            
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")

if __name__ == "__main__":
    print("开始测试教师任教科目显示...")
    test_teacher_login_and_subjects()
    print("测试完成")
