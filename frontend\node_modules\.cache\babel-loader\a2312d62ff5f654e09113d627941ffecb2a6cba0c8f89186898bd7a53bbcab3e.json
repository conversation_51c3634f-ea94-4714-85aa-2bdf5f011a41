{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys\\\\frontend\\\\src\\\\pages\\\\ParentDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Typography, Spin, message, Avatar, List, Badge, Statistic, Progress, Empty, Button, Space, Divider, Tag } from 'antd';\nimport { UserOutlined, BookOutlined, TrophyOutlined, ClockCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined, BarChartOutlined, EyeOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport api from '../utils/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst ParentDashboard = ({\n  user\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // 添加样式\n  const selectedStudentStyle = {\n    border: '2px solid #1890ff',\n    boxShadow: '0 2px 8px rgba(24, 144, 255, 0.2)'\n  };\n  const [loading, setLoading] = useState(true);\n  const [boundStudents, setBoundStudents] = useState([]);\n  const [selectedStudent, setSelectedStudent] = useState(null);\n  const [recentHomework, setRecentHomework] = useState({});\n  const [studentHomework, setStudentHomework] = useState([]);\n  const [studentStats, setStudentStats] = useState(null);\n\n  // 获取绑定学生列表\n  const fetchBoundStudents = async () => {\n    try {\n      const response = await api.get('/parent/bound-students');\n\n      // 检查响应格式\n      if (response && typeof response === 'object' && response.success) {\n        setBoundStudents(response.data);\n        // 默认选择第一个学生\n        if (response.data && response.data.length > 0) {\n          setSelectedStudent(response.data[0]);\n        }\n        // 获取每个学生的最近作业\n        for (const student of response.data) {\n          await fetchRecentHomework(student.student_id);\n        }\n      } else if (Array.isArray(response)) {\n        setBoundStudents(response);\n        // 默认选择第一个学生\n        if (response.length > 0) {\n          setSelectedStudent(response[0]);\n        }\n        // 获取每个学生的最近作业\n        for (const student of response) {\n          await fetchRecentHomework(student.student_id);\n        }\n      } else {\n        console.log('❌ 未知响应格式:', response);\n      }\n    } catch (error) {\n      console.error('获取绑定学生失败:', error);\n      message.error('获取绑定学生信息失败');\n    }\n  };\n\n  // 获取学生最近作业（按科目分组，每科最近2次）\n  const fetchRecentHomework = async studentId => {\n    try {\n      const response = await api.get(`/parent/student/${studentId}/homework`, {\n        params: {\n          limit: 20\n        } // 获取更多作业以便按科目分组\n      });\n      if (response && response.success && response.data && response.data.homework) {\n        const homeworkList = response.data.homework;\n\n        // 按科目分组\n        const homeworkBySubject = {};\n        homeworkList.forEach(hw => {\n          const subject = hw.subject_name || '未知科目';\n          if (!homeworkBySubject[subject]) {\n            homeworkBySubject[subject] = [];\n          }\n          homeworkBySubject[subject].push(hw);\n        });\n\n        // 每科只保留最近2次作业\n        const recentBySubject = {};\n        Object.keys(homeworkBySubject).forEach(subject => {\n          recentBySubject[subject] = homeworkBySubject[subject].sort((a, b) => new Date(b.created_at) - new Date(a.created_at)).slice(0, 2);\n        });\n        setRecentHomework(prev => ({\n          ...prev,\n          [studentId]: recentBySubject\n        }));\n      }\n    } catch (error) {\n      console.error('获取最近作业失败:', error);\n    }\n  };\n\n  // 获取学生作业列表\n  const fetchStudentHomework = async studentId => {\n    if (!studentId) return;\n    try {\n      const response = await api.get(`/parent/student/${studentId}/homework?limit=10`);\n      if (response.data.success) {\n        setStudentHomework(response.data.data.homeworks || []);\n      }\n    } catch (error) {\n      console.error('获取学生作业失败:', error);\n      message.error('获取学生作业信息失败');\n    }\n  };\n\n  // 获取学生统计信息\n  const fetchStudentStats = async studentId => {\n    if (!studentId) return;\n    try {\n      const response = await api.get(`/parent/student/${studentId}/statistics`);\n      if (response.data.success) {\n        setStudentStats(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取学生统计失败:', error);\n      // 不显示错误消息，因为这是增强功能\n    }\n  };\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      await fetchBoundStudents();\n      setLoading(false);\n    };\n    loadData();\n  }, []);\n  useEffect(() => {\n    if (selectedStudent) {\n      fetchStudentHomework(selectedStudent.student_id);\n      fetchStudentStats(selectedStudent.student_id);\n    }\n  }, [selectedStudent]);\n\n  // 查看作业详情\n  const handleViewHomework = student => {\n    // 导航到作业详情页面，而不是打开新窗口\n    navigate(`/parent/student/${student.student_id}/homework`, {\n      state: {\n        studentName: student.student_name,\n        studentId: student.student_id\n      }\n    });\n  };\n\n  // 查看学习报告\n  const handleViewReport = student => {\n    // 导航到学习报告页面，而不是打开新窗口\n    navigate(`/parent/student/${student.student_id}/report`, {\n      state: {\n        studentName: student.student_name,\n        studentId: student.student_id\n      }\n    });\n  };\n\n  // 渲染学生选择卡片\n  const renderStudentCards = () => {\n    if (boundStudents.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(Empty, {\n          description: \"\\u6682\\u65E0\\u7ED1\\u5B9A\\u5B66\\u751F\",\n          image: Empty.PRESENTED_IMAGE_SIMPLE,\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u8BF7\\u8054\\u7CFB\\u5B66\\u6821\\u7BA1\\u7406\\u5458\\u4E3A\\u60A8\\u7ED1\\u5B9A\\u5B66\\u751F\\u8D26\\u6237\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Row, {\n      className: \"student-cards-row\",\n      gutter: [16, 16],\n      children: boundStudents.map(student => {\n        const studentRecentHomework = recentHomework[student.student_id] || {};\n        const totalRecentHomework = Object.values(studentRecentHomework).flat().length;\n        const subjectCount = Object.keys(studentRecentHomework).length;\n        return /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"student-card\",\n            hoverable: true,\n            onClick: () => setSelectedStudent(student),\n            style: {\n              ...((selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.student_id) === student.student_id ? selectedStudentStyle : {}),\n              borderRadius: '12px',\n              overflow: 'hidden'\n            },\n            bodyStyle: {\n              padding: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"student-info-header\",\n              style: {\n                textAlign: 'center',\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                size: 64,\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 27\n                }, this),\n                style: {\n                  backgroundColor: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.student_id) === student.student_id ? '#1890ff' : '#87d068',\n                  marginBottom: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Title, {\n                  level: 4,\n                  style: {\n                    margin: 0,\n                    color: '#1890ff'\n                  },\n                  children: student.student_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: '13px'\n                  },\n                  children: student.class_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              style: {\n                margin: '12px 0'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              className: \"student-stats-row\",\n              gutter: 8,\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u79D1\\u76EE\\u6570\",\n                  value: subjectCount,\n                  valueStyle: {\n                    fontSize: '16px',\n                    color: '#1890ff'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u6700\\u8FD1\\u4F5C\\u4E1A\",\n                  value: totalRecentHomework,\n                  valueStyle: {\n                    fontSize: '16px',\n                    color: '#52c41a'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"student-card-buttons\",\n              style: {\n                marginTop: 16\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                size: \"small\",\n                style: {\n                  width: '100%',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  size: \"small\",\n                  icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 29\n                  }, this),\n                  onClick: e => {\n                    e.stopPropagation();\n                    handleViewHomework(student);\n                  },\n                  children: \"\\u4F5C\\u4E1A\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 29\n                  }, this),\n                  onClick: e => {\n                    e.stopPropagation();\n                    handleViewReport(student);\n                  },\n                  children: \"\\u5B66\\u4E60\\u62A5\\u544A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this), (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.student_id) === student.student_id && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 8,\n                right: 8,\n                background: '#1890ff',\n                color: 'white',\n                borderRadius: '50%',\n                width: 20,\n                height: 20,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '12px'\n              },\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)\n        }, student.student_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染统计信息\n  const renderStatistics = () => {\n    if (!selectedStudent || !studentStats) {\n      return null;\n    }\n    const stats = studentStats.homework_stats;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5B66\\u4E60\\u7EDF\\u8BA1\",\n      style: {\n        marginTop: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4F5C\\u4E1A\\u6570\",\n            value: stats.total_homework,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u63D0\\u4EA4\",\n            value: stats.submitted_homework,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5B8C\\u6210\\u7387\",\n            value: stats.completion_rate,\n            suffix: \"%\",\n            prefix: /*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u5206\",\n            value: stats.avg_score,\n            precision: 1,\n            prefix: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u5B8C\\u6210\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: stats.completion_rate,\n          status: stats.completion_rate >= 80 ? 'success' : stats.completion_rate >= 60 ? 'normal' : 'exception',\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染最近作业\n  const renderRecentHomework = () => {\n    if (!selectedStudent) {\n      return null;\n    }\n    const studentRecentHomework = recentHomework[selectedStudent.student_id] || {};\n    const subjects = Object.keys(studentRecentHomework);\n    return /*#__PURE__*/_jsxDEV(Card, {\n      className: \"recent-homework-card\",\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(BookOutlined, {\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 13\n        }, this), \"\\u6700\\u8FD1\\u4F5C\\u4E1A\", /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\uFF08\\u6BCF\\u79D1\\u6700\\u8FD12\\u6B21\\uFF09\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 11\n      }, this),\n      style: {\n        marginTop: 16\n      },\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewHomework(selectedStudent),\n        children: \"\\u67E5\\u770B\\u5168\\u90E8\\u4F5C\\u4E1A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 11\n      }, this),\n      children: subjects.length === 0 ? /*#__PURE__*/_jsxDEV(Empty, {\n        description: \"\\u6682\\u65E0\\u4F5C\\u4E1A\\u8BB0\\u5F55\",\n        image: Empty.PRESENTED_IMAGE_SIMPLE\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Row, {\n        className: \"recent-homework-subjects\",\n        gutter: [16, 16],\n        children: subjects.map(subject => {\n          const subjectHomework = studentRecentHomework[subject];\n          return /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            lg: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              size: \"small\",\n              title: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: [\"\\u6700\\u8FD1\", subjectHomework.length, \"\\u6B21\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 23\n              }, this),\n              style: {\n                height: '100%'\n              },\n              children: /*#__PURE__*/_jsxDEV(List, {\n                size: \"small\",\n                dataSource: subjectHomework,\n                renderItem: homework => /*#__PURE__*/_jsxDEV(List.Item, {\n                  style: {\n                    padding: '8px 0'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        marginBottom: 4\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Text, {\n                        strong: true,\n                        style: {\n                          fontSize: '13px'\n                        },\n                        children: homework.assignment_title || homework.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 431,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        status: homework.status === 'graded' ? 'success' : homework.status === 'submitted' ? 'processing' : 'warning',\n                        text: homework.status === 'graded' ? '已批改' : homework.status === 'submitted' ? '已提交' : '未完成'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 434,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        style: {\n                          fontSize: '12px'\n                        },\n                        children: new Date(homework.created_at).toLocaleDateString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 446,\n                        columnNumber: 31\n                      }, this), homework.score !== null && homework.score !== undefined ? /*#__PURE__*/_jsxDEV(Text, {\n                        style: {\n                          fontSize: '12px',\n                          color: homework.score >= 80 ? '#52c41a' : homework.score >= 60 ? '#faad14' : '#ff4d4f',\n                          fontWeight: 'bold'\n                        },\n                        children: [homework.score, \"\\u5206\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 450,\n                        columnNumber: 33\n                      }, this) : null]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 445,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 19\n            }, this)\n          }, subject, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u5BB6\\u957F\\u7AEF\\u6570\\u636E...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: 24,\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        border: 'none',\n        borderRadius: '12px'\n      },\n      bodyStyle: {\n        padding: '32px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        align: \"middle\",\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px'\n            },\n            children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDC69\\u200D\\uD83D\\uDC67\\u200D\\uD83D\\uDC66\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            style: {\n              color: 'white',\n              margin: 0\n            },\n            children: [\"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF0C\", (user === null || user === void 0 ? void 0 : user.full_name) || (user === null || user === void 0 ? void 0 : user.username), \"\\uFF01\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              color: 'rgba(255,255,255,0.8)',\n              margin: '8px 0 0 0',\n              fontSize: '16px'\n            },\n            children: \"\\u667A\\u6167\\u4E91\\u7AEF\\u5BB6\\u957F\\u7AEF\\u4E3A\\u60A8\\u63D0\\u4F9B\\u5B69\\u5B50\\u7684\\u5B66\\u4E60\\u60C5\\u51B5\\u67E5\\u770B\\u548C\\u5206\\u6790\\u670D\\u52A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                color: 'white',\n                fontSize: '14px'\n              },\n              children: \"\\u4ECA\\u65E5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                color: 'white',\n                fontSize: '18px',\n                fontWeight: 'bold'\n              },\n              children: new Date().toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(UserOutlined, {\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 13\n        }, this), \"\\u6211\\u7684\\u5B69\\u5B50\", /*#__PURE__*/_jsxDEV(Badge, {\n          count: boundStudents.length,\n          style: {\n            backgroundColor: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 11\n      }, this),\n      style: {\n        marginBottom: 24\n      },\n      bodyStyle: {\n        padding: '24px'\n      },\n      children: renderStudentCards()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 522,\n      columnNumber: 7\n    }, this), selectedStudent && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [renderStatistics(), renderRecentHomework()]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 487,\n    columnNumber: 5\n  }, this);\n};\n_s(ParentDashboard, \"dvNvaKx7bwjgUaJGLUkV2bsvdeU=\", false, function () {\n  return [useNavigate];\n});\n_c = ParentDashboard;\nexport default ParentDashboard;\nvar _c;\n$RefreshReg$(_c, \"ParentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Typography", "Spin", "message", "Avatar", "List", "Badge", "Statistic", "Progress", "Empty", "<PERSON><PERSON>", "Space", "Divider", "Tag", "UserOutlined", "BookOutlined", "TrophyOutlined", "ClockCircleOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "BarChartOutlined", "EyeOutlined", "useNavigate", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Paragraph", "ParentDashboard", "user", "_s", "navigate", "selectedStudentStyle", "border", "boxShadow", "loading", "setLoading", "boundStudents", "setBoundStudents", "selectedStudent", "setSelectedStudent", "recentHomework", "setRecentHomework", "studentHomework", "setStudentHomework", "studentStats", "setStudentStats", "fetchBoundStudents", "response", "get", "success", "data", "length", "student", "fetchRecentHomework", "student_id", "Array", "isArray", "console", "log", "error", "studentId", "params", "limit", "homework", "homeworkList", "homeworkBySubject", "for<PERSON>ach", "hw", "subject", "subject_name", "push", "recentBySubject", "Object", "keys", "sort", "a", "b", "Date", "created_at", "slice", "prev", "fetchStudentHomework", "homeworks", "fetchStudentStats", "loadData", "handleViewHomework", "state", "studentName", "student_name", "handleViewReport", "renderStudentCards", "children", "description", "image", "PRESENTED_IMAGE_SIMPLE", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "gutter", "map", "studentRecentHomework", "totalRecentHomework", "values", "flat", "subjectCount", "xs", "sm", "md", "lg", "hoverable", "onClick", "style", "borderRadius", "overflow", "bodyStyle", "padding", "textAlign", "marginBottom", "size", "icon", "backgroundColor", "level", "margin", "color", "fontSize", "class_name", "span", "title", "value", "valueStyle", "marginTop", "width", "justifyContent", "e", "stopPropagation", "position", "top", "right", "background", "height", "display", "alignItems", "renderStatistics", "stats", "homework_stats", "total_homework", "prefix", "submitted_homework", "completion_rate", "suffix", "avg_score", "precision", "strong", "percent", "status", "renderRecentHomework", "subjects", "extra", "subjectHomework", "dataSource", "renderItem", "<PERSON><PERSON>", "assignment_title", "text", "toLocaleDateString", "score", "undefined", "fontWeight", "align", "flex", "full_name", "username", "direction", "count", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys/frontend/src/pages/ParentDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Typography,\n  Spin,\n  message,\n  Avatar,\n  List,\n  Badge,\n  Statistic,\n  Progress,\n  Empty,\n  Button,\n  Space,\n  Divider,\n  Tag\n} from 'antd';\nimport {\n  UserOutlined,\n  BookOutlined,\n  TrophyOutlined,\n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  EyeOutlined\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport api from '../utils/api';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst ParentDashboard = ({ user }) => {\n  const navigate = useNavigate();\n\n  // 添加样式\n  const selectedStudentStyle = {\n    border: '2px solid #1890ff',\n    boxShadow: '0 2px 8px rgba(24, 144, 255, 0.2)'\n  };\n  const [loading, setLoading] = useState(true);\n  const [boundStudents, setBoundStudents] = useState([]);\n  const [selectedStudent, setSelectedStudent] = useState(null);\n  const [recentHomework, setRecentHomework] = useState({});\n  const [studentHomework, setStudentHomework] = useState([]);\n  const [studentStats, setStudentStats] = useState(null);\n\n  // 获取绑定学生列表\n  const fetchBoundStudents = async () => {\n    try {\n      const response = await api.get('/parent/bound-students');\n\n      // 检查响应格式\n      if (response && typeof response === 'object' && response.success) {\n        setBoundStudents(response.data);\n        // 默认选择第一个学生\n        if (response.data && response.data.length > 0) {\n          setSelectedStudent(response.data[0]);\n        }\n        // 获取每个学生的最近作业\n        for (const student of response.data) {\n          await fetchRecentHomework(student.student_id);\n        }\n      } else if (Array.isArray(response)) {\n        setBoundStudents(response);\n        // 默认选择第一个学生\n        if (response.length > 0) {\n          setSelectedStudent(response[0]);\n        }\n        // 获取每个学生的最近作业\n        for (const student of response) {\n          await fetchRecentHomework(student.student_id);\n        }\n      } else {\n        console.log('❌ 未知响应格式:', response);\n      }\n    } catch (error) {\n      console.error('获取绑定学生失败:', error);\n      message.error('获取绑定学生信息失败');\n    }\n  };\n\n  // 获取学生最近作业（按科目分组，每科最近2次）\n  const fetchRecentHomework = async (studentId) => {\n    try {\n      const response = await api.get(`/parent/student/${studentId}/homework`, {\n        params: { limit: 20 } // 获取更多作业以便按科目分组\n      });\n\n      if (response && response.success && response.data && response.data.homework) {\n        const homeworkList = response.data.homework;\n\n        // 按科目分组\n        const homeworkBySubject = {};\n        homeworkList.forEach(hw => {\n          const subject = hw.subject_name || '未知科目';\n          if (!homeworkBySubject[subject]) {\n            homeworkBySubject[subject] = [];\n          }\n          homeworkBySubject[subject].push(hw);\n        });\n\n        // 每科只保留最近2次作业\n        const recentBySubject = {};\n        Object.keys(homeworkBySubject).forEach(subject => {\n          recentBySubject[subject] = homeworkBySubject[subject]\n            .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))\n            .slice(0, 2);\n        });\n\n        setRecentHomework(prev => ({\n          ...prev,\n          [studentId]: recentBySubject\n        }));\n      }\n    } catch (error) {\n      console.error('获取最近作业失败:', error);\n    }\n  };\n\n  // 获取学生作业列表\n  const fetchStudentHomework = async (studentId) => {\n    if (!studentId) return;\n    \n    try {\n      const response = await api.get(`/parent/student/${studentId}/homework?limit=10`);\n      if (response.data.success) {\n        setStudentHomework(response.data.data.homeworks || []);\n      }\n    } catch (error) {\n      console.error('获取学生作业失败:', error);\n      message.error('获取学生作业信息失败');\n    }\n  };\n\n  // 获取学生统计信息\n  const fetchStudentStats = async (studentId) => {\n    if (!studentId) return;\n    \n    try {\n      const response = await api.get(`/parent/student/${studentId}/statistics`);\n      if (response.data.success) {\n        setStudentStats(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取学生统计失败:', error);\n      // 不显示错误消息，因为这是增强功能\n    }\n  };\n\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      await fetchBoundStudents();\n      setLoading(false);\n    };\n    \n    loadData();\n  }, []);\n\n  useEffect(() => {\n    if (selectedStudent) {\n      fetchStudentHomework(selectedStudent.student_id);\n      fetchStudentStats(selectedStudent.student_id);\n    }\n  }, [selectedStudent]);\n\n  // 查看作业详情\n  const handleViewHomework = (student) => {\n    // 导航到作业详情页面，而不是打开新窗口\n    navigate(`/parent/student/${student.student_id}/homework`, {\n      state: {\n        studentName: student.student_name,\n        studentId: student.student_id\n      }\n    });\n  };\n\n  // 查看学习报告\n  const handleViewReport = (student) => {\n    // 导航到学习报告页面，而不是打开新窗口\n    navigate(`/parent/student/${student.student_id}/report`, {\n      state: {\n        studentName: student.student_name,\n        studentId: student.student_id\n      }\n    });\n  };\n\n  // 渲染学生选择卡片\n  const renderStudentCards = () => {\n    if (boundStudents.length === 0) {\n      return (\n        <Card>\n          <Empty\n            description=\"暂无绑定学生\"\n            image={Empty.PRESENTED_IMAGE_SIMPLE}\n          >\n            <Text type=\"secondary\">\n              请联系学校管理员为您绑定学生账户\n            </Text>\n          </Empty>\n        </Card>\n      );\n    }\n\n    return (\n      <Row className=\"student-cards-row\" gutter={[16, 16]}>\n        {boundStudents.map((student) => {\n          const studentRecentHomework = recentHomework[student.student_id] || {};\n          const totalRecentHomework = Object.values(studentRecentHomework).flat().length;\n          const subjectCount = Object.keys(studentRecentHomework).length;\n\n          return (\n            <Col xs={24} sm={12} md={8} lg={6} key={student.student_id}>\n              <Card\n                className=\"student-card\"\n                hoverable\n                onClick={() => setSelectedStudent(student)}\n                style={{\n                  ...(selectedStudent?.student_id === student.student_id ? selectedStudentStyle : {}),\n                  borderRadius: '12px',\n                  overflow: 'hidden'\n                }}\n                bodyStyle={{ padding: '20px' }}\n              >\n                <div className=\"student-info-header\" style={{ textAlign: 'center', marginBottom: 16 }}>\n                  <Avatar\n                    size={64}\n                    icon={<UserOutlined />}\n                    style={{\n                      backgroundColor: selectedStudent?.student_id === student.student_id ? '#1890ff' : '#87d068',\n                      marginBottom: 8\n                    }}\n                  />\n                  <div>\n                    <Title level={4} style={{ margin: 0, color: '#1890ff' }}>\n                      {student.student_name}\n                    </Title>\n                    <Text type=\"secondary\" style={{ fontSize: '13px' }}>\n                      {student.class_name}\n                    </Text>\n                  </div>\n                </div>\n\n                <Divider style={{ margin: '12px 0' }} />\n\n                <Row className=\"student-stats-row\" gutter={8} style={{ textAlign: 'center' }}>\n                  <Col span={12}>\n                    <Statistic\n                      title=\"科目数\"\n                      value={subjectCount}\n                      valueStyle={{ fontSize: '16px', color: '#1890ff' }}\n                    />\n                  </Col>\n                  <Col span={12}>\n                    <Statistic\n                      title=\"最近作业\"\n                      value={totalRecentHomework}\n                      valueStyle={{ fontSize: '16px', color: '#52c41a' }}\n                    />\n                  </Col>\n                </Row>\n\n                <div className=\"student-card-buttons\" style={{ marginTop: 16 }}>\n                  <Space size=\"small\" style={{ width: '100%', justifyContent: 'center' }}>\n                    <Button\n                      type=\"primary\"\n                      size=\"small\"\n                      icon={<BookOutlined />}\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleViewHomework(student);\n                      }}\n                    >\n                      作业详情\n                    </Button>\n                    <Button\n                      size=\"small\"\n                      icon={<BarChartOutlined />}\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleViewReport(student);\n                      }}\n                    >\n                      学习报告\n                    </Button>\n                  </Space>\n                </div>\n\n                {selectedStudent?.student_id === student.student_id && (\n                  <div style={{\n                    position: 'absolute',\n                    top: 8,\n                    right: 8,\n                    background: '#1890ff',\n                    color: 'white',\n                    borderRadius: '50%',\n                    width: 20,\n                    height: 20,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: '12px'\n                  }}>\n                    ✓\n                  </div>\n                )}\n              </Card>\n            </Col>\n          );\n        })}\n      </Row>\n    );\n  };\n\n  // 渲染统计信息\n  const renderStatistics = () => {\n    if (!selectedStudent || !studentStats) {\n      return null;\n    }\n\n    const stats = studentStats.homework_stats;\n    \n    return (\n      <Card title=\"学习统计\" style={{ marginTop: 16 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={12} sm={6}>\n            <Statistic\n              title=\"总作业数\"\n              value={stats.total_homework}\n              prefix={<BookOutlined />}\n            />\n          </Col>\n          <Col xs={12} sm={6}>\n            <Statistic\n              title=\"已提交\"\n              value={stats.submitted_homework}\n              prefix={<CheckCircleOutlined />}\n            />\n          </Col>\n          <Col xs={12} sm={6}>\n            <Statistic\n              title=\"完成率\"\n              value={stats.completion_rate}\n              suffix=\"%\"\n              prefix={<TrophyOutlined />}\n            />\n          </Col>\n          <Col xs={12} sm={6}>\n            <Statistic\n              title=\"平均分\"\n              value={stats.avg_score}\n              precision={1}\n              prefix={<BarChartOutlined />}\n            />\n          </Col>\n        </Row>\n        \n        <Divider />\n        \n        <div>\n          <Text strong>完成进度</Text>\n          <Progress \n            percent={stats.completion_rate} \n            status={stats.completion_rate >= 80 ? 'success' : stats.completion_rate >= 60 ? 'normal' : 'exception'}\n            style={{ marginTop: 8 }}\n          />\n        </div>\n      </Card>\n    );\n  };\n\n  // 渲染最近作业\n  const renderRecentHomework = () => {\n    if (!selectedStudent) {\n      return null;\n    }\n\n    const studentRecentHomework = recentHomework[selectedStudent.student_id] || {};\n    const subjects = Object.keys(studentRecentHomework);\n\n    return (\n      <Card\n        className=\"recent-homework-card\"\n        title={\n          <Space>\n            <BookOutlined style={{ color: '#1890ff' }} />\n            最近作业\n            <Text type=\"secondary\">（每科最近2次）</Text>\n          </Space>\n        }\n        style={{ marginTop: 16 }}\n        extra={\n          <Button\n            type=\"primary\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewHomework(selectedStudent)}\n          >\n            查看全部作业\n          </Button>\n        }\n      >\n        {subjects.length === 0 ? (\n          <Empty description=\"暂无作业记录\" image={Empty.PRESENTED_IMAGE_SIMPLE} />\n        ) : (\n          <Row className=\"recent-homework-subjects\" gutter={[16, 16]}>\n            {subjects.map(subject => {\n              const subjectHomework = studentRecentHomework[subject];\n              return (\n                <Col xs={24} sm={12} lg={8} key={subject}>\n                  <Card\n                    size=\"small\"\n                    title={\n                      <Space>\n                        <Tag color=\"blue\">{subject}</Tag>\n                        <Text type=\"secondary\">最近{subjectHomework.length}次</Text>\n                      </Space>\n                    }\n                    style={{ height: '100%' }}\n                  >\n                    <List\n                      size=\"small\"\n                      dataSource={subjectHomework}\n                      renderItem={(homework) => (\n                        <List.Item style={{ padding: '8px 0' }}>\n                          <div style={{ width: '100%' }}>\n                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 }}>\n                              <Text strong style={{ fontSize: '13px' }}>\n                                {homework.assignment_title || homework.title}\n                              </Text>\n                              <Badge\n                                status={\n                                  homework.status === 'graded' ? 'success' :\n                                  homework.status === 'submitted' ? 'processing' : 'warning'\n                                }\n                                text={\n                                  homework.status === 'graded' ? '已批改' :\n                                  homework.status === 'submitted' ? '已提交' : '未完成'\n                                }\n                              />\n                            </div>\n                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                                {new Date(homework.created_at).toLocaleDateString()}\n                              </Text>\n                              {homework.score !== null && homework.score !== undefined ? (\n                                <Text\n                                  style={{\n                                    fontSize: '12px',\n                                    color: homework.score >= 80 ? '#52c41a' : homework.score >= 60 ? '#faad14' : '#ff4d4f',\n                                    fontWeight: 'bold'\n                                  }}\n                                >\n                                  {homework.score}分\n                                </Text>\n                              ) : null}\n                            </div>\n                          </div>\n                        </List.Item>\n                      )}\n                    />\n                  </Card>\n                </Col>\n              );\n            })}\n          </Row>\n        )}\n      </Card>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>\n          <Text>正在加载家长端数据...</Text>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ padding: '24px' }}>\n      {/* 欢迎信息 */}\n      <Card\n        style={{\n          marginBottom: 24,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          border: 'none',\n          borderRadius: '12px'\n        }}\n        bodyStyle={{ padding: '32px' }}\n      >\n        <Row align=\"middle\" gutter={16}>\n          <Col>\n            <div style={{ fontSize: '48px' }}>👨‍👩‍👧‍👦</div>\n          </Col>\n          <Col flex=\"auto\">\n            <Title level={2} style={{ color: 'white', margin: 0 }}>\n              欢迎回来，{user?.full_name || user?.username}！\n            </Title>\n            <Paragraph style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0', fontSize: '16px' }}>\n              智慧云端家长端为您提供孩子的学习情况查看和分析服务\n            </Paragraph>\n          </Col>\n          <Col>\n            <Space direction=\"vertical\" style={{ textAlign: 'center' }}>\n              <Text style={{ color: 'white', fontSize: '14px' }}>今日</Text>\n              <Text style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>\n                {new Date().toLocaleDateString()}\n              </Text>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 学生选择 */}\n      <Card\n        title={\n          <Space>\n            <UserOutlined style={{ color: '#1890ff' }} />\n            我的孩子\n            <Badge count={boundStudents.length} style={{ backgroundColor: '#52c41a' }} />\n          </Space>\n        }\n        style={{ marginBottom: 24 }}\n        bodyStyle={{ padding: '24px' }}\n      >\n        {renderStudentCards()}\n      </Card>\n\n      {/* 选中学生的详细信息 */}\n      {selectedStudent && (\n        <>\n          {renderStatistics()}\n          {renderRecentHomework()}\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default ParentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,GAAG,QACE,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,yBAAyB,EACzBC,gBAAgB,EAChBC,WAAW,QACN,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAG7B,UAAU;AAE7C,MAAM8B,eAAe,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMa,oBAAoB,GAAG;IAC3BC,MAAM,EAAE,mBAAmB;IAC3BC,SAAS,EAAE;EACb,CAAC;EACD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAMsD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM5B,GAAG,CAAC6B,GAAG,CAAC,wBAAwB,CAAC;;MAExD;MACA,IAAID,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACE,OAAO,EAAE;QAChEZ,gBAAgB,CAACU,QAAQ,CAACG,IAAI,CAAC;QAC/B;QACA,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;UAC7CZ,kBAAkB,CAACQ,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC;QACA;QACA,KAAK,MAAME,OAAO,IAAIL,QAAQ,CAACG,IAAI,EAAE;UACnC,MAAMG,mBAAmB,CAACD,OAAO,CAACE,UAAU,CAAC;QAC/C;MACF,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACT,QAAQ,CAAC,EAAE;QAClCV,gBAAgB,CAACU,QAAQ,CAAC;QAC1B;QACA,IAAIA,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;UACvBZ,kBAAkB,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC;QACA;QACA,KAAK,MAAMK,OAAO,IAAIL,QAAQ,EAAE;UAC9B,MAAMM,mBAAmB,CAACD,OAAO,CAACE,UAAU,CAAC;QAC/C;MACF,CAAC,MAAM;QACLG,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEX,QAAQ,CAAC;MACpC;IACF,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC5D,OAAO,CAAC4D,KAAK,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMN,mBAAmB,GAAG,MAAOO,SAAS,IAAK;IAC/C,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAM5B,GAAG,CAAC6B,GAAG,CAAC,mBAAmBY,SAAS,WAAW,EAAE;QACtEC,MAAM,EAAE;UAAEC,KAAK,EAAE;QAAG,CAAC,CAAC;MACxB,CAAC,CAAC;MAEF,IAAIf,QAAQ,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACa,QAAQ,EAAE;QAC3E,MAAMC,YAAY,GAAGjB,QAAQ,CAACG,IAAI,CAACa,QAAQ;;QAE3C;QACA,MAAME,iBAAiB,GAAG,CAAC,CAAC;QAC5BD,YAAY,CAACE,OAAO,CAACC,EAAE,IAAI;UACzB,MAAMC,OAAO,GAAGD,EAAE,CAACE,YAAY,IAAI,MAAM;UACzC,IAAI,CAACJ,iBAAiB,CAACG,OAAO,CAAC,EAAE;YAC/BH,iBAAiB,CAACG,OAAO,CAAC,GAAG,EAAE;UACjC;UACAH,iBAAiB,CAACG,OAAO,CAAC,CAACE,IAAI,CAACH,EAAE,CAAC;QACrC,CAAC,CAAC;;QAEF;QACA,MAAMI,eAAe,GAAG,CAAC,CAAC;QAC1BC,MAAM,CAACC,IAAI,CAACR,iBAAiB,CAAC,CAACC,OAAO,CAACE,OAAO,IAAI;UAChDG,eAAe,CAACH,OAAO,CAAC,GAAGH,iBAAiB,CAACG,OAAO,CAAC,CAClDM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,UAAU,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,UAAU,CAAC,CAAC,CAC/DC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC,CAAC;QAEFtC,iBAAiB,CAACuC,IAAI,KAAK;UACzB,GAAGA,IAAI;UACP,CAACpB,SAAS,GAAGW;QACf,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMsB,oBAAoB,GAAG,MAAOrB,SAAS,IAAK;IAChD,IAAI,CAACA,SAAS,EAAE;IAEhB,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAM5B,GAAG,CAAC6B,GAAG,CAAC,mBAAmBY,SAAS,oBAAoB,CAAC;MAChF,IAAIb,QAAQ,CAACG,IAAI,CAACD,OAAO,EAAE;QACzBN,kBAAkB,CAACI,QAAQ,CAACG,IAAI,CAACA,IAAI,CAACgC,SAAS,IAAI,EAAE,CAAC;MACxD;IACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC5D,OAAO,CAAC4D,KAAK,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMwB,iBAAiB,GAAG,MAAOvB,SAAS,IAAK;IAC7C,IAAI,CAACA,SAAS,EAAE;IAEhB,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAM5B,GAAG,CAAC6B,GAAG,CAAC,mBAAmBY,SAAS,aAAa,CAAC;MACzE,IAAIb,QAAQ,CAACG,IAAI,CAACD,OAAO,EAAE;QACzBJ,eAAe,CAACE,QAAQ,CAACG,IAAI,CAACA,IAAI,CAAC;MACrC;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;IACF;EACF,CAAC;EAEDlE,SAAS,CAAC,MAAM;IACd,MAAM2F,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3BjD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMW,kBAAkB,CAAC,CAAC;MAC1BX,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDiD,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN3F,SAAS,CAAC,MAAM;IACd,IAAI6C,eAAe,EAAE;MACnB2C,oBAAoB,CAAC3C,eAAe,CAACgB,UAAU,CAAC;MAChD6B,iBAAiB,CAAC7C,eAAe,CAACgB,UAAU,CAAC;IAC/C;EACF,CAAC,EAAE,CAAChB,eAAe,CAAC,CAAC;;EAErB;EACA,MAAM+C,kBAAkB,GAAIjC,OAAO,IAAK;IACtC;IACAtB,QAAQ,CAAC,mBAAmBsB,OAAO,CAACE,UAAU,WAAW,EAAE;MACzDgC,KAAK,EAAE;QACLC,WAAW,EAAEnC,OAAO,CAACoC,YAAY;QACjC5B,SAAS,EAAER,OAAO,CAACE;MACrB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMmC,gBAAgB,GAAIrC,OAAO,IAAK;IACpC;IACAtB,QAAQ,CAAC,mBAAmBsB,OAAO,CAACE,UAAU,SAAS,EAAE;MACvDgC,KAAK,EAAE;QACLC,WAAW,EAAEnC,OAAO,CAACoC,YAAY;QACjC5B,SAAS,EAAER,OAAO,CAACE;MACrB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMoC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAItD,aAAa,CAACe,MAAM,KAAK,CAAC,EAAE;MAC9B,oBACE9B,OAAA,CAAC3B,IAAI;QAAAiG,QAAA,eACHtE,OAAA,CAAChB,KAAK;UACJuF,WAAW,EAAC,sCAAQ;UACpBC,KAAK,EAAExF,KAAK,CAACyF,sBAAuB;UAAAH,QAAA,eAEpCtE,OAAA,CAACI,IAAI;YAACsE,IAAI,EAAC,WAAW;YAAAJ,QAAA,EAAC;UAEvB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEX;IAEA,oBACE9E,OAAA,CAAC1B,GAAG;MAACyG,SAAS,EAAC,mBAAmB;MAACC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAV,QAAA,EACjDvD,aAAa,CAACkE,GAAG,CAAElD,OAAO,IAAK;QAC9B,MAAMmD,qBAAqB,GAAG/D,cAAc,CAACY,OAAO,CAACE,UAAU,CAAC,IAAI,CAAC,CAAC;QACtE,MAAMkD,mBAAmB,GAAGhC,MAAM,CAACiC,MAAM,CAACF,qBAAqB,CAAC,CAACG,IAAI,CAAC,CAAC,CAACvD,MAAM;QAC9E,MAAMwD,YAAY,GAAGnC,MAAM,CAACC,IAAI,CAAC8B,qBAAqB,CAAC,CAACpD,MAAM;QAE9D,oBACE9B,OAAA,CAACzB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAApB,QAAA,eAChCtE,OAAA,CAAC3B,IAAI;YACH0G,SAAS,EAAC,cAAc;YACxBY,SAAS;YACTC,OAAO,EAAEA,CAAA,KAAM1E,kBAAkB,CAACa,OAAO,CAAE;YAC3C8D,KAAK,EAAE;cACL,IAAI,CAAA5E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgB,UAAU,MAAKF,OAAO,CAACE,UAAU,GAAGvB,oBAAoB,GAAG,CAAC,CAAC,CAAC;cACnFoF,YAAY,EAAE,MAAM;cACpBC,QAAQ,EAAE;YACZ,CAAE;YACFC,SAAS,EAAE;cAAEC,OAAO,EAAE;YAAO,CAAE;YAAA3B,QAAA,gBAE/BtE,OAAA;cAAK+E,SAAS,EAAC,qBAAqB;cAACc,KAAK,EAAE;gBAAEK,SAAS,EAAE,QAAQ;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAA7B,QAAA,gBACpFtE,OAAA,CAACrB,MAAM;gBACLyH,IAAI,EAAE,EAAG;gBACTC,IAAI,eAAErG,OAAA,CAACX,YAAY;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBe,KAAK,EAAE;kBACLS,eAAe,EAAE,CAAArF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgB,UAAU,MAAKF,OAAO,CAACE,UAAU,GAAG,SAAS,GAAG,SAAS;kBAC3FkE,YAAY,EAAE;gBAChB;cAAE;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF9E,OAAA;gBAAAsE,QAAA,gBACEtE,OAAA,CAACG,KAAK;kBAACoG,KAAK,EAAE,CAAE;kBAACV,KAAK,EAAE;oBAAEW,MAAM,EAAE,CAAC;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAnC,QAAA,EACrDvC,OAAO,CAACoC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACR9E,OAAA,CAACI,IAAI;kBAACsE,IAAI,EAAC,WAAW;kBAACmB,KAAK,EAAE;oBAAEa,QAAQ,EAAE;kBAAO,CAAE;kBAAApC,QAAA,EAChDvC,OAAO,CAAC4E;gBAAU;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9E,OAAA,CAACb,OAAO;cAAC0G,KAAK,EAAE;gBAAEW,MAAM,EAAE;cAAS;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAExC9E,OAAA,CAAC1B,GAAG;cAACyG,SAAS,EAAC,mBAAmB;cAACC,MAAM,EAAE,CAAE;cAACa,KAAK,EAAE;gBAAEK,SAAS,EAAE;cAAS,CAAE;cAAA5B,QAAA,gBAC3EtE,OAAA,CAACzB,GAAG;gBAACqI,IAAI,EAAE,EAAG;gBAAAtC,QAAA,eACZtE,OAAA,CAAClB,SAAS;kBACR+H,KAAK,EAAC,oBAAK;kBACXC,KAAK,EAAExB,YAAa;kBACpByB,UAAU,EAAE;oBAAEL,QAAQ,EAAE,MAAM;oBAAED,KAAK,EAAE;kBAAU;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9E,OAAA,CAACzB,GAAG;gBAACqI,IAAI,EAAE,EAAG;gBAAAtC,QAAA,eACZtE,OAAA,CAAClB,SAAS;kBACR+H,KAAK,EAAC,0BAAM;kBACZC,KAAK,EAAE3B,mBAAoB;kBAC3B4B,UAAU,EAAE;oBAAEL,QAAQ,EAAE,MAAM;oBAAED,KAAK,EAAE;kBAAU;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9E,OAAA;cAAK+E,SAAS,EAAC,sBAAsB;cAACc,KAAK,EAAE;gBAAEmB,SAAS,EAAE;cAAG,CAAE;cAAA1C,QAAA,eAC7DtE,OAAA,CAACd,KAAK;gBAACkH,IAAI,EAAC,OAAO;gBAACP,KAAK,EAAE;kBAAEoB,KAAK,EAAE,MAAM;kBAAEC,cAAc,EAAE;gBAAS,CAAE;gBAAA5C,QAAA,gBACrEtE,OAAA,CAACf,MAAM;kBACLyF,IAAI,EAAC,SAAS;kBACd0B,IAAI,EAAC,OAAO;kBACZC,IAAI,eAAErG,OAAA,CAACV,YAAY;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBc,OAAO,EAAGuB,CAAC,IAAK;oBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;oBACnBpD,kBAAkB,CAACjC,OAAO,CAAC;kBAC7B,CAAE;kBAAAuC,QAAA,EACH;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9E,OAAA,CAACf,MAAM;kBACLmH,IAAI,EAAC,OAAO;kBACZC,IAAI,eAAErG,OAAA,CAACL,gBAAgB;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC3Bc,OAAO,EAAGuB,CAAC,IAAK;oBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;oBACnBhD,gBAAgB,CAACrC,OAAO,CAAC;kBAC3B,CAAE;kBAAAuC,QAAA,EACH;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EAEL,CAAA7D,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgB,UAAU,MAAKF,OAAO,CAACE,UAAU,iBACjDjC,OAAA;cAAK6F,KAAK,EAAE;gBACVwB,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,CAAC;gBACNC,KAAK,EAAE,CAAC;gBACRC,UAAU,EAAE,SAAS;gBACrBf,KAAK,EAAE,OAAO;gBACdX,YAAY,EAAE,KAAK;gBACnBmB,KAAK,EAAE,EAAE;gBACTQ,MAAM,EAAE,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBT,cAAc,EAAE,QAAQ;gBACxBR,QAAQ,EAAE;cACZ,CAAE;cAAApC,QAAA,EAAC;YAEH;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC,GA9F+B/C,OAAO,CAACE,UAAU;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+FrD,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAM8C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC3G,eAAe,IAAI,CAACM,YAAY,EAAE;MACrC,OAAO,IAAI;IACb;IAEA,MAAMsG,KAAK,GAAGtG,YAAY,CAACuG,cAAc;IAEzC,oBACE9H,OAAA,CAAC3B,IAAI;MAACwI,KAAK,EAAC,0BAAM;MAAChB,KAAK,EAAE;QAAEmB,SAAS,EAAE;MAAG,CAAE;MAAA1C,QAAA,gBAC1CtE,OAAA,CAAC1B,GAAG;QAAC0G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAV,QAAA,gBACpBtE,OAAA,CAACzB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eACjBtE,OAAA,CAAClB,SAAS;YACR+H,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAEe,KAAK,CAACE,cAAe;YAC5BC,MAAM,eAAEhI,OAAA,CAACV,YAAY;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9E,OAAA,CAACzB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eACjBtE,OAAA,CAAClB,SAAS;YACR+H,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAEe,KAAK,CAACI,kBAAmB;YAChCD,MAAM,eAAEhI,OAAA,CAACP,mBAAmB;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9E,OAAA,CAACzB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eACjBtE,OAAA,CAAClB,SAAS;YACR+H,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAEe,KAAK,CAACK,eAAgB;YAC7BC,MAAM,EAAC,GAAG;YACVH,MAAM,eAAEhI,OAAA,CAACT,cAAc;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9E,OAAA,CAACzB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eACjBtE,OAAA,CAAClB,SAAS;YACR+H,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAEe,KAAK,CAACO,SAAU;YACvBC,SAAS,EAAE,CAAE;YACbL,MAAM,eAAEhI,OAAA,CAACL,gBAAgB;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9E,OAAA,CAACb,OAAO;QAAAwF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEX9E,OAAA;QAAAsE,QAAA,gBACEtE,OAAA,CAACI,IAAI;UAACkI,MAAM;UAAAhE,QAAA,EAAC;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxB9E,OAAA,CAACjB,QAAQ;UACPwJ,OAAO,EAAEV,KAAK,CAACK,eAAgB;UAC/BM,MAAM,EAAEX,KAAK,CAACK,eAAe,IAAI,EAAE,GAAG,SAAS,GAAGL,KAAK,CAACK,eAAe,IAAI,EAAE,GAAG,QAAQ,GAAG,WAAY;UACvGrC,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAE;QAAE;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX,CAAC;;EAED;EACA,MAAM2D,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACxH,eAAe,EAAE;MACpB,OAAO,IAAI;IACb;IAEA,MAAMiE,qBAAqB,GAAG/D,cAAc,CAACF,eAAe,CAACgB,UAAU,CAAC,IAAI,CAAC,CAAC;IAC9E,MAAMyG,QAAQ,GAAGvF,MAAM,CAACC,IAAI,CAAC8B,qBAAqB,CAAC;IAEnD,oBACElF,OAAA,CAAC3B,IAAI;MACH0G,SAAS,EAAC,sBAAsB;MAChC8B,KAAK,eACH7G,OAAA,CAACd,KAAK;QAAAoF,QAAA,gBACJtE,OAAA,CAACV,YAAY;UAACuG,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAU;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAE7C,eAAA9E,OAAA,CAACI,IAAI;UAACsE,IAAI,EAAC,WAAW;UAAAJ,QAAA,EAAC;QAAQ;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CACR;MACDe,KAAK,EAAE;QAAEmB,SAAS,EAAE;MAAG,CAAE;MACzB2B,KAAK,eACH3I,OAAA,CAACf,MAAM;QACLyF,IAAI,EAAC,SAAS;QACd2B,IAAI,eAAErG,OAAA,CAACJ,WAAW;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBc,OAAO,EAAEA,CAAA,KAAM5B,kBAAkB,CAAC/C,eAAe,CAAE;QAAAqD,QAAA,EACpD;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;MAAAR,QAAA,EAEAoE,QAAQ,CAAC5G,MAAM,KAAK,CAAC,gBACpB9B,OAAA,CAAChB,KAAK;QAACuF,WAAW,EAAC,sCAAQ;QAACC,KAAK,EAAExF,KAAK,CAACyF;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEnE9E,OAAA,CAAC1B,GAAG;QAACyG,SAAS,EAAC,0BAA0B;QAACC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAV,QAAA,EACxDoE,QAAQ,CAACzD,GAAG,CAAClC,OAAO,IAAI;UACvB,MAAM6F,eAAe,GAAG1D,qBAAqB,CAACnC,OAAO,CAAC;UACtD,oBACE/C,OAAA,CAACzB,GAAG;YAACgH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAApB,QAAA,eACzBtE,OAAA,CAAC3B,IAAI;cACH+H,IAAI,EAAC,OAAO;cACZS,KAAK,eACH7G,OAAA,CAACd,KAAK;gBAAAoF,QAAA,gBACJtE,OAAA,CAACZ,GAAG;kBAACqH,KAAK,EAAC,MAAM;kBAAAnC,QAAA,EAAEvB;gBAAO;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjC9E,OAAA,CAACI,IAAI;kBAACsE,IAAI,EAAC,WAAW;kBAAAJ,QAAA,GAAC,cAAE,EAACsE,eAAe,CAAC9G,MAAM,EAAC,QAAC;gBAAA;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CACR;cACDe,KAAK,EAAE;gBAAE4B,MAAM,EAAE;cAAO,CAAE;cAAAnD,QAAA,eAE1BtE,OAAA,CAACpB,IAAI;gBACHwH,IAAI,EAAC,OAAO;gBACZyC,UAAU,EAAED,eAAgB;gBAC5BE,UAAU,EAAGpG,QAAQ,iBACnB1C,OAAA,CAACpB,IAAI,CAACmK,IAAI;kBAAClD,KAAK,EAAE;oBAAEI,OAAO,EAAE;kBAAQ,CAAE;kBAAA3B,QAAA,eACrCtE,OAAA;oBAAK6F,KAAK,EAAE;sBAAEoB,KAAK,EAAE;oBAAO,CAAE;oBAAA3C,QAAA,gBAC5BtE,OAAA;sBAAK6F,KAAK,EAAE;wBAAE6B,OAAO,EAAE,MAAM;wBAAER,cAAc,EAAE,eAAe;wBAAES,UAAU,EAAE,QAAQ;wBAAExB,YAAY,EAAE;sBAAE,CAAE;sBAAA7B,QAAA,gBACtGtE,OAAA,CAACI,IAAI;wBAACkI,MAAM;wBAACzC,KAAK,EAAE;0BAAEa,QAAQ,EAAE;wBAAO,CAAE;wBAAApC,QAAA,EACtC5B,QAAQ,CAACsG,gBAAgB,IAAItG,QAAQ,CAACmE;sBAAK;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACP9E,OAAA,CAACnB,KAAK;wBACJ2J,MAAM,EACJ9F,QAAQ,CAAC8F,MAAM,KAAK,QAAQ,GAAG,SAAS,GACxC9F,QAAQ,CAAC8F,MAAM,KAAK,WAAW,GAAG,YAAY,GAAG,SAClD;wBACDS,IAAI,EACFvG,QAAQ,CAAC8F,MAAM,KAAK,QAAQ,GAAG,KAAK,GACpC9F,QAAQ,CAAC8F,MAAM,KAAK,WAAW,GAAG,KAAK,GAAG;sBAC3C;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACN9E,OAAA;sBAAK6F,KAAK,EAAE;wBAAE6B,OAAO,EAAE,MAAM;wBAAER,cAAc,EAAE,eAAe;wBAAES,UAAU,EAAE;sBAAS,CAAE;sBAAArD,QAAA,gBACrFtE,OAAA,CAACI,IAAI;wBAACsE,IAAI,EAAC,WAAW;wBAACmB,KAAK,EAAE;0BAAEa,QAAQ,EAAE;wBAAO,CAAE;wBAAApC,QAAA,EAChD,IAAId,IAAI,CAACd,QAAQ,CAACe,UAAU,CAAC,CAACyF,kBAAkB,CAAC;sBAAC;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,EACNpC,QAAQ,CAACyG,KAAK,KAAK,IAAI,IAAIzG,QAAQ,CAACyG,KAAK,KAAKC,SAAS,gBACtDpJ,OAAA,CAACI,IAAI;wBACHyF,KAAK,EAAE;0BACLa,QAAQ,EAAE,MAAM;0BAChBD,KAAK,EAAE/D,QAAQ,CAACyG,KAAK,IAAI,EAAE,GAAG,SAAS,GAAGzG,QAAQ,CAACyG,KAAK,IAAI,EAAE,GAAG,SAAS,GAAG,SAAS;0BACtFE,UAAU,EAAE;wBACd,CAAE;wBAAA/E,QAAA,GAED5B,QAAQ,CAACyG,KAAK,EAAC,QAClB;sBAAA;wBAAAxE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,GACL,IAAI;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACX;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GApDwB/B,OAAO;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqDnC,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEX,CAAC;EAED,IAAIjE,OAAO,EAAE;IACX,oBACEb,OAAA;MAAK6F,KAAK,EAAE;QAAEK,SAAS,EAAE,QAAQ;QAAED,OAAO,EAAE;MAAO,CAAE;MAAA3B,QAAA,gBACnDtE,OAAA,CAACvB,IAAI;QAAC2H,IAAI,EAAC;MAAO;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB9E,OAAA;QAAK6F,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAA1C,QAAA,eAC5BtE,OAAA,CAACI,IAAI;UAAAkE,QAAA,EAAC;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9E,OAAA;IAAK6F,KAAK,EAAE;MAAEI,OAAO,EAAE;IAAO,CAAE;IAAA3B,QAAA,gBAE9BtE,OAAA,CAAC3B,IAAI;MACHwH,KAAK,EAAE;QACLM,YAAY,EAAE,EAAE;QAChBqB,UAAU,EAAE,mDAAmD;QAC/D7G,MAAM,EAAE,MAAM;QACdmF,YAAY,EAAE;MAChB,CAAE;MACFE,SAAS,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAA3B,QAAA,eAE/BtE,OAAA,CAAC1B,GAAG;QAACgL,KAAK,EAAC,QAAQ;QAACtE,MAAM,EAAE,EAAG;QAAAV,QAAA,gBAC7BtE,OAAA,CAACzB,GAAG;UAAA+F,QAAA,eACFtE,OAAA;YAAK6F,KAAK,EAAE;cAAEa,QAAQ,EAAE;YAAO,CAAE;YAAApC,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACN9E,OAAA,CAACzB,GAAG;UAACgL,IAAI,EAAC,MAAM;UAAAjF,QAAA,gBACdtE,OAAA,CAACG,KAAK;YAACoG,KAAK,EAAE,CAAE;YAACV,KAAK,EAAE;cAAEY,KAAK,EAAE,OAAO;cAAED,MAAM,EAAE;YAAE,CAAE;YAAAlC,QAAA,GAAC,gCAChD,EAAC,CAAA/D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiJ,SAAS,MAAIjJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkJ,QAAQ,GAAC,QAC1C;UAAA;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9E,OAAA,CAACK,SAAS;YAACwF,KAAK,EAAE;cAAEY,KAAK,EAAE,uBAAuB;cAAED,MAAM,EAAE,WAAW;cAAEE,QAAQ,EAAE;YAAO,CAAE;YAAApC,QAAA,EAAC;UAE7F;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACN9E,OAAA,CAACzB,GAAG;UAAA+F,QAAA,eACFtE,OAAA,CAACd,KAAK;YAACwK,SAAS,EAAC,UAAU;YAAC7D,KAAK,EAAE;cAAEK,SAAS,EAAE;YAAS,CAAE;YAAA5B,QAAA,gBACzDtE,OAAA,CAACI,IAAI;cAACyF,KAAK,EAAE;gBAAEY,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAApC,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5D9E,OAAA,CAACI,IAAI;cAACyF,KAAK,EAAE;gBAAEY,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAE,MAAM;gBAAE2C,UAAU,EAAE;cAAO,CAAE;cAAA/E,QAAA,EACnE,IAAId,IAAI,CAAC,CAAC,CAAC0F,kBAAkB,CAAC;YAAC;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP9E,OAAA,CAAC3B,IAAI;MACHwI,KAAK,eACH7G,OAAA,CAACd,KAAK;QAAAoF,QAAA,gBACJtE,OAAA,CAACX,YAAY;UAACwG,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAU;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAE7C,eAAA9E,OAAA,CAACnB,KAAK;UAAC8K,KAAK,EAAE5I,aAAa,CAACe,MAAO;UAAC+D,KAAK,EAAE;YAAES,eAAe,EAAE;UAAU;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CACR;MACDe,KAAK,EAAE;QAAEM,YAAY,EAAE;MAAG,CAAE;MAC5BH,SAAS,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAA3B,QAAA,EAE9BD,kBAAkB,CAAC;IAAC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,EAGN7D,eAAe,iBACdjB,OAAA,CAAAE,SAAA;MAAAoE,QAAA,GACGsD,gBAAgB,CAAC,CAAC,EAClBa,oBAAoB,CAAC,CAAC;IAAA,eACvB,CACH;EAAA;IAAA9D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtE,EAAA,CA9fIF,eAAe;EAAA,QACFT,WAAW;AAAA;AAAA+J,EAAA,GADxBtJ,eAAe;AAggBrB,eAAeA,eAAe;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}