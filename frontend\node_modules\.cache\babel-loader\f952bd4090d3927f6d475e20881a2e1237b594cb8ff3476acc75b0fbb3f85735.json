{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { genFocusStyle, resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genSwitchSmallStyle = token => {\n  const {\n    componentCls,\n    trackHeightSM,\n    trackPadding,\n    trackMinWidthSM,\n    innerMinMarginSM,\n    innerMaxMarginSM,\n    handleSizeSM,\n    calc\n  } = token;\n  const switchInnerCls = `${componentCls}-inner`;\n  const trackPaddingCalc = unit(calc(handleSizeSM).add(calc(trackPadding).mul(2)).equal());\n  const innerMaxMarginCalc = unit(calc(innerMaxMarginSM).mul(2).equal());\n  return {\n    [componentCls]: {\n      [`&${componentCls}-small`]: {\n        minWidth: trackMinWidthSM,\n        height: trackHeightSM,\n        lineHeight: unit(trackHeightSM),\n        [`${componentCls}-inner`]: {\n          paddingInlineStart: innerMaxMarginSM,\n          paddingInlineEnd: innerMinMarginSM,\n          [`${switchInnerCls}-checked, ${switchInnerCls}-unchecked`]: {\n            minHeight: trackHeightSM\n          },\n          [`${switchInnerCls}-checked`]: {\n            marginInlineStart: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`,\n            marginInlineEnd: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`\n          },\n          [`${switchInnerCls}-unchecked`]: {\n            marginTop: calc(trackHeightSM).mul(-1).equal(),\n            marginInlineStart: 0,\n            marginInlineEnd: 0\n          }\n        },\n        [`${componentCls}-handle`]: {\n          width: handleSizeSM,\n          height: handleSizeSM\n        },\n        [`${componentCls}-loading-icon`]: {\n          top: calc(calc(handleSizeSM).sub(token.switchLoadingIconSize)).div(2).equal(),\n          fontSize: token.switchLoadingIconSize\n        },\n        [`&${componentCls}-checked`]: {\n          [`${componentCls}-inner`]: {\n            paddingInlineStart: innerMinMarginSM,\n            paddingInlineEnd: innerMaxMarginSM,\n            [`${switchInnerCls}-checked`]: {\n              marginInlineStart: 0,\n              marginInlineEnd: 0\n            },\n            [`${switchInnerCls}-unchecked`]: {\n              marginInlineStart: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`,\n              marginInlineEnd: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`\n            }\n          },\n          [`${componentCls}-handle`]: {\n            insetInlineStart: `calc(100% - ${unit(calc(handleSizeSM).add(trackPadding).equal())})`\n          }\n        },\n        [`&:not(${componentCls}-disabled):active`]: {\n          [`&:not(${componentCls}-checked) ${switchInnerCls}`]: {\n            [`${switchInnerCls}-unchecked`]: {\n              marginInlineStart: calc(token.marginXXS).div(2).equal(),\n              marginInlineEnd: calc(token.marginXXS).mul(-1).div(2).equal()\n            }\n          },\n          [`&${componentCls}-checked ${switchInnerCls}`]: {\n            [`${switchInnerCls}-checked`]: {\n              marginInlineStart: calc(token.marginXXS).mul(-1).div(2).equal(),\n              marginInlineEnd: calc(token.marginXXS).div(2).equal()\n            }\n          }\n        }\n      }\n    }\n  };\n};\nconst genSwitchLoadingStyle = token => {\n  const {\n    componentCls,\n    handleSize,\n    calc\n  } = token;\n  return {\n    [componentCls]: {\n      [`${componentCls}-loading-icon${token.iconCls}`]: {\n        position: 'relative',\n        top: calc(calc(handleSize).sub(token.fontSize)).div(2).equal(),\n        color: token.switchLoadingIconColor,\n        verticalAlign: 'top'\n      },\n      [`&${componentCls}-checked ${componentCls}-loading-icon`]: {\n        color: token.switchColor\n      }\n    }\n  };\n};\nconst genSwitchHandleStyle = token => {\n  const {\n    componentCls,\n    trackPadding,\n    handleBg,\n    handleShadow,\n    handleSize,\n    calc\n  } = token;\n  const switchHandleCls = `${componentCls}-handle`;\n  return {\n    [componentCls]: {\n      [switchHandleCls]: {\n        position: 'absolute',\n        top: trackPadding,\n        insetInlineStart: trackPadding,\n        width: handleSize,\n        height: handleSize,\n        transition: `all ${token.switchDuration} ease-in-out`,\n        '&::before': {\n          position: 'absolute',\n          top: 0,\n          insetInlineEnd: 0,\n          bottom: 0,\n          insetInlineStart: 0,\n          backgroundColor: handleBg,\n          borderRadius: calc(handleSize).div(2).equal(),\n          boxShadow: handleShadow,\n          transition: `all ${token.switchDuration} ease-in-out`,\n          content: '\"\"'\n        }\n      },\n      [`&${componentCls}-checked ${switchHandleCls}`]: {\n        insetInlineStart: `calc(100% - ${unit(calc(handleSize).add(trackPadding).equal())})`\n      },\n      [`&:not(${componentCls}-disabled):active`]: {\n        [`${switchHandleCls}::before`]: {\n          insetInlineEnd: token.switchHandleActiveInset,\n          insetInlineStart: 0\n        },\n        [`&${componentCls}-checked ${switchHandleCls}::before`]: {\n          insetInlineEnd: 0,\n          insetInlineStart: token.switchHandleActiveInset\n        }\n      }\n    }\n  };\n};\nconst genSwitchInnerStyle = token => {\n  const {\n    componentCls,\n    trackHeight,\n    trackPadding,\n    innerMinMargin,\n    innerMaxMargin,\n    handleSize,\n    calc\n  } = token;\n  const switchInnerCls = `${componentCls}-inner`;\n  const trackPaddingCalc = unit(calc(handleSize).add(calc(trackPadding).mul(2)).equal());\n  const innerMaxMarginCalc = unit(calc(innerMaxMargin).mul(2).equal());\n  return {\n    [componentCls]: {\n      [switchInnerCls]: {\n        display: 'block',\n        overflow: 'hidden',\n        borderRadius: 100,\n        height: '100%',\n        paddingInlineStart: innerMaxMargin,\n        paddingInlineEnd: innerMinMargin,\n        transition: `padding-inline-start ${token.switchDuration} ease-in-out, padding-inline-end ${token.switchDuration} ease-in-out`,\n        [`${switchInnerCls}-checked, ${switchInnerCls}-unchecked`]: {\n          display: 'block',\n          color: token.colorTextLightSolid,\n          fontSize: token.fontSizeSM,\n          transition: `margin-inline-start ${token.switchDuration} ease-in-out, margin-inline-end ${token.switchDuration} ease-in-out`,\n          pointerEvents: 'none',\n          minHeight: trackHeight\n        },\n        [`${switchInnerCls}-checked`]: {\n          marginInlineStart: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`,\n          marginInlineEnd: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`\n        },\n        [`${switchInnerCls}-unchecked`]: {\n          marginTop: calc(trackHeight).mul(-1).equal(),\n          marginInlineStart: 0,\n          marginInlineEnd: 0\n        }\n      },\n      [`&${componentCls}-checked ${switchInnerCls}`]: {\n        paddingInlineStart: innerMinMargin,\n        paddingInlineEnd: innerMaxMargin,\n        [`${switchInnerCls}-checked`]: {\n          marginInlineStart: 0,\n          marginInlineEnd: 0\n        },\n        [`${switchInnerCls}-unchecked`]: {\n          marginInlineStart: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`,\n          marginInlineEnd: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`\n        }\n      },\n      [`&:not(${componentCls}-disabled):active`]: {\n        [`&:not(${componentCls}-checked) ${switchInnerCls}`]: {\n          [`${switchInnerCls}-unchecked`]: {\n            marginInlineStart: calc(trackPadding).mul(2).equal(),\n            marginInlineEnd: calc(trackPadding).mul(-1).mul(2).equal()\n          }\n        },\n        [`&${componentCls}-checked ${switchInnerCls}`]: {\n          [`${switchInnerCls}-checked`]: {\n            marginInlineStart: calc(trackPadding).mul(-1).mul(2).equal(),\n            marginInlineEnd: calc(trackPadding).mul(2).equal()\n          }\n        }\n      }\n    }\n  };\n};\nconst genSwitchStyle = token => {\n  const {\n    componentCls,\n    trackHeight,\n    trackMinWidth\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'inline-block',\n      boxSizing: 'border-box',\n      minWidth: trackMinWidth,\n      height: trackHeight,\n      lineHeight: unit(trackHeight),\n      verticalAlign: 'middle',\n      background: token.colorTextQuaternary,\n      border: '0',\n      borderRadius: 100,\n      cursor: 'pointer',\n      transition: `all ${token.motionDurationMid}`,\n      userSelect: 'none',\n      [`&:hover:not(${componentCls}-disabled)`]: {\n        background: token.colorTextTertiary\n      }\n    }), genFocusStyle(token)), {\n      [`&${componentCls}-checked`]: {\n        background: token.switchColor,\n        [`&:hover:not(${componentCls}-disabled)`]: {\n          background: token.colorPrimaryHover\n        }\n      },\n      [`&${componentCls}-loading, &${componentCls}-disabled`]: {\n        cursor: 'not-allowed',\n        opacity: token.switchDisabledOpacity,\n        '*': {\n          boxShadow: 'none',\n          cursor: 'not-allowed'\n        }\n      },\n      // rtl style\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    fontSize,\n    lineHeight,\n    controlHeight,\n    colorWhite\n  } = token;\n  const height = fontSize * lineHeight;\n  const heightSM = controlHeight / 2;\n  const padding = 2; // Fixed value\n  const handleSize = height - padding * 2;\n  const handleSizeSM = heightSM - padding * 2;\n  return {\n    trackHeight: height,\n    trackHeightSM: heightSM,\n    trackMinWidth: handleSize * 2 + padding * 4,\n    trackMinWidthSM: handleSizeSM * 2 + padding * 2,\n    trackPadding: padding,\n    // Fixed value\n    handleBg: colorWhite,\n    handleSize,\n    handleSizeSM,\n    handleShadow: `0 2px 4px 0 ${new FastColor('#00230b').setA(0.2).toRgbString()}`,\n    innerMinMargin: handleSize / 2,\n    innerMaxMargin: handleSize + padding + padding * 2,\n    innerMinMarginSM: handleSizeSM / 2,\n    innerMaxMarginSM: handleSizeSM + padding + padding * 2\n  };\n};\nexport default genStyleHooks('Switch', token => {\n  const switchToken = mergeToken(token, {\n    switchDuration: token.motionDurationMid,\n    switchColor: token.colorPrimary,\n    switchDisabledOpacity: token.opacityLoading,\n    switchLoadingIconSize: token.calc(token.fontSizeIcon).mul(0.75).equal(),\n    switchLoadingIconColor: `rgba(0, 0, 0, ${token.opacityLoading})`,\n    switchHandleActiveInset: '-30%'\n  });\n  return [genSwitchStyle(switchToken),\n  // inner style\n  genSwitchInnerStyle(switchToken),\n  // handle style\n  genSwitchHandleStyle(switchToken),\n  // loading style\n  genSwitchLoadingStyle(switchToken),\n  // small style\n  genSwitchSmallStyle(switchToken)];\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}