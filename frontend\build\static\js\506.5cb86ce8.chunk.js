"use strict";(self.webpackChunksmart_edu_frontend=self.webpackChunksmart_edu_frontend||[]).push([[506],{506:(e,o,n)=>{n.d(o,{debugTeacherDataInconsistency:()=>r,fetchTeacherDataUnified:()=>t});const s=()=>{console.log("\u6e05\u9664\u6559\u5e08\u6570\u636e\u7f13\u5b58...");const e=[];for(let n=0;n<localStorage.length;n++){const o=localStorage.key(n);o&&(o.includes("teacher")||o.includes("class")||o.includes("TeacherData")||o.includes("ClassData"))&&e.push(o)}e.forEach(e=>{localStorage.removeItem(e),console.log("\u5df2\u6e05\u9664\u7f13\u5b58: ".concat(e))});const o=[];for(let n=0;n<sessionStorage.length;n++){const e=sessionStorage.key(n);e&&(e.includes("teacher")||e.includes("class")||e.includes("TeacherData")||e.includes("ClassData"))&&o.push(e)}o.forEach(e=>{sessionStorage.removeItem(e),console.log("\u5df2\u6e05\u9664\u4f1a\u8bdd\u7f13\u5b58: ".concat(e))}),console.log("\u6559\u5e08\u6570\u636e\u7f13\u5b58\u6e05\u9664\u5b8c\u6210")},c=e=>e&&Array.isArray(e)?e.map(e=>({id:e.id,username:e.username||"",name:e.name||e.full_name||e.username||"\u672a\u77e5\u6559\u5e08",email:e.email||"",phone:e.phone||"",subject:e.subject||"",role:e.role||"subject_teacher"})):(console.warn("\u6559\u5e08\u6570\u636e\u683c\u5f0f\u4e0d\u6b63\u786e:",e),[]),t=async(e,o)=>{console.log("\u7edf\u4e00\u83b7\u53d6\u73ed\u7ea7 ".concat(e," \u7684\u6559\u5e08\u6570\u636e"));try{s();const n=Date.now(),t="/admin/classes/".concat(e,"/teachers?t=").concat(n);console.log("API\u8c03\u7528: ".concat(t));const a=await o(t);console.log("\u539f\u59cb\u6559\u5e08\u6570\u636e:",a);const r=c(a);return console.log("\u6807\u51c6\u5316\u540e\u7684\u6559\u5e08\u6570\u636e:",r),r}catch(n){throw console.error("\u83b7\u53d6\u6559\u5e08\u6570\u636e\u5931\u8d25:",n),n}},a=(e,o)=>{if(!Array.isArray(e)||!Array.isArray(o))return!1;if(e.length!==o.length)return console.warn("\u6559\u5e08\u6570\u636e\u6570\u91cf\u4e0d\u4e00\u81f4:",e.length,"vs",o.length),!1;const n=[...e].sort((e,o)=>e.id-o.id),s=[...o].sort((e,o)=>e.id-o.id);for(let c=0;c<n.length;c++){const e=n[c],o=s[c];if(e.id!==o.id||e.name!==o.name||e.email!==o.email||e.role!==o.role)return console.warn("\u6559\u5e08\u6570\u636e\u4e0d\u4e00\u81f4:",e,"vs",o),!1}return!0},r=async(e,o)=>{console.log("\u5f00\u59cb\u8c03\u8bd5\u6559\u5e08\u6570\u636e\u4e0d\u4e00\u81f4\u95ee\u9898...");try{const n=[];for(let a=0;a<3;a++){console.log("\u7b2c ".concat(a+1," \u6b21\u83b7\u53d6\u6570\u636e...")),s(),await new Promise(e=>setTimeout(e,100));const c=await t(e,o);n.push(c),console.log("\u7b2c ".concat(a+1," \u6b21\u7ed3\u679c:"),c)}let c=!0;for(let e=1;e<n.length;e++)a(n[0],n[e])||(c=!1,console.error("\u7b2c1\u6b21\u548c\u7b2c".concat(e+1,"\u6b21\u6570\u636e\u4e0d\u4e00\u81f4!")));return c?console.log("\u2705 \u591a\u6b21\u83b7\u53d6\u7684\u6570\u636e\u4e00\u81f4\uff0cAPI\u5de5\u4f5c\u6b63\u5e38"):console.error("\u274c \u591a\u6b21\u83b7\u53d6\u7684\u6570\u636e\u4e0d\u4e00\u81f4\uff0c\u5b58\u5728\u95ee\u9898"),{consistent:c,results:n}}catch(n){throw console.error("\u8c03\u8bd5\u8fc7\u7a0b\u4e2d\u51fa\u9519:",n),n}}}}]);