#!/usr/bin/env python3
"""
统一权限控制服务
用于所有一级菜单的权限控制：作业管理、错题训练、统计报表、班级管理、作业分析、用户管理
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, text
from typing import List, Optional, Dict, Any
from ..models.user import User
from ..models.user_role import UserRole
from ..models.homework import HomeworkAssignment
from ..models.class_model import Class
import logging

logger = logging.getLogger(__name__)

class PermissionService:
    """统一权限控制服务类"""
    
    @staticmethod
    async def get_user_permission_scope(current_user: User, db: Session, context: str = "homework_management") -> Dict[str, Any]:
        """
        获取用户的权限范围
        参数:
            current_user: 当前用户
            db: 数据库会话
            context: 权限上下文
                - "homework_management" = 作业管理菜单
                - "system_homework_management" = 系统作业管理菜单
        返回用户可以访问的数据范围
        """
        try:
            # 超级管理员权限处理
            if current_user.is_admin and current_user.role == '超级管理员':
                if context == "system_homework_management":
                    # 系统作业管理菜单：全系统权限
                    return {
                        "scope": "system",
                        "school_ids": None,  # 所有学校
                        "grade_ids": None,   # 所有年级
                        "subject_ids": None, # 所有科目
                        "class_ids": None,   # 所有班级
                        "teacher_ids": None, # 所有教师
                        "description": "超级管理员 - 系统作业管理 - 全系统权限"
                    }
                elif context == "homework_management":
                    # 作业管理菜单：所属学校所有科目所有班级
                    return {
                        "scope": "school",
                        "school_ids": [current_user.school_id] if current_user.school_id else [],
                        "grade_ids": None,   # 所属学校所有年级
                        "subject_ids": None, # 所属学校所有科目
                        "class_ids": None,   # 所属学校所有班级
                        "teacher_ids": None, # 所属学校所有教师
                        "description": "超级管理员 - 作业管理 - 所属学校权限"
                    }

            # 其他管理员权限处理
            elif current_user.is_admin:
                return {
                    "scope": "system",
                    "school_ids": None,  # 所有学校
                    "grade_ids": None,   # 所有年级
                    "subject_ids": None, # 所有科目
                    "class_ids": None,   # 所有班级
                    "teacher_ids": None, # 所有教师
                    "description": "系统管理员 - 全系统权限"
                }
            
            # 查询用户的角色分配
            user_roles_query = db.execute(
                text("""
                    SELECT ur.role_id, ur.school_id, ur.grade_id, ur.subject_id, ur.class_id,
                           r.code as role_code, r.name as role_name
                    FROM user_roles ur
                    LEFT JOIN roles r ON ur.role_id = r.id
                    WHERE ur.user_id = :user_id
                """),
                {"user_id": current_user.id}
            ).fetchall()

            # 检查是否有学校管理员角色
            has_school_admin_role = any(
                role_data[5] in ['school_admin', 'principal', 'vice_principal']
                for role_data in user_roles_query
            )

            # 学校管理员（非超级管理员）的权限处理
            if has_school_admin_role and not (current_user.is_admin and current_user.role == '超级管理员'):
                if context == "system_homework_management":
                    # 系统作业管理菜单：所属学校所有班级的所有学科作业
                    return {
                        "scope": "school",
                        "school_ids": [current_user.school_id] if current_user.school_id else [],
                        "grade_ids": None,   # 所属学校所有年级
                        "subject_ids": None, # 所属学校所有科目
                        "class_ids": None,   # 所属学校所有班级
                        "teacher_ids": None, # 所属学校所有教师
                        "description": "学校管理员 - 系统作业管理 - 所属学校权限"
                    }
                elif context == "homework_management":
                    # 作业管理菜单：自己任教班级的注册时选择的科目的作业
                    user_subject_ids = []
                    if current_user.subject:
                        # 根据用户的科目名称查找科目ID
                        subject_result = db.execute(
                            text("SELECT id FROM subjects WHERE name = :subject_name"),
                            {"subject_name": current_user.subject}
                        ).fetchone()
                        if subject_result:
                            user_subject_ids = [subject_result[0]]

                    return {
                        "scope": "teacher_subject",
                        "school_ids": [current_user.school_id] if current_user.school_id else [],
                        "grade_ids": [],
                        "subject_ids": user_subject_ids,
                        "class_ids": [],  # 需要查询用户任教的班级
                        "teacher_ids": [current_user.id],
                        "description": f"学校管理员 - 作业管理 - 任教班级的{current_user.subject or '未指定'}科目"
                    }

            # 根据上下文决定权限范围
            if context == "teacher":
                # 教师身份上下文：只能查看自己的数据
                return {
                    "scope": "teacher",
                    "school_ids": [current_user.school_id] if current_user.school_id else [],
                    "grade_ids": [],
                    "subject_ids": [],
                    "class_ids": [],
                    "teacher_ids": [current_user.id],
                    "description": f"教师身份 - 只能查看自己的数据"
                }

            if not user_roles_query:
                # 如果没有角色分配，根据用户基本信息判断
                if current_user.is_teacher:
                    return {
                        "scope": "teacher",
                        "school_ids": [current_user.school_id] if current_user.school_id else [],
                        "grade_ids": [],
                        "subject_ids": [],
                        "class_ids": [],
                        "teacher_ids": [current_user.id],
                        "description": f"教师 - 只能查看自己的数据"
                    }
                else:
                    return {
                        "scope": "student",
                        "school_ids": [current_user.school_id] if current_user.school_id else [],
                        "grade_ids": [],
                        "subject_ids": [],
                        "class_ids": [],
                        "teacher_ids": [],
                        "description": "学生 - 只能查看自己的数据"
                    }
            
            # 分析角色权限，取最高权限
            max_scope = "teacher"
            school_ids = set()
            grade_ids = set()
            subject_ids = set()
            class_ids = set()
            teacher_ids = {current_user.id}  # 至少包含自己
            role_descriptions = []
            
            for role_data in user_roles_query:
                role_id, school_id, grade_id, subject_id, class_id, role_code, role_name = role_data
                role_descriptions.append(role_name)
                
                if school_id:
                    school_ids.add(school_id)
                
                # 根据角色代码确定权限范围
                if role_code in ['school_admin', 'principal', 'vice_principal']:
                    # 学校管理员、校长、副校长权限处理
                    if context == "system_homework_management":
                        # 系统作业管理菜单：本校所有年级班级在自己注册时指定科目作业
                        max_scope = "school_subject"
                        if school_id:
                            school_ids.add(school_id)
                        # 添加用户的科目
                        if current_user.subject:
                            subject_result = db.execute(
                                text("SELECT id FROM subjects WHERE name = :subject_name"),
                                {"subject_name": current_user.subject}
                            ).fetchone()
                            if subject_result:
                                subject_ids.add(subject_result[0])
                    elif context == "homework_management":
                        # 作业管理菜单：自己任教班级的注册科目的作业
                        if max_scope not in ['school', 'school_subject']:
                            max_scope = "teacher_subject"
                        teacher_ids.add(current_user.id)
                        # 添加用户的科目
                        if current_user.subject:
                            subject_result = db.execute(
                                text("SELECT id FROM subjects WHERE name = :subject_name"),
                                {"subject_name": current_user.subject}
                            ).fetchone()
                            if subject_result:
                                subject_ids.add(subject_result[0])

                elif role_code == 'academic_director':
                    # 教务处主任：本校所有数据
                    max_scope = "school"
                    if school_id:
                        school_ids.add(school_id)
                
                elif role_code == 'grade_director':
                    # 年级组长：该年级所有数据
                    if max_scope not in ['school']:
                        max_scope = "grade"
                    if grade_id:
                        grade_ids.add(grade_id)
                    if school_id:
                        school_ids.add(school_id)
                
                elif role_code == 'subject_leader':
                    # 教研组长权限处理
                    if context == "system_homework_management":
                        # 系统作业管理菜单：管理所属学校所有年级班级在自己注册时指定科目作业
                        if max_scope not in ['school', 'grade', 'school_subject']:
                            max_scope = "school_subject"
                        # 使用用户注册时的科目
                        if current_user.subject:
                            subject_result = db.execute(
                                text("SELECT id FROM subjects WHERE name = :subject_name"),
                                {"subject_name": current_user.subject}
                            ).fetchone()
                            if subject_result:
                                subject_ids.add(subject_result[0])
                        if school_id:
                            school_ids.add(school_id)
                    elif context == "homework_management":
                        # 作业管理菜单：管理自己任教班级的注册科目的作业
                        if max_scope not in ['school', 'grade', 'subject', 'grade_subject', 'school_subject', 'class_all_subjects']:
                            max_scope = "teacher_subject"
                        teacher_ids.add(current_user.id)
                        # 添加用户的科目
                        if current_user.subject:
                            subject_result = db.execute(
                                text("SELECT id FROM subjects WHERE name = :subject_name"),
                                {"subject_name": current_user.subject}
                            ).fetchone()
                            if subject_result:
                                subject_ids.add(subject_result[0])
                
                elif role_code == 'lesson_planner':
                    # 备课组长权限处理
                    if context == "system_homework_management":
                        # 系统作业管理菜单：管理所教年级所有班级的注册时指定科目的作业
                        if max_scope not in ['school', 'grade', 'subject', 'school_subject']:
                            max_scope = "grade_subject"
                        if grade_id:
                            grade_ids.add(grade_id)
                        # 使用用户注册时的科目，而不是角色分配的科目
                        if current_user.subject:
                            subject_result = db.execute(
                                text("SELECT id FROM subjects WHERE name = :subject_name"),
                                {"subject_name": current_user.subject}
                            ).fetchone()
                            if subject_result:
                                subject_ids.add(subject_result[0])
                        if school_id:
                            school_ids.add(school_id)
                    elif context == "homework_management":
                        # 作业管理菜单：管理自己任教班级的注册科目的作业
                        if max_scope not in ['school', 'grade', 'subject', 'grade_subject', 'school_subject', 'class_all_subjects']:
                            max_scope = "teacher_subject"
                        teacher_ids.add(current_user.id)
                        # 添加用户的科目
                        if current_user.subject:
                            subject_result = db.execute(
                                text("SELECT id FROM subjects WHERE name = :subject_name"),
                                {"subject_name": current_user.subject}
                            ).fetchone()
                            if subject_result:
                                subject_ids.add(subject_result[0])
                
                elif role_code == 'class_teacher':
                    # 班主任权限处理
                    if context == "system_homework_management":
                        # 系统作业管理菜单：管理该班所有科目作业
                        if max_scope not in ['school', 'grade', 'subject', 'grade_subject', 'school_subject']:
                            max_scope = "class_all_subjects"
                        if class_id:
                            class_ids.add(class_id)
                        if school_id:
                            school_ids.add(school_id)
                    elif context == "homework_management":
                        # 作业管理菜单：管理自己任教班级的注册科目的作业
                        if max_scope not in ['school', 'grade', 'subject', 'grade_subject', 'school_subject', 'class_all_subjects']:
                            max_scope = "teacher_subject"
                        teacher_ids.add(current_user.id)
                        # 添加用户的科目
                        if current_user.subject:
                            subject_result = db.execute(
                                text("SELECT id FROM subjects WHERE name = :subject_name"),
                                {"subject_name": current_user.subject}
                            ).fetchone()
                            if subject_result:
                                subject_ids.add(subject_result[0])
            
            # 如果没有明确的学校ID，使用用户的学校ID
            if not school_ids and current_user.school_id:
                school_ids.add(current_user.school_id)
            
            return {
                "scope": max_scope,
                "school_ids": list(school_ids) if school_ids else None,
                "grade_ids": list(grade_ids) if grade_ids else None,
                "subject_ids": list(subject_ids) if subject_ids else None,
                "class_ids": list(class_ids) if class_ids else None,
                "teacher_ids": list(teacher_ids),
                "description": f"{', '.join(role_descriptions)} - {max_scope}级权限"
            }
            
        except Exception as e:
            logger.error(f"获取用户权限范围失败: {e}")
            # 默认返回最小权限
            return {
                "scope": "teacher",
                "school_ids": [current_user.school_id] if current_user.school_id else [],
                "grade_ids": [],
                "subject_ids": [],
                "class_ids": [],
                "teacher_ids": [current_user.id],
                "description": "默认权限 - 只能查看自己的数据"
            }
    
    @staticmethod
    async def get_accessible_assignment_ids(current_user: User, db: Session, context: str = "homework_management") -> List[int]:
        """
        获取用户可访问的作业任务ID列表
        参数:
            current_user: 当前用户
            db: 数据库会话
            context: 权限上下文 ("homework_management", "system_homework_management")
        """
        permission_scope = await PermissionService.get_user_permission_scope(current_user, db, context)
        
        try:
            if permission_scope["scope"] == "system":
                # 超级管理员：所有作业任务
                result = db.execute(text("SELECT id FROM homework_assignments")).fetchall()
                return [row[0] for row in result]
            
            elif permission_scope["scope"] == "school":
                # 学校级权限：本校所有作业任务
                school_ids = permission_scope["school_ids"]
                if school_ids:
                    placeholders = ','.join([':school_id_' + str(i) for i in range(len(school_ids))])
                    params = {f'school_id_{i}': school_id for i, school_id in enumerate(school_ids)}
                    result = db.execute(
                        text(f"SELECT id FROM homework_assignments WHERE school_id IN ({placeholders})"),
                        params
                    ).fetchall()
                    return [row[0] for row in result]
            
            elif permission_scope["scope"] == "grade":
                # 年级级权限：该年级所有作业任务
                grade_ids = permission_scope["grade_ids"]
                school_ids = permission_scope["school_ids"]
                if grade_ids and school_ids:
                    # 通过班级表关联获取年级的作业任务
                    grade_placeholders = ','.join([':grade_id_' + str(i) for i in range(len(grade_ids))])
                    school_placeholders = ','.join([':school_id_' + str(i) for i in range(len(school_ids))])
                    params = {}
                    params.update({f'grade_id_{i}': grade_id for i, grade_id in enumerate(grade_ids)})
                    params.update({f'school_id_{i}': school_id for i, school_id in enumerate(school_ids)})
                    
                    result = db.execute(
                        text(f"""
                            SELECT DISTINCT ha.id 
                            FROM homework_assignments ha
                            JOIN classes c ON ha.class_id = c.id
                            WHERE c.grade IN ({grade_placeholders}) 
                            AND ha.school_id IN ({school_placeholders})
                        """),
                        params
                    ).fetchall()
                    return [row[0] for row in result]
            
            elif permission_scope["scope"] == "subject":
                # 科目级权限：某个科目的所有作业任务
                subject_ids = permission_scope["subject_ids"]
                school_ids = permission_scope["school_ids"]
                if subject_ids and school_ids:
                    subject_placeholders = ','.join([':subject_id_' + str(i) for i in range(len(subject_ids))])
                    school_placeholders = ','.join([':school_id_' + str(i) for i in range(len(school_ids))])
                    params = {}
                    params.update({f'subject_id_{i}': subject_id for i, subject_id in enumerate(subject_ids)})
                    params.update({f'school_id_{i}': school_id for i, school_id in enumerate(school_ids)})
                    
                    result = db.execute(
                        text(f"""
                            SELECT id FROM homework_assignments 
                            WHERE subject_id IN ({subject_placeholders}) 
                            AND school_id IN ({school_placeholders})
                        """),
                        params
                    ).fetchall()
                    return [row[0] for row in result]
            
            elif permission_scope["scope"] == "grade_subject":
                # 年级+科目权限：所属年级某个科目的数据
                grade_ids = permission_scope["grade_ids"]
                subject_ids = permission_scope["subject_ids"]
                school_ids = permission_scope["school_ids"]
                if grade_ids and subject_ids and school_ids:
                    grade_placeholders = ','.join([':grade_id_' + str(i) for i in range(len(grade_ids))])
                    subject_placeholders = ','.join([':subject_id_' + str(i) for i in range(len(subject_ids))])
                    school_placeholders = ','.join([':school_id_' + str(i) for i in range(len(school_ids))])
                    params = {}
                    params.update({f'grade_id_{i}': grade_id for i, grade_id in enumerate(grade_ids)})
                    params.update({f'subject_id_{i}': subject_id for i, subject_id in enumerate(subject_ids)})
                    params.update({f'school_id_{i}': school_id for i, school_id in enumerate(school_ids)})
                    
                    result = db.execute(
                        text(f"""
                            SELECT DISTINCT ha.id 
                            FROM homework_assignments ha
                            JOIN classes c ON ha.class_id = c.id
                            WHERE c.grade IN ({grade_placeholders}) 
                            AND ha.subject_id IN ({subject_placeholders})
                            AND ha.school_id IN ({school_placeholders})
                        """),
                        params
                    ).fetchall()
                    return [row[0] for row in result]
            
            elif permission_scope["scope"] == "class":
                # 班级权限：所管班级的所有数据
                class_ids = permission_scope["class_ids"]
                if class_ids:
                    placeholders = ','.join([':class_id_' + str(i) for i in range(len(class_ids))])
                    params = {f'class_id_{i}': class_id for i, class_id in enumerate(class_ids)}
                    result = db.execute(
                        text(f"SELECT id FROM homework_assignments WHERE class_id IN ({placeholders})"),
                        params
                    ).fetchall()
                    return [row[0] for row in result]

            elif permission_scope["scope"] == "school_subject":
                # 学校+科目权限：本校某个科目的所有作业任务
                school_ids = permission_scope["school_ids"]
                subject_ids = permission_scope["subject_ids"]
                if school_ids and subject_ids:
                    school_placeholders = ','.join([':school_id_' + str(i) for i in range(len(school_ids))])
                    subject_placeholders = ','.join([':subject_id_' + str(i) for i in range(len(subject_ids))])
                    params = {}
                    params.update({f'school_id_{i}': school_id for i, school_id in enumerate(school_ids)})
                    params.update({f'subject_id_{i}': subject_id for i, subject_id in enumerate(subject_ids)})

                    result = db.execute(
                        text(f"""
                            SELECT id FROM homework_assignments
                            WHERE school_id IN ({school_placeholders})
                            AND subject_id IN ({subject_placeholders})
                        """),
                        params
                    ).fetchall()
                    return [row[0] for row in result]

            elif permission_scope["scope"] == "teacher_subject":
                # 教师+科目权限：自己创建的指定科目的作业任务
                subject_ids = permission_scope["subject_ids"]
                teacher_ids = permission_scope["teacher_ids"]
                if subject_ids and teacher_ids:
                    subject_placeholders = ','.join([':subject_id_' + str(i) for i in range(len(subject_ids))])
                    teacher_placeholders = ','.join([':teacher_id_' + str(i) for i in range(len(teacher_ids))])
                    params = {}
                    params.update({f'subject_id_{i}': subject_id for i, subject_id in enumerate(subject_ids)})
                    params.update({f'teacher_id_{i}': teacher_id for i, teacher_id in enumerate(teacher_ids)})

                    result = db.execute(
                        text(f"""
                            SELECT id FROM homework_assignments
                            WHERE subject_id IN ({subject_placeholders})
                            AND teacher_id IN ({teacher_placeholders})
                        """),
                        params
                    ).fetchall()
                    return [row[0] for row in result]

            elif permission_scope["scope"] == "class_all_subjects":
                # 班级所有科目权限：所管班级的所有科目作业
                class_ids = permission_scope["class_ids"]
                if class_ids:
                    placeholders = ','.join([':class_id_' + str(i) for i in range(len(class_ids))])
                    params = {f'class_id_{i}': class_id for i, class_id in enumerate(class_ids)}
                    result = db.execute(
                        text(f"SELECT id FROM homework_assignments WHERE class_id IN ({placeholders})"),
                        params
                    ).fetchall()
                    return [row[0] for row in result]
            
            # 默认：只能查看自己创建的作业任务
            result = db.execute(
                text("SELECT id FROM homework_assignments WHERE teacher_id = :teacher_id"),
                {"teacher_id": current_user.id}
            ).fetchall()
            return [row[0] for row in result]
            
        except Exception as e:
            logger.error(f"获取可访问作业任务ID失败: {e}")
            # 出错时返回空列表，确保安全
            return []
