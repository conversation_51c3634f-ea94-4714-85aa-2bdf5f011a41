{"ast": null, "code": "/**\n * @import {<PERSON><PERSON>, Nodes, Parents, Root} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {ComponentType, JSX, ReactElement, ReactNode} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList, Processor} from 'unified'\n */\n\n/**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */\n\n/**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */\n\n/**\n * @typedef {{\n *   [Key in keyof JSX.IntrinsicElements]?: ComponentType<JSX.IntrinsicElements[Key] & ExtraProps> | keyof JSX.IntrinsicElements\n * }} Components\n *   Map tag names to components.\n */\n\n/**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */\n\n/**\n * @typedef HooksOptionsOnly\n *   Configuration specifically for {@linkcode MarkdownHooks}.\n * @property {ReactNode | null | undefined} [fallback]\n *   Content to render while the processor processing the markdown (optional).\n */\n\n/**\n * @typedef {Options & HooksOptionsOnly} HooksOptions\n *   Configuration for {@linkcode MarkdownHooks};\n *   extends the regular {@linkcode Options} with a `fallback` prop.\n */\n\n/**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */\n\nimport { unreachable } from 'devlop';\nimport { toJsxRuntime } from 'hast-util-to-jsx-runtime';\nimport { urlAttributes } from 'html-url-attributes';\nimport { Fragment, jsx, jsxs } from 'react/jsx-runtime';\nimport { useEffect, useState } from 'react';\nimport remarkParse from 'remark-parse';\nimport remarkRehype from 'remark-rehype';\nimport { unified } from 'unified';\nimport { visit } from 'unist-util-visit';\nimport { VFile } from 'vfile';\nconst changelog = 'https://github.com/remarkjs/react-markdown/blob/main/changelog.md';\n\n/** @type {PluggableList} */\nconst emptyPlugins = [];\n/** @type {Readonly<RemarkRehypeOptions>} */\nconst emptyRemarkRehypeOptions = {\n  allowDangerousHtml: true\n};\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i;\n\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */\nconst deprecations = [{\n  from: 'astPlugins',\n  id: 'remove-buggy-html-in-markdown-parser'\n}, {\n  from: 'allowDangerousHtml',\n  id: 'remove-buggy-html-in-markdown-parser'\n}, {\n  from: 'allowNode',\n  id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n  to: 'allowElement'\n}, {\n  from: 'allowedTypes',\n  id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n  to: 'allowedElements'\n}, {\n  from: 'className',\n  id: 'remove-classname'\n}, {\n  from: 'disallowedTypes',\n  id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n  to: 'disallowedElements'\n}, {\n  from: 'escapeHtml',\n  id: 'remove-buggy-html-in-markdown-parser'\n}, {\n  from: 'includeElementIndex',\n  id: '#remove-includeelementindex'\n}, {\n  from: 'includeNodeIndex',\n  id: 'change-includenodeindex-to-includeelementindex'\n}, {\n  from: 'linkTarget',\n  id: 'remove-linktarget'\n}, {\n  from: 'plugins',\n  id: 'change-plugins-to-remarkplugins',\n  to: 'remarkPlugins'\n}, {\n  from: 'rawSourcePos',\n  id: '#remove-rawsourcepos'\n}, {\n  from: 'renderers',\n  id: 'change-renderers-to-components',\n  to: 'components'\n}, {\n  from: 'source',\n  id: 'change-source-to-children',\n  to: 'children'\n}, {\n  from: 'sourcePos',\n  id: '#remove-sourcepos'\n}, {\n  from: 'transformImageUri',\n  id: '#add-urltransform',\n  to: 'urlTransform'\n}, {\n  from: 'transformLinkUri',\n  id: '#add-urltransform',\n  to: 'urlTransform'\n}];\n\n/**\n * Component to render markdown.\n *\n * This is a synchronous component.\n * When using async plugins,\n * see {@linkcode MarkdownAsync} or {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nexport function Markdown(options) {\n  const processor = createProcessor(options);\n  const file = createFile(options);\n  return post(processor.runSync(processor.parse(file), file), options);\n}\n\n/**\n * Component to render markdown with support for async plugins\n * through async/await.\n *\n * Components returning promises are supported on the server.\n * For async support on the client,\n * see {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Promise<ReactElement>}\n *   Promise to a React element.\n */\nexport async function MarkdownAsync(options) {\n  const processor = createProcessor(options);\n  const file = createFile(options);\n  const tree = await processor.run(processor.parse(file), file);\n  return post(tree, options);\n}\n\n/**\n * Component to render markdown with support for async plugins through hooks.\n *\n * This uses `useEffect` and `useState` hooks.\n * Hooks run on the client and do not immediately render something.\n * For async support on the server,\n * see {@linkcode MarkdownAsync}.\n *\n * @param {Readonly<HooksOptions>} options\n *   Props.\n * @returns {ReactNode}\n *   React node.\n */\nexport function MarkdownHooks(options) {\n  const processor = createProcessor(options);\n  const [error, setError] = useState(/** @type {Error | undefined} */undefined);\n  const [tree, setTree] = useState(/** @type {Root | undefined} */undefined);\n  useEffect(function () {\n    let cancelled = false;\n    const file = createFile(options);\n    processor.run(processor.parse(file), file, function (error, tree) {\n      if (!cancelled) {\n        setError(error);\n        setTree(tree);\n      }\n    });\n\n    /**\n     * @returns {undefined}\n     *   Nothing.\n     */\n    return function () {\n      cancelled = true;\n    };\n  }, [options.children, options.rehypePlugins, options.remarkPlugins, options.remarkRehypeOptions]);\n  if (error) throw error;\n  return tree ? post(tree, options) : options.fallback;\n}\n\n/**\n * Set up the `unified` processor.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Processor<MdastRoot, MdastRoot, Root, undefined, undefined>}\n *   Result.\n */\nfunction createProcessor(options) {\n  const rehypePlugins = options.rehypePlugins || emptyPlugins;\n  const remarkPlugins = options.remarkPlugins || emptyPlugins;\n  const remarkRehypeOptions = options.remarkRehypeOptions ? {\n    ...options.remarkRehypeOptions,\n    ...emptyRemarkRehypeOptions\n  } : emptyRemarkRehypeOptions;\n  const processor = unified().use(remarkParse).use(remarkPlugins).use(remarkRehype, remarkRehypeOptions).use(rehypePlugins);\n  return processor;\n}\n\n/**\n * Set up the virtual file.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {VFile}\n *   Result.\n */\nfunction createFile(options) {\n  const children = options.children || '';\n  const file = new VFile();\n  if (typeof children === 'string') {\n    file.value = children;\n  } else {\n    unreachable('Unexpected value `' + children + '` for `children` prop, expected `string`');\n  }\n  return file;\n}\n\n/**\n * Process the result from unified some more.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction post(tree, options) {\n  const allowedElements = options.allowedElements;\n  const allowElement = options.allowElement;\n  const components = options.components;\n  const disallowedElements = options.disallowedElements;\n  const skipHtml = options.skipHtml;\n  const unwrapDisallowed = options.unwrapDisallowed;\n  const urlTransform = options.urlTransform || defaultUrlTransform;\n  for (const deprecation of deprecations) {\n    if (Object.hasOwn(options, deprecation.from)) {\n      unreachable('Unexpected `' + deprecation.from + '` prop, ' + (deprecation.to ? 'use `' + deprecation.to + '` instead' : 'remove it') + ' (see <' + changelog + '#' + deprecation.id + '> for more info)');\n    }\n  }\n  if (allowedElements && disallowedElements) {\n    unreachable('Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other');\n  }\n  visit(tree, transform);\n  return toJsxRuntime(tree, {\n    Fragment,\n    components,\n    ignoreInvalidStyle: true,\n    jsx,\n    jsxs,\n    passKeys: true,\n    passNode: true\n  });\n\n  /** @type {BuildVisitor<Root>} */\n  function transform(node, index, parent) {\n    if (node.type === 'raw' && parent && typeof index === 'number') {\n      if (skipHtml) {\n        parent.children.splice(index, 1);\n      } else {\n        parent.children[index] = {\n          type: 'text',\n          value: node.value\n        };\n      }\n      return index;\n    }\n    if (node.type === 'element') {\n      /** @type {string} */\n      let key;\n      for (key in urlAttributes) {\n        if (Object.hasOwn(urlAttributes, key) && Object.hasOwn(node.properties, key)) {\n          const value = node.properties[key];\n          const test = urlAttributes[key];\n          if (test === null || test.includes(node.tagName)) {\n            node.properties[key] = urlTransform(String(value || ''), key, node);\n          }\n        }\n      }\n    }\n    if (node.type === 'element') {\n      let remove = allowedElements ? !allowedElements.includes(node.tagName) : disallowedElements ? disallowedElements.includes(node.tagName) : false;\n      if (!remove && allowElement && typeof index === 'number') {\n        remove = !allowElement(node, index, parent);\n      }\n      if (remove && parent && typeof index === 'number') {\n        if (unwrapDisallowed && node.children) {\n          parent.children.splice(index, 1, ...node.children);\n        } else {\n          parent.children.splice(index, 1);\n        }\n        return index;\n      }\n    }\n  }\n}\n\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */\nexport function defaultUrlTransform(value) {\n  // Same as:\n  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n  // But without the `encode` part.\n  const colon = value.indexOf(':');\n  const questionMark = value.indexOf('?');\n  const numberSign = value.indexOf('#');\n  const slash = value.indexOf('/');\n  if (\n  // If there is no protocol, it’s relative.\n  colon === -1 ||\n  // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n  slash !== -1 && colon > slash || questionMark !== -1 && colon > questionMark || numberSign !== -1 && colon > numberSign ||\n  // It is a protocol, it should be allowed.\n  safeProtocol.test(value.slice(0, colon))) {\n    return value;\n  }\n  return '';\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}