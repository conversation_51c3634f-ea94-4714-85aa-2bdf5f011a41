{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { DEPRECATED_TOKENS, prepareComponentToken } from '.';\nimport { genStyleHooks } from '../../theme/internal';\nconst genSiderStyle = token => {\n  const {\n    componentCls,\n    siderBg,\n    motionDurationMid,\n    motionDurationSlow,\n    antCls,\n    triggerHeight,\n    triggerColor,\n    triggerBg,\n    headerHeight,\n    zeroTriggerWidth,\n    zeroTriggerHeight,\n    borderRadiusLG,\n    lightSiderBg,\n    lightTriggerColor,\n    lightTriggerBg,\n    bodyBg\n  } = token;\n  return {\n    [componentCls]: {\n      position: 'relative',\n      // fix firefox can't set width smaller than content on flex item\n      minWidth: 0,\n      background: siderBg,\n      transition: `all ${motionDurationMid}, background 0s`,\n      '&-has-trigger': {\n        paddingBottom: triggerHeight\n      },\n      '&-right': {\n        order: 1\n      },\n      [`${componentCls}-children`]: {\n        height: '100%',\n        // Hack for fixing margin collapse bug\n        // https://github.com/ant-design/ant-design/issues/7967\n        // solution from https://stackoverflow.com/a/33132624/3040605\n        marginTop: -0.1,\n        paddingTop: 0.1,\n        [`${antCls}-menu${antCls}-menu-inline-collapsed`]: {\n          width: 'auto'\n        }\n      },\n      [`&-zero-width ${componentCls}-children`]: {\n        overflow: 'hidden'\n      },\n      [`${componentCls}-trigger`]: {\n        position: 'fixed',\n        bottom: 0,\n        zIndex: 1,\n        height: triggerHeight,\n        color: triggerColor,\n        lineHeight: unit(triggerHeight),\n        textAlign: 'center',\n        background: triggerBg,\n        cursor: 'pointer',\n        transition: `all ${motionDurationMid}`\n      },\n      [`${componentCls}-zero-width-trigger`]: {\n        position: 'absolute',\n        top: headerHeight,\n        insetInlineEnd: token.calc(zeroTriggerWidth).mul(-1).equal(),\n        zIndex: 1,\n        width: zeroTriggerWidth,\n        height: zeroTriggerHeight,\n        color: triggerColor,\n        fontSize: token.fontSizeXL,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        background: siderBg,\n        borderRadius: `0 ${unit(borderRadiusLG)} ${unit(borderRadiusLG)} 0`,\n        cursor: 'pointer',\n        transition: `background ${motionDurationSlow} ease`,\n        '&::after': {\n          position: 'absolute',\n          inset: 0,\n          background: 'transparent',\n          transition: `all ${motionDurationSlow}`,\n          content: '\"\"'\n        },\n        '&:hover::after': {\n          background: `rgba(255, 255, 255, 0.2)`\n        },\n        '&-right': {\n          insetInlineStart: token.calc(zeroTriggerWidth).mul(-1).equal(),\n          borderRadius: `${unit(borderRadiusLG)} 0 0 ${unit(borderRadiusLG)}`\n        }\n      },\n      // Light\n      '&-light': {\n        background: lightSiderBg,\n        [`${componentCls}-trigger`]: {\n          color: lightTriggerColor,\n          background: lightTriggerBg\n        },\n        [`${componentCls}-zero-width-trigger`]: {\n          color: lightTriggerColor,\n          background: lightTriggerBg,\n          border: `1px solid ${bodyBg}`,\n          // Safe to modify to any other color\n          borderInlineStart: 0\n        }\n      }\n    }\n  };\n};\nexport default genStyleHooks(['Layout', 'Sider'], token => [genSiderStyle(token)], prepareComponentToken, {\n  deprecatedTokens: DEPRECATED_TOKENS\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}