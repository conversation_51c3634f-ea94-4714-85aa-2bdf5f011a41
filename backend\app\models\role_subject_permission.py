from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Table
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

# 角色-科目权限关联表
class RoleSubjectPermission(Base):
    """角色-科目权限关联表"""
    __tablename__ = "role_subject_permissions"
    
    id = Column(Integer, primary_key=True, index=True)
    role_id = Column(Integer, ForeignKey("roles.id", ondelete="CASCADE"), nullable=False)
    subject_id = Column(Integer, ForeignKey("subjects.id", ondelete="CASCADE"), nullable=False)
    can_view = Column(Boolean, default=True)  # 是否可以查看
    can_edit = Column(Boolean, default=False)  # 是否可以编辑
    can_delete = Column(Boolean, default=False)  # 是否可以删除
    can_assign = Column(Boolean, default=False)  # 是否可以分配给其他用户
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联的角色
    role = relationship("Role", back_populates="subject_permissions")
    
    # 关联的科目
    subject = relationship("Subject", back_populates="role_permissions")
    
    def __repr__(self):
        return f"<RoleSubjectPermission(role_id={self.role_id}, subject_id={self.subject_id})>" 