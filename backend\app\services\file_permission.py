#!/usr/bin/env python3
"""
文件权限控制服务
"""

from typing import Optional, List
from sqlalchemy.orm import Session
from ..models.user import User
from ..models.file_metadata import FileMetadata, FileType

class FilePermissionService:
    """文件权限控制服务"""
    
    @staticmethod
    def can_access_file(user: User, file_metadata: FileMetadata, action: str = "view") -> bool:
        """
        检查用户是否可以访问文件
        
        Args:
            user: 用户对象
            file_metadata: 文件元数据
            action: 操作类型 (view, download, delete, edit)
            
        Returns:
            是否有权限
        """
        # 超级管理员有所有权限
        if user.is_superuser:
            return True
        
        # 系统管理员有大部分权限
        if user.is_admin and action in ["view", "download"]:
            return True
        
        # 文件创建者有所有权限
        if file_metadata.created_by == user.id:
            return True
        
        # 根据文件类型和用户角色判断权限
        if file_metadata.file_type == FileType.HOMEWORK:
            return FilePermissionService._can_access_homework_file(user, file_metadata, action)
        elif file_metadata.file_type == FileType.ASSIGNMENT:
            return FilePermissionService._can_access_assignment_file(user, file_metadata, action)
        elif file_metadata.file_type == FileType.ANNOTATION:
            return FilePermissionService._can_access_annotation_file(user, file_metadata, action)
        elif file_metadata.file_type == FileType.PROFILE:
            return FilePermissionService._can_access_profile_file(user, file_metadata, action)
        
        return False
    
    @staticmethod
    def _can_access_homework_file(user: User, file_metadata: FileMetadata, action: str) -> bool:
        """检查作业文件访问权限"""
        # 学生只能访问自己的作业
        if not user.is_teacher:
            return file_metadata.user_id == user.id and action in ["view", "download"]
        
        # 教师可以访问自己班级和科目的作业
        if user.is_teacher:
            # 检查是否是同一学校
            if file_metadata.school_id != user.school_id:
                return False
            
            # 检查是否教授该班级该科目
            # 这里需要根据实际的教师-班级-科目关系表来判断
            # 暂时简化为同学校的教师可以访问
            return action in ["view", "download", "edit"]
        
        return False
    
    @staticmethod
    def _can_access_assignment_file(user: User, file_metadata: FileMetadata, action: str) -> bool:
        """检查作业模板文件访问权限"""
        # 学生可以查看和下载作业模板
        if not user.is_teacher:
            return (file_metadata.school_id == user.school_id and 
                   action in ["view", "download"])
        
        # 教师可以管理作业模板
        if user.is_teacher:
            return file_metadata.school_id == user.school_id
        
        return False
    
    @staticmethod
    def _can_access_annotation_file(user: User, file_metadata: FileMetadata, action: str) -> bool:
        """检查批注文件访问权限"""
        # 批注文件的权限与对应的作业文件相同
        return FilePermissionService._can_access_homework_file(user, file_metadata, action)
    
    @staticmethod
    def _can_access_profile_file(user: User, file_metadata: FileMetadata, action: str) -> bool:
        """检查头像文件访问权限"""
        # 用户只能访问自己的头像
        if file_metadata.user_id == user.id:
            return True
        
        # 教师可以查看学生头像
        if user.is_teacher and not file_metadata.user.is_teacher:
            return (file_metadata.school_id == user.school_id and 
                   action in ["view", "download"])
        
        return False
    
    @staticmethod
    def filter_accessible_files(user: User, files: List[FileMetadata], action: str = "view") -> List[FileMetadata]:
        """
        过滤用户可访问的文件列表
        
        Args:
            user: 用户对象
            files: 文件列表
            action: 操作类型
            
        Returns:
            过滤后的文件列表
        """
        return [
            file_meta for file_meta in files
            if FilePermissionService.can_access_file(user, file_meta, action)
        ]
    
    @staticmethod
    def get_user_accessible_files(db: Session, user: User, 
                                 file_type: Optional[FileType] = None,
                                 school_id: Optional[int] = None,
                                 class_id: Optional[int] = None) -> List[FileMetadata]:
        """
        获取用户可访问的文件列表
        
        Args:
            db: 数据库会话
            user: 用户对象
            file_type: 文件类型过滤
            school_id: 学校ID过滤
            class_id: 班级ID过滤
            
        Returns:
            文件列表
        """
        query = db.query(FileMetadata).filter(FileMetadata.is_active == 1)
        
        # 根据用户角色添加基础过滤条件
        if not user.is_superuser:
            if user.is_admin:
                # 管理员可以看到所有文件
                pass
            elif user.is_teacher:
                # 教师只能看到自己学校的文件
                query = query.filter(FileMetadata.school_id == user.school_id)
            else:
                # 学生只能看到自己的文件和公共文件
                query = query.filter(
                    (FileMetadata.user_id == user.id) |
                    (FileMetadata.file_type == FileType.ASSIGNMENT)
                )
        
        # 添加其他过滤条件
        if file_type:
            query = query.filter(FileMetadata.file_type == file_type)
        if school_id:
            query = query.filter(FileMetadata.school_id == school_id)
        if class_id:
            query = query.filter(FileMetadata.class_id == class_id)
        
        files = query.all()
        
        # 进一步权限过滤
        return FilePermissionService.filter_accessible_files(user, files)
