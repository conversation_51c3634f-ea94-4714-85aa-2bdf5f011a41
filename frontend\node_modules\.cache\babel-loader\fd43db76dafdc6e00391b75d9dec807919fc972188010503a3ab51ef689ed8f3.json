{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\correcthomework4\\\\frontend\\\\src\\\\pages\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Breadcrumb, Table, Button, Modal, Form, Input, Select, Switch, message, Card, Tabs, Typography, Spin, Row, Col, Divider, Collapse, Tag, Tooltip, Alert, Space, Checkbox, Dropdown, Avatar, Drawer, Statistic } from 'antd';\nimport { UserOutlined, SettingOutlined, DashboardOutlined, PlusOutlined, EditOutlined, DeleteOutlined, BookOutlined, TeamOutlined, BookFilled, BankOutlined, LogoutOutlined, MessageOutlined, BarChartOutlined, UserSwitchOutlined, ExclamationCircleOutlined, InfoCircleOutlined, AuditOutlined, SafetyCertificateOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation, Link } from 'react-router-dom';\nimport { getUsers, getAiConfigs, createAiConfig, updateAiConfig, deleteAiConfig } from '../utils/api';\nimport api from '../utils/api';\nimport SubjectManagement from '../components/SubjectManagement';\nimport UserManagement from '../components/UserManagement';\nimport AIAssistant from '../components/AIAssistant';\nimport RegistrationApproval from '../components/RegistrationApproval';\nimport SchoolApplicationReview from '../components/SchoolApplicationReview';\nimport SubjectPermissionManager from '../components/SubjectPermissionManager';\nimport ParentFeatureManager from '../components/ParentFeatureManager';\nimport { Fragment as _Fragment, jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Content,\n  Sider\n} = Layout;\nconst {\n  Title\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Option\n} = Select;\nconst AdminDashboard = ({\n  user,\n  onLogout\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [activeTab, setActiveTab] = useState('2');\n  const [users, setUsers] = useState([]);\n  const [aiConfigs, setAiConfigs] = useState([]);\n  const [systemInfo, setSystemInfo] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [userModalVisible, setUserModalVisible] = useState(false);\n  const [aiConfigModalVisible, setAiConfigModalVisible] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [editingAiConfig, setEditingAiConfig] = useState(null);\n  const [form] = Form.useForm();\n  const [aiConfigForm] = Form.useForm();\n  // 新增统计信息状态\n  const [teacherStats, setTeacherStats] = useState(null);\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n\n  // 监听窗口大小变化\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  const [studentStats, setStudentStats] = useState(null);\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState(null);\n  const [aiDrawerVisible, setAiDrawerVisible] = useState(false);\n  // 添加注册设置状态\n  const [registrationSettings, setRegistrationSettings] = useState({\n    global_registration_enabled: true,\n    allow_student_registration: true,\n    allow_teacher_registration: true\n  });\n  const [advancedSettings, setAdvancedSettings] = useState(null);\n  const [settingsLoading, setSettingsLoading] = useState(false);\n\n  // 获取系统信息\n  const fetchSystemInfo = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await api.get('/admin/system-info');\n      setSystemInfo(response.data);\n    } catch (error) {\n      console.error('获取系统信息失败:', error);\n      message.error('获取系统信息失败');\n    }\n  };\n\n  // 获取所有用户\n  const fetchUsers = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await api.get('/admin/users');\n      setUsers(response.data);\n    } catch (error) {\n      console.error('获取用户列表失败:', error);\n      message.error('获取用户列表失败');\n    }\n  };\n\n  // 获取所有AI配置\n  const fetchAiConfigs = async () => {\n    try {\n      setLoading(true);\n      const data = await getAiConfigs();\n      console.log('获取到的AI配置:', data);\n      setAiConfigs(data);\n    } catch (error) {\n      console.error('获取AI配置失败:', error);\n      message.error('获取AI配置失败: ' + (error.detail || error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取统计信息\n  const fetchStatistics = async () => {\n    setStatsLoading(true);\n    setStatsError(null);\n    try {\n      // 获取token\n      const token = localStorage.getItem('token');\n\n      // 发起API请求\n      console.log('开始获取统计数据...');\n\n      // 使用api实例请求以便于调试\n      const teacherResponse = await api.get('/statistics/teacher');\n      const studentResponse = await api.get('/statistics/student');\n      console.log('获取到的教师统计数据:', JSON.stringify(teacherResponse, null, 2));\n      console.log('获取到的学生统计数据:', JSON.stringify(studentResponse, null, 2));\n\n      // 检查数据字段\n      if (!teacherResponse.hasOwnProperty('homework_count')) {\n        console.warn('教师统计数据缺少homework_count字段');\n      }\n      if (!teacherResponse.hasOwnProperty('corrected_count')) {\n        console.warn('教师统计数据缺少corrected_count字段');\n      }\n\n      // 获取学校ID为1的班级数量\n      try {\n        const schoolClassesResponse = await api.get('/admin/schools/1/classes');\n        if (Array.isArray(schoolClassesResponse)) {\n          // 使用学校ID为1的实际班级数量覆盖统计数据中的班级数量\n          teacherResponse.class_count = schoolClassesResponse.length;\n          console.log(`使用学校ID为1的实际班级数量: ${teacherResponse.class_count}`);\n        }\n      } catch (classError) {\n        console.error('获取学校班级数量失败:', classError);\n\n        // 如果API失败，尝试直接查询数据库\n        try {\n          const dbResponse = await api.get(`/admin/query`, {\n            params: {\n              query: `SELECT COUNT(*) FROM classes WHERE school_id = 1 /* ${new Date().getTime()} */`\n            }\n          });\n          if (dbResponse.data && dbResponse.data.result && dbResponse.data.result[0]) {\n            teacherResponse.data.class_count = parseInt(dbResponse.data.result[0][0]);\n            console.log(`使用数据库查询获取到的学校ID为1的班级数量: ${teacherResponse.data.class_count}`);\n          }\n        } catch (dbError) {\n          console.error('数据库查询班级数量失败:', dbError);\n        }\n      }\n\n      // 设置状态\n      setTeacherStats(teacherResponse);\n      setStudentStats(studentResponse);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('获取统计数据失败:', error);\n      setStatsError('获取统计数据失败: ' + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message || '未知错误'));\n\n      // 设置空数据，避免前端崩溃\n      setTeacherStats({\n        class_count: 0,\n        student_count: 0,\n        homework_count: 0,\n        corrected_count: 0\n      });\n      setStudentStats({\n        total_homework_count: 0,\n        completed_homework_count: 0,\n        average_accuracy: 0,\n        wrong_question_count: 0\n      });\n    } finally {\n      setStatsLoading(false);\n    }\n  };\n\n  // 获取注册设置\n  const fetchRegistrationSettings = async () => {\n    try {\n      setSettingsLoading(true);\n      const token = localStorage.getItem('token');\n\n      // 获取简单注册设置\n      const simpleResponse = await api.get('/admin/system-settings/registration');\n      console.log('获取简单注册设置成功:', simpleResponse);\n      setRegistrationSettings(simpleResponse);\n\n      // 获取高级注册设置\n      const advancedResponse = await api.get('/admin/system-settings/advanced-registration');\n      console.log('获取高级注册设置成功:', advancedResponse);\n      setAdvancedSettings(advancedResponse);\n    } catch (error) {\n      console.error('获取注册设置失败:', error);\n      message.error('获取注册设置失败');\n    } finally {\n      setSettingsLoading(false);\n    }\n  };\n\n  // 更新总注册开关\n  const updateGlobalRegistrationSwitch = async enabled => {\n    try {\n      setSettingsLoading(true);\n      const token = localStorage.getItem('token');\n      console.log('更新总注册开关 - 开始');\n      console.log('Token存在:', !!token);\n      console.log('Token长度:', token === null || token === void 0 ? void 0 : token.length);\n      console.log('启用状态:', enabled);\n      console.log('请求URL:', '/api/admin/system-settings/global-registration-switch');\n      console.log('请求方法:', 'PUT');\n      console.log('请求数据:', {\n        enabled\n      });\n      const response = await api.put('/admin/system-settings/global-registration-switch', {\n        enabled\n      });\n      console.log('更新成功:', response.data);\n\n      // 更新本地状态\n      setRegistrationSettings(prev => ({\n        ...prev,\n        global_registration_enabled: enabled\n      }));\n      if (advancedSettings) {\n        setAdvancedSettings(prev => ({\n          ...prev,\n          global_registration_enabled: enabled\n        }));\n      }\n      message.success(`总注册开关已${enabled ? '开启' : '关闭'}`);\n    } catch (error) {\n      var _error$response2, _error$response3, _error$response4, _error$response5, _error$response6, _error$response6$data;\n      console.error('更新总注册开关失败:', error);\n      console.error('错误详情:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data);\n      console.error('状态码:', (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status);\n      let errorMessage = '更新总注册开关失败';\n      if (((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status) === 401) {\n        errorMessage = '权限不足，请重新登录';\n      } else if (((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status) === 403) {\n        errorMessage = '需要管理员权限';\n      } else if ((_error$response6 = error.response) !== null && _error$response6 !== void 0 && (_error$response6$data = _error$response6.data) !== null && _error$response6$data !== void 0 && _error$response6$data.detail) {\n        errorMessage = error.response.data.detail;\n      }\n      message.error(errorMessage);\n    } finally {\n      setSettingsLoading(false);\n    }\n  };\n\n  // 更新简单注册设置\n  const updateRegistrationSettings = async (key, value) => {\n    try {\n      setSettingsLoading(true);\n      const token = localStorage.getItem('token');\n      const updatedSettings = {\n        ...registrationSettings,\n        [key]: value\n      };\n      await api.put('/admin/system-settings/registration', updatedSettings);\n      console.log('更新注册设置成功:', updatedSettings);\n      setRegistrationSettings(updatedSettings);\n\n      // 同步更新高级设置中的对应角色\n      if (advancedSettings) {\n        const updatedAdvanced = {\n          ...advancedSettings\n        };\n        if (key === 'allow_student_registration') {\n          updatedAdvanced.roles.student.enabled = value;\n        } else if (key === 'allow_teacher_registration') {\n          updatedAdvanced.roles.teacher.enabled = value;\n        }\n        setAdvancedSettings(updatedAdvanced);\n      }\n      message.success(`${key === 'allow_student_registration' ? '学生' : '教师'}注册功能已${value ? '开启' : '关闭'}`);\n    } catch (error) {\n      console.error('更新注册设置失败:', error);\n      message.error('更新注册设置失败');\n    } finally {\n      setSettingsLoading(false);\n    }\n  };\n\n  // 更新角色配置\n  const updateRoleConfig = async (roleName, field, value) => {\n    try {\n      setSettingsLoading(true);\n      const token = localStorage.getItem('token');\n\n      // 创建更新对象\n      const updateData = {\n        [field]: value\n      };\n\n      // 发送更新请求\n      await api.put(`/admin/system-settings/role-config/${roleName}`, updateData);\n\n      // 更新本地状态\n      const updatedAdvanced = {\n        ...advancedSettings\n      };\n      updatedAdvanced.roles[roleName][field] = value;\n      setAdvancedSettings(updatedAdvanced);\n\n      // 如果是学生或教师的enabled字段，同时更新简单设置\n      if (field === 'enabled') {\n        if (roleName === 'student') {\n          setRegistrationSettings(prev => ({\n            ...prev,\n            allow_student_registration: value\n          }));\n        } else if (roleName === 'teacher') {\n          setRegistrationSettings(prev => ({\n            ...prev,\n            allow_teacher_registration: value\n          }));\n        }\n      }\n      message.success(`${getRoleDisplayName(roleName)}的${getFieldDisplayName(field)}已${getValueDisplayText(field, value)}`);\n    } catch (error) {\n      console.error(`更新${roleName}配置失败:`, error);\n      message.error(`更新${getRoleDisplayName(roleName)}配置失败`);\n    } finally {\n      setSettingsLoading(false);\n    }\n  };\n\n  // 获取角色显示名称\n  const getRoleDisplayName = roleName => {\n    const roleNameMap = {\n      'student': '学生',\n      'teacher': '教师',\n      'parent': '家长',\n      'class_teacher': '班主任',\n      'subject_leader': '备课组长',\n      'academic_director': '教务主任',\n      'vice_principal': '副校长',\n      'principal': '校长',\n      'school_admin': '学校管理员'\n    };\n    return roleNameMap[roleName] || roleName;\n  };\n\n  // 获取字段显示名称\n  const getFieldDisplayName = field => {\n    const fieldNameMap = {\n      'enabled': '注册功能',\n      'requires_approval': '审核功能',\n      'approval_level': '审核级别'\n    };\n    return fieldNameMap[field] || field;\n  };\n\n  // 获取值的显示文本\n  const getValueDisplayText = (field, value) => {\n    if (field === 'enabled' || field === 'requires_approval') {\n      return value ? '开启' : '关闭';\n    }\n    if (field === 'approval_level') {\n      const approvalLevelMap = {\n        'school_admin': '学校管理员',\n        'principal': '校长',\n        'academic_director': '教务主任',\n        'super_admin': '超级管理员'\n      };\n      return approvalLevelMap[value] || value;\n    }\n    return value;\n  };\n\n  // 更新学校创建设置\n  const updateSchoolCreationSetting = async value => {\n    try {\n      setSettingsLoading(true);\n      const token = localStorage.getItem('token');\n\n      // 创建更新对象，包含完整的高级设置\n      const updatedSettings = {\n        ...advancedSettings,\n        allow_school_creation: value\n      };\n\n      // 发送更新请求\n      await api.put('/admin/system-settings/advanced-registration', updatedSettings);\n      message.success(`学校创建功能已${value ? '开启' : '关闭'}`);\n    } catch (error) {\n      console.error('更新学校创建设置失败:', error);\n      message.error('更新学校创建设置失败');\n      // 恢复原来的状态\n      setAdvancedSettings(prevSettings => ({\n        ...prevSettings\n      }));\n    } finally {\n      setSettingsLoading(false);\n    }\n  };\n\n  // 初始加载数据\n  useEffect(() => {\n    const fetchData = async () => {\n      setLoading(true);\n      try {\n        await Promise.all([fetchSystemInfo(), fetchUsers(), fetchAiConfigs(), fetchStatistics()]);\n      } catch (error) {\n        console.error('加载数据失败:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n\n    // 检查URL中的tab参数\n    const urlParams = new URLSearchParams(location.search);\n    const tabParam = urlParams.get('tab');\n    if (tabParam) {\n      setActiveTab(tabParam);\n    }\n\n    // 添加获取注册设置\n    fetchRegistrationSettings();\n  }, [location]);\n\n  // 处理用户表单提交\n  const handleUserFormSubmit = async values => {\n    try {\n      const token = localStorage.getItem('token');\n      if (editingUser) {\n        // 更新用户\n        await api.put(`/admin/users/${editingUser.id}`, values);\n        message.success('用户更新成功');\n      } else {\n        // 创建用户\n        await api.post('/register', values);\n        message.success('用户创建成功');\n      }\n      setUserModalVisible(false);\n      fetchUsers();\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      console.error('操作失败:', error);\n      message.error('操作失败: ' + (((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || error.message));\n    }\n  };\n\n  // 处理AI配置表单提交\n  const handleAiConfigFormSubmit = async values => {\n    try {\n      if (editingAiConfig) {\n        // 更新AI配置\n        await updateAiConfig(editingAiConfig.id, values);\n        message.success('AI配置更新成功');\n      } else {\n        // 创建AI配置\n        await createAiConfig(values);\n        message.success('AI配置创建成功');\n      }\n      setAiConfigModalVisible(false);\n      fetchAiConfigs();\n    } catch (error) {\n      console.error('操作失败:', error);\n      message.error('操作失败: ' + (error.detail || error.message || '未知错误'));\n    }\n  };\n\n  // 删除用户\n  const handleDeleteUser = async userId => {\n    try {\n      const token = localStorage.getItem('token');\n      await api.delete(`/admin/users/${userId}`);\n      message.success('用户删除成功');\n      fetchUsers();\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      console.error('删除用户失败:', error);\n      message.error('删除用户失败: ' + (((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.detail) || error.message));\n    }\n  };\n\n  // 删除AI配置\n  const handleDeleteAiConfig = async configId => {\n    try {\n      await deleteAiConfig(configId);\n      message.success('AI配置删除成功');\n      fetchAiConfigs();\n    } catch (error) {\n      console.error('删除AI配置失败:', error);\n      message.error('删除AI配置失败: ' + (error.detail || error.message || '未知错误'));\n    }\n  };\n\n  // 编辑用户\n  const handleEditUser = user => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      username: user.username,\n      email: user.email,\n      full_name: user.full_name,\n      phone: user.phone,\n      is_teacher: user.is_teacher,\n      is_admin: user.is_admin,\n      password: '',\n      // 不回显密码\n      school_id: user.school_id\n    });\n    setUserModalVisible(true);\n  };\n\n  // 编辑AI配置\n  const handleEditAiConfig = config => {\n    setEditingAiConfig(config);\n    aiConfigForm.setFieldsValue({\n      model_name: config.model_name,\n      model_id: config.model_id,\n      provider: config.provider,\n      api_key: config.api_key,\n      api_endpoint: config.api_endpoint,\n      is_active: config.is_active\n    });\n    setAiConfigModalVisible(true);\n  };\n\n  // 添加新用户\n  const handleAddUser = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setUserModalVisible(true);\n  };\n\n  // 添加新AI配置\n  const handleAddAiConfig = () => {\n    setEditingAiConfig(null);\n    aiConfigForm.resetFields();\n    setAiConfigModalVisible(true);\n  };\n\n  // 用户表格列定义\n  const userColumns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id'\n  }, {\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username'\n  }, {\n    title: '邮箱',\n    dataIndex: 'email',\n    key: 'email'\n  }, {\n    title: '姓名',\n    dataIndex: 'full_name',\n    key: 'full_name'\n  }, {\n    title: '电话',\n    dataIndex: 'phone',\n    key: 'phone'\n  }, {\n    title: '学校',\n    dataIndex: 'school_name',\n    key: 'school_name',\n    render: text => text || '未分配'\n  }, {\n    title: '角色',\n    key: 'role',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: record.is_admin ? '管理员' : record.is_teacher ? '教师' : '学生'\n    }, void 0, false)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 19\n        }, this),\n        style: {\n          marginRight: 8\n        },\n        onClick: () => handleEditUser(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 19\n        }, this),\n        onClick: () => {\n          Modal.confirm({\n            title: '确认删除',\n            content: `确定要删除用户 \"${record.username}\" 吗？`,\n            okText: '确定',\n            cancelText: '取消',\n            onOk: () => handleDeleteUser(record.id)\n          });\n        },\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)\n  }];\n\n  // AI配置表格列定义\n  const aiConfigColumns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id'\n  }, {\n    title: '模型名称',\n    dataIndex: 'model_name',\n    key: 'model_name'\n  }, {\n    title: '模型ID',\n    dataIndex: 'model_id',\n    key: 'model_id'\n  }, {\n    title: '提供商',\n    dataIndex: 'provider',\n    key: 'provider'\n  }, {\n    title: 'API端点',\n    dataIndex: 'api_endpoint',\n    key: 'api_endpoint',\n    ellipsis: true\n  }, {\n    title: '状态',\n    dataIndex: 'is_active',\n    key: 'is_active',\n    render: is_active => is_active ? '激活' : '禁用'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 19\n        }, this),\n        style: {\n          marginRight: 8\n        },\n        onClick: () => handleEditAiConfig(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 19\n        }, this),\n        onClick: () => {\n          Modal.confirm({\n            title: '确认删除',\n            content: `确定要删除AI配置 \"${record.model_name}\" 吗？`,\n            okText: '确定',\n            cancelText: '取消',\n            onOk: () => handleDeleteAiConfig(record.id)\n          });\n        },\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)\n  }];\n\n  // 导航到学校管理页面\n  const navigateToSchoolManagement = () => {\n    navigate('/schools');\n  };\n\n  // 渲染统计面板\n  const renderStatsDashboard = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-dashboard\",\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(Title, {\n            level: 3,\n            children: \"\\u7CFB\\u7EDF\\u6982\\u89C8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 9\n      }, this), statsError && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u83B7\\u53D6\\u7EDF\\u8BA1\\u6570\\u636E\\u5931\\u8D25\",\n        description: statsError,\n        type: \"error\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Spin, {\n        spinning: statsLoading,\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 8,\n            lg: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u73ED\\u7EA7\\u603B\\u6570\",\n                value: (teacherStats === null || teacherStats === void 0 ? void 0 : teacherStats.class_count) || 0,\n                prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 8,\n            lg: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5B66\\u751F\\u603B\\u6570\",\n                value: (teacherStats === null || teacherStats === void 0 ? void 0 : teacherStats.student_count) || 0,\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 8,\n            lg: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u4F5C\\u4E1A\\u603B\\u6570\",\n                value: (teacherStats === null || teacherStats === void 0 ? void 0 : teacherStats.homework_count) || 0,\n                prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 8,\n            lg: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5DF2\\u6279\\u6539\\u4F5C\\u4E1A\\u6570\",\n                value: (teacherStats === null || teacherStats === void 0 ? void 0 : teacherStats.corrected_count) || 0,\n                prefix: /*#__PURE__*/_jsxDEV(BookFilled, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            marginTop: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u7CFB\\u7EDF\\u7BA1\\u7406\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u7CFB\\u7EDF\\u914D\\u7F6E\\u4E0E\\u76D1\\u63A7\\u4FE1\\u606F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 736,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 679,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 添加注册设置组件\n  const renderRegistrationSettings = () => {\n    const globalEnabled = registrationSettings.global_registration_enabled;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u603B\\u6CE8\\u518C\\u63A7\\u5236\",\n        bordered: false,\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Spin, {\n          spinning: settingsLoading,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              padding: '16px 0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Typography.Title, {\n                level: 4,\n                style: {\n                  margin: 0,\n                  color: globalEnabled ? '#52c41a' : '#ff4d4f'\n                },\n                children: \"\\u7CFB\\u7EDF\\u6CE8\\u518C\\u603B\\u5F00\\u5173\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography.Text, {\n                type: \"secondary\",\n                children: globalEnabled ? '系统注册功能已开启，可以通过下方设置控制各角色的注册权限' : '系统注册功能已关闭，所有角色都无法注册，只能由管理员添加用户'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Switch, {\n              size: \"large\",\n              checked: globalEnabled,\n              onChange: updateGlobalRegistrationSwitch,\n              checkedChildren: \"\\u5F00\\u542F\",\n              unCheckedChildren: \"\\u5173\\u95ED\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 757,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 755,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u57FA\\u672C\\u6CE8\\u518C\\u8BBE\\u7F6E\",\n        bordered: false,\n        children: /*#__PURE__*/_jsxDEV(Spin, {\n          spinning: settingsLoading,\n          children: advancedSettings ? /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [24, 24],\n            children: Object.keys(advancedSettings.roles).map(roleName => {\n              const roleEnabled = globalEnabled && advancedSettings.roles[roleName].enabled;\n              return /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  type: \"inner\",\n                  title: `${getRoleDisplayName(roleName)}注册控制`,\n                  style: {\n                    opacity: globalEnabled ? 1 : 0.6\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n                      strong: true,\n                      children: `允许${getRoleDisplayName(roleName)}注册：`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 793,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: advancedSettings.roles[roleName].enabled,\n                      onChange: checked => updateRoleConfig(roleName, 'enabled', checked),\n                      disabled: !globalEnabled\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 794,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 792,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography.Text, {\n                    type: \"secondary\",\n                    style: {\n                      display: 'block',\n                      marginTop: 16\n                    },\n                    children: !globalEnabled ? '总开关已关闭，该角色无法注册' : advancedSettings.roles[roleName].enabled ? `${getRoleDisplayName(roleName)}可以自由注册账号` : `${getRoleDisplayName(roleName)}注册功能已关闭，只能由管理员添加账号`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 23\n                }, this)\n              }, roleName, false, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 24,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                type: \"inner\",\n                title: \"\\u5B66\\u751F\\u6CE8\\u518C\\u63A7\\u5236\",\n                style: {\n                  opacity: globalEnabled ? 1 : 0.6\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n                    strong: true,\n                    children: \"\\u5141\\u8BB8\\u5B66\\u751F\\u6CE8\\u518C\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 822,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                    checked: registrationSettings.allow_student_registration,\n                    onChange: checked => updateRegistrationSettings('allow_student_registration', checked),\n                    disabled: !globalEnabled\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography.Text, {\n                  type: \"secondary\",\n                  style: {\n                    display: 'block',\n                    marginTop: 16\n                  },\n                  children: !globalEnabled ? '总开关已关闭，学生无法注册' : registrationSettings.allow_student_registration ? '学生可以自由注册账号' : '学生注册功能已关闭，只能由管理员添加学生账号'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                type: \"inner\",\n                title: \"\\u6559\\u5E08\\u6CE8\\u518C\\u63A7\\u5236\",\n                style: {\n                  opacity: globalEnabled ? 1 : 0.6\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n                    strong: true,\n                    children: \"\\u5141\\u8BB8\\u6559\\u5E08\\u6CE8\\u518C\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                    checked: registrationSettings.allow_teacher_registration,\n                    onChange: checked => updateRegistrationSettings('allow_teacher_registration', checked),\n                    disabled: !globalEnabled\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 845,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography.Text, {\n                  type: \"secondary\",\n                  style: {\n                    display: 'block',\n                    marginTop: 16\n                  },\n                  children: !globalEnabled ? '总开关已关闭，教师无法注册' : registrationSettings.allow_teacher_registration ? '教师可以自由注册账号' : '教师注册功能已关闭，只能由管理员添加教师账号'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 779,\n        columnNumber: 9\n      }, this), advancedSettings && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u9AD8\\u7EA7\\u6CE8\\u518C\\u8BBE\\u7F6E\",\n        bordered: false,\n        style: {\n          marginTop: 16,\n          opacity: globalEnabled ? 1 : 0.6\n        },\n        children: /*#__PURE__*/_jsxDEV(Spin, {\n          spinning: settingsLoading,\n          children: [!globalEnabled && /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u603B\\u6CE8\\u518C\\u5F00\\u5173\\u5DF2\\u5173\\u95ED\",\n            description: \"\\u8BF7\\u5148\\u5F00\\u542F\\u603B\\u6CE8\\u518C\\u5F00\\u5173\\u624D\\u80FD\\u8FDB\\u884C\\u9AD8\\u7EA7\\u8BBE\\u7F6E\",\n            type: \"warning\",\n            showIcon: true,\n            style: {\n              marginBottom: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            defaultActiveKey: ['student', 'teacher'],\n            children: Object.keys(advancedSettings.roles).map(roleName => {\n              var _advancedSettings$rol, _advancedSettings$rol2, _advancedSettings$rol3, _advancedSettings$rol4, _advancedSettings$rol5, _advancedSettings$rol6, _advancedSettings$rol7, _advancedSettings$rol8;\n              const roleActuallyEnabled = globalEnabled && advancedSettings.roles[roleName].enabled;\n              return /*#__PURE__*/_jsxDEV(Collapse.Panel, {\n                header: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: getRoleDisplayName(roleName)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 895,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: roleActuallyEnabled ? 'green' : 'red',\n                    style: {\n                      marginLeft: 8\n                    },\n                    children: roleActuallyEnabled ? '已启用' : '已禁用'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 896,\n                    columnNumber: 27\n                  }, this), !globalEnabled && /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"orange\",\n                    style: {\n                      marginLeft: 8\n                    },\n                    children: \"\\u603B\\u5F00\\u5173\\u5173\\u95ED\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 900,\n                    columnNumber: 29\n                  }, this), advancedSettings.roles[roleName].requires_approval && /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"blue\",\n                    style: {\n                      marginLeft: 8\n                    },\n                    children: \"\\u9700\\u8981\\u5BA1\\u6838\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 903,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 894,\n                  columnNumber: 25\n                }, this),\n                disabled: !globalEnabled,\n                children: [/*#__PURE__*/_jsxDEV(Row, {\n                  gutter: [16, 16],\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n                        strong: true,\n                        children: \"\\u542F\\u7528\\u6CE8\\u518C\\uFF1A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 912,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                        checked: advancedSettings.roles[roleName].enabled,\n                        onChange: checked => updateRoleConfig(roleName, 'enabled', checked),\n                        disabled: !globalEnabled\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 913,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 911,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 910,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n                        strong: true,\n                        children: \"\\u9700\\u8981\\u5BA1\\u6838\\uFF1A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 922,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                        checked: advancedSettings.roles[roleName].requires_approval,\n                        onChange: checked => updateRoleConfig(roleName, 'requires_approval', checked),\n                        disabled: !globalEnabled\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 923,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 921,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 920,\n                    columnNumber: 23\n                  }, this), advancedSettings.roles[roleName].requires_approval && /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n                        strong: true,\n                        style: {\n                          marginRight: 8\n                        },\n                        children: \"\\u5BA1\\u6838\\u7EA7\\u522B\\uFF1A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 933,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography.Text, {\n                        children: getValueDisplayText('approval_level', advancedSettings.roles[roleName].approval_level)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 934,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                        title: \"\\u5BA1\\u6838\\u7EA7\\u522B\\u51B3\\u5B9A\\u7531\\u8C01\\u6765\\u5BA1\\u6838\\u8BE5\\u89D2\\u8272\\u7684\\u6CE8\\u518C\\u7533\\u8BF7\",\n                        children: /*#__PURE__*/_jsxDEV(InfoCircleOutlined, {\n                          style: {\n                            marginLeft: 8,\n                            color: '#1890ff'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 936,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 935,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 932,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                  orientation: \"left\",\n                  children: \"\\u5B57\\u6BB5\\u8BBE\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  gutter: [16, 16],\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      size: \"small\",\n                      title: \"\\u5B66\\u6821\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [\"\\u5FC5\\u586B\\uFF1A\", (_advancedSettings$rol = advancedSettings.roles[roleName].fields) !== null && _advancedSettings$rol !== void 0 && (_advancedSettings$rol2 = _advancedSettings$rol.school) !== null && _advancedSettings$rol2 !== void 0 && _advancedSettings$rol2.required ? '是' : '否']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 947,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 946,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 945,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      size: \"small\",\n                      title: \"\\u73ED\\u7EA7\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [\"\\u5FC5\\u586B\\uFF1A\", (_advancedSettings$rol3 = advancedSettings.roles[roleName].fields) !== null && _advancedSettings$rol3 !== void 0 && (_advancedSettings$rol4 = _advancedSettings$rol3.class) !== null && _advancedSettings$rol4 !== void 0 && _advancedSettings$rol4.required ? '是' : '否']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 952,\n                        columnNumber: 27\n                      }, this), ((_advancedSettings$rol5 = advancedSettings.roles[roleName].fields) === null || _advancedSettings$rol5 === void 0 ? void 0 : (_advancedSettings$rol6 = _advancedSettings$rol5.class) === null || _advancedSettings$rol6 === void 0 ? void 0 : _advancedSettings$rol6.max) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [\"\\u6700\\u591A\\u9009\\u62E9\\uFF1A\", advancedSettings.roles[roleName].fields.class.max, \" \\u4E2A\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 954,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 951,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 950,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      size: \"small\",\n                      title: \"\\u79D1\\u76EE\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [\"\\u5FC5\\u586B\\uFF1A\", (_advancedSettings$rol7 = advancedSettings.roles[roleName].fields) !== null && _advancedSettings$rol7 !== void 0 && (_advancedSettings$rol8 = _advancedSettings$rol7.subject) !== null && _advancedSettings$rol8 !== void 0 && _advancedSettings$rol8.required ? '是' : '否']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 960,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 959,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 958,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 21\n                }, this), roleName === 'parent' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 16\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Alert, {\n                    type: \"info\",\n                    message: \"\\u5BB6\\u957F\\u6CE8\\u518C\\u9700\\u8981\\u7ED1\\u5B9A\\u5B66\\u751F\",\n                    description: `家长注册时${advancedSettings.roles.parent.requires_student_binding ? '需要' : '不需要'}绑定学生`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 967,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 966,\n                  columnNumber: 23\n                }, this)]\n              }, roleName, true, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 887,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            style: {\n              marginTop: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n                strong: true,\n                children: \"\\u5141\\u8BB8\\u521B\\u5EFA\\u65B0\\u5B66\\u6821\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                checked: advancedSettings.allow_school_creation,\n                onChange: checked => {\n                  const updatedAdvanced = {\n                    ...advancedSettings,\n                    allow_school_creation: checked\n                  };\n                  setAdvancedSettings(updatedAdvanced);\n                  // 更新学校创建设置\n                  updateSchoolCreationSetting(checked);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 980,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography.Text, {\n              type: \"secondary\",\n              style: {\n                display: 'block',\n                marginTop: 8\n              },\n              children: advancedSettings.allow_school_creation ? '用户可以在注册时创建新学校（需要审核）' : '用户只能选择已有学校，不能创建新学校'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 979,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 869,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 753,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `admin-page system-page ${isMobile ? 'mobile' : 'desktop'}`,\n    style: {\n      width: '100%',\n      minHeight: '100vh',\n      background: isMobile ? '#f5f5f5' : 'transparent'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Layout, {\n      style: {\n        background: 'transparent'\n      },\n      children: /*#__PURE__*/_jsxDEV(Layout, {\n        style: {\n          padding: isMobile ? '12px 4px' : '0 24px 24px',\n          background: 'transparent'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: isMobile ? '0' : '16px 0',\n            padding: isMobile ? '0' : '16px',\n            background: isMobile ? 'transparent' : '#fff',\n            borderRadius: isMobile ? '0' : '8px'\n          },\n          children: [isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '16px',\n              padding: '16px',\n              background: 'white',\n              borderRadius: '12px',\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              style: {\n                margin: 0,\n                color: '#1890ff'\n              },\n              children: \"\\uD83D\\uDEE0\\uFE0F \\u7BA1\\u7406\\u5458\\u63A7\\u5236\\u53F0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Content, {\n            style: {\n              padding: isMobile ? '0' : '12px 0',\n              minHeight: 'calc(100vh - 200px)',\n              background: 'transparent'\n            },\n            children: /*#__PURE__*/_jsxDEV(Tabs, {\n              activeKey: activeTab,\n              onChange: key => {\n                setActiveTab(key);\n                navigate(`/admin?tab=${key}`, {\n                  replace: true\n                });\n              },\n              type: isMobile ? \"card\" : \"line\",\n              size: isMobile ? \"small\" : \"default\",\n              tabPosition: isMobile ? \"top\" : \"top\",\n              style: {\n                background: 'white',\n                borderRadius: '12px',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TabPane, {\n                tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: isMobile ? '12px' : '14px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1062,\n                    columnNumber: 21\n                  }, this), isMobile ? 'AI配置' : 'AI配置管理']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1061,\n                  columnNumber: 19\n                }, this),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: 16\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"primary\",\n                    icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1069,\n                      columnNumber: 29\n                    }, this),\n                    onClick: handleAddAiConfig,\n                    size: isMobile ? \"large\" : \"default\",\n                    style: isMobile ? {\n                      height: '44px',\n                      fontSize: '16px'\n                    } : {},\n                    children: \"\\u6DFB\\u52A0AI\\u914D\\u7F6E\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1067,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1066,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Table, {\n                  columns: aiConfigColumns,\n                  dataSource: aiConfigs,\n                  rowKey: \"id\",\n                  loading: loading,\n                  pagination: {\n                    pageSize: isMobile ? 5 : 10,\n                    showSizeChanger: !isMobile,\n                    showQuickJumper: !isMobile,\n                    simple: isMobile\n                  },\n                  scroll: isMobile ? {\n                    x: 800\n                  } : undefined\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1077,\n                  columnNumber: 19\n                }, this), !isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 16\n                  },\n                  children: [aiConfigs.length === 0 && !loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      textAlign: 'center',\n                      padding: '20px',\n                      backgroundColor: '#f9f9f9',\n                      borderRadius: '4px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"\\u6682\\u65E0AI\\u914D\\u7F6E\\u6570\\u636E\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1094,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1093,\n                    columnNumber: 25\n                  }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      children: \"\\u5F53\\u524DAI\\u914D\\u7F6E\\u4FE1\\u606F\\uFF1A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1098,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                      style: {\n                        backgroundColor: '#f5f5f5',\n                        padding: '10px',\n                        borderRadius: '4px',\n                        maxHeight: '200px',\n                        overflow: 'auto'\n                      },\n                      children: JSON.stringify(aiConfigs, null, 2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1099,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1097,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1091,\n                  columnNumber: 21\n                }, this)]\n              }, \"1\", true, {\n                fileName: _jsxFileName,\n                lineNumber: 1060,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: isMobile ? '12px' : '14px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(BookFilled, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1109,\n                    columnNumber: 21\n                  }, this), isMobile ? '科目管理' : '作业科目管理']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1108,\n                  columnNumber: 19\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(SubjectManagement, {\n                  isMobile: isMobile\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1113,\n                  columnNumber: 19\n                }, this)\n              }, \"2\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 1107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: isMobile ? '12px' : '14px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(UserSwitchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1118,\n                    columnNumber: 21\n                  }, this), isMobile ? '注册设置' : '注册设置']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1117,\n                  columnNumber: 19\n                }, this),\n                children: renderRegistrationSettings()\n              }, \"3\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 1116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: isMobile ? '12px' : '14px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(AuditOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1127,\n                    columnNumber: 21\n                  }, this), isMobile ? '注册审批' : '注册审批']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1126,\n                  columnNumber: 19\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(RegistrationApproval, {\n                  isMobile: isMobile\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1131,\n                  columnNumber: 19\n                }, this)\n              }, \"4\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 1125,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: isMobile ? '12px' : '14px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1136,\n                    columnNumber: 21\n                  }, this), isMobile ? '学校申请' : '学校申请']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1135,\n                  columnNumber: 19\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(SchoolApplicationReview, {\n                  isMobile: isMobile\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1140,\n                  columnNumber: 19\n                }, this)\n              }, \"5\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 1134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [/*#__PURE__*/_jsxDEV(SafetyCertificateOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1143,\n                    columnNumber: 37\n                  }, this), \"\\u79D1\\u76EE\\u6743\\u9650\\u7BA1\\u7406\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1143,\n                  columnNumber: 31\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  title: \"\\u89D2\\u8272\\u79D1\\u76EE\\u6743\\u9650\\u7BA1\\u7406\",\n                  children: /*#__PURE__*/_jsxDEV(SubjectPermissionManager, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1145,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1144,\n                  columnNumber: 19\n                }, this)\n              }, \"6\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 1143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: isMobile ? '12px' : '14px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1151,\n                    columnNumber: 21\n                  }, this), isMobile ? '家长端管理' : '家长端功能管理']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1150,\n                  columnNumber: 19\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(ParentFeatureManager, {\n                  isMobile: isMobile\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1155,\n                  columnNumber: 19\n                }, this)\n              }, \"7\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 1149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1045,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1040,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1020,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1016,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1014,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      title: \"AI\\u667A\\u80FD\\u52A9\\u624B\",\n      placement: \"right\",\n      onClose: () => setAiDrawerVisible(false),\n      open: aiDrawerVisible,\n      width: 400,\n      children: /*#__PURE__*/_jsxDEV(AIAssistant, {\n        onClose: () => setAiDrawerVisible(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1171,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingUser ? '编辑用户' : '添加用户',\n      open: userModalVisible,\n      onCancel: () => setUserModalVisible(false),\n      footer: null,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleUserFormSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          label: \"\\u7528\\u6237\\u540D\",\n          rules: [{\n            required: true,\n            message: '请输入用户名'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"email\",\n          label: \"\\u90AE\\u7BB1\",\n          rules: [{\n            required: true,\n            message: '请输入邮箱'\n          }, {\n            type: 'email',\n            message: '请输入有效的邮箱地址'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1194,\n          columnNumber: 11\n        }, this), !editingUser && /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          label: \"\\u5BC6\\u7801\",\n          rules: [{\n            required: !editingUser,\n            message: '请输入密码'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1211,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1206,\n          columnNumber: 13\n        }, this), editingUser && /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          label: \"\\u5BC6\\u7801 (\\u7559\\u7A7A\\u8868\\u793A\\u4E0D\\u4FEE\\u6539)\",\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1220,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1216,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"full_name\",\n          label: \"\\u59D3\\u540D\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u59D3\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"is_teacher\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Checkbox, {\n            children: \"\\u6559\\u5E08\\u6743\\u9650\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"is_admin\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Checkbox, {\n            children: \"\\u7BA1\\u7406\\u5458\\u6743\\u9650\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            marginBottom: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u4FDD\\u5B58\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setUserModalVisible(false),\n            children: \"\\u53D6\\u6D88\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1181,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingAiConfig ? '编辑AI配置' : '添加AI配置',\n      open: aiConfigModalVisible,\n      onCancel: () => setAiConfigModalVisible(false),\n      footer: null,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: aiConfigForm,\n        layout: \"vertical\",\n        onFinish: handleAiConfigFormSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"model_name\",\n          label: \"\\u6A21\\u578B\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入模型名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u6A21\\u578B\\u540D\\u79F0\\uFF0C\\u4F8B\\u5982 Doubao-1.5-vision-pro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"model_id\",\n          label: \"\\u6A21\\u578BID\",\n          rules: [{\n            required: true,\n            message: '请输入模型ID'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u6A21\\u578BID\\uFF0C\\u4F8B\\u5982 ep-xxxxxxxxxx\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1281,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"provider\",\n          label: \"\\u63D0\\u4F9B\\u5546\",\n          rules: [{\n            required: true,\n            message: '请选择提供商'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u63D0\\u4F9B\\u5546\",\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"volcano\",\n              children: \"\\u706B\\u5C71\\u5F15\\u64CE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"ollama\",\n              children: \"Ollama\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1291,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"qianwen\",\n              children: \"\\u901A\\u4E49\\u5343\\u95EE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1292,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1289,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"api_key\",\n          label: \"API \\u5BC6\\u94A5\",\n          rules: [{\n            required: true,\n            message: '请输入API密钥'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165API\\u5BC6\\u94A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1301,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1296,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"api_endpoint\",\n          label: \"API\\u7AEF\\u70B9\",\n          rules: [{\n            required: true,\n            message: '请输入API端点'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165API\\u7AEF\\u70B9\\uFF0C\\u4F8B\\u5982 https://api.volcengine.com/v1/chat/completions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1309,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"is_active\",\n          valuePropName: \"checked\",\n          initialValue: true,\n          children: [/*#__PURE__*/_jsxDEV(Switch, {\n            defaultChecked: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1317,\n            columnNumber: 13\n          }, this), \" \\u6FC0\\u6D3B\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            marginBottom: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u4FDD\\u5B58\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setAiConfigModalVisible(false),\n            children: \"\\u53D6\\u6D88\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1320,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1263,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1257,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1006,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"+MgytviGQmBpEXLWtqitHEvd0Tg=\", false, function () {\n  return [useNavigate, useLocation, Form.useForm, Form.useForm];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "<PERSON><PERSON>", "Breadcrumb", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Switch", "message", "Card", "Tabs", "Typography", "Spin", "Row", "Col", "Divider", "Collapse", "Tag", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Space", "Checkbox", "Dropdown", "Avatar", "Drawer", "Statistic", "UserOutlined", "SettingOutlined", "DashboardOutlined", "PlusOutlined", "EditOutlined", "DeleteOutlined", "BookOutlined", "TeamOutlined", "BookFilled", "BankOutlined", "LogoutOutlined", "MessageOutlined", "BarChartOutlined", "UserSwitchOutlined", "ExclamationCircleOutlined", "InfoCircleOutlined", "AuditOutlined", "SafetyCertificateOutlined", "useNavigate", "useLocation", "Link", "getUsers", "getAiConfigs", "createAiConfig", "updateAiConfig", "deleteAiConfig", "api", "SubjectManagement", "UserManagement", "AIAssistant", "RegistrationApproval", "SchoolApplicationReview", "SubjectPermissionManager", "ParentFeatureManager", "Fragment", "_Fragment", "jsxDEV", "_jsxDEV", "Header", "Content", "<PERSON><PERSON>", "Title", "TabPane", "Option", "AdminDashboard", "user", "onLogout", "_s", "navigate", "location", "activeTab", "setActiveTab", "users", "setUsers", "aiConfigs", "setAiConfigs", "systemInfo", "setSystemInfo", "loading", "setLoading", "userModalVisible", "setUserModalVisible", "aiConfigModalVisible", "setAiConfigModalVisible", "editingUser", "setEditingUser", "editingAiConfig", "setEditingAiConfig", "form", "useForm", "aiConfigForm", "teacherStats", "setTeacherStats", "isMobile", "setIsMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "studentStats", "setStudentStats", "statsLoading", "setStatsLoading", "statsError", "setStatsError", "aiDrawerVisible", "setAiDrawerVisible", "registrationSettings", "setRegistrationSettings", "global_registration_enabled", "allow_student_registration", "allow_teacher_registration", "advancedSettings", "setAdvancedSettings", "settingsLoading", "setSettingsLoading", "fetchSystemInfo", "token", "localStorage", "getItem", "response", "get", "data", "error", "console", "fetchUsers", "fetchAiConfigs", "log", "detail", "fetchStatistics", "teacherResponse", "studentResponse", "JSON", "stringify", "hasOwnProperty", "warn", "schoolClassesResponse", "Array", "isArray", "class_count", "length", "classError", "dbResponse", "params", "query", "Date", "getTime", "result", "parseInt", "db<PERSON><PERSON>r", "_error$response", "_error$response$data", "student_count", "homework_count", "corrected_count", "total_homework_count", "completed_homework_count", "average_accuracy", "wrong_question_count", "fetchRegistrationSettings", "simpleResponse", "advancedResponse", "updateGlobalRegistrationSwitch", "enabled", "put", "prev", "success", "_error$response2", "_error$response3", "_error$response4", "_error$response5", "_error$response6", "_error$response6$data", "status", "errorMessage", "updateRegistrationSettings", "key", "value", "updatedSettings", "updatedAdvanced", "roles", "student", "teacher", "updateRoleConfig", "<PERSON><PERSON><PERSON>", "field", "updateData", "getRoleDisplayName", "getFieldDisplayName", "getValueDisplayText", "roleNameMap", "fieldNameMap", "approvalLevelMap", "updateSchoolCreationSetting", "allow_school_creation", "prevSettings", "fetchData", "Promise", "all", "urlParams", "URLSearchParams", "search", "tabParam", "handleUserFormSubmit", "values", "id", "post", "_error$response7", "_error$response7$data", "handleAiConfigFormSubmit", "handleDeleteUser", "userId", "delete", "_error$response8", "_error$response8$data", "handleDeleteAiConfig", "configId", "handleEditUser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "username", "email", "full_name", "phone", "is_teacher", "is_admin", "password", "school_id", "handleEditAiConfig", "config", "model_name", "model_id", "provider", "api_key", "api_endpoint", "is_active", "handleAddUser", "resetFields", "handleAddAiConfig", "userColumns", "title", "dataIndex", "render", "text", "_", "record", "children", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginRight", "onClick", "danger", "confirm", "content", "okText", "cancelText", "onOk", "aiConfigColumns", "ellipsis", "navigateToSchoolManagement", "renderStatsDashboard", "className", "gutter", "marginBottom", "span", "level", "description", "showIcon", "spinning", "xs", "sm", "md", "lg", "prefix", "marginTop", "renderRegistrationSettings", "globalEnabled", "bordered", "display", "justifyContent", "alignItems", "padding", "margin", "color", "Text", "size", "checked", "onChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "Object", "keys", "map", "roleEnabled", "opacity", "strong", "disabled", "defaultActiveKey", "_advancedSettings$rol", "_advancedSettings$rol2", "_advancedSettings$rol3", "_advancedSettings$rol4", "_advancedSettings$rol5", "_advancedSettings$rol6", "_advancedSettings$rol7", "_advancedSettings$rol8", "roleActuallyEnabled", "Panel", "header", "marginLeft", "requires_approval", "approval_level", "orientation", "fields", "school", "required", "class", "max", "subject", "parent", "requires_student_binding", "width", "minHeight", "background", "borderRadius", "textAlign", "active<PERSON><PERSON>", "replace", "tabPosition", "overflow", "tab", "fontSize", "height", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "simple", "scroll", "x", "undefined", "backgroundColor", "maxHeight", "placement", "onClose", "open", "onCancel", "footer", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "rules", "placeholder", "Password", "valuePropName", "htmlType", "initialValue", "defaultChecked", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/correcthomework4/frontend/src/pages/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Layout, Menu, Breadcrumb, Table, Button, Modal, Form, Input, Select, Switch, message, Card, Tabs, Typography, Spin, Row, Col, Divider, Collapse, Tag, Tooltip, Alert, Space, Checkbox, Dropdown, Avatar, Drawer, Statistic } from 'antd';\r\nimport { UserOutlined, SettingOutlined, DashboardOutlined, PlusOutlined, EditOutlined, DeleteOutlined, BookOutlined, TeamOutlined, BookFilled, BankOutlined, LogoutOutlined, MessageOutlined, BarChartOutlined, UserSwitchOutlined, ExclamationCircleOutlined, InfoCircleOutlined, AuditOutlined, SafetyCertificateOutlined } from '@ant-design/icons';\r\nimport { useNavigate, useLocation, Link } from 'react-router-dom';\r\nimport { getUsers, getAiConfigs, createAiConfig, updateAiConfig, deleteAiConfig } from '../utils/api';\r\nimport api from '../utils/api';\r\nimport SubjectManagement from '../components/SubjectManagement';\r\nimport UserManagement from '../components/UserManagement';\r\nimport AIAssistant from '../components/AIAssistant';\r\nimport RegistrationApproval from '../components/RegistrationApproval';\r\nimport SchoolApplicationReview from '../components/SchoolApplicationReview';\r\nimport SubjectPermissionManager from '../components/SubjectPermissionManager';\r\nimport ParentFeatureManager from '../components/ParentFeatureManager';\r\n\r\nconst { Header, Content, Sider } = Layout;\r\nconst { Title } = Typography;\r\nconst { TabPane } = Tabs;\r\nconst { Option } = Select;\r\n\r\nconst AdminDashboard = ({ user, onLogout }) => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [activeTab, setActiveTab] = useState('2');\r\n  const [users, setUsers] = useState([]);\r\n  const [aiConfigs, setAiConfigs] = useState([]);\r\n  const [systemInfo, setSystemInfo] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [userModalVisible, setUserModalVisible] = useState(false);\r\n  const [aiConfigModalVisible, setAiConfigModalVisible] = useState(false);\r\n  const [editingUser, setEditingUser] = useState(null);\r\n  const [editingAiConfig, setEditingAiConfig] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const [aiConfigForm] = Form.useForm();\r\n  // 新增统计信息状态\r\n  const [teacherStats, setTeacherStats] = useState(null);\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\r\n\r\n  // 监听窗口大小变化\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth <= 768);\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n  const [studentStats, setStudentStats] = useState(null);\r\n  const [statsLoading, setStatsLoading] = useState(true);\r\n  const [statsError, setStatsError] = useState(null);\r\n  const [aiDrawerVisible, setAiDrawerVisible] = useState(false);\r\n  // 添加注册设置状态\r\n  const [registrationSettings, setRegistrationSettings] = useState({\r\n    global_registration_enabled: true,\r\n    allow_student_registration: true,\r\n    allow_teacher_registration: true\r\n  });\r\n  const [advancedSettings, setAdvancedSettings] = useState(null);\r\n  const [settingsLoading, setSettingsLoading] = useState(false);\r\n\r\n  // 获取系统信息\r\n  const fetchSystemInfo = async () => {\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      const response = await api.get('/admin/system-info');\r\n      setSystemInfo(response.data);\r\n    } catch (error) {\r\n      console.error('获取系统信息失败:', error);\r\n      message.error('获取系统信息失败');\r\n    }\r\n  };\r\n\r\n  // 获取所有用户\r\n  const fetchUsers = async () => {\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      const response = await api.get('/admin/users');\r\n      setUsers(response.data);\r\n    } catch (error) {\r\n      console.error('获取用户列表失败:', error);\r\n      message.error('获取用户列表失败');\r\n    }\r\n  };\r\n\r\n  // 获取所有AI配置\r\n  const fetchAiConfigs = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const data = await getAiConfigs();\r\n      console.log('获取到的AI配置:', data);\r\n      setAiConfigs(data);\r\n    } catch (error) {\r\n      console.error('获取AI配置失败:', error);\r\n      message.error('获取AI配置失败: ' + (error.detail || error.message || '未知错误'));\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 获取统计信息\r\n  const fetchStatistics = async () => {\r\n    setStatsLoading(true);\r\n    setStatsError(null);\r\n    \r\n    try {\r\n      // 获取token\r\n      const token = localStorage.getItem('token');\r\n      \r\n      // 发起API请求\r\n      console.log('开始获取统计数据...');\r\n      \r\n      // 使用api实例请求以便于调试\r\n      const teacherResponse = await api.get('/statistics/teacher');\r\n      const studentResponse = await api.get('/statistics/student');\r\n\r\n      console.log('获取到的教师统计数据:', JSON.stringify(teacherResponse, null, 2));\r\n      console.log('获取到的学生统计数据:', JSON.stringify(studentResponse, null, 2));\r\n      \r\n      // 检查数据字段\r\n      if (!teacherResponse.hasOwnProperty('homework_count')) {\r\n        console.warn('教师统计数据缺少homework_count字段');\r\n      }\r\n\r\n      if (!teacherResponse.hasOwnProperty('corrected_count')) {\r\n        console.warn('教师统计数据缺少corrected_count字段');\r\n      }\r\n      \r\n      // 获取学校ID为1的班级数量\r\n      try {\r\n        const schoolClassesResponse = await api.get('/admin/schools/1/classes');\r\n        \r\n        if (Array.isArray(schoolClassesResponse)) {\r\n          // 使用学校ID为1的实际班级数量覆盖统计数据中的班级数量\r\n          teacherResponse.class_count = schoolClassesResponse.length;\r\n          console.log(`使用学校ID为1的实际班级数量: ${teacherResponse.class_count}`);\r\n        }\r\n      } catch (classError) {\r\n        console.error('获取学校班级数量失败:', classError);\r\n        \r\n        // 如果API失败，尝试直接查询数据库\r\n        try {\r\n          const dbResponse = await api.get(`/admin/query`, {\r\n            params: {\r\n              query: `SELECT COUNT(*) FROM classes WHERE school_id = 1 /* ${new Date().getTime()} */`\r\n            }\r\n          });\r\n          \r\n          if (dbResponse.data && dbResponse.data.result && dbResponse.data.result[0]) {\r\n            teacherResponse.data.class_count = parseInt(dbResponse.data.result[0][0]);\r\n            console.log(`使用数据库查询获取到的学校ID为1的班级数量: ${teacherResponse.data.class_count}`);\r\n          }\r\n        } catch (dbError) {\r\n          console.error('数据库查询班级数量失败:', dbError);\r\n        }\r\n      }\r\n      \r\n      // 设置状态\r\n      setTeacherStats(teacherResponse);\r\n      setStudentStats(studentResponse);\r\n    } catch (error) {\r\n      console.error('获取统计数据失败:', error);\r\n      setStatsError('获取统计数据失败: ' + (error.response?.data?.detail || error.message || '未知错误'));\r\n      \r\n      // 设置空数据，避免前端崩溃\r\n      setTeacherStats({\r\n        class_count: 0,\r\n        student_count: 0, \r\n        homework_count: 0,\r\n        corrected_count: 0\r\n      });\r\n      \r\n      setStudentStats({\r\n        total_homework_count: 0,\r\n        completed_homework_count: 0,\r\n        average_accuracy: 0,\r\n        wrong_question_count: 0\r\n      });\r\n    } finally {\r\n      setStatsLoading(false);\r\n    }\r\n  };\r\n\r\n  // 获取注册设置\r\n  const fetchRegistrationSettings = async () => {\r\n    try {\r\n      setSettingsLoading(true);\r\n      const token = localStorage.getItem('token');\r\n      \r\n      // 获取简单注册设置\r\n      const simpleResponse = await api.get('/admin/system-settings/registration');\r\n      console.log('获取简单注册设置成功:', simpleResponse);\r\n      setRegistrationSettings(simpleResponse);\r\n\r\n      // 获取高级注册设置\r\n      const advancedResponse = await api.get('/admin/system-settings/advanced-registration');\r\n      console.log('获取高级注册设置成功:', advancedResponse);\r\n      setAdvancedSettings(advancedResponse);\r\n    } catch (error) {\r\n      console.error('获取注册设置失败:', error);\r\n      message.error('获取注册设置失败');\r\n    } finally {\r\n      setSettingsLoading(false);\r\n    }\r\n  };\r\n\r\n  // 更新总注册开关\r\n  const updateGlobalRegistrationSwitch = async (enabled) => {\r\n    try {\r\n      setSettingsLoading(true);\r\n      const token = localStorage.getItem('token');\r\n\r\n      console.log('更新总注册开关 - 开始');\r\n      console.log('Token存在:', !!token);\r\n      console.log('Token长度:', token?.length);\r\n      console.log('启用状态:', enabled);\r\n      console.log('请求URL:', '/api/admin/system-settings/global-registration-switch');\r\n      console.log('请求方法:', 'PUT');\r\n      console.log('请求数据:', { enabled });\r\n\r\n      const response = await api.put('/admin/system-settings/global-registration-switch',\r\n        { enabled }\r\n      );\r\n\r\n      console.log('更新成功:', response.data);\r\n\r\n      // 更新本地状态\r\n      setRegistrationSettings(prev => ({ ...prev, global_registration_enabled: enabled }));\r\n      if (advancedSettings) {\r\n        setAdvancedSettings(prev => ({ ...prev, global_registration_enabled: enabled }));\r\n      }\r\n\r\n      message.success(`总注册开关已${enabled ? '开启' : '关闭'}`);\r\n    } catch (error) {\r\n      console.error('更新总注册开关失败:', error);\r\n      console.error('错误详情:', error.response?.data);\r\n      console.error('状态码:', error.response?.status);\r\n\r\n      let errorMessage = '更新总注册开关失败';\r\n      if (error.response?.status === 401) {\r\n        errorMessage = '权限不足，请重新登录';\r\n      } else if (error.response?.status === 403) {\r\n        errorMessage = '需要管理员权限';\r\n      } else if (error.response?.data?.detail) {\r\n        errorMessage = error.response.data.detail;\r\n      }\r\n\r\n      message.error(errorMessage);\r\n    } finally {\r\n      setSettingsLoading(false);\r\n    }\r\n  };\r\n\r\n  // 更新简单注册设置\r\n  const updateRegistrationSettings = async (key, value) => {\r\n    try {\r\n      setSettingsLoading(true);\r\n      const token = localStorage.getItem('token');\r\n      const updatedSettings = { ...registrationSettings, [key]: value };\r\n\r\n      await api.put('/admin/system-settings/registration', updatedSettings);\r\n\r\n      console.log('更新注册设置成功:', updatedSettings);\r\n      setRegistrationSettings(updatedSettings);\r\n\r\n      // 同步更新高级设置中的对应角色\r\n      if (advancedSettings) {\r\n        const updatedAdvanced = { ...advancedSettings };\r\n        if (key === 'allow_student_registration') {\r\n          updatedAdvanced.roles.student.enabled = value;\r\n        } else if (key === 'allow_teacher_registration') {\r\n          updatedAdvanced.roles.teacher.enabled = value;\r\n        }\r\n        setAdvancedSettings(updatedAdvanced);\r\n      }\r\n\r\n      message.success(`${key === 'allow_student_registration' ? '学生' : '教师'}注册功能已${value ? '开启' : '关闭'}`);\r\n    } catch (error) {\r\n      console.error('更新注册设置失败:', error);\r\n      message.error('更新注册设置失败');\r\n    } finally {\r\n      setSettingsLoading(false);\r\n    }\r\n  };\r\n\r\n  // 更新角色配置\r\n  const updateRoleConfig = async (roleName, field, value) => {\r\n    try {\r\n      setSettingsLoading(true);\r\n      const token = localStorage.getItem('token');\r\n      \r\n      // 创建更新对象\r\n      const updateData = { [field]: value };\r\n      \r\n      // 发送更新请求\r\n      await api.put(`/admin/system-settings/role-config/${roleName}`, updateData);\r\n      \r\n      // 更新本地状态\r\n      const updatedAdvanced = { ...advancedSettings };\r\n      updatedAdvanced.roles[roleName][field] = value;\r\n      setAdvancedSettings(updatedAdvanced);\r\n      \r\n      // 如果是学生或教师的enabled字段，同时更新简单设置\r\n      if (field === 'enabled') {\r\n        if (roleName === 'student') {\r\n          setRegistrationSettings(prev => ({\r\n            ...prev,\r\n            allow_student_registration: value\r\n          }));\r\n        } else if (roleName === 'teacher') {\r\n          setRegistrationSettings(prev => ({\r\n            ...prev,\r\n            allow_teacher_registration: value\r\n          }));\r\n        }\r\n      }\r\n      \r\n      message.success(`${getRoleDisplayName(roleName)}的${getFieldDisplayName(field)}已${getValueDisplayText(field, value)}`);\r\n    } catch (error) {\r\n      console.error(`更新${roleName}配置失败:`, error);\r\n      message.error(`更新${getRoleDisplayName(roleName)}配置失败`);\r\n    } finally {\r\n      setSettingsLoading(false);\r\n    }\r\n  };\r\n\r\n  // 获取角色显示名称\r\n  const getRoleDisplayName = (roleName) => {\r\n    const roleNameMap = {\r\n      'student': '学生',\r\n      'teacher': '教师',\r\n      'parent': '家长',\r\n      'class_teacher': '班主任',\r\n      'subject_leader': '备课组长',\r\n      'academic_director': '教务主任',\r\n      'vice_principal': '副校长',\r\n      'principal': '校长',\r\n      'school_admin': '学校管理员'\r\n    };\r\n    return roleNameMap[roleName] || roleName;\r\n  };\r\n\r\n  // 获取字段显示名称\r\n  const getFieldDisplayName = (field) => {\r\n    const fieldNameMap = {\r\n      'enabled': '注册功能',\r\n      'requires_approval': '审核功能',\r\n      'approval_level': '审核级别'\r\n    };\r\n    return fieldNameMap[field] || field;\r\n  };\r\n\r\n  // 获取值的显示文本\r\n  const getValueDisplayText = (field, value) => {\r\n    if (field === 'enabled' || field === 'requires_approval') {\r\n      return value ? '开启' : '关闭';\r\n    }\r\n    if (field === 'approval_level') {\r\n      const approvalLevelMap = {\r\n        'school_admin': '学校管理员',\r\n        'principal': '校长',\r\n        'academic_director': '教务主任',\r\n        'super_admin': '超级管理员'\r\n      };\r\n      return approvalLevelMap[value] || value;\r\n    }\r\n    return value;\r\n  };\r\n\r\n  // 更新学校创建设置\r\n  const updateSchoolCreationSetting = async (value) => {\r\n    try {\r\n      setSettingsLoading(true);\r\n      const token = localStorage.getItem('token');\r\n      \r\n      // 创建更新对象，包含完整的高级设置\r\n      const updatedSettings = { ...advancedSettings, allow_school_creation: value };\r\n      \r\n      // 发送更新请求\r\n      await api.put('/admin/system-settings/advanced-registration', updatedSettings);\r\n      \r\n      message.success(`学校创建功能已${value ? '开启' : '关闭'}`);\r\n    } catch (error) {\r\n      console.error('更新学校创建设置失败:', error);\r\n      message.error('更新学校创建设置失败');\r\n      // 恢复原来的状态\r\n      setAdvancedSettings(prevSettings => ({ ...prevSettings }));\r\n    } finally {\r\n      setSettingsLoading(false);\r\n    }\r\n  };\r\n\r\n  // 初始加载数据\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      setLoading(true);\r\n      try {\r\n        await Promise.all([fetchSystemInfo(), fetchUsers(), fetchAiConfigs(), fetchStatistics()]);\r\n      } catch (error) {\r\n        console.error('加载数据失败:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchData();\r\n    \r\n    // 检查URL中的tab参数\r\n    const urlParams = new URLSearchParams(location.search);\r\n    const tabParam = urlParams.get('tab');\r\n    if (tabParam) {\r\n      setActiveTab(tabParam);\r\n    }\r\n    \r\n    // 添加获取注册设置\r\n    fetchRegistrationSettings();\r\n  }, [location]);\r\n\r\n  // 处理用户表单提交\r\n  const handleUserFormSubmit = async (values) => {\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      \r\n      if (editingUser) {\r\n        // 更新用户\r\n        await api.put(`/admin/users/${editingUser.id}`, values);\r\n        message.success('用户更新成功');\r\n      } else {\r\n        // 创建用户\r\n        await api.post('/register', values);\r\n        message.success('用户创建成功');\r\n      }\r\n      \r\n      setUserModalVisible(false);\r\n      fetchUsers();\r\n    } catch (error) {\r\n      console.error('操作失败:', error);\r\n      message.error('操作失败: ' + (error.response?.data?.detail || error.message));\r\n    }\r\n  };\r\n\r\n  // 处理AI配置表单提交\r\n  const handleAiConfigFormSubmit = async (values) => {\r\n    try {\r\n      if (editingAiConfig) {\r\n        // 更新AI配置\r\n        await updateAiConfig(editingAiConfig.id, values);\r\n        message.success('AI配置更新成功');\r\n      } else {\r\n        // 创建AI配置\r\n        await createAiConfig(values);\r\n        message.success('AI配置创建成功');\r\n      }\r\n      \r\n      setAiConfigModalVisible(false);\r\n      fetchAiConfigs();\r\n    } catch (error) {\r\n      console.error('操作失败:', error);\r\n      message.error('操作失败: ' + (error.detail || error.message || '未知错误'));\r\n    }\r\n  };\r\n\r\n  // 删除用户\r\n  const handleDeleteUser = async (userId) => {\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      await api.delete(`/admin/users/${userId}`);\r\n      message.success('用户删除成功');\r\n      fetchUsers();\r\n    } catch (error) {\r\n      console.error('删除用户失败:', error);\r\n      message.error('删除用户失败: ' + (error.response?.data?.detail || error.message));\r\n    }\r\n  };\r\n\r\n  // 删除AI配置\r\n  const handleDeleteAiConfig = async (configId) => {\r\n    try {\r\n      await deleteAiConfig(configId);\r\n      message.success('AI配置删除成功');\r\n      fetchAiConfigs();\r\n    } catch (error) {\r\n      console.error('删除AI配置失败:', error);\r\n      message.error('删除AI配置失败: ' + (error.detail || error.message || '未知错误'));\r\n    }\r\n  };\r\n\r\n  // 编辑用户\r\n  const handleEditUser = (user) => {\r\n    setEditingUser(user);\r\n    form.setFieldsValue({\r\n      username: user.username,\r\n      email: user.email,\r\n      full_name: user.full_name,\r\n      phone: user.phone,\r\n      is_teacher: user.is_teacher,\r\n      is_admin: user.is_admin,\r\n      password: '', // 不回显密码\r\n      school_id: user.school_id,\r\n    });\r\n    setUserModalVisible(true);\r\n  };\r\n\r\n  // 编辑AI配置\r\n  const handleEditAiConfig = (config) => {\r\n    setEditingAiConfig(config);\r\n    aiConfigForm.setFieldsValue({\r\n      model_name: config.model_name,\r\n      model_id: config.model_id,\r\n      provider: config.provider,\r\n      api_key: config.api_key,\r\n      api_endpoint: config.api_endpoint,\r\n      is_active: config.is_active\r\n    });\r\n    setAiConfigModalVisible(true);\r\n  };\r\n\r\n  // 添加新用户\r\n  const handleAddUser = () => {\r\n    setEditingUser(null);\r\n    form.resetFields();\r\n    setUserModalVisible(true);\r\n  };\r\n\r\n  // 添加新AI配置\r\n  const handleAddAiConfig = () => {\r\n    setEditingAiConfig(null);\r\n    aiConfigForm.resetFields();\r\n    setAiConfigModalVisible(true);\r\n  };\r\n\r\n  // 用户表格列定义\r\n  const userColumns = [\r\n    {\r\n      title: 'ID',\r\n      dataIndex: 'id',\r\n      key: 'id',\r\n    },\r\n    {\r\n      title: '用户名',\r\n      dataIndex: 'username',\r\n      key: 'username',\r\n    },\r\n    {\r\n      title: '邮箱',\r\n      dataIndex: 'email',\r\n      key: 'email',\r\n    },\r\n    {\r\n      title: '姓名',\r\n      dataIndex: 'full_name',\r\n      key: 'full_name',\r\n    },\r\n    {\r\n      title: '电话',\r\n      dataIndex: 'phone',\r\n      key: 'phone',\r\n    },\r\n    {\r\n      title: '学校',\r\n      dataIndex: 'school_name',\r\n      key: 'school_name',\r\n      render: (text) => text || '未分配'\r\n    },\r\n    {\r\n      title: '角色',\r\n      key: 'role',\r\n      render: (_, record) => (\r\n        <>\r\n          {record.is_admin ? '管理员' : (record.is_teacher ? '教师' : '学生')}\r\n        </>\r\n      ),\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      render: (_, record) => (\r\n        <>\r\n          <Button \r\n            type=\"primary\" \r\n            icon={<EditOutlined />} \r\n            style={{ marginRight: 8 }} \r\n            onClick={() => handleEditUser(record)}\r\n          >\r\n            编辑\r\n          </Button>\r\n          <Button \r\n            danger \r\n            icon={<DeleteOutlined />} \r\n            onClick={() => {\r\n              Modal.confirm({\r\n                title: '确认删除',\r\n                content: `确定要删除用户 \"${record.username}\" 吗？`,\r\n                okText: '确定',\r\n                cancelText: '取消',\r\n                onOk: () => handleDeleteUser(record.id)\r\n              });\r\n            }}\r\n          >\r\n            删除\r\n          </Button>\r\n        </>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // AI配置表格列定义\r\n  const aiConfigColumns = [\r\n    {\r\n      title: 'ID',\r\n      dataIndex: 'id',\r\n      key: 'id',\r\n    },\r\n    {\r\n      title: '模型名称',\r\n      dataIndex: 'model_name',\r\n      key: 'model_name',\r\n    },\r\n    {\r\n      title: '模型ID',\r\n      dataIndex: 'model_id',\r\n      key: 'model_id',\r\n    },\r\n    {\r\n      title: '提供商',\r\n      dataIndex: 'provider',\r\n      key: 'provider',\r\n    },\r\n    {\r\n      title: 'API端点',\r\n      dataIndex: 'api_endpoint',\r\n      key: 'api_endpoint',\r\n      ellipsis: true,\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'is_active',\r\n      key: 'is_active',\r\n      render: (is_active) => (is_active ? '激活' : '禁用')\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      render: (_, record) => (\r\n        <>\r\n          <Button \r\n            type=\"primary\" \r\n            icon={<EditOutlined />} \r\n            style={{ marginRight: 8 }} \r\n            onClick={() => handleEditAiConfig(record)}\r\n          >\r\n            编辑\r\n          </Button>\r\n          <Button \r\n            danger \r\n            icon={<DeleteOutlined />} \r\n            onClick={() => {\r\n              Modal.confirm({\r\n                title: '确认删除',\r\n                content: `确定要删除AI配置 \"${record.model_name}\" 吗？`,\r\n                okText: '确定',\r\n                cancelText: '取消',\r\n                onOk: () => handleDeleteAiConfig(record.id)\r\n              });\r\n            }}\r\n          >\r\n            删除\r\n          </Button>\r\n        </>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 导航到学校管理页面\r\n  const navigateToSchoolManagement = () => {\r\n    navigate('/schools');\r\n  };\r\n\r\n  // 渲染统计面板\r\n  const renderStatsDashboard = () => {\r\n    return (\r\n      <div className=\"stats-dashboard\">\r\n        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\r\n          <Col span={24}>\r\n            <Title level={3}>系统概览</Title>\r\n          </Col>\r\n        </Row>\r\n        \r\n        {statsError && (\r\n          <Alert \r\n            message=\"获取统计数据失败\" \r\n            description={statsError} \r\n            type=\"error\" \r\n            showIcon \r\n            style={{ marginBottom: 16 }} \r\n          />\r\n        )}\r\n        \r\n        <Spin spinning={statsLoading}>\r\n          <Row gutter={[16, 16]}>\r\n            <Col xs={24} sm={12} md={8} lg={6}>\r\n              <Card>\r\n                <Statistic\r\n                  title=\"班级总数\"\r\n                  value={teacherStats?.class_count || 0}\r\n                  prefix={<TeamOutlined />}\r\n                />\r\n              </Card>\r\n            </Col>\r\n            <Col xs={24} sm={12} md={8} lg={6}>\r\n              <Card>\r\n                <Statistic\r\n                  title=\"学生总数\"\r\n                  value={teacherStats?.student_count || 0}\r\n                  prefix={<UserOutlined />}\r\n                />\r\n              </Card>\r\n            </Col>\r\n            <Col xs={24} sm={12} md={8} lg={6}>\r\n              <Card>\r\n                <Statistic\r\n                  title=\"作业总数\"\r\n                  value={teacherStats?.homework_count || 0}\r\n                  prefix={<BookOutlined />}\r\n                />\r\n              </Card>\r\n            </Col>\r\n            <Col xs={24} sm={12} md={8} lg={6}>\r\n              <Card>\r\n                <Statistic\r\n                  title=\"已批改作业数\"\r\n                  value={teacherStats?.corrected_count || 0}\r\n                  prefix={<BookFilled />}\r\n                />\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n          \r\n          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>\r\n            <Col span={24}>\r\n              <Card title=\"系统管理\">\r\n                <p>系统配置与监控信息</p>\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n        </Spin>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 添加注册设置组件\r\n  const renderRegistrationSettings = () => {\r\n    const globalEnabled = registrationSettings.global_registration_enabled;\r\n\r\n    return (\r\n      <div>\r\n        {/* 总开关 */}\r\n        <Card title=\"总注册控制\" bordered={false} style={{ marginBottom: 16 }}>\r\n          <Spin spinning={settingsLoading}>\r\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '16px 0' }}>\r\n              <div>\r\n                <Typography.Title level={4} style={{ margin: 0, color: globalEnabled ? '#52c41a' : '#ff4d4f' }}>\r\n                  系统注册总开关\r\n                </Typography.Title>\r\n                <Typography.Text type=\"secondary\">\r\n                  {globalEnabled ?\r\n                    '系统注册功能已开启，可以通过下方设置控制各角色的注册权限' :\r\n                    '系统注册功能已关闭，所有角色都无法注册，只能由管理员添加用户'}\r\n                </Typography.Text>\r\n              </div>\r\n              <Switch\r\n                size=\"large\"\r\n                checked={globalEnabled}\r\n                onChange={updateGlobalRegistrationSwitch}\r\n                checkedChildren=\"开启\"\r\n                unCheckedChildren=\"关闭\"\r\n              />\r\n            </div>\r\n          </Spin>\r\n        </Card>\r\n\r\n        <Card title=\"基本注册设置\" bordered={false}>\r\n          <Spin spinning={settingsLoading}>\r\n            {advancedSettings ? (\r\n              <Row gutter={[24, 24]}>\r\n                {Object.keys(advancedSettings.roles).map(roleName => {\r\n                  const roleEnabled = globalEnabled && advancedSettings.roles[roleName].enabled;\r\n                  return (\r\n                    <Col span={12} key={roleName}>\r\n                      <Card\r\n                        type=\"inner\"\r\n                        title={`${getRoleDisplayName(roleName)}注册控制`}\r\n                        style={{ opacity: globalEnabled ? 1 : 0.6 }}\r\n                      >\r\n                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                          <Typography.Text strong>{`允许${getRoleDisplayName(roleName)}注册：`}</Typography.Text>\r\n                          <Switch\r\n                            checked={advancedSettings.roles[roleName].enabled}\r\n                            onChange={(checked) => updateRoleConfig(roleName, 'enabled', checked)}\r\n                            disabled={!globalEnabled}\r\n                          />\r\n                        </div>\r\n                        <Typography.Text type=\"secondary\" style={{ display: 'block', marginTop: 16 }}>\r\n                          {!globalEnabled ?\r\n                            '总开关已关闭，该角色无法注册' :\r\n                            (advancedSettings.roles[roleName].enabled ?\r\n                              `${getRoleDisplayName(roleName)}可以自由注册账号` :\r\n                              `${getRoleDisplayName(roleName)}注册功能已关闭，只能由管理员添加账号`)\r\n                          }\r\n                        </Typography.Text>\r\n                      </Card>\r\n                    </Col>\r\n                  );\r\n                })}\r\n              </Row>\r\n            ) : (\r\n              <Row gutter={24}>\r\n                <Col span={12}>\r\n                  <Card\r\n                    type=\"inner\"\r\n                    title=\"学生注册控制\"\r\n                    style={{ opacity: globalEnabled ? 1 : 0.6 }}\r\n                  >\r\n                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                      <Typography.Text strong>允许学生注册：</Typography.Text>\r\n                      <Switch\r\n                        checked={registrationSettings.allow_student_registration}\r\n                        onChange={(checked) => updateRegistrationSettings('allow_student_registration', checked)}\r\n                        disabled={!globalEnabled}\r\n                      />\r\n                    </div>\r\n                    <Typography.Text type=\"secondary\" style={{ display: 'block', marginTop: 16 }}>\r\n                      {!globalEnabled ?\r\n                        '总开关已关闭，学生无法注册' :\r\n                        (registrationSettings.allow_student_registration ?\r\n                          '学生可以自由注册账号' :\r\n                          '学生注册功能已关闭，只能由管理员添加学生账号')\r\n                      }\r\n                    </Typography.Text>\r\n                  </Card>\r\n                </Col>\r\n                <Col span={12}>\r\n                  <Card\r\n                    type=\"inner\"\r\n                    title=\"教师注册控制\"\r\n                    style={{ opacity: globalEnabled ? 1 : 0.6 }}\r\n                  >\r\n                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                      <Typography.Text strong>允许教师注册：</Typography.Text>\r\n                      <Switch\r\n                        checked={registrationSettings.allow_teacher_registration}\r\n                        onChange={(checked) => updateRegistrationSettings('allow_teacher_registration', checked)}\r\n                        disabled={!globalEnabled}\r\n                      />\r\n                    </div>\r\n                    <Typography.Text type=\"secondary\" style={{ display: 'block', marginTop: 16 }}>\r\n                      {!globalEnabled ?\r\n                        '总开关已关闭，教师无法注册' :\r\n                        (registrationSettings.allow_teacher_registration ?\r\n                          '教师可以自由注册账号' :\r\n                          '教师注册功能已关闭，只能由管理员添加教师账号')\r\n                      }\r\n                    </Typography.Text>\r\n                  </Card>\r\n                </Col>\r\n              </Row>\r\n            )}\r\n          </Spin>\r\n        </Card>\r\n\r\n        {advancedSettings && (\r\n          <Card\r\n            title=\"高级注册设置\"\r\n            bordered={false}\r\n            style={{\r\n              marginTop: 16,\r\n              opacity: globalEnabled ? 1 : 0.6\r\n            }}\r\n          >\r\n            <Spin spinning={settingsLoading}>\r\n              {!globalEnabled && (\r\n                <Alert\r\n                  message=\"总注册开关已关闭\"\r\n                  description=\"请先开启总注册开关才能进行高级设置\"\r\n                  type=\"warning\"\r\n                  showIcon\r\n                  style={{ marginBottom: 16 }}\r\n                />\r\n              )}\r\n              <Collapse defaultActiveKey={['student', 'teacher']}>\r\n                {Object.keys(advancedSettings.roles).map(roleName => {\r\n                  const roleActuallyEnabled = globalEnabled && advancedSettings.roles[roleName].enabled;\r\n                  return (\r\n                    <Collapse.Panel\r\n                      key={roleName}\r\n                      header={\r\n                        <div style={{ display: 'flex', alignItems: 'center' }}>\r\n                          <span>{getRoleDisplayName(roleName)}</span>\r\n                          <Tag color={roleActuallyEnabled ? 'green' : 'red'} style={{ marginLeft: 8 }}>\r\n                            {roleActuallyEnabled ? '已启用' : '已禁用'}\r\n                          </Tag>\r\n                          {!globalEnabled && (\r\n                            <Tag color=\"orange\" style={{ marginLeft: 8 }}>总开关关闭</Tag>\r\n                          )}\r\n                          {advancedSettings.roles[roleName].requires_approval && (\r\n                            <Tag color=\"blue\" style={{ marginLeft: 8 }}>需要审核</Tag>\r\n                          )}\r\n                        </div>\r\n                      }\r\n                      disabled={!globalEnabled}\r\n                    >\r\n                    <Row gutter={[16, 16]}>\r\n                      <Col span={8}>\r\n                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                          <Typography.Text strong>启用注册：</Typography.Text>\r\n                          <Switch\r\n                            checked={advancedSettings.roles[roleName].enabled}\r\n                            onChange={(checked) => updateRoleConfig(roleName, 'enabled', checked)}\r\n                            disabled={!globalEnabled}\r\n                          />\r\n                        </div>\r\n                      </Col>\r\n                      <Col span={8}>\r\n                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                          <Typography.Text strong>需要审核：</Typography.Text>\r\n                          <Switch\r\n                            checked={advancedSettings.roles[roleName].requires_approval}\r\n                            onChange={(checked) => updateRoleConfig(roleName, 'requires_approval', checked)}\r\n                            disabled={!globalEnabled}\r\n                          />\r\n                        </div>\r\n                      </Col>\r\n                      {advancedSettings.roles[roleName].requires_approval && (\r\n                        <Col span={8}>\r\n                          <div style={{ display: 'flex', alignItems: 'center' }}>\r\n                            <Typography.Text strong style={{ marginRight: 8 }}>审核级别：</Typography.Text>\r\n                            <Typography.Text>{getValueDisplayText('approval_level', advancedSettings.roles[roleName].approval_level)}</Typography.Text>\r\n                            <Tooltip title=\"审核级别决定由谁来审核该角色的注册申请\">\r\n                              <InfoCircleOutlined style={{ marginLeft: 8, color: '#1890ff' }} />\r\n                            </Tooltip>\r\n                          </div>\r\n                        </Col>\r\n                      )}\r\n                    </Row>\r\n                    \r\n                    <Divider orientation=\"left\">字段设置</Divider>\r\n                    <Row gutter={[16, 16]}>\r\n                      <Col span={8}>\r\n                        <Card size=\"small\" title=\"学校\">\r\n                          <div>必填：{advancedSettings.roles[roleName].fields?.school?.required ? '是' : '否'}</div>\r\n                        </Card>\r\n                      </Col>\r\n                      <Col span={8}>\r\n                        <Card size=\"small\" title=\"班级\">\r\n                          <div>必填：{advancedSettings.roles[roleName].fields?.class?.required ? '是' : '否'}</div>\r\n                          {advancedSettings.roles[roleName].fields?.class?.max && (\r\n                            <div>最多选择：{advancedSettings.roles[roleName].fields.class.max} 个</div>\r\n                          )}\r\n                        </Card>\r\n                      </Col>\r\n                      <Col span={8}>\r\n                        <Card size=\"small\" title=\"科目\">\r\n                          <div>必填：{advancedSettings.roles[roleName].fields?.subject?.required ? '是' : '否'}</div>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n                    \r\n                    {roleName === 'parent' && (\r\n                      <div style={{ marginTop: 16 }}>\r\n                        <Alert \r\n                          type=\"info\" \r\n                          message=\"家长注册需要绑定学生\" \r\n                          description={`家长注册时${advancedSettings.roles.parent.requires_student_binding ? '需要' : '不需要'}绑定学生`} \r\n                        />\r\n                      </div>\r\n                    )}\r\n                  </Collapse.Panel>\r\n                  );\r\n                })}\r\n              </Collapse>\r\n              \r\n              <Card style={{ marginTop: 16 }}>\r\n                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                  <Typography.Text strong>允许创建新学校：</Typography.Text>\r\n                  <Switch \r\n                    checked={advancedSettings.allow_school_creation} \r\n                    onChange={(checked) => {\r\n                      const updatedAdvanced = { ...advancedSettings, allow_school_creation: checked };\r\n                      setAdvancedSettings(updatedAdvanced);\r\n                      // 更新学校创建设置\r\n                      updateSchoolCreationSetting(checked);\r\n                    }}\r\n                  />\r\n                </div>\r\n                <Typography.Text type=\"secondary\" style={{ display: 'block', marginTop: 8 }}>\r\n                  {advancedSettings.allow_school_creation ? \r\n                    '用户可以在注册时创建新学校（需要审核）' : \r\n                    '用户只能选择已有学校，不能创建新学校'}\r\n                </Typography.Text>\r\n              </Card>\r\n            </Spin>\r\n          </Card>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`admin-page system-page ${isMobile ? 'mobile' : 'desktop'}`}\r\n      style={{\r\n        width: '100%',\r\n        minHeight: '100vh',\r\n        background: isMobile ? '#f5f5f5' : 'transparent'\r\n      }}\r\n    >\r\n      <Layout style={{ background: 'transparent' }}>\r\n        {/* 移除整个侧边栏 */}\r\n        <Layout style={{\r\n          padding: isMobile ? '12px 4px' : '0 24px 24px',\r\n          background: 'transparent'\r\n        }}>\r\n          <div style={{\r\n            margin: isMobile ? '0' : '16px 0',\r\n            padding: isMobile ? '0' : '16px',\r\n            background: isMobile ? 'transparent' : '#fff',\r\n            borderRadius: isMobile ? '0' : '8px'\r\n          }}>\r\n            {isMobile && (\r\n              <div style={{\r\n                marginBottom: '16px',\r\n                padding: '16px',\r\n                background: 'white',\r\n                borderRadius: '12px',\r\n                textAlign: 'center'\r\n              }}>\r\n                <Title level={4} style={{ margin: 0, color: '#1890ff' }}>\r\n                  🛠️ 管理员控制台\r\n                </Title>\r\n              </div>\r\n            )}\r\n\r\n            <Content style={{\r\n              padding: isMobile ? '0' : '12px 0',\r\n              minHeight: 'calc(100vh - 200px)',\r\n              background: 'transparent'\r\n            }}>\r\n              <Tabs\r\n                activeKey={activeTab}\r\n                onChange={(key) => {\r\n                  setActiveTab(key);\r\n                  navigate(`/admin?tab=${key}`, { replace: true });\r\n                }}\r\n                type={isMobile ? \"card\" : \"line\"}\r\n                size={isMobile ? \"small\" : \"default\"}\r\n                tabPosition={isMobile ? \"top\" : \"top\"}\r\n                style={{\r\n                  background: 'white',\r\n                  borderRadius: '12px',\r\n                  overflow: 'hidden'\r\n                }}\r\n              >\r\n                <TabPane tab={\r\n                  <span style={{ fontSize: isMobile ? '12px' : '14px' }}>\r\n                    <SettingOutlined />\r\n                    {isMobile ? 'AI配置' : 'AI配置管理'}\r\n                  </span>\r\n                } key=\"1\">\r\n                  <div style={{ marginBottom: 16 }}>\r\n                    <Button\r\n                      type=\"primary\"\r\n                      icon={<PlusOutlined />}\r\n                      onClick={handleAddAiConfig}\r\n                      size={isMobile ? \"large\" : \"default\"}\r\n                      style={isMobile ? { height: '44px', fontSize: '16px' } : {}}\r\n                    >\r\n                      添加AI配置\r\n                    </Button>\r\n                  </div>\r\n                  <Table\r\n                    columns={aiConfigColumns}\r\n                    dataSource={aiConfigs}\r\n                    rowKey=\"id\"\r\n                    loading={loading}\r\n                    pagination={{\r\n                      pageSize: isMobile ? 5 : 10,\r\n                      showSizeChanger: !isMobile,\r\n                      showQuickJumper: !isMobile,\r\n                      simple: isMobile\r\n                    }}\r\n                    scroll={isMobile ? { x: 800 } : undefined}\r\n                  />\r\n                  {!isMobile && (\r\n                    <div style={{ marginTop: 16 }}>\r\n                      {aiConfigs.length === 0 && !loading ? (\r\n                        <div style={{ textAlign: 'center', padding: '20px', backgroundColor: '#f9f9f9', borderRadius: '4px' }}>\r\n                          <p>暂无AI配置数据</p>\r\n                        </div>\r\n                      ) : null}\r\n                      <div>\r\n                        <h3>当前AI配置信息：</h3>\r\n                        <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px', maxHeight: '200px', overflow: 'auto' }}>\r\n                          {JSON.stringify(aiConfigs, null, 2)}\r\n                        </pre>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </TabPane>\r\n                \r\n                <TabPane tab={\r\n                  <span style={{ fontSize: isMobile ? '12px' : '14px' }}>\r\n                    <BookFilled />\r\n                    {isMobile ? '科目管理' : '作业科目管理'}\r\n                  </span>\r\n                } key=\"2\">\r\n                  <SubjectManagement isMobile={isMobile} />\r\n                </TabPane>\r\n\r\n                <TabPane tab={\r\n                  <span style={{ fontSize: isMobile ? '12px' : '14px' }}>\r\n                    <UserSwitchOutlined />\r\n                    {isMobile ? '注册设置' : '注册设置'}\r\n                  </span>\r\n                } key=\"3\">\r\n                  {renderRegistrationSettings()}\r\n                </TabPane>\r\n\r\n                <TabPane tab={\r\n                  <span style={{ fontSize: isMobile ? '12px' : '14px' }}>\r\n                    <AuditOutlined />\r\n                    {isMobile ? '注册审批' : '注册审批'}\r\n                  </span>\r\n                } key=\"4\">\r\n                  <RegistrationApproval isMobile={isMobile} />\r\n                </TabPane>\r\n\r\n                <TabPane tab={\r\n                  <span style={{ fontSize: isMobile ? '12px' : '14px' }}>\r\n                    <BankOutlined />\r\n                    {isMobile ? '学校申请' : '学校申请'}\r\n                  </span>\r\n                } key=\"5\">\r\n                  <SchoolApplicationReview isMobile={isMobile} />\r\n                </TabPane>\r\n\r\n                <TabPane tab={<span><SafetyCertificateOutlined />科目权限管理</span>} key=\"6\">\r\n                  <Card title=\"角色科目权限管理\">\r\n                    <SubjectPermissionManager />\r\n                  </Card>\r\n                </TabPane>\r\n\r\n                <TabPane tab={\r\n                  <span style={{ fontSize: isMobile ? '12px' : '14px' }}>\r\n                    <SettingOutlined />\r\n                    {isMobile ? '家长端管理' : '家长端功能管理'}\r\n                  </span>\r\n                } key=\"7\">\r\n                  <ParentFeatureManager isMobile={isMobile} />\r\n                </TabPane>\r\n              </Tabs>\r\n            </Content>\r\n          </div>\r\n        </Layout>\r\n      </Layout>\r\n      \r\n      {/* AI助手抽屉 */}\r\n      <Drawer\r\n        title=\"AI智能助手\"\r\n        placement=\"right\"\r\n        onClose={() => setAiDrawerVisible(false)}\r\n        open={aiDrawerVisible}\r\n        width={400}\r\n      >\r\n        <AIAssistant onClose={() => setAiDrawerVisible(false)} />\r\n      </Drawer>\r\n\r\n      {/* 用户表单对话框 */}\r\n      <Modal\r\n        title={editingUser ? '编辑用户' : '添加用户'}\r\n        open={userModalVisible}\r\n        onCancel={() => setUserModalVisible(false)}\r\n        footer={null}\r\n      >\r\n        <Form\r\n          form={form}\r\n          layout=\"vertical\"\r\n          onFinish={handleUserFormSubmit}\r\n        >\r\n          <Form.Item\r\n            name=\"username\"\r\n            label=\"用户名\"\r\n            rules={[{ required: true, message: '请输入用户名' }]}\r\n          >\r\n            <Input placeholder=\"请输入用户名\" />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"email\"\r\n            label=\"邮箱\"\r\n            rules={[\r\n              { required: true, message: '请输入邮箱' },\r\n              { type: 'email', message: '请输入有效的邮箱地址' }\r\n            ]}\r\n          >\r\n            <Input placeholder=\"请输入邮箱\" />\r\n          </Form.Item>\r\n\r\n          {!editingUser && (\r\n            <Form.Item\r\n              name=\"password\"\r\n              label=\"密码\"\r\n              rules={[{ required: !editingUser, message: '请输入密码' }]}\r\n            >\r\n              <Input.Password placeholder=\"请输入密码\" />\r\n            </Form.Item>\r\n          )}\r\n\r\n          {editingUser && (\r\n            <Form.Item\r\n              name=\"password\"\r\n              label=\"密码 (留空表示不修改)\"\r\n            >\r\n              <Input.Password placeholder=\"请输入新密码\" />\r\n            </Form.Item>\r\n          )}\r\n\r\n          <Form.Item\r\n            name=\"full_name\"\r\n            label=\"姓名\"\r\n          >\r\n            <Input placeholder=\"请输入姓名\" />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"is_teacher\"\r\n            valuePropName=\"checked\"\r\n          >\r\n            <Checkbox>教师权限</Checkbox>\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"is_admin\"\r\n            valuePropName=\"checked\"\r\n          >\r\n            <Checkbox>管理员权限</Checkbox>\r\n          </Form.Item>\r\n\r\n          <Form.Item style={{ marginBottom: 0 }}>\r\n            <Button type=\"primary\" htmlType=\"submit\" style={{ marginRight: 8 }}>\r\n              保存\r\n            </Button>\r\n            <Button onClick={() => setUserModalVisible(false)}>\r\n              取消\r\n            </Button>\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* AI配置表单对话框 */}\r\n      <Modal\r\n        title={editingAiConfig ? '编辑AI配置' : '添加AI配置'}\r\n        open={aiConfigModalVisible}\r\n        onCancel={() => setAiConfigModalVisible(false)}\r\n        footer={null}\r\n      >\r\n        <Form\r\n          form={aiConfigForm}\r\n          layout=\"vertical\"\r\n          onFinish={handleAiConfigFormSubmit}\r\n        >\r\n          <Form.Item\r\n            name=\"model_name\"\r\n            label=\"模型名称\"\r\n            rules={[{ required: true, message: '请输入模型名称' }]}\r\n          >\r\n            <Input placeholder=\"请输入模型名称，例如 Doubao-1.5-vision-pro\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"model_id\"\r\n            label=\"模型ID\"\r\n            rules={[{ required: true, message: '请输入模型ID' }]}\r\n          >\r\n            <Input placeholder=\"请输入模型ID，例如 ep-xxxxxxxxxx\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"provider\"\r\n            label=\"提供商\"\r\n            rules={[{ required: true, message: '请选择提供商' }]}\r\n          >\r\n            <Select placeholder=\"请选择提供商\">\r\n              <Select.Option value=\"volcano\">火山引擎</Select.Option>\r\n              <Select.Option value=\"ollama\">Ollama</Select.Option>\r\n              <Select.Option value=\"qianwen\">通义千问</Select.Option>\r\n            </Select>\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"api_key\"\r\n            label=\"API 密钥\"\r\n            rules={[{ required: true, message: '请输入API密钥' }]}\r\n          >\r\n            <Input.Password placeholder=\"请输入API密钥\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"api_endpoint\"\r\n            label=\"API端点\"\r\n            rules={[{ required: true, message: '请输入API端点' }]}\r\n          >\r\n            <Input placeholder=\"请输入API端点，例如 https://api.volcengine.com/v1/chat/completions\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"is_active\"\r\n            valuePropName=\"checked\"\r\n            initialValue={true}\r\n          >\r\n            <Switch defaultChecked /> 激活\r\n          </Form.Item>\r\n          \r\n          <Form.Item style={{ marginBottom: 0 }}>\r\n            <Button type=\"primary\" htmlType=\"submit\" style={{ marginRight: 8 }}>\r\n              保存\r\n            </Button>\r\n            <Button onClick={() => setAiConfigModalVisible(false)}>\r\n              取消\r\n            </Button>\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdminDashboard; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,QAAQ,MAAM;AACjP,SAASC,YAAY,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,YAAY,EAAEC,YAAY,EAAEC,UAAU,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,yBAAyB,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,yBAAyB,QAAQ,mBAAmB;AACtV,SAASC,WAAW,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACjE,SAASC,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,cAAc;AACrG,OAAOC,GAAG,MAAM,cAAc;AAC9B,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,oBAAoB,MAAM,oCAAoC;AACrE,OAAOC,uBAAuB,MAAM,uCAAuC;AAC3E,OAAOC,wBAAwB,MAAM,wCAAwC;AAC7E,OAAOC,oBAAoB,MAAM,oCAAoC;AAAC,SAAAC,QAAA,IAAAC,SAAA,EAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAM;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAM,CAAC,GAAGpE,MAAM;AACzC,MAAM;EAAEqE;AAAM,CAAC,GAAGxD,UAAU;AAC5B,MAAM;EAAEyD;AAAQ,CAAC,GAAG1D,IAAI;AACxB,MAAM;EAAE2D;AAAO,CAAC,GAAG/D,MAAM;AAEzB,MAAMgE,cAAc,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAMC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM+B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC,GAAG,CAAC;EAC/C,MAAM,CAACkF,KAAK,EAAEC,QAAQ,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoF,SAAS,EAAEC,YAAY,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsF,UAAU,EAAEC,aAAa,CAAC,GAAGvF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwF,OAAO,EAAEC,UAAU,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4F,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC8F,WAAW,EAAEC,cAAc,CAAC,GAAG/F,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgG,eAAe,EAAEC,kBAAkB,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkG,IAAI,CAAC,GAAG1F,IAAI,CAAC2F,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,YAAY,CAAC,GAAG5F,IAAI,CAAC2F,OAAO,CAAC,CAAC;EACrC;EACA,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAGtG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuG,QAAQ,EAAEC,WAAW,CAAC,GAAGxG,QAAQ,CAACyG,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;;EAElE;EACAzG,SAAS,CAAC,MAAM;IACd,MAAM0G,YAAY,GAAGA,CAAA,KAAM;MACzBH,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAEDD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EACN,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG/G,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgH,YAAY,EAAEC,eAAe,CAAC,GAAGjH,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkH,UAAU,EAAEC,aAAa,CAAC,GAAGnH,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACoH,eAAe,EAAEC,kBAAkB,CAAC,GAAGrH,QAAQ,CAAC,KAAK,CAAC;EAC7D;EACA,MAAM,CAACsH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvH,QAAQ,CAAC;IAC/DwH,2BAA2B,EAAE,IAAI;IACjCC,0BAA0B,EAAE,IAAI;IAChCC,0BAA0B,EAAE;EAC9B,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5H,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC6H,eAAe,EAAEC,kBAAkB,CAAC,GAAG9H,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM+H,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM3E,GAAG,CAAC4E,GAAG,CAAC,oBAAoB,CAAC;MACpD7C,aAAa,CAAC4C,QAAQ,CAACE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC1H,OAAO,CAAC0H,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMR,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM3E,GAAG,CAAC4E,GAAG,CAAC,cAAc,CAAC;MAC9CjD,QAAQ,CAACgD,QAAQ,CAACE,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC1H,OAAO,CAAC0H,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMG,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFhD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM4C,IAAI,GAAG,MAAMjF,YAAY,CAAC,CAAC;MACjCmF,OAAO,CAACG,GAAG,CAAC,WAAW,EAAEL,IAAI,CAAC;MAC9BhD,YAAY,CAACgD,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC1H,OAAO,CAAC0H,KAAK,CAAC,YAAY,IAAIA,KAAK,CAACK,MAAM,IAAIL,KAAK,CAAC1H,OAAO,IAAI,MAAM,CAAC,CAAC;IACzE,CAAC,SAAS;MACR6E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC3B,eAAe,CAAC,IAAI,CAAC;IACrBE,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF;MACA,MAAMa,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACAK,OAAO,CAACG,GAAG,CAAC,aAAa,CAAC;;MAE1B;MACA,MAAMG,eAAe,GAAG,MAAMrF,GAAG,CAAC4E,GAAG,CAAC,qBAAqB,CAAC;MAC5D,MAAMU,eAAe,GAAG,MAAMtF,GAAG,CAAC4E,GAAG,CAAC,qBAAqB,CAAC;MAE5DG,OAAO,CAACG,GAAG,CAAC,aAAa,EAAEK,IAAI,CAACC,SAAS,CAACH,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MACpEN,OAAO,CAACG,GAAG,CAAC,aAAa,EAAEK,IAAI,CAACC,SAAS,CAACF,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAEpE;MACA,IAAI,CAACD,eAAe,CAACI,cAAc,CAAC,gBAAgB,CAAC,EAAE;QACrDV,OAAO,CAACW,IAAI,CAAC,0BAA0B,CAAC;MAC1C;MAEA,IAAI,CAACL,eAAe,CAACI,cAAc,CAAC,iBAAiB,CAAC,EAAE;QACtDV,OAAO,CAACW,IAAI,CAAC,2BAA2B,CAAC;MAC3C;;MAEA;MACA,IAAI;QACF,MAAMC,qBAAqB,GAAG,MAAM3F,GAAG,CAAC4E,GAAG,CAAC,0BAA0B,CAAC;QAEvE,IAAIgB,KAAK,CAACC,OAAO,CAACF,qBAAqB,CAAC,EAAE;UACxC;UACAN,eAAe,CAACS,WAAW,GAAGH,qBAAqB,CAACI,MAAM;UAC1DhB,OAAO,CAACG,GAAG,CAAC,oBAAoBG,eAAe,CAACS,WAAW,EAAE,CAAC;QAChE;MACF,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBjB,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEkB,UAAU,CAAC;;QAExC;QACA,IAAI;UACF,MAAMC,UAAU,GAAG,MAAMjG,GAAG,CAAC4E,GAAG,CAAC,cAAc,EAAE;YAC/CsB,MAAM,EAAE;cACNC,KAAK,EAAE,uDAAuD,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;YACpF;UACF,CAAC,CAAC;UAEF,IAAIJ,UAAU,CAACpB,IAAI,IAAIoB,UAAU,CAACpB,IAAI,CAACyB,MAAM,IAAIL,UAAU,CAACpB,IAAI,CAACyB,MAAM,CAAC,CAAC,CAAC,EAAE;YAC1EjB,eAAe,CAACR,IAAI,CAACiB,WAAW,GAAGS,QAAQ,CAACN,UAAU,CAACpB,IAAI,CAACyB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzEvB,OAAO,CAACG,GAAG,CAAC,2BAA2BG,eAAe,CAACR,IAAI,CAACiB,WAAW,EAAE,CAAC;UAC5E;QACF,CAAC,CAAC,OAAOU,OAAO,EAAE;UAChBzB,OAAO,CAACD,KAAK,CAAC,cAAc,EAAE0B,OAAO,CAAC;QACxC;MACF;;MAEA;MACA1D,eAAe,CAACuC,eAAe,CAAC;MAChC9B,eAAe,CAAC+B,eAAe,CAAC;IAClC,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAA2B,eAAA,EAAAC,oBAAA;MACd3B,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCnB,aAAa,CAAC,YAAY,IAAI,EAAA8C,eAAA,GAAA3B,KAAK,CAACH,QAAQ,cAAA8B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB5B,IAAI,cAAA6B,oBAAA,uBAApBA,oBAAA,CAAsBvB,MAAM,KAAIL,KAAK,CAAC1H,OAAO,IAAI,MAAM,CAAC,CAAC;;MAEvF;MACA0F,eAAe,CAAC;QACdgD,WAAW,EAAE,CAAC;QACda,aAAa,EAAE,CAAC;QAChBC,cAAc,EAAE,CAAC;QACjBC,eAAe,EAAE;MACnB,CAAC,CAAC;MAEFtD,eAAe,CAAC;QACduD,oBAAoB,EAAE,CAAC;QACvBC,wBAAwB,EAAE,CAAC;QAC3BC,gBAAgB,EAAE,CAAC;QACnBC,oBAAoB,EAAE;MACxB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRxD,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMyD,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI;MACF5C,kBAAkB,CAAC,IAAI,CAAC;MACxB,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMyC,cAAc,GAAG,MAAMnH,GAAG,CAAC4E,GAAG,CAAC,qCAAqC,CAAC;MAC3EG,OAAO,CAACG,GAAG,CAAC,aAAa,EAAEiC,cAAc,CAAC;MAC1CpD,uBAAuB,CAACoD,cAAc,CAAC;;MAEvC;MACA,MAAMC,gBAAgB,GAAG,MAAMpH,GAAG,CAAC4E,GAAG,CAAC,8CAA8C,CAAC;MACtFG,OAAO,CAACG,GAAG,CAAC,aAAa,EAAEkC,gBAAgB,CAAC;MAC5ChD,mBAAmB,CAACgD,gBAAgB,CAAC;IACvC,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC1H,OAAO,CAAC0H,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRR,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM+C,8BAA8B,GAAG,MAAOC,OAAO,IAAK;IACxD,IAAI;MACFhD,kBAAkB,CAAC,IAAI,CAAC;MACxB,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3CK,OAAO,CAACG,GAAG,CAAC,cAAc,CAAC;MAC3BH,OAAO,CAACG,GAAG,CAAC,UAAU,EAAE,CAAC,CAACV,KAAK,CAAC;MAChCO,OAAO,CAACG,GAAG,CAAC,UAAU,EAAEV,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuB,MAAM,CAAC;MACtChB,OAAO,CAACG,GAAG,CAAC,OAAO,EAAEoC,OAAO,CAAC;MAC7BvC,OAAO,CAACG,GAAG,CAAC,QAAQ,EAAE,uDAAuD,CAAC;MAC9EH,OAAO,CAACG,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;MAC3BH,OAAO,CAACG,GAAG,CAAC,OAAO,EAAE;QAAEoC;MAAQ,CAAC,CAAC;MAEjC,MAAM3C,QAAQ,GAAG,MAAM3E,GAAG,CAACuH,GAAG,CAAC,mDAAmD,EAChF;QAAED;MAAQ,CACZ,CAAC;MAEDvC,OAAO,CAACG,GAAG,CAAC,OAAO,EAAEP,QAAQ,CAACE,IAAI,CAAC;;MAEnC;MACAd,uBAAuB,CAACyD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExD,2BAA2B,EAAEsD;MAAQ,CAAC,CAAC,CAAC;MACpF,IAAInD,gBAAgB,EAAE;QACpBC,mBAAmB,CAACoD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAExD,2BAA2B,EAAEsD;QAAQ,CAAC,CAAC,CAAC;MAClF;MAEAlK,OAAO,CAACqK,OAAO,CAAC,SAASH,OAAO,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOxC,KAAK,EAAE;MAAA,IAAA4C,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdhD,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCC,OAAO,CAACD,KAAK,CAAC,OAAO,GAAA4C,gBAAA,GAAE5C,KAAK,CAACH,QAAQ,cAAA+C,gBAAA,uBAAdA,gBAAA,CAAgB7C,IAAI,CAAC;MAC5CE,OAAO,CAACD,KAAK,CAAC,MAAM,GAAA6C,gBAAA,GAAE7C,KAAK,CAACH,QAAQ,cAAAgD,gBAAA,uBAAdA,gBAAA,CAAgBK,MAAM,CAAC;MAE7C,IAAIC,YAAY,GAAG,WAAW;MAC9B,IAAI,EAAAL,gBAAA,GAAA9C,KAAK,CAACH,QAAQ,cAAAiD,gBAAA,uBAAdA,gBAAA,CAAgBI,MAAM,MAAK,GAAG,EAAE;QAClCC,YAAY,GAAG,YAAY;MAC7B,CAAC,MAAM,IAAI,EAAAJ,gBAAA,GAAA/C,KAAK,CAACH,QAAQ,cAAAkD,gBAAA,uBAAdA,gBAAA,CAAgBG,MAAM,MAAK,GAAG,EAAE;QACzCC,YAAY,GAAG,SAAS;MAC1B,CAAC,MAAM,KAAAH,gBAAA,GAAIhD,KAAK,CAACH,QAAQ,cAAAmD,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjD,IAAI,cAAAkD,qBAAA,eAApBA,qBAAA,CAAsB5C,MAAM,EAAE;QACvC8C,YAAY,GAAGnD,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACM,MAAM;MAC3C;MAEA/H,OAAO,CAAC0H,KAAK,CAACmD,YAAY,CAAC;IAC7B,CAAC,SAAS;MACR3D,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM4D,0BAA0B,GAAG,MAAAA,CAAOC,GAAG,EAAEC,KAAK,KAAK;IACvD,IAAI;MACF9D,kBAAkB,CAAC,IAAI,CAAC;MACxB,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAM2D,eAAe,GAAG;QAAE,GAAGvE,oBAAoB;QAAE,CAACqE,GAAG,GAAGC;MAAM,CAAC;MAEjE,MAAMpI,GAAG,CAACuH,GAAG,CAAC,qCAAqC,EAAEc,eAAe,CAAC;MAErEtD,OAAO,CAACG,GAAG,CAAC,WAAW,EAAEmD,eAAe,CAAC;MACzCtE,uBAAuB,CAACsE,eAAe,CAAC;;MAExC;MACA,IAAIlE,gBAAgB,EAAE;QACpB,MAAMmE,eAAe,GAAG;UAAE,GAAGnE;QAAiB,CAAC;QAC/C,IAAIgE,GAAG,KAAK,4BAA4B,EAAE;UACxCG,eAAe,CAACC,KAAK,CAACC,OAAO,CAAClB,OAAO,GAAGc,KAAK;QAC/C,CAAC,MAAM,IAAID,GAAG,KAAK,4BAA4B,EAAE;UAC/CG,eAAe,CAACC,KAAK,CAACE,OAAO,CAACnB,OAAO,GAAGc,KAAK;QAC/C;QACAhE,mBAAmB,CAACkE,eAAe,CAAC;MACtC;MAEAlL,OAAO,CAACqK,OAAO,CAAC,GAAGU,GAAG,KAAK,4BAA4B,GAAG,IAAI,GAAG,IAAI,QAAQC,KAAK,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;IACrG,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC1H,OAAO,CAAC0H,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRR,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMoE,gBAAgB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,KAAK,EAAER,KAAK,KAAK;IACzD,IAAI;MACF9D,kBAAkB,CAAC,IAAI,CAAC;MACxB,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMmE,UAAU,GAAG;QAAE,CAACD,KAAK,GAAGR;MAAM,CAAC;;MAErC;MACA,MAAMpI,GAAG,CAACuH,GAAG,CAAC,sCAAsCoB,QAAQ,EAAE,EAAEE,UAAU,CAAC;;MAE3E;MACA,MAAMP,eAAe,GAAG;QAAE,GAAGnE;MAAiB,CAAC;MAC/CmE,eAAe,CAACC,KAAK,CAACI,QAAQ,CAAC,CAACC,KAAK,CAAC,GAAGR,KAAK;MAC9ChE,mBAAmB,CAACkE,eAAe,CAAC;;MAEpC;MACA,IAAIM,KAAK,KAAK,SAAS,EAAE;QACvB,IAAID,QAAQ,KAAK,SAAS,EAAE;UAC1B5E,uBAAuB,CAACyD,IAAI,KAAK;YAC/B,GAAGA,IAAI;YACPvD,0BAA0B,EAAEmE;UAC9B,CAAC,CAAC,CAAC;QACL,CAAC,MAAM,IAAIO,QAAQ,KAAK,SAAS,EAAE;UACjC5E,uBAAuB,CAACyD,IAAI,KAAK;YAC/B,GAAGA,IAAI;YACPtD,0BAA0B,EAAEkE;UAC9B,CAAC,CAAC,CAAC;QACL;MACF;MAEAhL,OAAO,CAACqK,OAAO,CAAC,GAAGqB,kBAAkB,CAACH,QAAQ,CAAC,IAAII,mBAAmB,CAACH,KAAK,CAAC,IAAII,mBAAmB,CAACJ,KAAK,EAAER,KAAK,CAAC,EAAE,CAAC;IACvH,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,KAAK6D,QAAQ,OAAO,EAAE7D,KAAK,CAAC;MAC1C1H,OAAO,CAAC0H,KAAK,CAAC,KAAKgE,kBAAkB,CAACH,QAAQ,CAAC,MAAM,CAAC;IACxD,CAAC,SAAS;MACRrE,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMwE,kBAAkB,GAAIH,QAAQ,IAAK;IACvC,MAAMM,WAAW,GAAG;MAClB,SAAS,EAAE,IAAI;MACf,SAAS,EAAE,IAAI;MACf,QAAQ,EAAE,IAAI;MACd,eAAe,EAAE,KAAK;MACtB,gBAAgB,EAAE,MAAM;MACxB,mBAAmB,EAAE,MAAM;MAC3B,gBAAgB,EAAE,KAAK;MACvB,WAAW,EAAE,IAAI;MACjB,cAAc,EAAE;IAClB,CAAC;IACD,OAAOA,WAAW,CAACN,QAAQ,CAAC,IAAIA,QAAQ;EAC1C,CAAC;;EAED;EACA,MAAMI,mBAAmB,GAAIH,KAAK,IAAK;IACrC,MAAMM,YAAY,GAAG;MACnB,SAAS,EAAE,MAAM;MACjB,mBAAmB,EAAE,MAAM;MAC3B,gBAAgB,EAAE;IACpB,CAAC;IACD,OAAOA,YAAY,CAACN,KAAK,CAAC,IAAIA,KAAK;EACrC,CAAC;;EAED;EACA,MAAMI,mBAAmB,GAAGA,CAACJ,KAAK,EAAER,KAAK,KAAK;IAC5C,IAAIQ,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,mBAAmB,EAAE;MACxD,OAAOR,KAAK,GAAG,IAAI,GAAG,IAAI;IAC5B;IACA,IAAIQ,KAAK,KAAK,gBAAgB,EAAE;MAC9B,MAAMO,gBAAgB,GAAG;QACvB,cAAc,EAAE,OAAO;QACvB,WAAW,EAAE,IAAI;QACjB,mBAAmB,EAAE,MAAM;QAC3B,aAAa,EAAE;MACjB,CAAC;MACD,OAAOA,gBAAgB,CAACf,KAAK,CAAC,IAAIA,KAAK;IACzC;IACA,OAAOA,KAAK;EACd,CAAC;;EAED;EACA,MAAMgB,2BAA2B,GAAG,MAAOhB,KAAK,IAAK;IACnD,IAAI;MACF9D,kBAAkB,CAAC,IAAI,CAAC;MACxB,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAM2D,eAAe,GAAG;QAAE,GAAGlE,gBAAgB;QAAEkF,qBAAqB,EAAEjB;MAAM,CAAC;;MAE7E;MACA,MAAMpI,GAAG,CAACuH,GAAG,CAAC,8CAA8C,EAAEc,eAAe,CAAC;MAE9EjL,OAAO,CAACqK,OAAO,CAAC,UAAUW,KAAK,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;IAClD,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC1H,OAAO,CAAC0H,KAAK,CAAC,YAAY,CAAC;MAC3B;MACAV,mBAAmB,CAACkF,YAAY,KAAK;QAAE,GAAGA;MAAa,CAAC,CAAC,CAAC;IAC5D,CAAC,SAAS;MACRhF,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA7H,SAAS,CAAC,MAAM;IACd,MAAM8M,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BtH,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMuH,OAAO,CAACC,GAAG,CAAC,CAAClF,eAAe,CAAC,CAAC,EAAES,UAAU,CAAC,CAAC,EAAEC,cAAc,CAAC,CAAC,EAAEG,eAAe,CAAC,CAAC,CAAC,CAAC;MAC3F,CAAC,CAAC,OAAON,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MACjC,CAAC,SAAS;QACR7C,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDsH,SAAS,CAAC,CAAC;;IAEX;IACA,MAAMG,SAAS,GAAG,IAAIC,eAAe,CAACpI,QAAQ,CAACqI,MAAM,CAAC;IACtD,MAAMC,QAAQ,GAAGH,SAAS,CAAC9E,GAAG,CAAC,KAAK,CAAC;IACrC,IAAIiF,QAAQ,EAAE;MACZpI,YAAY,CAACoI,QAAQ,CAAC;IACxB;;IAEA;IACA3C,yBAAyB,CAAC,CAAC;EAC7B,CAAC,EAAE,CAAC3F,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMuI,oBAAoB,GAAG,MAAOC,MAAM,IAAK;IAC7C,IAAI;MACF,MAAMvF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAIpC,WAAW,EAAE;QACf;QACA,MAAMtC,GAAG,CAACuH,GAAG,CAAC,gBAAgBjF,WAAW,CAAC0H,EAAE,EAAE,EAAED,MAAM,CAAC;QACvD3M,OAAO,CAACqK,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMzH,GAAG,CAACiK,IAAI,CAAC,WAAW,EAAEF,MAAM,CAAC;QACnC3M,OAAO,CAACqK,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEAtF,mBAAmB,CAAC,KAAK,CAAC;MAC1B6C,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOF,KAAK,EAAE;MAAA,IAAAoF,gBAAA,EAAAC,qBAAA;MACdpF,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B1H,OAAO,CAAC0H,KAAK,CAAC,QAAQ,IAAI,EAAAoF,gBAAA,GAAApF,KAAK,CAACH,QAAQ,cAAAuF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrF,IAAI,cAAAsF,qBAAA,uBAApBA,qBAAA,CAAsBhF,MAAM,KAAIL,KAAK,CAAC1H,OAAO,CAAC,CAAC;IAC3E;EACF,CAAC;;EAED;EACA,MAAMgN,wBAAwB,GAAG,MAAOL,MAAM,IAAK;IACjD,IAAI;MACF,IAAIvH,eAAe,EAAE;QACnB;QACA,MAAM1C,cAAc,CAAC0C,eAAe,CAACwH,EAAE,EAAED,MAAM,CAAC;QAChD3M,OAAO,CAACqK,OAAO,CAAC,UAAU,CAAC;MAC7B,CAAC,MAAM;QACL;QACA,MAAM5H,cAAc,CAACkK,MAAM,CAAC;QAC5B3M,OAAO,CAACqK,OAAO,CAAC,UAAU,CAAC;MAC7B;MAEApF,uBAAuB,CAAC,KAAK,CAAC;MAC9B4C,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B1H,OAAO,CAAC0H,KAAK,CAAC,QAAQ,IAAIA,KAAK,CAACK,MAAM,IAAIL,KAAK,CAAC1H,OAAO,IAAI,MAAM,CAAC,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAMiN,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACzC,IAAI;MACF,MAAM9F,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAM1E,GAAG,CAACuK,MAAM,CAAC,gBAAgBD,MAAM,EAAE,CAAC;MAC1ClN,OAAO,CAACqK,OAAO,CAAC,QAAQ,CAAC;MACzBzC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOF,KAAK,EAAE;MAAA,IAAA0F,gBAAA,EAAAC,qBAAA;MACd1F,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B1H,OAAO,CAAC0H,KAAK,CAAC,UAAU,IAAI,EAAA0F,gBAAA,GAAA1F,KAAK,CAACH,QAAQ,cAAA6F,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3F,IAAI,cAAA4F,qBAAA,uBAApBA,qBAAA,CAAsBtF,MAAM,KAAIL,KAAK,CAAC1H,OAAO,CAAC,CAAC;IAC7E;EACF,CAAC;;EAED;EACA,MAAMsN,oBAAoB,GAAG,MAAOC,QAAQ,IAAK;IAC/C,IAAI;MACF,MAAM5K,cAAc,CAAC4K,QAAQ,CAAC;MAC9BvN,OAAO,CAACqK,OAAO,CAAC,UAAU,CAAC;MAC3BxC,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC1H,OAAO,CAAC0H,KAAK,CAAC,YAAY,IAAIA,KAAK,CAACK,MAAM,IAAIL,KAAK,CAAC1H,OAAO,IAAI,MAAM,CAAC,CAAC;IACzE;EACF,CAAC;;EAED;EACA,MAAMwN,cAAc,GAAIzJ,IAAI,IAAK;IAC/BoB,cAAc,CAACpB,IAAI,CAAC;IACpBuB,IAAI,CAACmI,cAAc,CAAC;MAClBC,QAAQ,EAAE3J,IAAI,CAAC2J,QAAQ;MACvBC,KAAK,EAAE5J,IAAI,CAAC4J,KAAK;MACjBC,SAAS,EAAE7J,IAAI,CAAC6J,SAAS;MACzBC,KAAK,EAAE9J,IAAI,CAAC8J,KAAK;MACjBC,UAAU,EAAE/J,IAAI,CAAC+J,UAAU;MAC3BC,QAAQ,EAAEhK,IAAI,CAACgK,QAAQ;MACvBC,QAAQ,EAAE,EAAE;MAAE;MACdC,SAAS,EAAElK,IAAI,CAACkK;IAClB,CAAC,CAAC;IACFlJ,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMmJ,kBAAkB,GAAIC,MAAM,IAAK;IACrC9I,kBAAkB,CAAC8I,MAAM,CAAC;IAC1B3I,YAAY,CAACiI,cAAc,CAAC;MAC1BW,UAAU,EAAED,MAAM,CAACC,UAAU;MAC7BC,QAAQ,EAAEF,MAAM,CAACE,QAAQ;MACzBC,QAAQ,EAAEH,MAAM,CAACG,QAAQ;MACzBC,OAAO,EAAEJ,MAAM,CAACI,OAAO;MACvBC,YAAY,EAAEL,MAAM,CAACK,YAAY;MACjCC,SAAS,EAAEN,MAAM,CAACM;IACpB,CAAC,CAAC;IACFxJ,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMyJ,aAAa,GAAGA,CAAA,KAAM;IAC1BvJ,cAAc,CAAC,IAAI,CAAC;IACpBG,IAAI,CAACqJ,WAAW,CAAC,CAAC;IAClB5J,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM6J,iBAAiB,GAAGA,CAAA,KAAM;IAC9BvJ,kBAAkB,CAAC,IAAI,CAAC;IACxBG,YAAY,CAACmJ,WAAW,CAAC,CAAC;IAC1B1J,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM4J,WAAW,GAAG,CAClB;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfhE,GAAG,EAAE;EACP,CAAC,EACD;IACE+D,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBhE,GAAG,EAAE;EACP,CAAC,EACD;IACE+D,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBhE,GAAG,EAAE;EACP,CAAC,EACD;IACE+D,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,WAAW;IACtBhE,GAAG,EAAE;EACP,CAAC,EACD;IACE+D,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBhE,GAAG,EAAE;EACP,CAAC,EACD;IACE+D,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,aAAa;IACxBhE,GAAG,EAAE,aAAa;IAClBiE,MAAM,EAAGC,IAAI,IAAKA,IAAI,IAAI;EAC5B,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACX/D,GAAG,EAAE,MAAM;IACXiE,MAAM,EAAEA,CAACE,CAAC,EAAEC,MAAM,kBAChB5L,OAAA,CAAAF,SAAA;MAAA+L,QAAA,EACGD,MAAM,CAACpB,QAAQ,GAAG,KAAK,GAAIoB,MAAM,CAACrB,UAAU,GAAG,IAAI,GAAG;IAAK,gBAC5D;EAEN,CAAC,EACD;IACEgB,KAAK,EAAE,IAAI;IACX/D,GAAG,EAAE,QAAQ;IACbiE,MAAM,EAAEA,CAACE,CAAC,EAAEC,MAAM,kBAChB5L,OAAA,CAAAF,SAAA;MAAA+L,QAAA,gBACE7L,OAAA,CAAC7D,MAAM;QACL2P,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE/L,OAAA,CAACjC,YAAY;UAAAiO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,KAAK,EAAE;UAAEC,WAAW,EAAE;QAAE,CAAE;QAC1BC,OAAO,EAAEA,CAAA,KAAMrC,cAAc,CAAC2B,MAAM,CAAE;QAAAC,QAAA,EACvC;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnM,OAAA,CAAC7D,MAAM;QACLoQ,MAAM;QACNR,IAAI,eAAE/L,OAAA,CAAChC,cAAc;UAAAgO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBG,OAAO,EAAEA,CAAA,KAAM;UACblQ,KAAK,CAACoQ,OAAO,CAAC;YACZjB,KAAK,EAAE,MAAM;YACbkB,OAAO,EAAE,YAAYb,MAAM,CAACzB,QAAQ,MAAM;YAC1CuC,MAAM,EAAE,IAAI;YACZC,UAAU,EAAE,IAAI;YAChBC,IAAI,EAAEA,CAAA,KAAMlD,gBAAgB,CAACkC,MAAM,CAACvC,EAAE;UACxC,CAAC,CAAC;QACJ,CAAE;QAAAwC,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA,eACT;EAEN,CAAC,CACF;;EAED;EACA,MAAMU,eAAe,GAAG,CACtB;IACEtB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfhE,GAAG,EAAE;EACP,CAAC,EACD;IACE+D,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBhE,GAAG,EAAE;EACP,CAAC,EACD;IACE+D,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBhE,GAAG,EAAE;EACP,CAAC,EACD;IACE+D,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBhE,GAAG,EAAE;EACP,CAAC,EACD;IACE+D,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,cAAc;IACzBhE,GAAG,EAAE,cAAc;IACnBsF,QAAQ,EAAE;EACZ,CAAC,EACD;IACEvB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,WAAW;IACtBhE,GAAG,EAAE,WAAW;IAChBiE,MAAM,EAAGP,SAAS,IAAMA,SAAS,GAAG,IAAI,GAAG;EAC7C,CAAC,EACD;IACEK,KAAK,EAAE,IAAI;IACX/D,GAAG,EAAE,QAAQ;IACbiE,MAAM,EAAEA,CAACE,CAAC,EAAEC,MAAM,kBAChB5L,OAAA,CAAAF,SAAA;MAAA+L,QAAA,gBACE7L,OAAA,CAAC7D,MAAM;QACL2P,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE/L,OAAA,CAACjC,YAAY;UAAAiO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,KAAK,EAAE;UAAEC,WAAW,EAAE;QAAE,CAAE;QAC1BC,OAAO,EAAEA,CAAA,KAAM3B,kBAAkB,CAACiB,MAAM,CAAE;QAAAC,QAAA,EAC3C;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnM,OAAA,CAAC7D,MAAM;QACLoQ,MAAM;QACNR,IAAI,eAAE/L,OAAA,CAAChC,cAAc;UAAAgO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBG,OAAO,EAAEA,CAAA,KAAM;UACblQ,KAAK,CAACoQ,OAAO,CAAC;YACZjB,KAAK,EAAE,MAAM;YACbkB,OAAO,EAAE,cAAcb,MAAM,CAACf,UAAU,MAAM;YAC9C6B,MAAM,EAAE,IAAI;YACZC,UAAU,EAAE,IAAI;YAChBC,IAAI,EAAEA,CAAA,KAAM7C,oBAAoB,CAAC6B,MAAM,CAACvC,EAAE;UAC5C,CAAC,CAAC;QACJ,CAAE;QAAAwC,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA,eACT;EAEN,CAAC,CACF;;EAED;EACA,MAAMY,0BAA0B,GAAGA,CAAA,KAAM;IACvCpM,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;;EAED;EACA,MAAMqM,oBAAoB,GAAGA,CAAA,KAAM;IACjC,oBACEhN,OAAA;MAAKiN,SAAS,EAAC,iBAAiB;MAAApB,QAAA,gBAC9B7L,OAAA,CAAClD,GAAG;QAACoQ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACd,KAAK,EAAE;UAAEe,YAAY,EAAE;QAAG,CAAE;QAAAtB,QAAA,eACjD7L,OAAA,CAACjD,GAAG;UAACqQ,IAAI,EAAE,EAAG;UAAAvB,QAAA,eACZ7L,OAAA,CAACI,KAAK;YAACiN,KAAK,EAAE,CAAE;YAAAxB,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELpJ,UAAU,iBACT/C,OAAA,CAAC5C,KAAK;QACJX,OAAO,EAAC,kDAAU;QAClB6Q,WAAW,EAAEvK,UAAW;QACxB+I,IAAI,EAAC,OAAO;QACZyB,QAAQ;QACRnB,KAAK,EAAE;UAAEe,YAAY,EAAE;QAAG;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACF,eAEDnM,OAAA,CAACnD,IAAI;QAAC2Q,QAAQ,EAAE3K,YAAa;QAAAgJ,QAAA,gBAC3B7L,OAAA,CAAClD,GAAG;UAACoQ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAArB,QAAA,gBACpB7L,OAAA,CAACjD,GAAG;YAAC0Q,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eAChC7L,OAAA,CAACtD,IAAI;cAAAmP,QAAA,eACH7L,OAAA,CAACtC,SAAS;gBACR6N,KAAK,EAAC,0BAAM;gBACZ9D,KAAK,EAAE,CAAAvF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiD,WAAW,KAAI,CAAE;gBACtC0I,MAAM,eAAE7N,OAAA,CAAC9B,YAAY;kBAAA8N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNnM,OAAA,CAACjD,GAAG;YAAC0Q,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eAChC7L,OAAA,CAACtD,IAAI;cAAAmP,QAAA,eACH7L,OAAA,CAACtC,SAAS;gBACR6N,KAAK,EAAC,0BAAM;gBACZ9D,KAAK,EAAE,CAAAvF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE8D,aAAa,KAAI,CAAE;gBACxC6H,MAAM,eAAE7N,OAAA,CAACrC,YAAY;kBAAAqO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNnM,OAAA,CAACjD,GAAG;YAAC0Q,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eAChC7L,OAAA,CAACtD,IAAI;cAAAmP,QAAA,eACH7L,OAAA,CAACtC,SAAS;gBACR6N,KAAK,EAAC,0BAAM;gBACZ9D,KAAK,EAAE,CAAAvF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE+D,cAAc,KAAI,CAAE;gBACzC4H,MAAM,eAAE7N,OAAA,CAAC/B,YAAY;kBAAA+N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNnM,OAAA,CAACjD,GAAG;YAAC0Q,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eAChC7L,OAAA,CAACtD,IAAI;cAAAmP,QAAA,eACH7L,OAAA,CAACtC,SAAS;gBACR6N,KAAK,EAAC,sCAAQ;gBACd9D,KAAK,EAAE,CAAAvF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgE,eAAe,KAAI,CAAE;gBAC1C2H,MAAM,eAAE7N,OAAA,CAAC7B,UAAU;kBAAA6N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnM,OAAA,CAAClD,GAAG;UAACoQ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACd,KAAK,EAAE;YAAE0B,SAAS,EAAE;UAAG,CAAE;UAAAjC,QAAA,eAC9C7L,OAAA,CAACjD,GAAG;YAACqQ,IAAI,EAAE,EAAG;YAAAvB,QAAA,eACZ7L,OAAA,CAACtD,IAAI;cAAC6O,KAAK,EAAC,0BAAM;cAAAM,QAAA,eAChB7L,OAAA;gBAAA6L,QAAA,EAAG;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV,CAAC;;EAED;EACA,MAAM4B,0BAA0B,GAAGA,CAAA,KAAM;IACvC,MAAMC,aAAa,GAAG7K,oBAAoB,CAACE,2BAA2B;IAEtE,oBACErD,OAAA;MAAA6L,QAAA,gBAEE7L,OAAA,CAACtD,IAAI;QAAC6O,KAAK,EAAC,gCAAO;QAAC0C,QAAQ,EAAE,KAAM;QAAC7B,KAAK,EAAE;UAAEe,YAAY,EAAE;QAAG,CAAE;QAAAtB,QAAA,eAC/D7L,OAAA,CAACnD,IAAI;UAAC2Q,QAAQ,EAAE9J,eAAgB;UAAAmI,QAAA,eAC9B7L,OAAA;YAAKoM,KAAK,EAAE;cAAE8B,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE,QAAQ;cAAEC,OAAO,EAAE;YAAS,CAAE;YAAAxC,QAAA,gBACxG7L,OAAA;cAAA6L,QAAA,gBACE7L,OAAA,CAACpD,UAAU,CAACwD,KAAK;gBAACiN,KAAK,EAAE,CAAE;gBAACjB,KAAK,EAAE;kBAAEkC,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAEP,aAAa,GAAG,SAAS,GAAG;gBAAU,CAAE;gBAAAnC,QAAA,EAAC;cAEhG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,eACnBnM,OAAA,CAACpD,UAAU,CAAC4R,IAAI;gBAAC1C,IAAI,EAAC,WAAW;gBAAAD,QAAA,EAC9BmC,aAAa,GACZ,8BAA8B,GAC9B;cAAgC;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACNnM,OAAA,CAACxD,MAAM;cACLiS,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEV,aAAc;cACvBW,QAAQ,EAAEjI,8BAA+B;cACzCkI,eAAe,EAAC,cAAI;cACpBC,iBAAiB,EAAC;YAAI;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPnM,OAAA,CAACtD,IAAI;QAAC6O,KAAK,EAAC,sCAAQ;QAAC0C,QAAQ,EAAE,KAAM;QAAApC,QAAA,eACnC7L,OAAA,CAACnD,IAAI;UAAC2Q,QAAQ,EAAE9J,eAAgB;UAAAmI,QAAA,EAC7BrI,gBAAgB,gBACfxD,OAAA,CAAClD,GAAG;YAACoQ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;YAAArB,QAAA,EACnBiD,MAAM,CAACC,IAAI,CAACvL,gBAAgB,CAACoE,KAAK,CAAC,CAACoH,GAAG,CAAChH,QAAQ,IAAI;cACnD,MAAMiH,WAAW,GAAGjB,aAAa,IAAIxK,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACrB,OAAO;cAC7E,oBACE3G,OAAA,CAACjD,GAAG;gBAACqQ,IAAI,EAAE,EAAG;gBAAAvB,QAAA,eACZ7L,OAAA,CAACtD,IAAI;kBACHoP,IAAI,EAAC,OAAO;kBACZP,KAAK,EAAE,GAAGpD,kBAAkB,CAACH,QAAQ,CAAC,MAAO;kBAC7CoE,KAAK,EAAE;oBAAE8C,OAAO,EAAElB,aAAa,GAAG,CAAC,GAAG;kBAAI,CAAE;kBAAAnC,QAAA,gBAE5C7L,OAAA;oBAAKoM,KAAK,EAAE;sBAAE8B,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEC,UAAU,EAAE;oBAAS,CAAE;oBAAAvC,QAAA,gBACrF7L,OAAA,CAACpD,UAAU,CAAC4R,IAAI;sBAACW,MAAM;sBAAAtD,QAAA,EAAE,KAAK1D,kBAAkB,CAACH,QAAQ,CAAC;oBAAK;sBAAAgE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAkB,CAAC,eAClFnM,OAAA,CAACxD,MAAM;sBACLkS,OAAO,EAAElL,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACrB,OAAQ;sBAClDgI,QAAQ,EAAGD,OAAO,IAAK3G,gBAAgB,CAACC,QAAQ,EAAE,SAAS,EAAE0G,OAAO,CAAE;sBACtEU,QAAQ,EAAE,CAACpB;oBAAc;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNnM,OAAA,CAACpD,UAAU,CAAC4R,IAAI;oBAAC1C,IAAI,EAAC,WAAW;oBAACM,KAAK,EAAE;sBAAE8B,OAAO,EAAE,OAAO;sBAAEJ,SAAS,EAAE;oBAAG,CAAE;oBAAAjC,QAAA,EAC1E,CAACmC,aAAa,GACb,gBAAgB,GACfxK,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACrB,OAAO,GACvC,GAAGwB,kBAAkB,CAACH,QAAQ,CAAC,UAAU,GACzC,GAAGG,kBAAkB,CAACH,QAAQ,CAAC;kBAAqB;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC,GAtBWnE,QAAQ;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBvB,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENnM,OAAA,CAAClD,GAAG;YAACoQ,MAAM,EAAE,EAAG;YAAArB,QAAA,gBACd7L,OAAA,CAACjD,GAAG;cAACqQ,IAAI,EAAE,EAAG;cAAAvB,QAAA,eACZ7L,OAAA,CAACtD,IAAI;gBACHoP,IAAI,EAAC,OAAO;gBACZP,KAAK,EAAC,sCAAQ;gBACda,KAAK,EAAE;kBAAE8C,OAAO,EAAElB,aAAa,GAAG,CAAC,GAAG;gBAAI,CAAE;gBAAAnC,QAAA,gBAE5C7L,OAAA;kBAAKoM,KAAK,EAAE;oBAAE8B,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE,eAAe;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAAvC,QAAA,gBACrF7L,OAAA,CAACpD,UAAU,CAAC4R,IAAI;oBAACW,MAAM;oBAAAtD,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiB,CAAC,eACjDnM,OAAA,CAACxD,MAAM;oBACLkS,OAAO,EAAEvL,oBAAoB,CAACG,0BAA2B;oBACzDqL,QAAQ,EAAGD,OAAO,IAAKnH,0BAA0B,CAAC,4BAA4B,EAAEmH,OAAO,CAAE;oBACzFU,QAAQ,EAAE,CAACpB;kBAAc;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNnM,OAAA,CAACpD,UAAU,CAAC4R,IAAI;kBAAC1C,IAAI,EAAC,WAAW;kBAACM,KAAK,EAAE;oBAAE8B,OAAO,EAAE,OAAO;oBAAEJ,SAAS,EAAE;kBAAG,CAAE;kBAAAjC,QAAA,EAC1E,CAACmC,aAAa,GACb,eAAe,GACd7K,oBAAoB,CAACG,0BAA0B,GAC9C,YAAY,GACZ;gBAAyB;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNnM,OAAA,CAACjD,GAAG;cAACqQ,IAAI,EAAE,EAAG;cAAAvB,QAAA,eACZ7L,OAAA,CAACtD,IAAI;gBACHoP,IAAI,EAAC,OAAO;gBACZP,KAAK,EAAC,sCAAQ;gBACda,KAAK,EAAE;kBAAE8C,OAAO,EAAElB,aAAa,GAAG,CAAC,GAAG;gBAAI,CAAE;gBAAAnC,QAAA,gBAE5C7L,OAAA;kBAAKoM,KAAK,EAAE;oBAAE8B,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE,eAAe;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAAvC,QAAA,gBACrF7L,OAAA,CAACpD,UAAU,CAAC4R,IAAI;oBAACW,MAAM;oBAAAtD,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiB,CAAC,eACjDnM,OAAA,CAACxD,MAAM;oBACLkS,OAAO,EAAEvL,oBAAoB,CAACI,0BAA2B;oBACzDoL,QAAQ,EAAGD,OAAO,IAAKnH,0BAA0B,CAAC,4BAA4B,EAAEmH,OAAO,CAAE;oBACzFU,QAAQ,EAAE,CAACpB;kBAAc;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNnM,OAAA,CAACpD,UAAU,CAAC4R,IAAI;kBAAC1C,IAAI,EAAC,WAAW;kBAACM,KAAK,EAAE;oBAAE8B,OAAO,EAAE,OAAO;oBAAEJ,SAAS,EAAE;kBAAG,CAAE;kBAAAjC,QAAA,EAC1E,CAACmC,aAAa,GACb,eAAe,GACd7K,oBAAoB,CAACI,0BAA0B,GAC9C,YAAY,GACZ;gBAAyB;kBAAAyI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEN3I,gBAAgB,iBACfxD,OAAA,CAACtD,IAAI;QACH6O,KAAK,EAAC,sCAAQ;QACd0C,QAAQ,EAAE,KAAM;QAChB7B,KAAK,EAAE;UACL0B,SAAS,EAAE,EAAE;UACboB,OAAO,EAAElB,aAAa,GAAG,CAAC,GAAG;QAC/B,CAAE;QAAAnC,QAAA,eAEF7L,OAAA,CAACnD,IAAI;UAAC2Q,QAAQ,EAAE9J,eAAgB;UAAAmI,QAAA,GAC7B,CAACmC,aAAa,iBACbhO,OAAA,CAAC5C,KAAK;YACJX,OAAO,EAAC,kDAAU;YAClB6Q,WAAW,EAAC,wGAAmB;YAC/BxB,IAAI,EAAC,SAAS;YACdyB,QAAQ;YACRnB,KAAK,EAAE;cAAEe,YAAY,EAAE;YAAG;UAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CACF,eACDnM,OAAA,CAAC/C,QAAQ;YAACoS,gBAAgB,EAAE,CAAC,SAAS,EAAE,SAAS,CAAE;YAAAxD,QAAA,EAChDiD,MAAM,CAACC,IAAI,CAACvL,gBAAgB,CAACoE,KAAK,CAAC,CAACoH,GAAG,CAAChH,QAAQ,IAAI;cAAA,IAAAsH,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cACnD,MAAMC,mBAAmB,GAAG9B,aAAa,IAAIxK,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACrB,OAAO;cACrF,oBACE3G,OAAA,CAAC/C,QAAQ,CAAC8S,KAAK;gBAEbC,MAAM,eACJhQ,OAAA;kBAAKoM,KAAK,EAAE;oBAAE8B,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAvC,QAAA,gBACpD7L,OAAA;oBAAA6L,QAAA,EAAO1D,kBAAkB,CAACH,QAAQ;kBAAC;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3CnM,OAAA,CAAC9C,GAAG;oBAACqR,KAAK,EAAEuB,mBAAmB,GAAG,OAAO,GAAG,KAAM;oBAAC1D,KAAK,EAAE;sBAAE6D,UAAU,EAAE;oBAAE,CAAE;oBAAApE,QAAA,EACzEiE,mBAAmB,GAAG,KAAK,GAAG;kBAAK;oBAAA9D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,EACL,CAAC6B,aAAa,iBACbhO,OAAA,CAAC9C,GAAG;oBAACqR,KAAK,EAAC,QAAQ;oBAACnC,KAAK,EAAE;sBAAE6D,UAAU,EAAE;oBAAE,CAAE;oBAAApE,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACzD,EACA3I,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACkI,iBAAiB,iBACjDlQ,OAAA,CAAC9C,GAAG;oBAACqR,KAAK,EAAC,MAAM;oBAACnC,KAAK,EAAE;sBAAE6D,UAAU,EAAE;oBAAE,CAAE;oBAAApE,QAAA,EAAC;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACtD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACDiD,QAAQ,EAAE,CAACpB,aAAc;gBAAAnC,QAAA,gBAE3B7L,OAAA,CAAClD,GAAG;kBAACoQ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;kBAAArB,QAAA,gBACpB7L,OAAA,CAACjD,GAAG;oBAACqQ,IAAI,EAAE,CAAE;oBAAAvB,QAAA,eACX7L,OAAA;sBAAKoM,KAAK,EAAE;wBAAE8B,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEC,UAAU,EAAE;sBAAS,CAAE;sBAAAvC,QAAA,gBACrF7L,OAAA,CAACpD,UAAU,CAAC4R,IAAI;wBAACW,MAAM;wBAAAtD,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAiB,CAAC,eAC/CnM,OAAA,CAACxD,MAAM;wBACLkS,OAAO,EAAElL,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACrB,OAAQ;wBAClDgI,QAAQ,EAAGD,OAAO,IAAK3G,gBAAgB,CAACC,QAAQ,EAAE,SAAS,EAAE0G,OAAO,CAAE;wBACtEU,QAAQ,EAAE,CAACpB;sBAAc;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnM,OAAA,CAACjD,GAAG;oBAACqQ,IAAI,EAAE,CAAE;oBAAAvB,QAAA,eACX7L,OAAA;sBAAKoM,KAAK,EAAE;wBAAE8B,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEC,UAAU,EAAE;sBAAS,CAAE;sBAAAvC,QAAA,gBACrF7L,OAAA,CAACpD,UAAU,CAAC4R,IAAI;wBAACW,MAAM;wBAAAtD,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAiB,CAAC,eAC/CnM,OAAA,CAACxD,MAAM;wBACLkS,OAAO,EAAElL,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACkI,iBAAkB;wBAC5DvB,QAAQ,EAAGD,OAAO,IAAK3G,gBAAgB,CAACC,QAAQ,EAAE,mBAAmB,EAAE0G,OAAO,CAAE;wBAChFU,QAAQ,EAAE,CAACpB;sBAAc;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EACL3I,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACkI,iBAAiB,iBACjDlQ,OAAA,CAACjD,GAAG;oBAACqQ,IAAI,EAAE,CAAE;oBAAAvB,QAAA,eACX7L,OAAA;sBAAKoM,KAAK,EAAE;wBAAE8B,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE;sBAAS,CAAE;sBAAAvC,QAAA,gBACpD7L,OAAA,CAACpD,UAAU,CAAC4R,IAAI;wBAACW,MAAM;wBAAC/C,KAAK,EAAE;0BAAEC,WAAW,EAAE;wBAAE,CAAE;wBAAAR,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAiB,CAAC,eAC1EnM,OAAA,CAACpD,UAAU,CAAC4R,IAAI;wBAAA3C,QAAA,EAAExD,mBAAmB,CAAC,gBAAgB,EAAE7E,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACmI,cAAc;sBAAC;wBAAAnE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAkB,CAAC,eAC3HnM,OAAA,CAAC7C,OAAO;wBAACoO,KAAK,EAAC,oHAAqB;wBAAAM,QAAA,eAClC7L,OAAA,CAACtB,kBAAkB;0BAAC0N,KAAK,EAAE;4BAAE6D,UAAU,EAAE,CAAC;4BAAE1B,KAAK,EAAE;0BAAU;wBAAE;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENnM,OAAA,CAAChD,OAAO;kBAACoT,WAAW,EAAC,MAAM;kBAAAvE,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAC1CnM,OAAA,CAAClD,GAAG;kBAACoQ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;kBAAArB,QAAA,gBACpB7L,OAAA,CAACjD,GAAG;oBAACqQ,IAAI,EAAE,CAAE;oBAAAvB,QAAA,eACX7L,OAAA,CAACtD,IAAI;sBAAC+R,IAAI,EAAC,OAAO;sBAAClD,KAAK,EAAC,cAAI;sBAAAM,QAAA,eAC3B7L,OAAA;wBAAA6L,QAAA,GAAK,oBAAG,EAAC,CAAAyD,qBAAA,GAAA9L,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACqI,MAAM,cAAAf,qBAAA,gBAAAC,sBAAA,GAAvCD,qBAAA,CAAyCgB,MAAM,cAAAf,sBAAA,eAA/CA,sBAAA,CAAiDgB,QAAQ,GAAG,GAAG,GAAG,GAAG;sBAAA;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNnM,OAAA,CAACjD,GAAG;oBAACqQ,IAAI,EAAE,CAAE;oBAAAvB,QAAA,eACX7L,OAAA,CAACtD,IAAI;sBAAC+R,IAAI,EAAC,OAAO;sBAAClD,KAAK,EAAC,cAAI;sBAAAM,QAAA,gBAC3B7L,OAAA;wBAAA6L,QAAA,GAAK,oBAAG,EAAC,CAAA2D,sBAAA,GAAAhM,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACqI,MAAM,cAAAb,sBAAA,gBAAAC,sBAAA,GAAvCD,sBAAA,CAAyCgB,KAAK,cAAAf,sBAAA,eAA9CA,sBAAA,CAAgDc,QAAQ,GAAG,GAAG,GAAG,GAAG;sBAAA;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EACnF,EAAAuD,sBAAA,GAAAlM,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACqI,MAAM,cAAAX,sBAAA,wBAAAC,sBAAA,GAAvCD,sBAAA,CAAyCc,KAAK,cAAAb,sBAAA,uBAA9CA,sBAAA,CAAgDc,GAAG,kBAClDzQ,OAAA;wBAAA6L,QAAA,GAAK,gCAAK,EAACrI,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACqI,MAAM,CAACG,KAAK,CAACC,GAAG,EAAC,SAAE;sBAAA;wBAAAzE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CACrE;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNnM,OAAA,CAACjD,GAAG;oBAACqQ,IAAI,EAAE,CAAE;oBAAAvB,QAAA,eACX7L,OAAA,CAACtD,IAAI;sBAAC+R,IAAI,EAAC,OAAO;sBAAClD,KAAK,EAAC,cAAI;sBAAAM,QAAA,eAC3B7L,OAAA;wBAAA6L,QAAA,GAAK,oBAAG,EAAC,CAAA+D,sBAAA,GAAApM,gBAAgB,CAACoE,KAAK,CAACI,QAAQ,CAAC,CAACqI,MAAM,cAAAT,sBAAA,gBAAAC,sBAAA,GAAvCD,sBAAA,CAAyCc,OAAO,cAAAb,sBAAA,eAAhDA,sBAAA,CAAkDU,QAAQ,GAAG,GAAG,GAAG,GAAG;sBAAA;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAELnE,QAAQ,KAAK,QAAQ,iBACpBhI,OAAA;kBAAKoM,KAAK,EAAE;oBAAE0B,SAAS,EAAE;kBAAG,CAAE;kBAAAjC,QAAA,eAC5B7L,OAAA,CAAC5C,KAAK;oBACJ0O,IAAI,EAAC,MAAM;oBACXrP,OAAO,EAAC,8DAAY;oBACpB6Q,WAAW,EAAE,QAAQ9J,gBAAgB,CAACoE,KAAK,CAAC+I,MAAM,CAACC,wBAAwB,GAAG,IAAI,GAAG,KAAK;kBAAO;oBAAA5E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA,GAjFMnE,QAAQ;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkFD,CAAC;YAEnB,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEXnM,OAAA,CAACtD,IAAI;YAAC0P,KAAK,EAAE;cAAE0B,SAAS,EAAE;YAAG,CAAE;YAAAjC,QAAA,gBAC7B7L,OAAA;cAAKoM,KAAK,EAAE;gBAAE8B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAvC,QAAA,gBACrF7L,OAAA,CAACpD,UAAU,CAAC4R,IAAI;gBAACW,MAAM;gBAAAtD,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClDnM,OAAA,CAACxD,MAAM;gBACLkS,OAAO,EAAElL,gBAAgB,CAACkF,qBAAsB;gBAChDiG,QAAQ,EAAGD,OAAO,IAAK;kBACrB,MAAM/G,eAAe,GAAG;oBAAE,GAAGnE,gBAAgB;oBAAEkF,qBAAqB,EAAEgG;kBAAQ,CAAC;kBAC/EjL,mBAAmB,CAACkE,eAAe,CAAC;kBACpC;kBACAc,2BAA2B,CAACiG,OAAO,CAAC;gBACtC;cAAE;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnM,OAAA,CAACpD,UAAU,CAAC4R,IAAI;cAAC1C,IAAI,EAAC,WAAW;cAACM,KAAK,EAAE;gBAAE8B,OAAO,EAAE,OAAO;gBAAEJ,SAAS,EAAE;cAAE,CAAE;cAAAjC,QAAA,EACzErI,gBAAgB,CAACkF,qBAAqB,GACrC,qBAAqB,GACrB;YAAoB;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,oBACEnM,OAAA;IACEiN,SAAS,EAAE,0BAA0B7K,QAAQ,GAAG,QAAQ,GAAG,SAAS,EAAG;IACvEgK,KAAK,EAAE;MACLyE,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE3O,QAAQ,GAAG,SAAS,GAAG;IACrC,CAAE;IAAAyJ,QAAA,gBAEF7L,OAAA,CAACjE,MAAM;MAACqQ,KAAK,EAAE;QAAE2E,UAAU,EAAE;MAAc,CAAE;MAAAlF,QAAA,eAE3C7L,OAAA,CAACjE,MAAM;QAACqQ,KAAK,EAAE;UACbiC,OAAO,EAAEjM,QAAQ,GAAG,UAAU,GAAG,aAAa;UAC9C2O,UAAU,EAAE;QACd,CAAE;QAAAlF,QAAA,eACA7L,OAAA;UAAKoM,KAAK,EAAE;YACVkC,MAAM,EAAElM,QAAQ,GAAG,GAAG,GAAG,QAAQ;YACjCiM,OAAO,EAAEjM,QAAQ,GAAG,GAAG,GAAG,MAAM;YAChC2O,UAAU,EAAE3O,QAAQ,GAAG,aAAa,GAAG,MAAM;YAC7C4O,YAAY,EAAE5O,QAAQ,GAAG,GAAG,GAAG;UACjC,CAAE;UAAAyJ,QAAA,GACCzJ,QAAQ,iBACPpC,OAAA;YAAKoM,KAAK,EAAE;cACVe,YAAY,EAAE,MAAM;cACpBkB,OAAO,EAAE,MAAM;cACf0C,UAAU,EAAE,OAAO;cACnBC,YAAY,EAAE,MAAM;cACpBC,SAAS,EAAE;YACb,CAAE;YAAApF,QAAA,eACA7L,OAAA,CAACI,KAAK;cAACiN,KAAK,EAAE,CAAE;cAACjB,KAAK,EAAE;gBAAEkC,MAAM,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAU,CAAE;cAAA1C,QAAA,EAAC;YAEzD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN,eAEDnM,OAAA,CAACE,OAAO;YAACkM,KAAK,EAAE;cACdiC,OAAO,EAAEjM,QAAQ,GAAG,GAAG,GAAG,QAAQ;cAClC0O,SAAS,EAAE,qBAAqB;cAChCC,UAAU,EAAE;YACd,CAAE;YAAAlF,QAAA,eACA7L,OAAA,CAACrD,IAAI;cACHuU,SAAS,EAAErQ,SAAU;cACrB8N,QAAQ,EAAGnH,GAAG,IAAK;gBACjB1G,YAAY,CAAC0G,GAAG,CAAC;gBACjB7G,QAAQ,CAAC,cAAc6G,GAAG,EAAE,EAAE;kBAAE2J,OAAO,EAAE;gBAAK,CAAC,CAAC;cAClD,CAAE;cACFrF,IAAI,EAAE1J,QAAQ,GAAG,MAAM,GAAG,MAAO;cACjCqM,IAAI,EAAErM,QAAQ,GAAG,OAAO,GAAG,SAAU;cACrCgP,WAAW,EAAEhP,QAAQ,GAAG,KAAK,GAAG,KAAM;cACtCgK,KAAK,EAAE;gBACL2E,UAAU,EAAE,OAAO;gBACnBC,YAAY,EAAE,MAAM;gBACpBK,QAAQ,EAAE;cACZ,CAAE;cAAAxF,QAAA,gBAEF7L,OAAA,CAACK,OAAO;gBAACiR,GAAG,eACVtR,OAAA;kBAAMoM,KAAK,EAAE;oBAAEmF,QAAQ,EAAEnP,QAAQ,GAAG,MAAM,GAAG;kBAAO,CAAE;kBAAAyJ,QAAA,gBACpD7L,OAAA,CAACpC,eAAe;oBAAAoO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAClB/J,QAAQ,GAAG,MAAM,GAAG,QAAQ;gBAAA;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CACP;gBAAAN,QAAA,gBACC7L,OAAA;kBAAKoM,KAAK,EAAE;oBAAEe,YAAY,EAAE;kBAAG,CAAE;kBAAAtB,QAAA,eAC/B7L,OAAA,CAAC7D,MAAM;oBACL2P,IAAI,EAAC,SAAS;oBACdC,IAAI,eAAE/L,OAAA,CAAClC,YAAY;sBAAAkO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACvBG,OAAO,EAAEjB,iBAAkB;oBAC3BoD,IAAI,EAAErM,QAAQ,GAAG,OAAO,GAAG,SAAU;oBACrCgK,KAAK,EAAEhK,QAAQ,GAAG;sBAAEoP,MAAM,EAAE,MAAM;sBAAED,QAAQ,EAAE;oBAAO,CAAC,GAAG,CAAC,CAAE;oBAAA1F,QAAA,EAC7D;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNnM,OAAA,CAAC9D,KAAK;kBACJuV,OAAO,EAAE5E,eAAgB;kBACzB6E,UAAU,EAAEzQ,SAAU;kBACtB0Q,MAAM,EAAC,IAAI;kBACXtQ,OAAO,EAAEA,OAAQ;kBACjBuQ,UAAU,EAAE;oBACVC,QAAQ,EAAEzP,QAAQ,GAAG,CAAC,GAAG,EAAE;oBAC3B0P,eAAe,EAAE,CAAC1P,QAAQ;oBAC1B2P,eAAe,EAAE,CAAC3P,QAAQ;oBAC1B4P,MAAM,EAAE5P;kBACV,CAAE;kBACF6P,MAAM,EAAE7P,QAAQ,GAAG;oBAAE8P,CAAC,EAAE;kBAAI,CAAC,GAAGC;gBAAU;kBAAAnG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,EACD,CAAC/J,QAAQ,iBACRpC,OAAA;kBAAKoM,KAAK,EAAE;oBAAE0B,SAAS,EAAE;kBAAG,CAAE;kBAAAjC,QAAA,GAC3B5K,SAAS,CAACmE,MAAM,KAAK,CAAC,IAAI,CAAC/D,OAAO,gBACjCrB,OAAA;oBAAKoM,KAAK,EAAE;sBAAE6E,SAAS,EAAE,QAAQ;sBAAE5C,OAAO,EAAE,MAAM;sBAAE+D,eAAe,EAAE,SAAS;sBAAEpB,YAAY,EAAE;oBAAM,CAAE;oBAAAnF,QAAA,eACpG7L,OAAA;sBAAA6L,QAAA,EAAG;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,GACJ,IAAI,eACRnM,OAAA;oBAAA6L,QAAA,gBACE7L,OAAA;sBAAA6L,QAAA,EAAI;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClBnM,OAAA;sBAAKoM,KAAK,EAAE;wBAAEgG,eAAe,EAAE,SAAS;wBAAE/D,OAAO,EAAE,MAAM;wBAAE2C,YAAY,EAAE,KAAK;wBAAEqB,SAAS,EAAE,OAAO;wBAAEhB,QAAQ,EAAE;sBAAO,CAAE;sBAAAxF,QAAA,EACpHjH,IAAI,CAACC,SAAS,CAAC5D,SAAS,EAAE,IAAI,EAAE,CAAC;oBAAC;sBAAA+K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA,GAvCG,GAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwCA,CAAC,eAEVnM,OAAA,CAACK,OAAO;gBAACiR,GAAG,eACVtR,OAAA;kBAAMoM,KAAK,EAAE;oBAAEmF,QAAQ,EAAEnP,QAAQ,GAAG,MAAM,GAAG;kBAAO,CAAE;kBAAAyJ,QAAA,gBACpD7L,OAAA,CAAC7B,UAAU;oBAAA6N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACb/J,QAAQ,GAAG,MAAM,GAAG,QAAQ;gBAAA;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CACP;gBAAAN,QAAA,eACC7L,OAAA,CAACV,iBAAiB;kBAAC8C,QAAQ,EAAEA;gBAAS;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC,GADrC,GAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEA,CAAC,eAEVnM,OAAA,CAACK,OAAO;gBAACiR,GAAG,eACVtR,OAAA;kBAAMoM,KAAK,EAAE;oBAAEmF,QAAQ,EAAEnP,QAAQ,GAAG,MAAM,GAAG;kBAAO,CAAE;kBAAAyJ,QAAA,gBACpD7L,OAAA,CAACxB,kBAAkB;oBAAAwN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACrB/J,QAAQ,GAAG,MAAM,GAAG,MAAM;gBAAA;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CACP;gBAAAN,QAAA,EACEkC,0BAA0B,CAAC;cAAC,GADzB,GAAG;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEA,CAAC,eAEVnM,OAAA,CAACK,OAAO;gBAACiR,GAAG,eACVtR,OAAA;kBAAMoM,KAAK,EAAE;oBAAEmF,QAAQ,EAAEnP,QAAQ,GAAG,MAAM,GAAG;kBAAO,CAAE;kBAAAyJ,QAAA,gBACpD7L,OAAA,CAACrB,aAAa;oBAAAqN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAChB/J,QAAQ,GAAG,MAAM,GAAG,MAAM;gBAAA;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CACP;gBAAAN,QAAA,eACC7L,OAAA,CAACP,oBAAoB;kBAAC2C,QAAQ,EAAEA;gBAAS;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC,GADxC,GAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEA,CAAC,eAEVnM,OAAA,CAACK,OAAO;gBAACiR,GAAG,eACVtR,OAAA;kBAAMoM,KAAK,EAAE;oBAAEmF,QAAQ,EAAEnP,QAAQ,GAAG,MAAM,GAAG;kBAAO,CAAE;kBAAAyJ,QAAA,gBACpD7L,OAAA,CAAC5B,YAAY;oBAAA4N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACf/J,QAAQ,GAAG,MAAM,GAAG,MAAM;gBAAA;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CACP;gBAAAN,QAAA,eACC7L,OAAA,CAACN,uBAAuB;kBAAC0C,QAAQ,EAAEA;gBAAS;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC,GAD3C,GAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEA,CAAC,eAEVnM,OAAA,CAACK,OAAO;gBAACiR,GAAG,eAAEtR,OAAA;kBAAA6L,QAAA,gBAAM7L,OAAA,CAACpB,yBAAyB;oBAAAoN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,wCAAM;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAE;gBAAAN,QAAA,eAC7D7L,OAAA,CAACtD,IAAI;kBAAC6O,KAAK,EAAC,kDAAU;kBAAAM,QAAA,eACpB7L,OAAA,CAACL,wBAAwB;oBAAAqM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC,GAH2D,GAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAI9D,CAAC,eAEVnM,OAAA,CAACK,OAAO;gBAACiR,GAAG,eACVtR,OAAA;kBAAMoM,KAAK,EAAE;oBAAEmF,QAAQ,EAAEnP,QAAQ,GAAG,MAAM,GAAG;kBAAO,CAAE;kBAAAyJ,QAAA,gBACpD7L,OAAA,CAACpC,eAAe;oBAAAoO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAClB/J,QAAQ,GAAG,OAAO,GAAG,SAAS;gBAAA;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CACP;gBAAAN,QAAA,eACC7L,OAAA,CAACJ,oBAAoB;kBAACwC,QAAQ,EAAEA;gBAAS;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC,GADxC,GAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGTnM,OAAA,CAACvC,MAAM;MACL8N,KAAK,EAAC,4BAAQ;MACd+G,SAAS,EAAC,OAAO;MACjBC,OAAO,EAAEA,CAAA,KAAMrP,kBAAkB,CAAC,KAAK,CAAE;MACzCsP,IAAI,EAAEvP,eAAgB;MACtB4N,KAAK,EAAE,GAAI;MAAAhF,QAAA,eAEX7L,OAAA,CAACR,WAAW;QAAC+S,OAAO,EAAEA,CAAA,KAAMrP,kBAAkB,CAAC,KAAK;MAAE;QAAA8I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAGTnM,OAAA,CAAC5D,KAAK;MACJmP,KAAK,EAAE5J,WAAW,GAAG,MAAM,GAAG,MAAO;MACrC6Q,IAAI,EAAEjR,gBAAiB;MACvBkR,QAAQ,EAAEA,CAAA,KAAMjR,mBAAmB,CAAC,KAAK,CAAE;MAC3CkR,MAAM,EAAE,IAAK;MAAA7G,QAAA,eAEb7L,OAAA,CAAC3D,IAAI;QACH0F,IAAI,EAAEA,IAAK;QACX4Q,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEzJ,oBAAqB;QAAA0C,QAAA,gBAE/B7L,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEzC,QAAQ,EAAE,IAAI;YAAE9T,OAAO,EAAE;UAAS,CAAC,CAAE;UAAAoP,QAAA,eAE/C7L,OAAA,CAAC1D,KAAK;YAAC2W,WAAW,EAAC;UAAQ;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZnM,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YAAEzC,QAAQ,EAAE,IAAI;YAAE9T,OAAO,EAAE;UAAQ,CAAC,EACpC;YAAEqP,IAAI,EAAE,OAAO;YAAErP,OAAO,EAAE;UAAa,CAAC,CACxC;UAAAoP,QAAA,eAEF7L,OAAA,CAAC1D,KAAK;YAAC2W,WAAW,EAAC;UAAO;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,EAEX,CAACxK,WAAW,iBACX3B,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEzC,QAAQ,EAAE,CAAC5O,WAAW;YAAElF,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAoP,QAAA,eAEtD7L,OAAA,CAAC1D,KAAK,CAAC4W,QAAQ;YAACD,WAAW,EAAC;UAAO;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACZ,EAEAxK,WAAW,iBACV3B,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,2DAAc;UAAAlH,QAAA,eAEpB7L,OAAA,CAAC1D,KAAK,CAAC4W,QAAQ;YAACD,WAAW,EAAC;UAAQ;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CACZ,eAEDnM,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,WAAW;UAChBC,KAAK,EAAC,cAAI;UAAAlH,QAAA,eAEV7L,OAAA,CAAC1D,KAAK;YAAC2W,WAAW,EAAC;UAAO;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEZnM,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,YAAY;UACjBK,aAAa,EAAC,SAAS;UAAAtH,QAAA,eAEvB7L,OAAA,CAAC1C,QAAQ;YAAAuO,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAEZnM,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,UAAU;UACfK,aAAa,EAAC,SAAS;UAAAtH,QAAA,eAEvB7L,OAAA,CAAC1C,QAAQ;YAAAuO,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eAEZnM,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UAACzG,KAAK,EAAE;YAAEe,YAAY,EAAE;UAAE,CAAE;UAAAtB,QAAA,gBACpC7L,OAAA,CAAC7D,MAAM;YAAC2P,IAAI,EAAC,SAAS;YAACsH,QAAQ,EAAC,QAAQ;YAAChH,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAEpE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnM,OAAA,CAAC7D,MAAM;YAACmQ,OAAO,EAAEA,CAAA,KAAM9K,mBAAmB,CAAC,KAAK,CAAE;YAAAqK,QAAA,EAAC;UAEnD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRnM,OAAA,CAAC5D,KAAK;MACJmP,KAAK,EAAE1J,eAAe,GAAG,QAAQ,GAAG,QAAS;MAC7C2Q,IAAI,EAAE/Q,oBAAqB;MAC3BgR,QAAQ,EAAEA,CAAA,KAAM/Q,uBAAuB,CAAC,KAAK,CAAE;MAC/CgR,MAAM,EAAE,IAAK;MAAA7G,QAAA,eAEb7L,OAAA,CAAC3D,IAAI;QACH0F,IAAI,EAAEE,YAAa;QACnB0Q,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEnJ,wBAAyB;QAAAoC,QAAA,gBAEnC7L,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,YAAY;UACjBC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEzC,QAAQ,EAAE,IAAI;YAAE9T,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAoP,QAAA,eAEhD7L,OAAA,CAAC1D,KAAK;YAAC2W,WAAW,EAAC;UAAkC;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAEZnM,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,gBAAM;UACZC,KAAK,EAAE,CAAC;YAAEzC,QAAQ,EAAE,IAAI;YAAE9T,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAoP,QAAA,eAEhD7L,OAAA,CAAC1D,KAAK;YAAC2W,WAAW,EAAC;UAA0B;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eAEZnM,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEzC,QAAQ,EAAE,IAAI;YAAE9T,OAAO,EAAE;UAAS,CAAC,CAAE;UAAAoP,QAAA,eAE/C7L,OAAA,CAACzD,MAAM;YAAC0W,WAAW,EAAC,sCAAQ;YAAApH,QAAA,gBAC1B7L,OAAA,CAACzD,MAAM,CAAC+D,MAAM;cAACmH,KAAK,EAAC,SAAS;cAAAoE,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACnDnM,OAAA,CAACzD,MAAM,CAAC+D,MAAM;cAACmH,KAAK,EAAC,QAAQ;cAAAoE,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACpDnM,OAAA,CAACzD,MAAM,CAAC+D,MAAM;cAACmH,KAAK,EAAC,SAAS;cAAAoE,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZnM,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,SAAS;UACdC,KAAK,EAAC,kBAAQ;UACdC,KAAK,EAAE,CAAC;YAAEzC,QAAQ,EAAE,IAAI;YAAE9T,OAAO,EAAE;UAAW,CAAC,CAAE;UAAAoP,QAAA,eAEjD7L,OAAA,CAAC1D,KAAK,CAAC4W,QAAQ;YAACD,WAAW,EAAC;UAAU;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEZnM,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,cAAc;UACnBC,KAAK,EAAC,iBAAO;UACbC,KAAK,EAAE,CAAC;YAAEzC,QAAQ,EAAE,IAAI;YAAE9T,OAAO,EAAE;UAAW,CAAC,CAAE;UAAAoP,QAAA,eAEjD7L,OAAA,CAAC1D,KAAK;YAAC2W,WAAW,EAAC;UAA4D;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eAEZnM,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UACRC,IAAI,EAAC,WAAW;UAChBK,aAAa,EAAC,SAAS;UACvBE,YAAY,EAAE,IAAK;UAAAxH,QAAA,gBAEnB7L,OAAA,CAACxD,MAAM;YAAC8W,cAAc;UAAA;YAAAtH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAC3B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZnM,OAAA,CAAC3D,IAAI,CAACwW,IAAI;UAACzG,KAAK,EAAE;YAAEe,YAAY,EAAE;UAAE,CAAE;UAAAtB,QAAA,gBACpC7L,OAAA,CAAC7D,MAAM;YAAC2P,IAAI,EAAC,SAAS;YAACsH,QAAQ,EAAC,QAAQ;YAAChH,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAEpE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnM,OAAA,CAAC7D,MAAM;YAACmQ,OAAO,EAAEA,CAAA,KAAM5K,uBAAuB,CAAC,KAAK,CAAE;YAAAmK,QAAA,EAAC;UAEvD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACzL,EAAA,CAhyCIH,cAAc;EAAA,QACD1B,WAAW,EACXC,WAAW,EAUbzC,IAAI,CAAC2F,OAAO,EACJ3F,IAAI,CAAC2F,OAAO;AAAA;AAAAuR,EAAA,GAb/BhT,cAAc;AAkyCpB,eAAeA,cAAc;AAAC,IAAAgT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}