{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genPresetsStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    colorTextQuaternary,\n    paddingXXS,\n    colorPickerPresetColorSize,\n    fontSizeSM,\n    colorText,\n    lineHeightSM,\n    lineWidth,\n    borderRadius,\n    colorFill,\n    colorWhite,\n    marginXXS,\n    paddingXS,\n    fontHeightSM\n  } = token;\n  return {\n    [`${componentCls}-presets`]: {\n      [`${antCls}-collapse-item > ${antCls}-collapse-header`]: {\n        padding: 0,\n        [`${antCls}-collapse-expand-icon`]: {\n          height: fontHeightSM,\n          color: colorTextQuaternary,\n          paddingInlineEnd: paddingXXS\n        }\n      },\n      [`${antCls}-collapse`]: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: marginXXS\n      },\n      [`${antCls}-collapse-item > ${antCls}-collapse-content > ${antCls}-collapse-content-box`]: {\n        padding: `${unit(paddingXS)} 0`\n      },\n      '&-label': {\n        fontSize: fontSizeSM,\n        color: colorText,\n        lineHeight: lineHeightSM\n      },\n      '&-items': {\n        display: 'flex',\n        flexWrap: 'wrap',\n        gap: token.calc(marginXXS).mul(1.5).equal(),\n        [`${componentCls}-presets-color`]: {\n          position: 'relative',\n          cursor: 'pointer',\n          width: colorPickerPresetColorSize,\n          height: colorPickerPresetColorSize,\n          '&::before': {\n            content: '\"\"',\n            pointerEvents: 'none',\n            width: token.calc(colorPickerPresetColorSize).add(token.calc(lineWidth).mul(4)).equal(),\n            height: token.calc(colorPickerPresetColorSize).add(token.calc(lineWidth).mul(4)).equal(),\n            position: 'absolute',\n            top: token.calc(lineWidth).mul(-2).equal(),\n            insetInlineStart: token.calc(lineWidth).mul(-2).equal(),\n            borderRadius,\n            border: `${unit(lineWidth)} solid transparent`,\n            transition: `border-color ${token.motionDurationMid} ${token.motionEaseInBack}`\n          },\n          '&:hover::before': {\n            borderColor: colorFill\n          },\n          '&::after': {\n            boxSizing: 'border-box',\n            position: 'absolute',\n            top: '50%',\n            insetInlineStart: '21.5%',\n            display: 'table',\n            width: token.calc(colorPickerPresetColorSize).div(13).mul(5).equal(),\n            height: token.calc(colorPickerPresetColorSize).div(13).mul(8).equal(),\n            border: `${unit(token.lineWidthBold)} solid ${token.colorWhite}`,\n            borderTop: 0,\n            borderInlineStart: 0,\n            transform: 'rotate(45deg) scale(0) translate(-50%,-50%)',\n            opacity: 0,\n            content: '\"\"',\n            transition: `all ${token.motionDurationFast} ${token.motionEaseInBack}, opacity ${token.motionDurationFast}`\n          },\n          [`&${componentCls}-presets-color-checked`]: {\n            '&::after': {\n              opacity: 1,\n              borderColor: colorWhite,\n              transform: 'rotate(45deg) scale(1) translate(-50%,-50%)',\n              transition: `transform ${token.motionDurationMid} ${token.motionEaseOutBack} ${token.motionDurationFast}`\n            },\n            [`&${componentCls}-presets-color-bright`]: {\n              '&::after': {\n                borderColor: 'rgba(0, 0, 0, 0.45)'\n              }\n            }\n          }\n        }\n      },\n      '&-empty': {\n        fontSize: fontSizeSM,\n        color: colorTextQuaternary\n      }\n    }\n  };\n};\nexport default genPresetsStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}