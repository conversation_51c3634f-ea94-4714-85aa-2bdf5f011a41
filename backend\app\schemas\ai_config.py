from typing import Optional
from pydantic import BaseModel
from datetime import datetime
from enum import Enum

class AIUsageTypeEnum(str, Enum):
    """AI使用类型枚举"""
    HOMEWORK_GRADING = "homework_grading"
    ERROR_ANALYSIS = "error_analysis"
    REINFORCEMENT_EXERCISE = "reinforcement_exercise"
    AI_ASSISTANT = "ai_assistant"

class AIModelConfigBase(BaseModel):
    model_name: str  # 模型名称，用于显示
    model_id: Optional[str] = None  # 模型ID，用于API调用
    provider: str
    usage_type: str  # 使用类型
    api_key: Optional[str] = None
    api_endpoint: Optional[str] = None
    is_active: bool = True  # 是否可用，True表示可以使用，False表示禁用
    
    model_config = {
        "protected_namespaces": ()  # 禁用保护命名空间，解决"model_"前缀警告
    }

class AIModelConfigCreate(AIModelConfigBase):
    pass

class AIModelConfigUpdate(BaseModel):
    model_name: Optional[str] = None
    model_id: Optional[str] = None
    provider: Optional[str] = None
    usage_type: Optional[str] = None
    api_key: Optional[str] = None
    api_endpoint: Optional[str] = None
    is_active: Optional[bool] = None
    
    model_config = {
        "protected_namespaces": ()  # 禁用保护命名空间，解决"model_"前缀警告
    }

class AIModelConfig(AIModelConfigBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    } 