{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Field, FieldContext, ListContext } from 'rc-field-form';\nimport useState from \"rc-util/es/hooks/useState\";\nimport { supportRef } from \"rc-util/es/ref\";\nimport { cloneElement } from '../../_util/reactNode';\nimport { devUseWarning } from '../../_util/warning';\nimport { ConfigContext } from '../../config-provider';\nimport useCSSVarCls from '../../config-provider/hooks/useCSSVarCls';\nimport { FormContext, NoStyleItemContext } from '../context';\nimport useChildren from '../hooks/useChildren';\nimport useFormItemStatus from '../hooks/useFormItemStatus';\nimport useFrameState from '../hooks/useFrameState';\nimport useItemRef from '../hooks/useItemRef';\nimport useStyle from '../style';\nimport { getFieldId, toArray } from '../util';\nimport ItemHolder from './ItemHolder';\nimport StatusProvider from './StatusProvider';\nconst NAME_SPLIT = '__SPLIT__';\nconst _ValidateStatuses = ['success', 'warning', 'error', 'validating', ''];\n// https://github.com/ant-design/ant-design/issues/46417\n// `getValueProps` may modify the value props name,\n// we should check if the control is similar.\nfunction isSimilarControl(a, b) {\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  return keysA.length === keysB.length && keysA.every(key => {\n    const propValueA = a[key];\n    const propValueB = b[key];\n    return propValueA === propValueB || typeof propValueA === 'function' || typeof propValueB === 'function';\n  });\n}\nconst MemoInput = /*#__PURE__*/React.memo(({\n  children\n}) => children, (prev, next) => isSimilarControl(prev.control, next.control) && prev.update === next.update && prev.childProps.length === next.childProps.length && prev.childProps.every((value, index) => value === next.childProps[index]));\nfunction genEmptyMeta() {\n  return {\n    errors: [],\n    warnings: [],\n    touched: false,\n    validating: false,\n    name: [],\n    validated: false\n  };\n}\nfunction InternalFormItem(props) {\n  const {\n    name,\n    noStyle,\n    className,\n    dependencies,\n    prefixCls: customizePrefixCls,\n    shouldUpdate,\n    rules,\n    children,\n    required,\n    label,\n    messageVariables,\n    trigger = 'onChange',\n    validateTrigger,\n    hidden,\n    help,\n    layout\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const {\n    name: formName\n  } = React.useContext(FormContext);\n  const mergedChildren = useChildren(children);\n  const isRenderProps = typeof mergedChildren === 'function';\n  const notifyParentMetaChange = React.useContext(NoStyleItemContext);\n  const {\n    validateTrigger: contextValidateTrigger\n  } = React.useContext(FieldContext);\n  const mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : contextValidateTrigger;\n  const hasName = !(name === undefined || name === null);\n  const prefixCls = getPrefixCls('form', customizePrefixCls);\n  // Style\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  // ========================= Warn =========================\n  const warning = devUseWarning('Form.Item');\n  if (process.env.NODE_ENV !== 'production') {\n    process.env.NODE_ENV !== \"production\" ? warning(name !== null, 'usage', '`null` is passed as `name` property') : void 0;\n  }\n  // ========================= MISC =========================\n  // Get `noStyle` required info\n  const listContext = React.useContext(ListContext);\n  const fieldKeyPathRef = React.useRef(null);\n  // ======================== Errors ========================\n  // >>>>> Collect sub field errors\n  const [subFieldErrors, setSubFieldErrors] = useFrameState({});\n  // >>>>> Current field errors\n  const [meta, setMeta] = useState(() => genEmptyMeta());\n  const onMetaChange = nextMeta => {\n    // This keyInfo is not correct when field is removed\n    // Since origin keyManager no longer keep the origin key anymore\n    // Which means we need cache origin one and reuse when removed\n    const keyInfo = listContext === null || listContext === void 0 ? void 0 : listContext.getKey(nextMeta.name);\n    // Destroy will reset all the meta\n    setMeta(nextMeta.destroy ? genEmptyMeta() : nextMeta, true);\n    // Bump to parent since noStyle\n    if (noStyle && help !== false && notifyParentMetaChange) {\n      let namePath = nextMeta.name;\n      if (!nextMeta.destroy) {\n        if (keyInfo !== undefined) {\n          const [fieldKey, restPath] = keyInfo;\n          namePath = [fieldKey].concat(_toConsumableArray(restPath));\n          fieldKeyPathRef.current = namePath;\n        }\n      } else {\n        // Use origin cache data\n        namePath = fieldKeyPathRef.current || namePath;\n      }\n      notifyParentMetaChange(nextMeta, namePath);\n    }\n  };\n  // >>>>> Collect noStyle Field error to the top FormItem\n  const onSubItemMetaChange = (subMeta, uniqueKeys) => {\n    // Only `noStyle` sub item will trigger\n    setSubFieldErrors(prevSubFieldErrors => {\n      const clone = Object.assign({}, prevSubFieldErrors);\n      // name: ['user', 1] + key: [4] = ['user', 4]\n      const mergedNamePath = [].concat(_toConsumableArray(subMeta.name.slice(0, -1)), _toConsumableArray(uniqueKeys));\n      const mergedNameKey = mergedNamePath.join(NAME_SPLIT);\n      if (subMeta.destroy) {\n        // Remove\n        delete clone[mergedNameKey];\n      } else {\n        // Update\n        clone[mergedNameKey] = subMeta;\n      }\n      return clone;\n    });\n  };\n  // >>>>> Get merged errors\n  const [mergedErrors, mergedWarnings] = React.useMemo(() => {\n    const errorList = _toConsumableArray(meta.errors);\n    const warningList = _toConsumableArray(meta.warnings);\n    Object.values(subFieldErrors).forEach(subFieldError => {\n      errorList.push.apply(errorList, _toConsumableArray(subFieldError.errors || []));\n      warningList.push.apply(warningList, _toConsumableArray(subFieldError.warnings || []));\n    });\n    return [errorList, warningList];\n  }, [subFieldErrors, meta.errors, meta.warnings]);\n  // ===================== Children Ref =====================\n  const getItemRef = useItemRef();\n  // ======================== Render ========================\n  function renderLayout(baseChildren, fieldId, isRequired) {\n    if (noStyle && !hidden) {\n      return /*#__PURE__*/React.createElement(StatusProvider, {\n        prefixCls: prefixCls,\n        hasFeedback: props.hasFeedback,\n        validateStatus: props.validateStatus,\n        meta: meta,\n        errors: mergedErrors,\n        warnings: mergedWarnings,\n        noStyle: true\n      }, baseChildren);\n    }\n    return /*#__PURE__*/React.createElement(ItemHolder, Object.assign({\n      key: \"row\"\n    }, props, {\n      className: classNames(className, cssVarCls, rootCls, hashId),\n      prefixCls: prefixCls,\n      fieldId: fieldId,\n      isRequired: isRequired,\n      errors: mergedErrors,\n      warnings: mergedWarnings,\n      meta: meta,\n      onSubItemMetaChange: onSubItemMetaChange,\n      layout: layout\n    }), baseChildren);\n  }\n  if (!hasName && !isRenderProps && !dependencies) {\n    return wrapCSSVar(renderLayout(mergedChildren));\n  }\n  let variables = {};\n  if (typeof label === 'string') {\n    variables.label = label;\n  } else if (name) {\n    variables.label = String(name);\n  }\n  if (messageVariables) {\n    variables = Object.assign(Object.assign({}, variables), messageVariables);\n  }\n  // >>>>> With Field\n  return wrapCSSVar(/*#__PURE__*/React.createElement(Field, Object.assign({}, props, {\n    messageVariables: variables,\n    trigger: trigger,\n    validateTrigger: mergedValidateTrigger,\n    onMetaChange: onMetaChange\n  }), (control, renderMeta, context) => {\n    const mergedName = toArray(name).length && renderMeta ? renderMeta.name : [];\n    const fieldId = getFieldId(mergedName, formName);\n    const isRequired = required !== undefined ? required : !!(rules === null || rules === void 0 ? void 0 : rules.some(rule => {\n      if (rule && typeof rule === 'object' && rule.required && !rule.warningOnly) {\n        return true;\n      }\n      if (typeof rule === 'function') {\n        const ruleEntity = rule(context);\n        return (ruleEntity === null || ruleEntity === void 0 ? void 0 : ruleEntity.required) && !(ruleEntity === null || ruleEntity === void 0 ? void 0 : ruleEntity.warningOnly);\n      }\n      return false;\n    }));\n    // ======================= Children =======================\n    const mergedControl = Object.assign({}, control);\n    let childNode = null;\n    process.env.NODE_ENV !== \"production\" ? warning(!(shouldUpdate && dependencies), 'usage', \"`shouldUpdate` and `dependencies` shouldn't be used together. See https://u.ant.design/form-deps.\") : void 0;\n    if (Array.isArray(mergedChildren) && hasName) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'usage', 'A `Form.Item` with a `name` prop must have a single child element. For information on how to render more complex form items, see https://u.ant.design/complex-form-item.') : void 0;\n      childNode = mergedChildren;\n    } else if (isRenderProps && (!(shouldUpdate || dependencies) || hasName)) {\n      process.env.NODE_ENV !== \"production\" ? warning(!!(shouldUpdate || dependencies), 'usage', 'A `Form.Item` with a render function must have either `shouldUpdate` or `dependencies`.') : void 0;\n      process.env.NODE_ENV !== \"production\" ? warning(!hasName, 'usage', 'A `Form.Item` with a render function cannot be a field, and thus cannot have a `name` prop.') : void 0;\n    } else if (dependencies && !isRenderProps && !hasName) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'usage', 'Must set `name` or use a render function when `dependencies` is set.') : void 0;\n    } else if (/*#__PURE__*/React.isValidElement(mergedChildren)) {\n      process.env.NODE_ENV !== \"production\" ? warning(mergedChildren.props.defaultValue === undefined, 'usage', '`defaultValue` will not work on controlled Field. You should use `initialValues` of Form instead.') : void 0;\n      const childProps = Object.assign(Object.assign({}, mergedChildren.props), mergedControl);\n      if (!childProps.id) {\n        childProps.id = fieldId;\n      }\n      if (help || mergedErrors.length > 0 || mergedWarnings.length > 0 || props.extra) {\n        const describedbyArr = [];\n        if (help || mergedErrors.length > 0) {\n          describedbyArr.push(`${fieldId}_help`);\n        }\n        if (props.extra) {\n          describedbyArr.push(`${fieldId}_extra`);\n        }\n        childProps['aria-describedby'] = describedbyArr.join(' ');\n      }\n      if (mergedErrors.length > 0) {\n        childProps['aria-invalid'] = 'true';\n      }\n      if (isRequired) {\n        childProps['aria-required'] = 'true';\n      }\n      if (supportRef(mergedChildren)) {\n        childProps.ref = getItemRef(mergedName, mergedChildren);\n      }\n      // We should keep user origin event handler\n      const triggers = new Set([].concat(_toConsumableArray(toArray(trigger)), _toConsumableArray(toArray(mergedValidateTrigger))));\n      triggers.forEach(eventName => {\n        childProps[eventName] = (...args) => {\n          var _a2, _c2;\n          var _a, _b, _c;\n          (_a = mergedControl[eventName]) === null || _a === void 0 ? void 0 : (_a2 = _a).call.apply(_a2, [mergedControl].concat(args));\n          (_c = (_b = mergedChildren.props)[eventName]) === null || _c === void 0 ? void 0 : (_c2 = _c).call.apply(_c2, [_b].concat(args));\n        };\n      });\n      // List of props that need to be watched for changes -> if changes are detected in MemoInput -> rerender\n      const watchingChildProps = [childProps['aria-required'], childProps['aria-invalid'], childProps['aria-describedby']];\n      childNode = /*#__PURE__*/React.createElement(MemoInput, {\n        control: mergedControl,\n        update: mergedChildren,\n        childProps: watchingChildProps\n      }, cloneElement(mergedChildren, childProps));\n    } else if (isRenderProps && (shouldUpdate || dependencies) && !hasName) {\n      childNode = mergedChildren(context);\n    } else {\n      process.env.NODE_ENV !== \"production\" ? warning(!mergedName.length || !!noStyle, 'usage', '`name` is only used for validate React element. If you are using Form.Item as layout display, please remove `name` instead.') : void 0;\n      childNode = mergedChildren;\n    }\n    return renderLayout(childNode, fieldId, isRequired);\n  }));\n}\nconst FormItem = InternalFormItem;\nFormItem.useStatus = useFormItemStatus;\nexport default FormItem;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}