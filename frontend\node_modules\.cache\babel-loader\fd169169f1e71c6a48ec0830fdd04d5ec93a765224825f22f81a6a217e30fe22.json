{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { prepareComponentToken, prepareToken } from '.';\nimport { resetComponent } from '../../style';\nimport { genPresetColor, genStyleHooks } from '../../theme/internal';\n// ============================== Ribbon ==============================\nconst genRibbonStyle = token => {\n  const {\n    antCls,\n    badgeFontHeight,\n    marginXS,\n    badgeRibbonOffset,\n    calc\n  } = token;\n  const ribbonPrefixCls = `${antCls}-ribbon`;\n  const ribbonWrapperPrefixCls = `${antCls}-ribbon-wrapper`;\n  const statusRibbonPreset = genPresetColor(token, (colorKey, {\n    darkColor\n  }) => ({\n    [`&${ribbonPrefixCls}-color-${colorKey}`]: {\n      background: darkColor,\n      color: darkColor\n    }\n  }));\n  return {\n    [ribbonWrapperPrefixCls]: {\n      position: 'relative'\n    },\n    [ribbonPrefixCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'absolute',\n      top: marginXS,\n      padding: `0 ${unit(token.paddingXS)}`,\n      color: token.colorPrimary,\n      lineHeight: unit(badgeFontHeight),\n      whiteSpace: 'nowrap',\n      backgroundColor: token.colorPrimary,\n      borderRadius: token.borderRadiusSM,\n      [`${ribbonPrefixCls}-text`]: {\n        color: token.badgeTextColor\n      },\n      [`${ribbonPrefixCls}-corner`]: {\n        position: 'absolute',\n        top: '100%',\n        width: badgeRibbonOffset,\n        height: badgeRibbonOffset,\n        color: 'currentcolor',\n        border: `${unit(calc(badgeRibbonOffset).div(2).equal())} solid`,\n        transform: token.badgeRibbonCornerTransform,\n        transformOrigin: 'top',\n        filter: token.badgeRibbonCornerFilter\n      }\n    }), statusRibbonPreset), {\n      [`&${ribbonPrefixCls}-placement-end`]: {\n        insetInlineEnd: calc(badgeRibbonOffset).mul(-1).equal(),\n        borderEndEndRadius: 0,\n        [`${ribbonPrefixCls}-corner`]: {\n          insetInlineEnd: 0,\n          borderInlineEndColor: 'transparent',\n          borderBlockEndColor: 'transparent'\n        }\n      },\n      [`&${ribbonPrefixCls}-placement-start`]: {\n        insetInlineStart: calc(badgeRibbonOffset).mul(-1).equal(),\n        borderEndStartRadius: 0,\n        [`${ribbonPrefixCls}-corner`]: {\n          insetInlineStart: 0,\n          borderBlockEndColor: 'transparent',\n          borderInlineStartColor: 'transparent'\n        }\n      },\n      // ====================== RTL =======================\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks(['Badge', 'Ribbon'], token => {\n  const badgeToken = prepareToken(token);\n  return genRibbonStyle(badgeToken);\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}