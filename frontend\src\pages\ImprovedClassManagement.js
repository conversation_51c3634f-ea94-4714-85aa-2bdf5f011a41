import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>, Button, Table, Form, Input, Modal, message,
  Popconfirm, Spin, Card, Tag, Space, Select, Badge, Empty,
  Upload, Tabs, Row, Col
} from 'antd';
import {
  PlusOutlined, DeleteOutlined, EditOutlined, UserOutlined,
  TeamOutlined, UploadOutlined, DownloadOutlined,
  SearchOutlined, FileExcelOutlined, FileTextOutlined
} from '@ant-design/icons';
import * as XLSX from 'xlsx';
import { hasPermission, Permissions } from '../utils/permissionUtils';
import { isSuperAdmin, isSchoolAdmin } from '../utils/roleUtils';

const { Title, Text } = Typography;
const { Option } = Select;

const ImprovedClassManagement = ({ user }) => {
  // 权限检查函数
  const canManageClass = () => {
    if (!user) return false;

    // 超级管理员拥有所有权限
    if (isSuperAdmin(user)) return true;

    // 学校管理员有管理权限
    if (isSchoolAdmin(user)) return true;

    // 检查是否有班级管理权限
    if (user.permissions && hasPermission(user.permissions, Permissions.MANAGE_CLASS)) {
      return true;
    }

    // 基于角色的权限检查：班主任、教务处主任
    const manageRoles = [
      '班主任', 'class_teacher', 'head_teacher',
      '教务处主任', 'academic_director'
    ];

    return manageRoles.includes(user.role);
  };

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [classesData, setClassesData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [gradeFilter, setGradeFilter] = useState('all');
  const [classFilter, setClassFilter] = useState('all');
  const [statistics, setStatistics] = useState({});
  const [selectedClass, setSelectedClass] = useState(null);

  // 人员管理数据
  const [activeTab, setActiveTab] = useState('students');
  const [studentsData, setStudentsData] = useState([]);
  const [teachersData, setTeachersData] = useState([]);
  const [parentsData, setParentsData] = useState([]);
  const [classStatistics, setClassStatistics] = useState({
    students: 0,
    teachers: 0,
    parents: 0,
    headTeacher: null
  });

  // 模态框状态
  const [isClassModalVisible, setIsClassModalVisible] = useState(false);
  const [isStudentModalVisible, setIsStudentModalVisible] = useState(false);
  const [isBatchStudentModalVisible, setIsBatchStudentModalVisible] = useState(false);
  const [isStudentImportModalVisible, setIsStudentImportModalVisible] = useState(false);
  const [isTeacherModalVisible, setIsTeacherModalVisible] = useState(false);
  const [isParentModalVisible, setIsParentModalVisible] = useState(false);
  const [isParentBatchImportModalVisible, setIsParentBatchImportModalVisible] = useState(false);
  const [isTeacherBatchImportModalVisible, setIsTeacherBatchImportModalVisible] = useState(false);
  const [isHeadTeacherSelectModalVisible, setIsHeadTeacherSelectModalVisible] = useState(false);
  const [isExportModalVisible, setIsExportModalVisible] = useState(false);
  const [isAdvancedFilterVisible, setIsAdvancedFilterVisible] = useState(false);
  const [editingClass, setEditingClass] = useState(null);
  const [editingStudent, setEditingStudent] = useState(null);
  const [editingTeacher, setEditingTeacher] = useState(null);
  const [editingParent, setEditingParent] = useState(null);
  const [studentImportData, setStudentImportData] = useState([]);
  const [parentImportData, setParentImportData] = useState([]);
  const [teacherImportData, setTeacherImportData] = useState([]);

  // 学生添加模式
  const [studentAddMode, setStudentAddMode] = useState('create'); // 'create' | 'existing'

  // 搜索和筛选状态（新增，独立功能）
  const [searchKeyword, setSearchKeyword] = useState('');
  const [advancedFilters, setAdvancedFilters] = useState({
    role: 'all',           // 角色筛选：all, student, teacher, parent
    subject: 'all',        // 科目筛选（教师）
    relationship: 'all',   // 关系筛选（家长）
    dateRange: null,       // 日期范围
    hasContact: 'all'      // 是否有联系方式：all, yes, no
  });

  // 科目数据状态
  const [subjectsData, setSubjectsData] = useState([]);
  const [filteredSubjects, setFilteredSubjects] = useState([]);
  const [searchFilteredData, setSearchFilteredData] = useState({
    students: [],
    teachers: [],
    parents: []
  });

  // 表单实例
  const [classForm] = Form.useForm();
  const [studentForm] = Form.useForm();
  const [batchStudentForm] = Form.useForm();
  const [teacherForm] = Form.useForm();
  const [parentForm] = Form.useForm();

  // API调用函数
  const apiCall = async (url, options = {}) => {
    const token = localStorage.getItem('token');
    const defaultOptions = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const response = await fetch(`/api${url}`, {
      ...defaultOptions,
      ...options,
      headers: { ...defaultOptions.headers, ...options.headers }
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    // 如果是204状态码（无内容），直接返回空对象
    if (response.status === 204) {
      return {};
    }

    // 检查响应是否有内容
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    }

    // 如果不是JSON，返回文本
    return response.text();
  };

  // 获取科目数据
  const fetchSubjectsData = async () => {
    try {
      console.log('开始获取科目数据...');
      console.log('当前token:', localStorage.getItem('token'));

      // 使用admin API
      const data = await apiCall('/admin/subjects');
      console.log('获取到的科目数据:', data);
      
      // 处理API返回的数据结构：{ total: number, items: Subject[] }
      const subjects = data && data.items ? data.items : (Array.isArray(data) ? data : []);
      setSubjectsData(subjects);
      console.log('科目数据已设置到状态中:', subjects);
    } catch (error) {
      console.error('获取科目数据失败:', error);
      console.error('错误详情:', error);

      // 如果API失败，使用硬编码的科目数据作为后备
      console.log('使用硬编码的科目数据作为后备...');
      const fallbackSubjects = [
        { id: 1, name: '小学语文' },
        { id: 2, name: '小学数学' },
        { id: 3, name: '小学英语' },
        { id: 4, name: '小学科学' },
        { id: 5, name: '小学道德与法治' },
        { id: 6, name: '初中语文' },
        { id: 7, name: '初中数学' },
        { id: 8, name: '初中英语' },
        { id: 9, name: '初中物理' },
        { id: 10, name: '初中化学' },
        { id: 11, name: '初中生物' },
        { id: 12, name: '初中历史' },
        { id: 13, name: '初中地理' },
        { id: 14, name: '初中道德与法治' },
        { id: 15, name: '高中语文' },
        { id: 16, name: '高中数学' },
        { id: 17, name: '高中英语' },
        { id: 18, name: '高中物理' },
        { id: 19, name: '高中化学' },
        { id: 20, name: '高中生物' },
        { id: 21, name: '高中历史' },
        { id: 22, name: '高中地理' },
        { id: 23, name: '高中政治' }
      ];
      setSubjectsData(fallbackSubjects);
      console.log('已设置后备科目数据');
    }
  };

  // 根据班级年级筛选对应教育阶段的科目
  const filterSubjectsByGrade = (grade) => {
    // 确保 subjectsData 是数组
    if (!Array.isArray(subjectsData) || subjectsData.length === 0) {
      console.log('subjectsData 不是数组或为空，返回空数组');
      return [];
    }

    let educationLevel = '';

    // 根据年级确定教育阶段
    if (grade) {
      // 直接检查是否包含教育阶段关键词
      if (grade.includes('高') || grade.includes('高中')) {
        educationLevel = '高中';
      } else if (grade.includes('初') || grade.includes('初中')) {
        educationLevel = '初中';
      } else if (grade.includes('小') || grade.includes('小学')) {
        educationLevel = '小学';
      } else {
        // 如果没有直接关键词，尝试解析年级数字
        let gradeNum = 0;

        // 处理中文数字
        const chineseNumbers = {
          '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6,
          '七': 7, '八': 8, '九': 9, '十': 10, '十一': 11, '十二': 12
        };

        // 先尝试提取阿拉伯数字
        const arabicMatch = grade.match(/(\d+)/);
        if (arabicMatch) {
          gradeNum = parseInt(arabicMatch[1]);
        } else {
          // 尝试匹配中文数字
          for (const [chinese, num] of Object.entries(chineseNumbers)) {
            if (grade.includes(chinese)) {
              gradeNum = num;
              break;
            }
          }
        }

        // 根据年级数字确定教育阶段
        if (gradeNum >= 1 && gradeNum <= 6) {
          educationLevel = '小学';
        } else if (gradeNum >= 7 && gradeNum <= 9) {
          educationLevel = '初中';
        } else if (gradeNum >= 10 && gradeNum <= 12) {
          educationLevel = '高中';
        }
      }

      console.log(`年级字符串: "${grade}", 确定的教育阶段: "${educationLevel}"`);
    }

    // 如果无法确定教育阶段，返回所有科目
    if (!educationLevel) {
      console.log(`无法确定教育阶段，年级: "${grade}", 返回所有科目`);
      return subjectsData;
    }

    // 筛选对应教育阶段的科目
    const filtered = subjectsData.filter(subject =>
      subject.name && subject.name.startsWith(educationLevel)
    );

    console.log(`班级年级: "${grade}", 教育阶段: "${educationLevel}", 筛选出的科目:`, filtered);
    return filtered;
  };

  // 当选中班级变化时，更新筛选的科目
  const updateFilteredSubjects = () => {
    console.log('=== updateFilteredSubjects 开始 ===');
    console.log('selectedClass:', selectedClass);
    console.log('subjectsData:', subjectsData);

    // 确保 subjectsData 是数组
    const safeSubjectsData = Array.isArray(subjectsData) ? subjectsData : [];

    if (selectedClass && selectedClass.grade) {
      console.log('班级年级:', selectedClass.grade);
      const filtered = filterSubjectsByGrade(selectedClass.grade);
      setFilteredSubjects(filtered);
      console.log('更新筛选科目，选中班级:', selectedClass, '筛选结果:', filtered);
    } else {
      console.log('没有选中班级或年级，使用所有科目');
      setFilteredSubjects(safeSubjectsData);
    }
    console.log('=== updateFilteredSubjects 结束 ===');
  };

  // 获取班级数据（仅限当前用户所属学校）
  const fetchClassesData = async () => {
    try {
      setLoading(true);
      console.log('用户信息:', user);

      if (!user || !user.school_id) {
        console.error('用户没有学校ID，无法获取班级数据');
        message.error('用户信息不完整，请重新登录');
        setLoading(false);
        return;
      }

      // 使用系统班级管理的API，然后过滤出当前学校的班级
      console.log('使用系统API获取班级，然后过滤学校:', user.school_id);
      const allData = await apiCall('/admin/classes/all');
      
      // 过滤出当前学校的班级
      const data = allData.filter(class_ => class_.school_id === user.school_id);
      console.log('过滤后的班级数据:', data);

      setClassesData(data);
      setFilteredData(data);
      calculateStatistics(data);
    } catch (error) {
      console.error('获取班级数据失败:', error);
      message.error('获取班级数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取班级学生数据
  const fetchStudentsData = async (classId) => {
    try {
      const data = await apiCall(`/admin/classes/${classId}`);
      setStudentsData(data.students || []);
      updateClassStatistics('students', (data.students || []).length);
    } catch (error) {
      console.error('获取学生数据失败:', error);
      // 暂时使用模拟数据
      const mockStudents = [
        {
          id: 1,
          username: '202501001',
          full_name: '张小明',
          email: '<EMAIL>'
        },
        {
          id: 2,
          username: '202501002',
          full_name: '李小红',
          email: '<EMAIL>'
        },
        {
          id: 3,
          username: '202501003',
          full_name: '王小华',
          email: '<EMAIL>'
        },
        {
          id: 4,
          username: '202501004',
          full_name: '赵小强',
          email: '<EMAIL>'
        },
        {
          id: 5,
          username: '202501005',
          full_name: '孙小美',
          email: '<EMAIL>'
        }
      ];
      setStudentsData(mockStudents);
      updateClassStatistics('students', mockStudents.length);
      console.log('使用模拟学生数据');
    }
  };

  // 获取教师数据（使用修复工具）
  const fetchTeachersData = async (classId) => {
    try {
      console.log('开始获取教师数据，班级ID:', classId);

      // 导入修复工具
      const { fetchTeacherDataUnified } = await import('../utils/teacherDataFix');

      // 使用统一的获取方法
      const teachers = await fetchTeacherDataUnified(classId, apiCall);
      console.log('处理后的教师数据:', teachers);
      setTeachersData(teachers);

      // 查找班主任
      const headTeacher = teachers.find(teacher => teacher.role === 'head_teacher');
      updateClassStatistics('teachers', teachers.length);
      updateClassStatistics('headTeacher', headTeacher);
    } catch (error) {
      console.error('获取教师数据失败:', error);
      message.error('获取教师数据失败');
      // 清空教师数据
      setTeachersData([]);
      updateClassStatistics('teachers', 0);
      updateClassStatistics('headTeacher', null);
    }
  };

  // 获取家长数据
  const fetchParentsData = async (classId) => {
    try {
      const data = await apiCall(`/admin/classes/${classId}/parents`);
      setParentsData(data);
      updateClassStatistics('parents', data.length);
    } catch (error) {
      console.error('获取家长数据失败:', error);
      // 暂时使用模拟数据
      const mockParents = [
        {
          id: 1,
          name: '张三家长',
          relationship: 'father',
          phone: '13900139001',
          student_name: '张小明',
          student_id: 1
        },
        {
          id: 2,
          name: '李四家长',
          relationship: 'mother',
          phone: '13900139002',
          student_name: '李小红',
          student_id: 2
        }
      ];
      setParentsData(mockParents);
      updateClassStatistics('parents', mockParents.length);
    }
  };

  // 更新班级统计信息
  const updateClassStatistics = (type, value) => {
    setClassStatistics(prev => ({
      ...prev,
      [type]: value
    }));
  };

  // 教师管理函数
  const createTeacherForClass = async (classId, teacherData) => {
    try {
      await apiCall(`/admin/classes/${classId}/teachers`, {
        method: 'POST',
        body: JSON.stringify(teacherData)
      });
      message.success('教师添加成功');
      fetchTeachersData(classId);
    } catch (error) {
      console.error('添加教师失败:', error);
      message.error('添加教师失败: ' + (error.message || '未知错误'));
      throw error;
    }
  };

  const updateTeacherInClass = async (classId, teacherId, teacherData) => {
    try {
      await apiCall(`/admin/classes/${classId}/teachers/${teacherId}`, {
        method: 'PUT',
        body: JSON.stringify(teacherData)
      });
      message.success('教师信息更新成功');
      fetchTeachersData(classId);
    } catch (error) {
      console.error('更新教师失败:', error);
      message.error('更新教师失败: ' + (error.message || '未知错误'));
      throw error;
    }
  };

  const removeTeacherFromClass = async (classId, teacherId) => {
    try {
      await apiCall(`/admin/classes/${classId}/teachers/${teacherId}`, {
        method: 'DELETE'
      });
      message.success('教师移除成功');
      fetchTeachersData(classId);
    } catch (error) {
      console.error('移除教师失败:', error);
      message.error('移除教师失败: ' + (error.message || '未知错误'));
      throw error;
    }
  };

  const assignHeadTeacher = async (classId, teacherId) => {
    try {
      await apiCall(`/admin/classes/${classId}/head-teacher`, {
        method: 'PUT',
        body: JSON.stringify({ teacher_id: teacherId })
      });
      message.success('班主任指定成功');
      fetchTeachersData(classId);
    } catch (error) {
      console.error('指定班主任失败:', error);
      message.error('指定班主任失败: ' + (error.message || '未知错误'));
      throw error;
    }
  };

  // 家长管理函数
  const createParentForClass = async (classId, parentData) => {
    try {
      await apiCall(`/admin/classes/${classId}/parents`, {
        method: 'POST',
        body: JSON.stringify(parentData)
      });
      message.success('家长添加成功');
      if (selectedClass) {
        fetchParentsData(selectedClass.id);
      }
    } catch (error) {
      console.error('添加家长失败:', error);
      throw error;
    }
  };

  const createParentForStudent = async (studentId, parentData) => {
    try {
      // 使用班级API，需要先找到学生所在的班级
      if (!selectedClass) {
        throw new Error('请先选择班级');
      }

      // 添加学生ID到parentData
      const dataWithStudent = {
        ...parentData,
        student_id: studentId
      };

      await createParentForClass(selectedClass.id, dataWithStudent);
    } catch (error) {
      console.error('添加家长失败:', error);
      // 暂时使用本地数据模拟
      const student = studentsData.find(s => s.id === studentId);
      const newParent = {
        id: Date.now(),
        name: parentData.name,
        phone: parentData.phone,
        relationship: parentData.relationship,
        student_name: student?.full_name || '未知学生',
        student_id: studentId,
        is_primary: parentData.is_primary || false
      };
      setParentsData(prev => [...prev, newParent]);
      updateClassStatistics('parents', parentsData.length + 1);
      message.success('家长添加成功（模拟数据）');
    }
  };

  const updateParentInfo = async (parentId, parentData) => {
    try {
      await apiCall(`/admin/parents/${parentId}`, {
        method: 'PUT',
        body: JSON.stringify(parentData)
      });
      message.success('家长信息更新成功');
      if (selectedClass) {
        fetchParentsData(selectedClass.id);
      }
    } catch (error) {
      console.error('更新家长失败:', error);
      // 暂时使用本地数据模拟
      setParentsData(prev => prev.map(parent =>
        parent.id === parentId
          ? { ...parent, ...parentData }
          : parent
      ));
      message.success('家长信息更新成功（模拟数据）');
    }
  };

  const removeParentFromStudent = async (parentId) => {
    if (!selectedClass) {
      message.error('请先选择班级');
      return;
    }

    try {
      // 使用班级特定的删除API，与系统班级管理保持一致
      await apiCall(`/admin/classes/${selectedClass.id}/parents/${parentId}`, {
        method: 'DELETE'
      });
      message.success('家长移除成功');
      fetchParentsData(selectedClass.id);
    } catch (error) {
      console.error('移除家长失败:', error);
      message.error('移除家长失败: ' + (error.message || '未知错误'));
      throw error;
    }
  };

  // 家长批量导入功能（新增，独立功能）
  const handleParentBatchImport = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);

          // 数据格式转换和验证
          const processedData = jsonData.map((row, index) => {
            // 支持多种列名格式
            const name = row['家长姓名'] || row['姓名'] || row['name'] || '';
            const username = row['用户名'] || row['登录用户名'] || row['username'] || '';
            const password = row['密码'] || row['初始密码'] || row['password'] || '123456';
            const phone = row['联系电话'] || row['电话'] || row['手机号'] || row['phone'] || '';
            const relationship = row['关系类型'] || row['关系'] || row['relationship'] || '';
            const studentName = row['学生姓名'] || row['学生'] || row['student_name'] || '';
            const studentUsername = row['学生学号'] || row['学号'] || row['student_username'] || '';

            // 关系类型标准化
            let standardRelationship = '';
            if (relationship.includes('父') || relationship === 'father') {
              standardRelationship = 'father';
            } else if (relationship.includes('母') || relationship === 'mother') {
              standardRelationship = 'mother';
            } else if (relationship.includes('监护') || relationship === 'guardian') {
              standardRelationship = 'guardian';
            }

            return {
              index: index + 1,
              name: String(name).trim(),
              username: String(username).trim(),
              password: String(password).trim(),
              phone: String(phone).trim(),
              relationship: standardRelationship,
              student_name: String(studentName).trim(),
              student_username: String(studentUsername).trim(),
              original: row,
              errors: []
            };
          }).filter(item => item.name && item.phone); // 过滤掉无效数据

          // 处理用户名
          const usernameSet = new Set();
          processedData.forEach(item => {
            if (!item.username) {
              // 自动生成用户名
              let baseUsername = item.name.replace(/[^a-zA-Z0-9]/g, '');
              if (!baseUsername) {
                baseUsername = 'parent';
              }
              let username = baseUsername;
              let counter = 1;
              while (usernameSet.has(username)) {
                username = `${baseUsername}${counter}`;
                counter++;
              }
              item.username = username;
            }
            usernameSet.add(item.username);
          });

          // 数据验证
          processedData.forEach(item => {
            // 验证必填字段
            if (!item.name) item.errors.push('家长姓名不能为空');
            if (!item.username) item.errors.push('用户名不能为空');
            if (!item.phone) item.errors.push('联系电话不能为空');
            if (!item.relationship) item.errors.push('关系类型无效');
            if (!item.student_name && !item.student_username) {
              item.errors.push('学生姓名或学号至少填写一个');
            }

            // 验证用户名格式
            if (item.username && !/^[a-zA-Z0-9_]+$/.test(item.username)) {
              item.errors.push('用户名只能包含字母、数字和下划线');
            }

            // 检查重复用户名（同一批次内）
            const usernameDuplicates = processedData.filter(other =>
              other !== item && other.username === item.username
            );
            if (usernameDuplicates.length > 0) {
              item.errors.push('用户名重复');
            }

            // 验证电话格式
            if (item.phone && !/^1[3-9]\d{9}$/.test(item.phone)) {
              item.errors.push('联系电话格式不正确');
            }

            // 匹配学生
            const matchedStudent = studentsData.find(student =>
              (item.student_name && student.full_name === item.student_name) ||
              (item.student_username && student.username === item.student_username)
            );

            if (matchedStudent) {
              item.matched_student = matchedStudent;
            } else {
              item.errors.push('未找到匹配的学生');
            }
          });

          setParentImportData(processedData);
          resolve(processedData);
        } catch (error) {
          console.error('家长数据解析失败:', error);
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  };

  // 下载家长导入模板（新增，独立功能）
  const downloadParentImportTemplate = () => {
    const templateData = [
      {
        家长姓名: '张三',
        用户名: 'zhangsan',
        密码: '123456',
        联系电话: '13800138001',
        关系类型: '父亲',
        学生姓名: '张小明',
        学生学号: '202501001'
      },
      {
        家长姓名: '李四',
        用户名: 'lisi',
        密码: '123456',
        联系电话: '13800138002',
        关系类型: '母亲',
        学生姓名: '李小红',
        学生学号: '202501002'
      },
      {
        家长姓名: '王五',
        用户名: 'wangwu',
        密码: '123456',
        联系电话: '13800138003',
        关系类型: '监护人',
        学生姓名: '王小华',
        学生学号: '202501003'
      }
    ];

    try {
      const ws = XLSX.utils.json_to_sheet(templateData);
      const wb = XLSX.utils.book_new();

      // 设置列宽
      ws['!cols'] = [
        { wch: 12 }, // 家长姓名
        { wch: 12 }, // 用户名
        { wch: 10 }, // 密码
        { wch: 15 }, // 联系电话
        { wch: 10 }, // 关系类型
        { wch: 12 }, // 学生姓名
        { wch: 12 }  // 学生学号
      ];

      XLSX.utils.book_append_sheet(wb, ws, '家长信息');

      const fileName = selectedClass
        ? `${selectedClass.name}_家长信息导入模板.xlsx`
        : '家长信息导入模板.xlsx';

      XLSX.writeFile(wb, fileName);
      message.success('模板下载成功');
    } catch (error) {
      console.error('模板下载失败:', error);
      message.error('模板下载失败');
    }
  };

  // 执行家长批量导入（新增，独立功能）
  const executeParentBatchImport = async () => {
    if (!selectedClass) {
      message.error('请先选择要导入的班级');
      return;
    }

    if (parentImportData.length === 0) {
      message.error('没有可导入的数据');
      return;
    }

    // 过滤出没有错误的数据
    const validData = parentImportData.filter(item => item.errors.length === 0);

    if (validData.length === 0) {
      message.error('没有有效的数据可以导入');
      return;
    }

    try {
      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      for (const parentData of validData) {
        try {
          await createParentForClass(selectedClass.id, {
            name: parentData.name,
            username: parentData.username,
            password: parentData.password,
            phone: parentData.phone,
            relationship: parentData.relationship,
            student_id: parentData.matched_student.id,
            is_primary: false // 批量导入默认不设为主要联系人
          });
          successCount++;
        } catch (error) {
          errorCount++;
          errors.push(`第${parentData.index}行 ${parentData.name}: ${error.message}`);
        }
      }

      // 显示导入结果
      if (successCount > 0) {
        message.success(`成功导入 ${successCount} 名家长${errorCount > 0 ? `，失败 ${errorCount} 名` : ''}`);
      }

      if (errors.length > 0 && errors.length <= 3) {
        errors.forEach(error => message.error(error, 3));
      } else if (errors.length > 3) {
        message.error(`导入过程中有 ${errorCount} 个错误，请检查数据`, 5);
      }

      // 关闭模态框并刷新数据
      setIsParentBatchImportModalVisible(false);
      setParentImportData([]);

    } catch (error) {
      console.error('批量导入失败:', error);
      message.error('批量导入失败');
    }
  };

  // 教师批量导入功能（新增，独立功能）
  const handleTeacherBatchImport = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);

          // 数据格式转换和验证
          const processedData = jsonData.map((row, index) => {
            // 支持多种列名格式
            const name = row['教师姓名'] || row['姓名'] || row['name'] || '';
            const username = row['用户名'] || row['登录用户名'] || row['username'] || '';
            // 处理密码字段 - 修复显示问题
            let originalPassword = row['密码'] || row['初始密码'] || row['password'] || '';
            let password = String(originalPassword).trim();

            // 如果是Excel中的密码掩码格式或空值，使用默认密码
            if (password.match(/^[●•*]+$/) || password === '' || password.length === 0) {
              password = '123456';
            }

            console.log(`🔐 密码处理: 原始="${originalPassword}" -> 最终="${password}"`);
            const phone = row['联系电话'] || row['电话'] || row['手机号'] || row['phone'] || '';
            const email = row['邮箱地址'] || row['邮箱'] || row['email'] || '';
            const subject = row['任教科目'] || row['科目'] || row['subject'] || '';
            const role = row['教师角色'] || row['角色'] || row['role'] || '';

            // 角色类型标准化
            let standardRole = 'subject_teacher'; // 默认为任课教师
            if (role.includes('班主任') || role === 'head_teacher') {
              standardRole = 'head_teacher';
            }

            // 科目标准化 - 数据库中存储的是带前缀的科目
            const subjectMapping = {
              // 不带前缀的科目映射为带前缀（数据库中的实际科目名）
              '语文': '初中语文', '数学': '初中数学', '英语': '初中英语',
              '物理': '初中物理', '化学': '初中化学', '生物': '初中生物',
              '历史': '初中历史', '地理': '初中地理', '政治': '初中道德与法治',
              '道德与法治': '初中道德与法治', '科学': '小学科学',

              // 带前缀的科目直接保持（数据库中的实际科目名）
              '初中语文': '初中语文', '初中数学': '初中数学', '初中英语': '初中英语',
              '初中物理': '初中物理', '初中化学': '初中化学', '初中生物': '初中生物',
              '初中历史': '初中历史', '初中地理': '初中地理', '初中政治': '初中道德与法治',
              '初中道德与法治': '初中道德与法治',

              '高中语文': '高中语文', '高中数学': '高中数学', '高中英语': '高中英语',
              '高中物理': '高中物理', '高中化学': '高中化学', '高中生物': '高中生物',
              '高中历史': '高中历史', '高中地理': '高中地理', '高中政治': '高中政治',

              '小学语文': '小学语文', '小学数学': '小学数学', '小学英语': '小学英语',
              '小学科学': '小学科学', '小学道德与法治': '小学道德与法治'
            };

            let standardSubject = subjectMapping[subject] || '';
            console.log(`🔍 科目映射: "${subject}" -> "${standardSubject}"`);

            return {
              index: index + 1,
              teacher_name: String(name).trim(),
              username: String(username).trim(),
              password: password, // 使用处理后的密码
              teacher_phone: String(phone).trim(),
              teacher_email: String(email).trim(),
              subject: standardSubject,
              role: standardRole,
              original: row,
              errors: []
            };
          }).filter(item => item.teacher_name); // 过滤掉无效数据

          // 处理用户名 - 改进唯一性生成
          const usernameSet = new Set();
          const timestamp = Date.now().toString().slice(-6); // 使用时间戳后6位

          processedData.forEach((item, index) => {
            if (!item.username || item.username === 'teacher' || item.username.match(/^teacher\d*$/)) {
              // 自动生成唯一用户名
              let baseUsername = item.teacher_name.replace(/[^a-zA-Z0-9]/g, '');
              if (!baseUsername || baseUsername.length < 2) {
                baseUsername = 'teacher';
              }

              // 使用时间戳和索引确保唯一性
              let username = `${baseUsername}_${timestamp}_${index + 1}`;

              // 确保用户名不重复
              let counter = 1;
              while (usernameSet.has(username)) {
                username = `${baseUsername}_${timestamp}_${index + 1}_${counter}`;
                counter++;
              }

              item.username = username;
              console.log(`🔄 自动生成用户名: "${item.teacher_name}" -> "${username}"`);
            }
            usernameSet.add(item.username);
          });

          // 数据验证
          processedData.forEach(item => {
            // 验证必填字段
            if (!item.teacher_name) item.errors.push('教师姓名不能为空');
            if (!item.username) item.errors.push('用户名不能为空');
            if (!item.subject) item.errors.push('任教科目不能为空或无效');

            // 验证用户名格式
            if (item.username && !/^[a-zA-Z0-9_]+$/.test(item.username)) {
              item.errors.push('用户名只能包含字母、数字和下划线');
            }

            // 验证电话格式（如果填写了）
            if (item.teacher_phone && !/^1[3-9]\d{9}$/.test(item.teacher_phone)) {
              item.errors.push('联系电话格式不正确');
            }

            // 验证邮箱格式（如果填写了）
            if (item.teacher_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(item.teacher_email)) {
              item.errors.push('邮箱地址格式不正确');
            }

            // 检查重复用户名（同一批次内）
            const usernameDuplicates = processedData.filter(other =>
              other !== item && other.username === item.username
            );
            if (usernameDuplicates.length > 0) {
              item.errors.push('用户名重复');
            }

            // 检查重复教师（同一批次内）
            const duplicates = processedData.filter(other =>
              other !== item &&
              other.teacher_name === item.teacher_name &&
              other.subject === item.subject
            );
            if (duplicates.length > 0) {
              item.errors.push('存在重复的教师（姓名+科目相同）');
            }
          });

          setTeacherImportData(processedData);
          resolve(processedData);
        } catch (error) {
          console.error('教师数据解析失败:', error);
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  };

  // 下载教师导入模板（新增，独立功能）
  const downloadTeacherImportTemplate = () => {
    // 创建教师导入模板，使用筛选后的科目数据
    const sampleSubjects = filteredSubjects.length >= 3
      ? [filteredSubjects[0].name, filteredSubjects[1].name, filteredSubjects[2].name]
      : selectedClass && selectedClass.grade && selectedClass.grade.includes('七')
        ? ['初中语文', '初中数学', '初中英语'] // 七年级默认使用初中科目
        : ['小学语文', '小学数学', '小学英语']; // 其他情况使用小学科目

    // 生成唯一的示例用户名
    const timestamp = Date.now().toString().slice(-4);

    const templateData = [
      {
        教师姓名: '张老师',
        用户名: `zhang_${timestamp}`,
        密码: '123456',
        任教科目: sampleSubjects[0],
        联系电话: '13800138001',
        邮箱地址: '<EMAIL>',
        教师角色: '班主任'
      },
      {
        教师姓名: '李老师',
        用户名: `li_${timestamp}`,
        密码: '123456',
        任教科目: sampleSubjects[1],
        联系电话: '13800138002',
        邮箱地址: '<EMAIL>',
        教师角色: '任课教师'
      },
      {
        教师姓名: '王老师',
        用户名: `wang_${timestamp}`,
        密码: '123456',
        任教科目: sampleSubjects[2],
        联系电话: '13800138003',
        邮箱地址: '<EMAIL>',
        教师角色: '任课教师'
      }
    ];

    try {
      const ws = XLSX.utils.json_to_sheet(templateData);
      const wb = XLSX.utils.book_new();

      // 设置列宽
      ws['!cols'] = [
        { wch: 12 }, // 教师姓名
        { wch: 15 }, // 用户名
        { wch: 10 }, // 密码
        { wch: 12 }, // 任教科目
        { wch: 15 }, // 联系电话
        { wch: 20 }, // 邮箱地址
        { wch: 12 }  // 教师角色
      ];

      XLSX.utils.book_append_sheet(wb, ws, '教师信息');

      const fileName = selectedClass
        ? `${selectedClass.name}_教师信息导入模板.xlsx`
        : '教师信息导入模板.xlsx';

      XLSX.writeFile(wb, fileName);
      message.success('模板下载成功');
    } catch (error) {
      console.error('模板下载失败:', error);
      message.error('模板下载失败');
    }
  };

  // 执行教师批量导入（新增，独立功能）
  const executeTeacherBatchImport = async () => {
    if (!selectedClass) {
      message.error('请先选择要导入的班级');
      return;
    }

    if (teacherImportData.length === 0) {
      message.error('没有可导入的数据');
      return;
    }

    // 过滤出没有错误的数据
    const validData = teacherImportData.filter(item => item.errors.length === 0);

    if (validData.length === 0) {
      message.error('没有有效的数据可以导入');
      return;
    }

    // 检查班主任冲突
    const headTeachers = validData.filter(item => item.role === 'head_teacher');
    const currentHeadTeacher = teachersData.find(t => t.role === 'head_teacher');

    if (headTeachers.length > 1) {
      message.error('不能同时导入多个班主任');
      return;
    }

    if (headTeachers.length === 1 && currentHeadTeacher) {
      const confirmed = await new Promise(resolve => {
        Modal.confirm({
          title: '班主任冲突',
          content: `当前班级已有班主任 ${currentHeadTeacher.name}，导入的 ${headTeachers[0].teacher_name} 将替换现有班主任，是否继续？`,
          onOk: () => resolve(true),
          onCancel: () => resolve(false)
        });
      });

      if (!confirmed) return;
    }

    try {
      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      for (const teacherData of validData) {
        try {
          // 转换数据格式以匹配后端API
          const apiData = {
            name: teacherData.teacher_name,
            username: teacherData.username,
            password: teacherData.password,
            phone: teacherData.teacher_phone,
            email: teacherData.teacher_email,
            subject: teacherData.subject,
            role: teacherData.role
          };

          console.log(`🚀 发送教师数据:`, apiData);
          await createTeacherForClass(selectedClass.id, apiData);
          successCount++;
        } catch (error) {
          errorCount++;
          errors.push(`第${teacherData.index}行 ${teacherData.teacher_name}: ${error.message}`);
        }
      }

      // 显示导入结果
      if (successCount > 0) {
        message.success(`成功导入 ${successCount} 名教师${errorCount > 0 ? `，失败 ${errorCount} 名` : ''}`);
      }

      if (errors.length > 0 && errors.length <= 3) {
        errors.forEach(error => message.error(error, 3));
      } else if (errors.length > 3) {
        message.error(`导入过程中有 ${errorCount} 个错误，请检查数据`, 5);
      }

      // 关闭模态框并刷新数据
      setIsTeacherBatchImportModalVisible(false);
      setTeacherImportData([]);

    } catch (error) {
      console.error('批量导入失败:', error);
      message.error('批量导入失败');
    }
  };

  // 数据导出功能（新增，独立功能）
  const exportClassData = (exportType, dataType) => {
    if (!selectedClass) {
      message.error('请先选择要导出的班级');
      return;
    }

    try {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const className = selectedClass.name;

      if (exportType === 'excel') {
        exportToExcel(dataType, className, timestamp);
      } else if (exportType === 'pdf') {
        exportToPDF(dataType, className, timestamp);
      }
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    }
  };

  // Excel导出功能（新增，独立功能）
  const exportToExcel = (dataType, className, timestamp) => {
    const workbook = XLSX.utils.book_new();

    if (dataType === 'all' || dataType === 'students') {
      // 导出学生数据
      const studentData = studentsData.map((student, index) => ({
        序号: index + 1,
        学号: student.username,
        姓名: student.full_name,
        邮箱: student.email || '',
        班级: className
      }));

      const studentSheet = XLSX.utils.json_to_sheet(studentData);
      studentSheet['!cols'] = [
        { wch: 8 },  // 序号
        { wch: 15 }, // 学号
        { wch: 12 }, // 姓名
        { wch: 25 }, // 邮箱
        { wch: 15 }  // 班级
      ];
      XLSX.utils.book_append_sheet(workbook, studentSheet, '学生名单');
    }

    if (dataType === 'all' || dataType === 'teachers') {
      // 导出教师数据
      const teacherData = teachersData.map((teacher, index) => ({
        序号: index + 1,
        姓名: teacher.name,
        任教科目: teacher.subject,
        角色: teacher.role === 'head_teacher' ? '班主任' : '任课教师',
        联系电话: teacher.phone || '',
        邮箱: teacher.email || '',
        班级: className
      }));

      const teacherSheet = XLSX.utils.json_to_sheet(teacherData);
      teacherSheet['!cols'] = [
        { wch: 8 },  // 序号
        { wch: 12 }, // 姓名
        { wch: 12 }, // 任教科目
        { wch: 12 }, // 角色
        { wch: 15 }, // 联系电话
        { wch: 25 }, // 邮箱
        { wch: 15 }  // 班级
      ];
      XLSX.utils.book_append_sheet(workbook, teacherSheet, '教师名单');
    }

    if (dataType === 'all' || dataType === 'parents') {
      // 导出家长数据
      const parentData = parentsData.map((parent, index) => ({
        序号: index + 1,
        家长姓名: parent.name,
        关系类型: parent.relationship === 'father' ? '父亲' :
                 parent.relationship === 'mother' ? '母亲' : '监护人',
        学生姓名: parent.student_name,
        联系电话: parent.phone || '',
        主要联系人: parent.is_primary ? '是' : '否',
        班级: className
      }));

      const parentSheet = XLSX.utils.json_to_sheet(parentData);
      parentSheet['!cols'] = [
        { wch: 8 },  // 序号
        { wch: 12 }, // 家长姓名
        { wch: 10 }, // 关系类型
        { wch: 12 }, // 学生姓名
        { wch: 15 }, // 联系电话
        { wch: 12 }, // 主要联系人
        { wch: 15 }  // 班级
      ];
      XLSX.utils.book_append_sheet(workbook, parentSheet, '家长名单');
    }

    if (dataType === 'all' || dataType === 'summary') {
      // 导出统计汇总
      const summaryData = [
        { 项目: '班级名称', 数量: className, 备注: '' },
        { 项目: '学生人数', 数量: studentsData.length, 备注: '人' },
        { 项目: '教师人数', 数量: teachersData.length, 备注: '人' },
        { 项目: '家长人数', 数量: parentsData.length, 备注: '人' },
        { 项目: '班主任', 数量: classStatistics.headTeacher?.name || '未指定', 备注: '' },
        { 项目: '导出时间', 数量: new Date().toLocaleString(), 备注: '' }
      ];

      const summarySheet = XLSX.utils.json_to_sheet(summaryData);
      summarySheet['!cols'] = [
        { wch: 15 }, // 项目
        { wch: 20 }, // 数量
        { wch: 10 }  // 备注
      ];
      XLSX.utils.book_append_sheet(workbook, summarySheet, '统计汇总');
    }

    // 生成文件名
    const dataTypeMap = {
      all: '完整数据',
      students: '学生名单',
      teachers: '教师名单',
      parents: '家长名单',
      summary: '统计汇总'
    };

    const fileName = `${className}_${dataTypeMap[dataType]}_${timestamp}.xlsx`;
    XLSX.writeFile(workbook, fileName);
    message.success(`Excel文件导出成功：${fileName}`);
  };

  // PDF导出功能（新增，独立功能）
  const exportToPDF = (dataType, className, timestamp) => {
    // 创建PDF内容
    let pdfContent = `班级管理报表\n\n`;
    pdfContent += `班级：${className}\n`;
    pdfContent += `导出时间：${new Date().toLocaleString()}\n\n`;

    if (dataType === 'all' || dataType === 'summary') {
      pdfContent += `统计汇总：\n`;
      pdfContent += `学生人数：${studentsData.length} 人\n`;
      pdfContent += `教师人数：${teachersData.length} 人\n`;
      pdfContent += `家长人数：${parentsData.length} 人\n`;
      pdfContent += `班主任：${classStatistics.headTeacher?.name || '未指定'}\n\n`;
    }

    if (dataType === 'all' || dataType === 'students') {
      pdfContent += `学生名单：\n`;
      studentsData.forEach((student, index) => {
        pdfContent += `${index + 1}. ${student.full_name} (${student.username})\n`;
      });
      pdfContent += `\n`;
    }

    if (dataType === 'all' || dataType === 'teachers') {
      pdfContent += `教师名单：\n`;
      teachersData.forEach((teacher, index) => {
        const role = teacher.role === 'head_teacher' ? '班主任' : '任课教师';
        pdfContent += `${index + 1}. ${teacher.name} - ${teacher.subject} (${role})\n`;
      });
      pdfContent += `\n`;
    }

    if (dataType === 'all' || dataType === 'parents') {
      pdfContent += `家长名单：\n`;
      parentsData.forEach((parent, index) => {
        const relationship = parent.relationship === 'father' ? '父亲' :
                           parent.relationship === 'mother' ? '母亲' : '监护人';
        pdfContent += `${index + 1}. ${parent.name} (${parent.student_name}的${relationship})\n`;
      });
    }

    // 创建并下载文本文件（简化的PDF实现）
    const dataTypeMap = {
      all: '完整报表',
      students: '学生名单',
      teachers: '教师名单',
      parents: '家长名单',
      summary: '统计汇总'
    };

    const fileName = `${className}_${dataTypeMap[dataType]}_${timestamp}.txt`;
    const blob = new Blob([pdfContent], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    message.success(`报表文件导出成功：${fileName}`);
  };

  // 学生批量导入功能（新增，独立功能）
  const handleStudentImport = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);

          // 数据格式转换和验证
          const processedData = jsonData.map((row, index) => {
            // 支持多种列名格式
            const username = row['学号'] || row['用户名'] || row['username'] || row['学生学号'] || '';
            const fullName = row['姓名'] || row['学生姓名'] || row['full_name'] || row['name'] || '';
            const email = row['邮箱'] || row['邮箱地址'] || row['email'] || row['电子邮箱'] || '';

            return {
              index: index + 1,
              username: String(username).trim(),
              full_name: String(fullName).trim(),
              email: String(email).trim() || `${username}@example.com`,
              original: row
            };
          }).filter(item => item.username && item.full_name); // 过滤掉无效数据

          setStudentImportData(processedData);
          resolve(processedData);
        } catch (error) {
          console.error('文件解析失败:', error);
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  };

  // 下载学生导入模板（新增，独立功能）
  const downloadStudentImportTemplate = () => {
    // 创建模板数据
    const templateData = [
      {
        学号: '202501001',
        姓名: '张三',
        邮箱: '<EMAIL>'
      },
      {
        学号: '202501002',
        姓名: '李四',
        邮箱: '<EMAIL>'
      },
      {
        学号: '202501003',
        姓名: '王五',
        邮箱: '<EMAIL>'
      },
      {
        学号: '202501004',
        姓名: '赵六',
        邮箱: '<EMAIL>'
      },
      {
        学号: '202501005',
        姓名: '孙七',
        邮箱: '<EMAIL>'
      }
    ];

    try {
      // 创建工作簿
      const ws = XLSX.utils.json_to_sheet(templateData);
      const wb = XLSX.utils.book_new();

      // 设置列宽
      ws['!cols'] = [
        { wch: 12 }, // 学号列宽度
        { wch: 10 }, // 姓名列宽度
        { wch: 25 }  // 邮箱列宽度
      ];

      // 添加工作表
      XLSX.utils.book_append_sheet(wb, ws, '学生信息');

      // 生成文件名
      const fileName = selectedClass
        ? `${selectedClass.name}_学生导入模板.xlsx`
        : '学生导入模板.xlsx';

      // 下载文件
      XLSX.writeFile(wb, fileName);

      message.success('模板下载成功');
    } catch (error) {
      console.error('模板下载失败:', error);
      message.error('模板下载失败');
    }
  };

  // 执行学生批量导入（新增，独立功能）
  const executeStudentImport = async () => {
    if (!selectedClass) {
      message.error('请先选择要导入的班级');
      return;
    }

    if (studentImportData.length === 0) {
      message.error('没有可导入的数据');
      return;
    }

    try {
      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      for (const student of studentImportData) {
        try {
          await createStudentForClass(selectedClass.id, {
            username: student.username,
            full_name: student.full_name,
            email: student.email
          });
          successCount++;
        } catch (error) {
          errorCount++;
          errors.push(`第${student.index}行 ${student.full_name}(${student.username}): ${error.message}`);
        }
      }

      // 显示导入结果
      if (successCount > 0) {
        message.success(`成功导入 ${successCount} 名学生${errorCount > 0 ? `，失败 ${errorCount} 名` : ''}`);
      }

      if (errors.length > 0 && errors.length <= 5) {
        // 如果错误不多，显示具体错误信息
        errors.forEach(error => message.error(error, 3));
      } else if (errors.length > 5) {
        message.error(`导入过程中有 ${errorCount} 个错误，请检查数据格式`, 5);
      }

      // 关闭模态框并刷新数据
      setIsStudentImportModalVisible(false);
      setStudentImportData([]);

      // 刷新学生数据
      if (selectedClass) {
        await fetchStudentsData(selectedClass.id);
      }

    } catch (error) {
      console.error('批量导入失败:', error);
      message.error('批量导入失败');
    }
  };

  // 搜索和筛选功能（新增，独立功能）
  const performSearch = (keyword, filters) => {
    if (!selectedClass) {
      setSearchFilteredData({ students: [], teachers: [], parents: [] });
      return;
    }

    const searchTerm = keyword.toLowerCase().trim();

    // 搜索学生
    let filteredStudents = studentsData.filter(student => {
      const matchKeyword = !searchTerm ||
        student.full_name.toLowerCase().includes(searchTerm) ||
        student.username.toLowerCase().includes(searchTerm) ||
        (student.email && student.email.toLowerCase().includes(searchTerm));

      const matchRole = filters.role === 'all' || filters.role === 'student';
      const matchContact = filters.hasContact === 'all' ||
        (filters.hasContact === 'yes' && student.email) ||
        (filters.hasContact === 'no' && !student.email);

      return matchKeyword && matchRole && matchContact;
    });

    // 搜索教师
    let filteredTeachers = teachersData.filter(teacher => {
      const matchKeyword = !searchTerm ||
        teacher.name.toLowerCase().includes(searchTerm) ||
        teacher.subject.toLowerCase().includes(searchTerm) ||
        (teacher.phone && teacher.phone.includes(searchTerm)) ||
        (teacher.email && teacher.email.toLowerCase().includes(searchTerm));

      const matchRole = filters.role === 'all' || filters.role === 'teacher';
      const matchSubject = filters.subject === 'all' || teacher.subject === filters.subject;
      const matchContact = filters.hasContact === 'all' ||
        (filters.hasContact === 'yes' && (teacher.phone || teacher.email)) ||
        (filters.hasContact === 'no' && !teacher.phone && !teacher.email);

      return matchKeyword && matchRole && matchSubject && matchContact;
    });

    // 搜索家长
    let filteredParents = parentsData.filter(parent => {
      const matchKeyword = !searchTerm ||
        parent.name.toLowerCase().includes(searchTerm) ||
        parent.student_name.toLowerCase().includes(searchTerm) ||
        (parent.phone && parent.phone.includes(searchTerm));

      const matchRole = filters.role === 'all' || filters.role === 'parent';
      const matchRelationship = filters.relationship === 'all' || parent.relationship === filters.relationship;
      const matchContact = filters.hasContact === 'all' ||
        (filters.hasContact === 'yes' && parent.phone) ||
        (filters.hasContact === 'no' && !parent.phone);

      return matchKeyword && matchRole && matchRelationship && matchContact;
    });

    setSearchFilteredData({
      students: filteredStudents,
      teachers: filteredTeachers,
      parents: filteredParents
    });
  };

  // 处理搜索关键词变化（新增，独立功能）
  const handleSearchChange = (value) => {
    setSearchKeyword(value);
    performSearch(value, advancedFilters);
  };

  // 处理高级筛选变化（新增，独立功能）
  const handleAdvancedFilterChange = (filterType, value) => {
    const newFilters = { ...advancedFilters, [filterType]: value };
    setAdvancedFilters(newFilters);
    performSearch(searchKeyword, newFilters);
  };

  // 重置搜索和筛选（新增，独立功能）
  const resetSearchAndFilters = () => {
    setSearchKeyword('');
    setAdvancedFilters({
      role: 'all',
      subject: 'all',
      relationship: 'all',
      dateRange: null,
      hasContact: 'all'
    });
    setSearchFilteredData({
      students: studentsData,
      teachers: teachersData,
      parents: parentsData
    });
  };

  // 获取当前显示的数据（新增，独立功能）
  const getCurrentDisplayData = (dataType) => {
    const hasActiveSearch = searchKeyword ||
      advancedFilters.role !== 'all' ||
      advancedFilters.subject !== 'all' ||
      advancedFilters.relationship !== 'all' ||
      advancedFilters.hasContact !== 'all';

    if (!hasActiveSearch) {
      // 没有搜索筛选时，返回原始数据
      switch(dataType) {
        case 'students': return studentsData;
        case 'teachers': return teachersData;
        case 'parents': return parentsData;
        default: return [];
      }
    } else {
      // 有搜索筛选时，返回过滤后的数据
      return searchFilteredData[dataType] || [];
    }
  };

  // 获取搜索结果统计（新增，独立功能）
  const getSearchResultStats = () => {
    const hasActiveSearch = searchKeyword ||
      advancedFilters.role !== 'all' ||
      advancedFilters.subject !== 'all' ||
      advancedFilters.relationship !== 'all' ||
      advancedFilters.hasContact !== 'all';

    if (!hasActiveSearch) {
      return null;
    }

    const totalResults = searchFilteredData.students.length + searchFilteredData.teachers.length + searchFilteredData.parents.length;
    return {
      total: totalResults,
      students: searchFilteredData.students.length,
      teachers: searchFilteredData.teachers.length,
      parents: searchFilteredData.parents.length
    };
  };

  // 添加已存在学生到班级
  const addExistingStudentToClass = async (classId, studentId) => {
    try {
      await apiCall(`/admin/classes/${classId}/students`, {
        method: 'POST',
        body: JSON.stringify({ student_id: studentId })
      });
      message.success('学生添加成功');
      fetchStudentsData(classId);
      fetchClassesData(); // 更新班级学生数量
    } catch (error) {
      console.error('添加已存在学生失败:', error);
      message.error('添加已存在学生失败');
      throw error;
    }
  };

  // 创建新学生并添加到班级
  const createStudentForClass = async (classId, studentData) => {
    try {
      await apiCall(`/admin/classes/${classId}/students/create`, {
        method: 'POST',
        body: JSON.stringify(studentData)
      });
      message.success('学生创建并添加成功');
      fetchStudentsData(classId);
      fetchClassesData(); // 更新班级学生数量
    } catch (error) {
      console.error('创建学生失败:', error);
      message.error('创建学生失败');
      throw error;
    }
  };

  // 删除学生
  const removeStudentFromClass = async (classId, studentId) => {
    try {
      await apiCall(`/admin/classes/${classId}/students/${studentId}`, {
        method: 'DELETE'
      });
      message.success('学生移除成功');
      fetchStudentsData(classId);
      fetchClassesData(); // 更新班级学生数量
    } catch (error) {
      console.error('移除学生失败:', error);
      message.error('移除学生失败');
    }
  };

  // 创建班级
  const createClass = async (classData) => {
    try {
      if (!user || !user.school_id) {
        message.error('用户信息不完整，无法创建班级');
        return;
      }

      const payload = {
        ...classData,
        school_id: user.school_id // 使用当前用户的学校ID
      };

      await apiCall('/admin/classes', {
        method: 'POST',
        body: JSON.stringify(payload)
      });
      message.success('班级创建成功');
      fetchClassesData();
    } catch (error) {
      console.error('创建班级失败:', error);
      message.error('创建班级失败');
      throw error;
    }
  };

  // 更新班级
  const updateClass = async (classId, classData) => {
    try {
      await apiCall(`/admin/classes/${classId}`, {
        method: 'PUT',
        body: JSON.stringify(classData)
      });
      message.success('班级更新成功');
      fetchClassesData();
    } catch (error) {
      console.error('更新班级失败:', error);
      message.error('更新班级失败');
      throw error;
    }
  };



  // 删除班级
  const deleteClass = async (classId) => {
    try {
      console.log('开始删除班级:', classId);

      // 检查班级是否存在于当前数据中
      const classExists = classesData.find(cls => cls.id === classId);
      if (!classExists) {
        message.error('班级不存在或已被删除');
        fetchClassesData(); // 刷新数据
        return;
      }

      // 使用axios实例而不是自定义的fetch函数
      const { deleteClassById } = await import('../utils/api');
      await deleteClassById(classId);

      console.log('班级删除成功:', classId);
      message.success('班级删除成功');

      // 清除选中状态
      if (selectedClass && selectedClass.id === classId) {
        setSelectedClass(null);
        setStudentsData([]);
      }

      // 刷新数据
      fetchClassesData();
    } catch (error) {
      console.error('删除班级失败:', error);

      if (error.message && error.message.includes('404')) {
        message.error('班级不存在或已被删除');
        fetchClassesData(); // 刷新数据以同步状态
      } else if (error.message && error.message.includes('401')) {
        message.error('认证失败，请重新登录');
        // 清除本地存储的认证信息
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        // 跳转到登录页
        window.location.href = '/login';
      } else {
        message.error(`删除班级失败: ${error.message || '未知错误'}`);
      }
    }
  };



  // 计算统计数据
  const calculateStatistics = (data) => {
    const stats = {
      totalClasses: data.length,
      totalStudents: data.reduce((sum, cls) => sum + (cls.student_count || 0), 0),
      gradeDistribution: {},
      averageStudentsPerClass: 0
    };

    // 年级分布
    data.forEach(cls => {
      const grade = cls.grade || '未分年级';
      if (!stats.gradeDistribution[grade]) {
        stats.gradeDistribution[grade] = { classes: 0, students: 0 };
      }
      stats.gradeDistribution[grade].classes++;
      stats.gradeDistribution[grade].students += cls.student_count || 0;
    });

    // 平均每班人数
    if (stats.totalClasses > 0) {
      stats.averageStudentsPerClass = Math.round(stats.totalStudents / stats.totalClasses);
    }

    setStatistics(stats);
  };

  // 年级排序函数
  const sortGrades = (grades) => {
    const gradeOrder = [
      '一年级', '二年级', '三年级', '四年级', '五年级', '六年级',
      '七年级', '八年级', '九年级',
      '高一', '高二', '高三'
    ];

    return grades.sort((a, b) => {
      const indexA = gradeOrder.indexOf(a);
      const indexB = gradeOrder.indexOf(b);

      // 如果都在预定义列表中，按顺序排序
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB;
      }

      // 如果只有一个在预定义列表中，预定义的排在前面
      if (indexA !== -1) return -1;
      if (indexB !== -1) return 1;

      // 如果都不在预定义列表中，按字母排序
      return a.localeCompare(b);
    });
  };

  // 获取唯一年级列表（按逻辑顺序排序）
  const uniqueGrades = sortGrades([...new Set(classesData.map(cls => cls.grade).filter(Boolean))]);

  // 班级排序函数
  const sortClasses = (classes) => {
    return classes.sort((a, b) => {
      const nameA = a.name;
      const nameB = b.name;

      // 提取班级名称中的数字部分
      const extractNumber = (name) => {
        const match = name.match(/(\d+)/);
        return match ? parseInt(match[1]) : 999;
      };

      // 提取班级名称的前缀部分（如"高一"、"七年级"等）
      const extractPrefix = (name) => {
        return name.replace(/[(\d)班]/g, '').trim();
      };

      const prefixA = extractPrefix(nameA);
      const prefixB = extractPrefix(nameB);
      const numberA = extractNumber(nameA);
      const numberB = extractNumber(nameB);

      // 如果前缀相同，按数字排序
      if (prefixA === prefixB) {
        return numberA - numberB;
      }

      // 如果前缀不同，按字母排序
      return prefixA.localeCompare(prefixB);
    });
  };

  // 获取当前年级下的班级列表（按逻辑顺序排序）
  const getClassesInGrade = () => {
    if (gradeFilter === 'all') return [];
    const classes = classesData
      .filter(cls => cls.grade === gradeFilter)
      .map(cls => ({ id: cls.id, name: cls.name }));

    return sortClasses(classes);
  };

  const classesInGrade = getClassesInGrade();

  // 筛选数据
  const filterData = () => {
    let filtered = classesData;

    // 搜索筛选
    if (searchText) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // 年级筛选
    if (gradeFilter !== 'all') {
      filtered = filtered.filter(item => item.grade === gradeFilter);
    }

    // 班级筛选
    if (classFilter !== 'all') {
      filtered = filtered.filter(item => item.id.toString() === classFilter);
    }

    setFilteredData(filtered);
  };

  // 年级筛选变化处理
  const handleGradeChange = (value) => {
    setGradeFilter(value);
    setClassFilter('all'); // 重置班级筛选
    if (selectedClass && value !== 'all') {
      // 如果当前选中的班级不在新年级中，清除选择
      const selectedClassGrade = classesData.find(cls => cls.id === selectedClass.id)?.grade;
      if (selectedClassGrade !== value) {
        setSelectedClass(null);
        setStudentsData([]);
      }
    }
  };

  // 班级筛选变化处理
  const handleClassChange = (value) => {
    console.log('班级筛选变化:', value, 'classesData长度:', classesData.length);
    setClassFilter(value);
    if (value !== 'all') {
      // 自动选择该班级
      const classData = classesData.find(cls => cls.id.toString() === value);
      console.log('找到的班级数据:', classData);
      if (classData) {
        setSelectedClass(classData);
        console.log('开始加载班级数据, classId:', classData.id, 'activeTab:', activeTab);
        loadClassData(classData.id);
      } else {
        console.error('未找到班级数据, value:', value, '可用班级:', classesData.map(c => ({id: c.id, name: c.name})));
        message.error('未找到指定班级');
      }
    } else {
      // 选择"所有班级"时，清除选中状态
      setSelectedClass(null);
      clearAllData();
    }
  };

  // 根据当前标签页加载对应数据
  const loadClassData = (classId) => {
    console.log('loadClassData 被调用, classId:', classId, 'activeTab:', activeTab);
    if (!classId) {
      console.error('classId 为空，无法加载数据');
      return;
    }

    // 总是先加载学生数据，因为家长管理等功能需要学生信息
    console.log('加载学生数据...');
    fetchStudentsData(classId);

    // 根据当前标签页加载对应的额外数据
    switch(activeTab) {
      case 'teachers':
        console.log('加载教师数据...');
        fetchTeachersData(classId);
        break;
      case 'parents':
        console.log('加载家长数据...');
        fetchParentsData(classId);
        break;
      case 'students':
      default:
        // 学生数据已经在上面加载了
        console.log('学生标签页，学生数据已加载');
        break;
    }
  };

  // 清除所有数据
  const clearAllData = () => {
    setStudentsData([]);
    setTeachersData([]);
    setParentsData([]);
    setClassStatistics({
      students: 0,
      teachers: 0,
      parents: 0,
      headTeacher: null
    });
  };

  // 标签页切换处理
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    if (selectedClass) {
      loadClassData(selectedClass.id);
    }
  };



  // 初始化数据
  useEffect(() => {
    if (user && user.school_id) {
      fetchClassesData();
      fetchSubjectsData(); // 获取科目数据
    }
  }, [user]);

  useEffect(() => {
    filterData();
  }, [searchText, gradeFilter, classFilter, classesData]);

  // 当选中班级或科目数据变化时，更新筛选的科目
  useEffect(() => {
    updateFilteredSubjects();
  }, [selectedClass, subjectsData]);



  return (
    <div style={{ padding: 24, background: '#f0f2f5', minHeight: '100vh' }}>
      <Title level={3} style={{ marginBottom: 24 }}>班级管理</Title>
          {/* 统计卡片 */}
          <div style={{ marginBottom: 24 }}>
            <Space size="large">
              <Card size="small" style={{ minWidth: 120 }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                    {statistics.totalClasses || 0}
                  </div>
                  <div style={{ color: '#666' }}>班级总数</div>
                </div>
              </Card>
              
              <Card size="small" style={{ minWidth: 120 }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                    {statistics.totalStudents || 0}
                  </div>
                  <div style={{ color: '#666' }}>学生总数</div>
                </div>
              </Card>
              
              <Card size="small" style={{ minWidth: 120 }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#fa8c16' }}>
                    {statistics.averageStudentsPerClass || 0}
                  </div>
                  <div style={{ color: '#666' }}>平均班级人数</div>
                </div>
              </Card>
              
              <Card size="small" style={{ minWidth: 120 }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#722ed1' }}>
                    {Object.keys(statistics.gradeDistribution || {}).length}
                  </div>
                  <div style={{ color: '#666' }}>年级数量</div>
                </div>
              </Card>
            </Space>
          </div>

          {/* 筛选条件和操作按钮 */}
          <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 16 }}>
            {/* 左侧筛选条件 */}
            <Space size="middle" wrap>
              <Input
                placeholder="搜索班级"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 200 }}
                prefix={<SearchOutlined />}
                allowClear
              />

              <Select
                placeholder="筛选年级"
                value={gradeFilter}
                onChange={handleGradeChange}
                style={{ width: 120 }}
                allowClear
              >
                <Option value="all">所有年级</Option>
                {uniqueGrades.map(grade => (
                  <Option key={grade} value={grade}>
                    {grade}
                  </Option>
                ))}
              </Select>

              <Select
                placeholder="筛选班级"
                value={classFilter}
                onChange={handleClassChange}
                style={{ width: 150 }}
                disabled={gradeFilter === 'all'}
                notFoundContent={gradeFilter === 'all' ? '请先选择年级' : '该年级下暂无班级'}
                allowClear
              >
                <Option value="all">所有班级</Option>
                {classesInGrade.map(cls => (
                  <Option key={cls.id} value={cls.id.toString()}>
                    {cls.name}
                  </Option>
                ))}
              </Select>

              {gradeFilter !== 'all' && classesInGrade.length > 0 && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {gradeFilter} 共 {classesInGrade.length} 个班级
                </Text>
              )}
            </Space>

            {/* 右侧操作按钮 */}
            <Space>
              {canManageClass() && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingClass(null);
                    classForm.resetFields();
                    setIsClassModalVisible(true);
                  }}
                >
                  新建班级
                </Button>
              )}

              <Button
                icon={<DownloadOutlined />}
                onClick={() => {
                  if (!selectedClass) {
                    message.warning('请先选择班级');
                    return;
                  }
                  setIsExportModalVisible(true);
                }}
              >
                导出数据
              </Button>

              <Input.Search
                placeholder="搜索姓名、学号、科目..."
                allowClear
                style={{ width: 250 }}
                value={searchKeyword}
                onChange={(e) => handleSearchChange(e.target.value)}
                onSearch={(value) => handleSearchChange(value)}
              />

              <Button
                icon={<SearchOutlined />}
                onClick={() => setIsAdvancedFilterVisible(!isAdvancedFilterVisible)}
                type={isAdvancedFilterVisible ? 'primary' : 'default'}
              >
                高级筛选
              </Button>

              {(searchKeyword || advancedFilters.role !== 'all' || advancedFilters.subject !== 'all' ||
                advancedFilters.relationship !== 'all' || advancedFilters.hasContact !== 'all') && (
                <Button
                  onClick={resetSearchAndFilters}
                  type="text"
                  style={{ color: '#ff4d4f' }}
                >
                  清除筛选
                </Button>
              )}
            </Space>
          </div>

          {/* 高级筛选面板 */}
          {isAdvancedFilterVisible && selectedClass && (
            <Card
              size="small"
              style={{ marginBottom: 16 }}
              title={
                <Space>
                  <SearchOutlined />
                  <span>高级筛选</span>
                  {(() => {
                    const stats = getSearchResultStats();
                    return stats && (
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        （找到 {stats.total} 条结果）
                      </Text>
                    );
                  })()}
                </Space>
              }
              extra={
                <Button
                  type="text"
                  size="small"
                  onClick={() => setIsAdvancedFilterVisible(false)}
                >
                  收起
                </Button>
              }
            >
              <Row gutter={[16, 16]}>
                <Col span={6}>
                  <div>
                    <Text strong style={{ fontSize: '12px' }}>角色类型</Text>
                    <Select
                      style={{ width: '100%', marginTop: 4 }}
                      value={advancedFilters.role}
                      onChange={(value) => handleAdvancedFilterChange('role', value)}
                      size="small"
                    >
                      <Option value="all">全部角色</Option>
                      <Option value="student">学生</Option>
                      <Option value="teacher">教师</Option>
                      <Option value="parent">家长</Option>
                    </Select>
                  </div>
                </Col>

                <Col span={6}>
                  <div>
                    <Text strong style={{ fontSize: '12px' }}>任教科目</Text>
                    <Select
                      style={{ width: '100%', marginTop: 4 }}
                      value={advancedFilters.subject}
                      onChange={(value) => handleAdvancedFilterChange('subject', value)}
                      size="small"
                    >
                      <Option value="all">全部科目</Option>
                      <Option value="语文">语文</Option>
                      <Option value="数学">数学</Option>
                      <Option value="英语">英语</Option>
                      <Option value="物理">物理</Option>
                      <Option value="化学">化学</Option>
                      <Option value="生物">生物</Option>
                      <Option value="历史">历史</Option>
                      <Option value="地理">地理</Option>
                      <Option value="政治">政治</Option>
                      <Option value="体育">体育</Option>
                      <Option value="音乐">音乐</Option>
                      <Option value="美术">美术</Option>
                      <Option value="信息技术">信息技术</Option>
                    </Select>
                  </div>
                </Col>

                <Col span={6}>
                  <div>
                    <Text strong style={{ fontSize: '12px' }}>家长关系</Text>
                    <Select
                      style={{ width: '100%', marginTop: 4 }}
                      value={advancedFilters.relationship}
                      onChange={(value) => handleAdvancedFilterChange('relationship', value)}
                      size="small"
                    >
                      <Option value="all">全部关系</Option>
                      <Option value="father">父亲</Option>
                      <Option value="mother">母亲</Option>
                      <Option value="guardian">监护人</Option>
                    </Select>
                  </div>
                </Col>

                <Col span={6}>
                  <div>
                    <Text strong style={{ fontSize: '12px' }}>联系方式</Text>
                    <Select
                      style={{ width: '100%', marginTop: 4 }}
                      value={advancedFilters.hasContact}
                      onChange={(value) => handleAdvancedFilterChange('hasContact', value)}
                      size="small"
                    >
                      <Option value="all">全部</Option>
                      <Option value="yes">有联系方式</Option>
                      <Option value="no">无联系方式</Option>
                    </Select>
                  </div>
                </Col>
              </Row>

              {(() => {
                const stats = getSearchResultStats();
                return stats && (
                  <div style={{ marginTop: 12, padding: 8, background: '#f6ffed', borderRadius: 4 }}>
                    <Text style={{ fontSize: '12px' }}>
                      搜索结果：学生 {stats.students} 人，教师 {stats.teachers} 人，家长 {stats.parents} 人，共 {stats.total} 条
                    </Text>
                  </div>
                );
              })()}
            </Card>
          )}

          {/* 班级统计卡片 */}
          {selectedClass && (
            <div style={{ marginBottom: 24 }}>
              <div style={{ marginBottom: 16 }}>
                <Text strong style={{ fontSize: '16px' }}>
                  {selectedClass.name} - 班级概况
                </Text>
              </div>

              <div style={{ display: 'flex', gap: 16 }}>
                <Card size="small" style={{ flex: 1, textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                    {classStatistics.students}
                  </div>
                  <div style={{ color: '#666', fontSize: '12px' }}>学生人数</div>
                </Card>

                <Card size="small" style={{ flex: 1, textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                    {classStatistics.teachers}
                  </div>
                  <div style={{ color: '#666', fontSize: '12px' }}>教师人数</div>
                </Card>

                <Card size="small" style={{ flex: 1, textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                    {classStatistics.parents}
                  </div>
                  <div style={{ color: '#666', fontSize: '12px' }}>家长人数</div>
                </Card>

                <Card size="small" style={{ flex: 1, textAlign: 'center' }}>
                  <div style={{ fontSize: '14px', fontWeight: 'bold', color: '#722ed1' }}>
                    {classStatistics.headTeacher?.name || '未指定'}
                  </div>
                  <div style={{ color: '#666', fontSize: '12px' }}>班主任</div>
                </Card>
              </div>
            </div>
          )}

          {/* 班级列表和人员管理 */}
          <div style={{ display: 'flex', gap: 24 }}>
            {/* 班级列表 */}
            <Card
              title="班级列表"
              style={{ flex: 1 }}
              bodyStyle={{ padding: 0 }}
            >
              {loading ? (
                <div style={{ textAlign: 'center', padding: 50 }}>
                  <Spin size="large" tip="加载中..." />
                </div>
              ) : (
                <Table
                  dataSource={filteredData}
                  rowKey="id"
                  pagination={{
                    total: filteredData.length,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 个班级`
                  }}
                  onRow={(record) => ({
                    onClick: () => {
                      setSelectedClass(record);
                      loadClassData(record.id);

                      // 同步更新筛选状态
                      if (record.grade && gradeFilter !== record.grade) {
                        setGradeFilter(record.grade);
                      }
                      setClassFilter(record.id.toString());
                    },
                    style: {
                      cursor: 'pointer',
                      backgroundColor: selectedClass?.id === record.id ? '#e6f7ff' : undefined
                    }
                  })}
                  columns={[
                    {
                      title: '班级名称',
                      dataIndex: 'name',
                      key: 'name',
                      render: (text, record) => (
                        <Space>
                          <TeamOutlined />
                          <span style={{ fontWeight: selectedClass?.id === record.id ? 'bold' : 'normal' }}>
                            {text}
                          </span>
                        </Space>
                      )
                    },
                    {
                      title: '年级',
                      dataIndex: 'grade',
                      key: 'grade',
                      render: (grade) => grade ? <Tag color="blue">{grade}</Tag> : <Tag>未设置</Tag>
                    },
                    {
                      title: '学生人数',
                      dataIndex: 'student_count',
                      key: 'student_count',
                      render: (count) => (
                        <Badge
                          count={count || 0}
                          style={{ backgroundColor: '#52c41a' }}
                          showZero
                        />
                      )
                    },
                    {
                      title: '创建时间',
                      dataIndex: 'created_at',
                      key: 'created_at',
                      render: (date) => date ? new Date(date).toLocaleDateString() : '-'
                    },
                    {
                      title: '班级管理',
                      key: 'management',
                      width: 120,
                      render: (_, record) => (
                        <Space>
                          <Button
                            type="text"
                            size="small"
                            icon={<UserOutlined />}
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedClass(record);
                              setActiveTab('students'); // 切换到学生标签页
                              loadClassData(record.id);

                              // 同步更新筛选状态
                              if (record.grade && gradeFilter !== record.grade) {
                                setGradeFilter(record.grade);
                              }
                              setClassFilter(record.id.toString());
                            }}
                            title="管理学生"
                          >
                            学生
                          </Button>

                          {canManageClass() && (
                            <Button
                              type="text"
                              size="small"
                              icon={<EditOutlined />}
                              onClick={(e) => {
                                e.stopPropagation();
                                setEditingClass(record);
                                classForm.setFieldsValue(record);
                                setIsClassModalVisible(true);
                              }}
                              title="编辑班级"
                            >
                              编辑
                            </Button>
                          )}

                          <Popconfirm
                            title="确定要删除这个班级吗？"
                            description={`删除后无法恢复，请谨慎操作。班级：${record.name}`}
                            onConfirm={async (e) => {
                              if (e) e.stopPropagation();

                              // 防止重复点击
                              const button = e?.target?.closest('button');
                              if (button) {
                                button.disabled = true;
                                button.style.opacity = '0.5';
                              }

                              try {
                                await deleteClass(record.id);
                              } finally {
                                // 恢复按钮状态
                                if (button) {
                                  button.disabled = false;
                                  button.style.opacity = '1';
                                }
                              }
                            }}
                            okText="确定删除"
                            cancelText="取消"
                            okButtonProps={{ danger: true }}
                          >
                            <Button
                              type="text"
                              size="small"
                              danger
                              icon={<DeleteOutlined />}
                              onClick={(e) => e.stopPropagation()}
                              title="删除班级"
                            >
                              删除
                            </Button>
                          </Popconfirm>
                        </Space>
                      )
                    }
                  ]}
                />
              )}
            </Card>

            {/* 人员管理 */}
            {selectedClass ? (
              <Card
                title={`${selectedClass.name} - 人员管理`}
                style={{ flex: 1 }}
                bodyStyle={{ padding: 0 }}
              >
                <Tabs
                  activeKey={activeTab}
                  onChange={handleTabChange}
                  items={[
                    {
                      key: 'students',
                      label: (
                        <Space>
                          <UserOutlined />
                          <span>学生信息</span>
                          <Badge count={classStatistics.students} style={{ backgroundColor: '#1890ff' }} showZero />
                        </Space>
                      ),
                      children: (
                        <div style={{ padding: 16 }}>
                          <div style={{ marginBottom: 16 }}>
                            <Space>
                              <Button
                                type="primary"
                                icon={<PlusOutlined />}
                                onClick={() => {
                                  setEditingStudent(null);
                                  setStudentAddMode('create');
                                  studentForm.resetFields();
                                  setIsStudentModalVisible(true);
                                }}
                              >
                                添加学生
                              </Button>



                              <Button
                                icon={<TeamOutlined />}
                                onClick={() => {
                                  batchStudentForm.resetFields();
                                  setIsBatchStudentModalVisible(true);
                                }}
                              >
                                批量添加
                              </Button>

                              <Button
                                icon={<UploadOutlined />}
                                onClick={() => {
                                  setStudentImportData([]);
                                  setIsStudentImportModalVisible(true);
                                }}
                              >
                                批量导入
                              </Button>
                            </Space>
                          </div>

                          {studentsData.length === 0 && (
                            <div style={{
                              textAlign: 'center',
                              padding: '40px 20px',
                              background: '#fafafa',
                              borderRadius: 6,
                              marginBottom: 16,
                              border: '1px dashed #d9d9d9'
                            }}>
                              <UserOutlined style={{ fontSize: 48, color: '#bfbfbf', marginBottom: 16 }} />
                              <div style={{ fontSize: 16, color: '#595959', marginBottom: 8 }}>
                                暂无学生数据
                              </div>
                              <div style={{ fontSize: 14, color: '#8c8c8c', marginBottom: 16 }}>
                                您可以通过以下方式添加学生：
                              </div>
                              <Space direction="vertical" size="small">
                                <div style={{ fontSize: 12, color: '#8c8c8c' }}>
                                  • 点击"添加学生"逐个添加
                                </div>
                                <div style={{ fontSize: 12, color: '#8c8c8c' }}>
                                  • 点击"批量添加"一次添加多个学生
                                </div>
                                <div style={{ fontSize: 12, color: '#8c8c8c' }}>
                                  • 点击"批量导入"从Excel文件导入学生数据
                                </div>
                              </Space>
                            </div>
                          )}

                          <Table
                            dataSource={getCurrentDisplayData('students')}
                            rowKey="id"
                            size="small"
                            pagination={{
                              pageSize: 8,
                              showSizeChanger: false,
                              showTotal: (total) => {
                                const stats = getSearchResultStats();
                                if (stats) {
                                  return `显示 ${total} 名学生（共 ${studentsData.length} 名）`;
                                }
                                return `共 ${total} 名学生`;
                              }
                            }}
                            columns={[
                              {
                                title: '学号',
                                dataIndex: 'username',
                                key: 'username',
                                width: 120
                              },
                              {
                                title: '姓名',
                                dataIndex: 'full_name',
                                key: 'full_name',
                                render: (text) => (
                                  <Space>
                                    <UserOutlined />
                                    {text}
                                  </Space>
                                )
                              },
                              {
                                title: '邮箱',
                                dataIndex: 'email',
                                key: 'email',
                                ellipsis: true
                              },
                              {
                                title: '操作',
                                key: 'actions',
                                width: 100,
                                render: (_, record) => (
                                  <Space>
                                    <Button
                                      type="text"
                                      size="small"
                                      icon={<EditOutlined />}
                                      onClick={() => {
                                        setEditingStudent(record);
                                        studentForm.setFieldsValue(record);
                                        setIsStudentModalVisible(true);
                                      }}
                                    >
                                      编辑
                                    </Button>

                                    <Popconfirm
                                      title="确定要移除这名学生吗？"
                                      onConfirm={() => removeStudentFromClass(selectedClass.id, record.id)}
                                      okText="确定"
                                      cancelText="取消"
                                    >
                                      <Button
                                        type="text"
                                        size="small"
                                        danger
                                        icon={<DeleteOutlined />}
                                      >
                                        移除
                                      </Button>
                                    </Popconfirm>
                                  </Space>
                                )
                              }
                            ]}
                          />
                        </div>
                      )
                    },
                    {
                      key: 'teachers',
                      label: (
                        <Space>
                          <TeamOutlined />
                          <span>教师信息</span>
                          <Badge count={classStatistics.teachers} style={{ backgroundColor: '#52c41a' }} showZero />
                        </Space>
                      ),
                      children: (
                        <div style={{ padding: 16 }}>
                          <div style={{ marginBottom: 16 }}>
                            <Space>
                              <Button
                                type="primary"
                                icon={<PlusOutlined />}
                                onClick={() => {
                                  setEditingTeacher(null);
                                  teacherForm.resetFields();
                                  setIsTeacherModalVisible(true);
                                }}
                              >
                                添加教师
                              </Button>

                              <Button
                                icon={<UserOutlined />}
                                onClick={() => {
                                  if (teachersData.length === 0) {
                                    message.warning('请先添加教师');
                                    return;
                                  }
                                  // 打开班主任选择界面
                                  setIsHeadTeacherSelectModalVisible(true);
                                }}
                              >
                                指定班主任
                              </Button>

                              <Button
                                icon={<UploadOutlined />}
                                onClick={() => {
                                  setTeacherImportData([]);
                                  setIsTeacherBatchImportModalVisible(true);
                                }}
                              >
                                批量导入
                              </Button>

                              <Button
                                icon={<TeamOutlined />}
                                onClick={() => {
                                  message.info('科目分配功能：可在添加/编辑教师时设置科目');
                                }}
                              >
                                科目说明
                              </Button>

                              <Button
                                type="dashed"
                                onClick={async () => {
                                  if (!selectedClass) {
                                    message.warning('请先选择班级');
                                    return;
                                  }

                                  try {
                                    const { debugTeacherDataInconsistency } = await import('../utils/teacherDataFix');
                                    message.info('开始调试教师数据一致性...');

                                    const result = await debugTeacherDataInconsistency(selectedClass.id, apiCall);

                                    if (result.consistent) {
                                      message.success('✅ 教师数据一致性检查通过');
                                    } else {
                                      message.error('❌ 发现教师数据不一致问题，请查看控制台');
                                    }
                                  } catch (error) {
                                    console.error('调试失败:', error);
                                    message.error('调试过程中出错');
                                  }
                                }}
                              >
                                🔧 调试数据
                              </Button>
                            </Space>
                          </div>

                          <Table
                            dataSource={getCurrentDisplayData('teachers')}
                            rowKey="id"
                            size="small"
                            pagination={{
                              pageSize: 10,
                              showSizeChanger: false,
                              showTotal: (total) => {
                                const stats = getSearchResultStats();
                                if (stats) {
                                  return `显示 ${total} 名教师（共 ${teachersData.length} 名）`;
                                }
                                return `共 ${total} 名教师`;
                              }
                            }}
                            columns={[
                              {
                                title: '用户名',
                                dataIndex: 'username',
                                key: 'username',
                                width: 120,
                                render: (text) => text || '-'
                              },
                              {
                                title: '姓名',
                                dataIndex: 'name',
                                key: 'name',
                                render: (text, record) => (
                                  <Space>
                                    <TeamOutlined />
                                    {text}
                                    {record.role === 'head_teacher' && (
                                      <Tag color="gold">班主任</Tag>
                                    )}
                                  </Space>
                                )
                              },
                              {
                                title: '科目',
                                dataIndex: 'subject',
                                key: 'subject'
                              },
                              {
                                title: '联系电话',
                                dataIndex: 'phone',
                                key: 'phone'
                              },
                              {
                                title: '邮箱',
                                dataIndex: 'email',
                                key: 'email',
                                ellipsis: true
                              },
                              {
                                title: '操作',
                                key: 'actions',
                                width: 160,
                                render: (_, record) => (
                                  <Space>
                                    {record.role !== 'head_teacher' && (
                                      <Button
                                        type="text"
                                        size="small"
                                        icon={<UserOutlined />}
                                        onClick={() => {
                                          assignHeadTeacher(selectedClass.id, record.id);
                                        }}
                                        title="设为班主任"
                                      >
                                        设为班主任
                                      </Button>
                                    )}

                                    <Button
                                      type="text"
                                      size="small"
                                      icon={<EditOutlined />}
                                      onClick={() => {
                                        setEditingTeacher(record);
                                        teacherForm.setFieldsValue({
                                          teacher_name: record.name,
                                          username: record.username,
                                          teacher_phone: record.phone,
                                          teacher_email: record.email,
                                          subject: record.subject,
                                          role: record.role
                                        });
                                        setIsTeacherModalVisible(true);
                                      }}
                                    >
                                      编辑
                                    </Button>

                                    <Popconfirm
                                      title="确定要移除这名教师吗？"
                                      onConfirm={() => removeTeacherFromClass(selectedClass.id, record.id)}
                                      okText="确定"
                                      cancelText="取消"
                                    >
                                      <Button
                                        type="text"
                                        size="small"
                                        danger
                                        icon={<DeleteOutlined />}
                                      >
                                        移除
                                      </Button>
                                    </Popconfirm>
                                  </Space>
                                )
                              }
                            ]}
                          />
                        </div>
                      )
                    },
                    {
                      key: 'parents',
                      label: (
                        <Space>
                          <UserOutlined />
                          <span>家长信息</span>
                          <Badge count={classStatistics.parents} style={{ backgroundColor: '#fa8c16' }} showZero />
                        </Space>
                      ),
                      children: (
                        <div style={{ padding: 16 }}>
                          <div style={{ marginBottom: 16 }}>
                            <Space>
                              <Button
                                type="primary"
                                icon={<PlusOutlined />}
                                onClick={() => {
                                  if (studentsData.length === 0) {
                                    message.warning('请先添加学生');
                                    return;
                                  }
                                  setEditingParent(null);
                                  parentForm.resetFields();
                                  setIsParentModalVisible(true);
                                }}
                              >
                                添加家长
                              </Button>

                              <Button
                                icon={<TeamOutlined />}
                                onClick={() => {
                                  message.info('关联功能：在添加家长时选择关联学生');
                                }}
                              >
                                关联说明
                              </Button>

                              <Button
                                icon={<UploadOutlined />}
                                onClick={() => {
                                  setParentImportData([]);
                                  setIsParentBatchImportModalVisible(true);
                                }}
                              >
                                批量导入
                              </Button>
                            </Space>
                          </div>

                          <Table
                            dataSource={getCurrentDisplayData('parents')}
                            rowKey="id"
                            size="small"
                            pagination={{
                              pageSize: 10,
                              showSizeChanger: false,
                              showTotal: (total) => {
                                const stats = getSearchResultStats();
                                if (stats) {
                                  return `显示 ${total} 名家长（共 ${parentsData.length} 名）`;
                                }
                                return `共 ${total} 名家长`;
                              }
                            }}
                            columns={[
                              {
                                title: '用户名',
                                dataIndex: 'username',
                                key: 'username',
                                width: 120,
                                render: (text) => text || '-'
                              },
                              {
                                title: '家长姓名',
                                dataIndex: 'name',
                                key: 'name',
                                render: (text) => (
                                  <Space>
                                    <UserOutlined />
                                    {text}
                                  </Space>
                                )
                              },
                              {
                                title: '关系',
                                dataIndex: 'relationship',
                                key: 'relationship',
                                render: (relationship) => {
                                  const relationshipMap = {
                                    father: '父亲',
                                    mother: '母亲',
                                    guardian: '监护人'
                                  };
                                  return <Tag>{relationshipMap[relationship] || relationship}</Tag>;
                                }
                              },
                              {
                                title: '学生姓名',
                                dataIndex: 'student_name',
                                key: 'student_name'
                              },
                              {
                                title: '联系电话',
                                dataIndex: 'phone',
                                key: 'phone'
                              },
                              {
                                title: '操作',
                                key: 'actions',
                                width: 120,
                                render: (_, record) => (
                                  <Space>
                                    <Button
                                      type="text"
                                      size="small"
                                      icon={<EditOutlined />}
                                      onClick={() => {
                                        setEditingParent(record);
                                        parentForm.setFieldsValue({
                                          name: record.name,
                                          phone: record.phone,
                                          relationship: record.relationship,
                                          student_id: record.student_id,
                                          is_primary: record.is_primary
                                        });
                                        setIsParentModalVisible(true);
                                      }}
                                    >
                                      编辑
                                    </Button>

                                    <Popconfirm
                                      title="确定要移除这名家长吗？"
                                      onConfirm={() => removeParentFromStudent(record.id)}
                                      okText="确定"
                                      cancelText="取消"
                                    >
                                      <Button
                                        type="text"
                                        size="small"
                                        danger
                                        icon={<DeleteOutlined />}
                                      >
                                        移除
                                      </Button>
                                    </Popconfirm>
                                  </Space>
                                )
                              }
                            ]}
                          />
                        </div>
                      )
                    }
                  ]}
                />
              </Card>
            ) : (
              <Card
                title="班级人员管理"
                style={{ flex: 1 }}
                bodyStyle={{ padding: 40, textAlign: 'center' }}
              >
                <Empty
                  description={
                    <div>
                      <div style={{ marginBottom: 8 }}>请先选择班级</div>
                      <div style={{ fontSize: '12px', color: '#999' }}>
                        点击左侧班级列表中的班级，或点击"学生"按钮来管理班级人员
                      </div>
                    </div>
                  }
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              </Card>
            )}
        </div>

      {/* 班级添加/编辑模态框 */}
      <Modal
        title={editingClass ? "编辑班级" : "新建班级"}
        open={isClassModalVisible}
        onCancel={() => setIsClassModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form
          form={classForm}
          layout="vertical"
          onFinish={async (values) => {
            try {
              if (editingClass) {
                await updateClass(editingClass.id, values);
              } else {
                await createClass(values);
              }
              setIsClassModalVisible(false);
            } catch (error) {
              // 错误已在API函数中处理
            }
          }}
        >
          <Form.Item
            name="name"
            label="班级名称"
            rules={[{ required: true, message: '请输入班级名称' }]}
          >
            <Input
              placeholder="请输入班级名称，如：高一(1)班"
              autoComplete="off"
              maxLength={50}
              allowClear
              style={{ imeMode: 'auto' }}
            />
          </Form.Item>

          <Form.Item
            name="grade"
            label="年级"
            rules={[{ required: true, message: '请选择年级' }]}
          >
            <Select placeholder="请选择年级">
              <Option value="一年级">一年级</Option>
              <Option value="二年级">二年级</Option>
              <Option value="三年级">三年级</Option>
              <Option value="四年级">四年级</Option>
              <Option value="五年级">五年级</Option>
              <Option value="六年级">六年级</Option>
              <Option value="七年级">七年级</Option>
              <Option value="八年级">八年级</Option>
              <Option value="九年级">九年级</Option>
              <Option value="高一">高一</Option>
              <Option value="高二">高二</Option>
              <Option value="高三">高三</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="班级描述"
          >
            <Input.TextArea
              placeholder="请输入班级描述（可选）"
              rows={3}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsClassModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingClass ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 学生添加/编辑模态框 */}
      <Modal
        title={editingStudent ? "编辑学生信息" : "添加学生到班级"}
        open={isStudentModalVisible}
        onCancel={() => {
          setIsStudentModalVisible(false);
          setEditingStudent(null);
          studentForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        {!editingStudent && (
          <div style={{ marginBottom: 16 }}>
            <Text strong>添加方式：</Text>
            <Select
              value={studentAddMode}
              onChange={setStudentAddMode}
              style={{ width: 200, marginLeft: 8 }}
            >
              <Option value="create">创建新学生</Option>
              <Option value="existing">添加已存在学生</Option>
            </Select>
          </div>
        )}

        <Form
          form={studentForm}
          layout="vertical"
          onFinish={async (values) => {
            try {
              if (editingStudent) {
                // 实现学生信息更新
                const studentData = {
                  username: String(values.username || '').trim(),
                  full_name: String(values.full_name || '').trim(),
                  email: String(values.email || '').trim()
                };
                // 只有在填写了密码时才包含密码字段
                if (values.password && String(values.password).trim()) {
                  studentData.password = String(values.password).trim();
                }

                await apiCall(`/admin/classes/${selectedClass.id}/students/${editingStudent.id}`, {
                  method: 'PUT',
                  body: JSON.stringify(studentData)
                });

                message.success('学生信息更新成功');
                // 刷新学生列表
                await fetchStudentsData(selectedClass.id);
                setEditingStudent(null);
              } else {
                if (studentAddMode === 'create') {
                  // 确保所有字段都是字符串类型
                  const studentData = {
                    username: String(values.username || '').trim(),
                    full_name: String(values.full_name || '').trim(),
                    email: String(values.email || '').trim()
                  };
                  // 只有在填写了密码时才包含密码字段
                  if (values.password && String(values.password).trim()) {
                    studentData.password = String(values.password).trim();
                  }
                  await createStudentForClass(selectedClass.id, studentData);
                } else {
                  await addExistingStudentToClass(selectedClass.id, values.student_id);
                }
              }
              setIsStudentModalVisible(false);
              studentForm.resetFields();
            } catch (error) {
              console.error('操作失败:', error);
              message.error(editingStudent ? '更新学生信息失败' : '添加学生失败');
            }
          }}
        >
          {studentAddMode === 'existing' && !editingStudent ? (
            // 添加已存在学生的表单
            <Form.Item
              name="student_id"
              label="选择学生"
              rules={[{ required: true, message: '请选择学生' }]}
            >
              <Select
                placeholder="请输入学号或姓名搜索学生"
                showSearch
                filterOption={false}
                onSearch={async (value) => {
                  // TODO: 实现学生搜索功能
                  console.log('搜索学生:', value);
                }}
                notFoundContent="暂无数据，请输入关键词搜索"
              >
                {/* TODO: 动态加载学生选项 */}
              </Select>
            </Form.Item>
          ) : (
            // 创建新学生或编辑学生的表单
            <>
              <Form.Item
                name="username"
                label="学号/用户名"
                rules={[{ required: true, message: '请输入学号' }]}
              >
                <Input placeholder="请输入学号或用户名" />
              </Form.Item>

              <Form.Item
                name="full_name"
                label="学生姓名"
                rules={[{ required: true, message: '请输入学生姓名' }]}
              >
                <Input placeholder="请输入学生真实姓名" />
              </Form.Item>

              <Form.Item
                name="email"
                label="邮箱地址"
                rules={[
                  { required: true, message: '请输入邮箱地址' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input placeholder="请输入邮箱地址" />
              </Form.Item>

              {!editingStudent && studentAddMode === 'create' && (
                <Form.Item
                  name="password"
                  label="初始密码"
                  extra="如不填写，默认密码为 123456"
                >
                  <Input.Password placeholder="请输入初始密码（可选）" />
                </Form.Item>
              )}
            </>
          )}

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsStudentModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingStudent ? '更新' : (studentAddMode === 'create' ? '创建并添加' : '添加到班级')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量添加学生模态框 */}
      <Modal
        title="批量添加学生"
        open={isBatchStudentModalVisible}
        onCancel={() => setIsBatchStudentModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={batchStudentForm}
          layout="vertical"
          onFinish={async (values) => {
            try {
              const students = values.students.split('\n')
                .filter(line => line.trim())
                .map(line => {
                  const parts = line.trim().split(/\s+/);
                  return {
                    username: String(parts[0] || '').trim(),
                    full_name: String(parts[1] || parts[0] || '').trim(),
                    email: String(parts[2] || `${parts[0]}@example.com`).trim()
                  };
                });

              for (const student of students) {
                await createStudentForClass(selectedClass.id, student);
              }

              setIsBatchStudentModalVisible(false);
              message.success(`成功添加 ${students.length} 名学生`);
            } catch (error) {
              // 错误已在API函数中处理
            }
          }}
        >
          <Form.Item
            name="students"
            label="学生信息"
            rules={[{ required: true, message: '请输入学生信息' }]}
            extra="每行一个学生，格式：学号 姓名 邮箱（邮箱可选）"
          >
            <Input.TextArea
              placeholder={`示例：
202501001 张三 <EMAIL>
202501002 李四 <EMAIL>
202501003 王五`}
              rows={8}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsBatchStudentModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                批量添加
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>



      {/* 教师信息模态框 */}
      <Modal
        title={editingTeacher ? "编辑教师信息" : "添加教师"}
        open={isTeacherModalVisible}
        onCancel={() => {
          setIsTeacherModalVisible(false);
          setEditingTeacher(null);
          teacherForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={teacherForm}
          layout="vertical"
          onFinish={async (values) => {
            try {
              if (editingTeacher) {
                await updateTeacherInClass(selectedClass.id, editingTeacher.id, values);
              } else {
                await createTeacherForClass(selectedClass.id, values);
              }
              setIsTeacherModalVisible(false);
              setEditingTeacher(null);
              teacherForm.resetFields();
            } catch (error) {
              // 错误已在函数内处理
            }
          }}
        >
          <Form.Item
            name="teacher_name"
            label="教师姓名"
            rules={[{ required: true, message: '请输入教师姓名' }]}
          >
            <Input placeholder="请输入教师姓名" />
          </Form.Item>

          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
            ]}
          >
            <Input placeholder="请输入用户名（用于登录）" />
          </Form.Item>

          <Form.Item
            name="password"
            label="初始密码"
            rules={[
              { required: true, message: '请输入初始密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password placeholder="请输入初始密码" />
          </Form.Item>

          <Form.Item
            name="teacher_phone"
            label="联系电话"
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
            ]}
          >
            <Input placeholder="请输入联系电话" />
          </Form.Item>

          <Form.Item
            name="teacher_email"
            label="邮箱地址"
            rules={[
              { type: 'email', message: '请输入正确的邮箱地址' }
            ]}
          >
            <Input placeholder="请输入邮箱地址" />
          </Form.Item>

          <Form.Item
            name="subject"
            label="任教科目"
            rules={[{ required: true, message: '请选择任教科目' }]}
          >
            <Select placeholder="请选择任教科目" showSearch optionFilterProp="children">
              {(() => {
                console.log('=== 渲染科目选择器 ===');
                console.log('选中班级:', selectedClass);
                console.log('原始科目数据:', subjectsData);
                console.log('筛选后的科目:', filteredSubjects);
                console.log('筛选后科目数量:', filteredSubjects ? filteredSubjects.length : 0);
                return null; // 不渲染任何内容，只是为了执行console.log
              })()}
              {filteredSubjects && filteredSubjects.length > 0 ? (
                filteredSubjects.map(subject => {
                  console.log('渲染科目选项:', subject);
                  // 确保科目对象有正确的属性
                  const subjectName = subject && typeof subject === 'object' ?
                    (subject.name || subject.subject_name || '未知科目') :
                    (typeof subject === 'string' ? subject : '未知科目');
                  const subjectId = subject && typeof subject === 'object' ?
                    (subject.id || subject.subject_id || Math.random()) :
                    Math.random();

                  return (
                    <Option key={subjectId} value={subjectName}>
                      {subjectName}
                    </Option>
                  );
                })
              ) : (
                <Option disabled>暂无对应年级的科目数据</Option>
              )}
            </Select>
          </Form.Item>

          <Form.Item
            name="role"
            label="教师角色"
            initialValue="subject_teacher"
          >
            <Select>
              <Option value="subject_teacher">任课教师</Option>
              <Option value="head_teacher">班主任</Option>
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsTeacherModalVisible(false);
                setEditingTeacher(null);
                teacherForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingTeacher ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 家长信息模态框 */}
      <Modal
        title={editingParent ? "编辑家长信息" : "添加家长"}
        open={isParentModalVisible}
        onCancel={() => {
          setIsParentModalVisible(false);
          setEditingParent(null);
          parentForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={parentForm}
          layout="vertical"
          onFinish={async (values) => {
            try {
              if (editingParent) {
                await updateParentInfo(editingParent.id, values);
              } else {
                await createParentForStudent(values.student_id, values);
              }
              setIsParentModalVisible(false);
              setEditingParent(null);
              parentForm.resetFields();
            } catch (error) {
              // 错误已在函数内处理
            }
          }}
        >
          <Form.Item
            name="name"
            label="家长姓名"
            rules={[{ required: true, message: '请输入家长姓名' }]}
          >
            <Input placeholder="请输入家长姓名" />
          </Form.Item>

          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
            ]}
          >
            <Input placeholder="请输入用户名（用于登录）" />
          </Form.Item>

          <Form.Item
            name="password"
            label="初始密码"
            rules={[
              { required: true, message: '请输入初始密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password placeholder="请输入初始密码" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="联系电话"
            rules={[
              { required: true, message: '请输入联系电话' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
            ]}
          >
            <Input placeholder="请输入联系电话" />
          </Form.Item>

          <Form.Item
            name="relationship"
            label="关系类型"
            rules={[{ required: true, message: '请选择关系类型' }]}
          >
            <Select placeholder="请选择与学生的关系">
              <Option value="father">父亲</Option>
              <Option value="mother">母亲</Option>
              <Option value="guardian">监护人</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="student_id"
            label="关联学生"
            rules={[{ required: true, message: '请选择关联学生' }]}
          >
            <Select placeholder="请选择要关联的学生">
              {studentsData.map(student => (
                <Option key={student.id} value={student.id}>
                  {student.full_name} ({student.username})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="is_primary"
            valuePropName="checked"
          >
            <Space>
              <input type="checkbox" />
              <span>设为主要联系人</span>
            </Space>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsParentModalVisible(false);
                setEditingParent(null);
                parentForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingParent ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 家长批量导入模态框 */}
      <Modal
        title="批量导入家长信息"
        open={isParentBatchImportModalVisible}
        onCancel={() => {
          setIsParentBatchImportModalVisible(false);
          setParentImportData([]);
        }}
        width={800}
        footer={[
          <Button key="template" onClick={downloadParentImportTemplate}>
            下载模板
          </Button>,
          <Button key="cancel" onClick={() => {
            setIsParentBatchImportModalVisible(false);
            setParentImportData([]);
          }}>
            取消
          </Button>,
          <Button
            key="import"
            type="primary"
            disabled={parentImportData.length === 0}
            onClick={executeParentBatchImport}
          >
            确认导入
          </Button>
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          <Upload
            accept=".xlsx,.xls"
            showUploadList={false}
            beforeUpload={(file) => {
              handleParentBatchImport(file).catch(error => {
                message.error('文件解析失败，请检查文件格式');
              });
              return false;
            }}
          >
            <Button icon={<UploadOutlined />}>选择Excel文件</Button>
          </Upload>
          <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
            支持 .xlsx 和 .xls 格式，请先下载模板了解数据格式要求
          </div>
        </div>

        {parentImportData.length > 0 && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Text strong>
                预览数据（共 {parentImportData.length} 条，
                有效 {parentImportData.filter(item => item.errors.length === 0).length} 条，
                错误 {parentImportData.filter(item => item.errors.length > 0).length} 条）
              </Text>
            </div>

            <Table
              dataSource={parentImportData}
              rowKey="index"
              size="small"
              pagination={{ pageSize: 10 }}
              scroll={{ y: 300 }}
              columns={[
                {
                  title: '行号',
                  dataIndex: 'index',
                  width: 60
                },
                {
                  title: '家长姓名',
                  dataIndex: 'name',
                  width: 100
                },
                {
                  title: '用户名',
                  dataIndex: 'username',
                  width: 120
                },
                {
                  title: '密码',
                  dataIndex: 'password',
                  width: 80,
                  render: (text) => '●●●●●●'
                },
                {
                  title: '联系电话',
                  dataIndex: 'phone',
                  width: 120
                },
                {
                  title: '关系类型',
                  dataIndex: 'relationship',
                  width: 80,
                  render: (relationship) => {
                    const relationshipMap = {
                      father: '父亲',
                      mother: '母亲',
                      guardian: '监护人'
                    };
                    return relationshipMap[relationship] || relationship;
                  }
                },
                {
                  title: '关联学生',
                  dataIndex: 'matched_student',
                  width: 100,
                  render: (student, record) => {
                    if (student) {
                      return `${student.full_name}(${student.username})`;
                    }
                    return record.student_name || record.student_username || '-';
                  }
                },
                {
                  title: '状态',
                  dataIndex: 'errors',
                  width: 80,
                  render: (errors) => {
                    if (errors.length === 0) {
                      return <Tag color="green">有效</Tag>;
                    } else {
                      return <Tag color="red">错误</Tag>;
                    }
                  }
                },
                {
                  title: '错误信息',
                  dataIndex: 'errors',
                  render: (errors) => {
                    if (errors.length > 0) {
                      return (
                        <div style={{ fontSize: '12px', color: '#ff4d4f' }}>
                          {errors.join('；')}
                        </div>
                      );
                    }
                    return '-';
                  }
                }
              ]}
            />
          </div>
        )}

        {parentImportData.length === 0 && (
          <div style={{ textAlign: 'center', padding: 40 }}>
            <Empty
              description="请选择Excel文件以预览家长数据"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        )}
      </Modal>

      {/* 班主任选择模态框 */}
      <Modal
        title="选择班主任"
        open={isHeadTeacherSelectModalVisible}
        onCancel={() => setIsHeadTeacherSelectModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsHeadTeacherSelectModalVisible(false)}>
            取消
          </Button>
        ]}
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <Text>请选择要指定为班主任的教师：</Text>
        </div>

        {teachersData.length > 0 ? (
          <div>
            {/* 当前班主任信息 */}
            {(() => {
              const currentHeadTeacher = teachersData.find(t => t.role === 'head_teacher');
              if (currentHeadTeacher) {
                return (
                  <div style={{ marginBottom: 16, padding: 12, background: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
                    <Text strong style={{ color: '#52c41a' }}>
                      当前班主任：{currentHeadTeacher.name} ({currentHeadTeacher.subject})
                    </Text>
                  </div>
                );
              }
              return (
                <div style={{ marginBottom: 16, padding: 12, background: '#fff7e6', border: '1px solid #ffd591', borderRadius: 6 }}>
                  <Text style={{ color: '#fa8c16' }}>
                    当前班级暂无班主任
                  </Text>
                </div>
              );
            })()}

            {/* 候选教师列表 */}
            <div style={{ marginBottom: 16 }}>
              <Text strong>候选教师：</Text>
            </div>

            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              {teachersData.map(teacher => (
                <Card
                  key={teacher.id}
                  size="small"
                  style={{
                    marginBottom: 8,
                    cursor: teacher.role === 'head_teacher' ? 'not-allowed' : 'pointer',
                    opacity: teacher.role === 'head_teacher' ? 0.6 : 1
                  }}
                  onClick={() => {
                    if (teacher.role !== 'head_teacher') {
                      Modal.confirm({
                        title: '确认指定班主任',
                        content: `确定要将 ${teacher.name} 指定为班主任吗？`,
                        onOk: async () => {
                          await assignHeadTeacher(selectedClass.id, teacher.id);
                          setIsHeadTeacherSelectModalVisible(false);
                        }
                      });
                    }
                  }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <Space>
                        <TeamOutlined />
                        <Text strong>{teacher.name}</Text>
                        <Tag color="blue">{teacher.subject}</Tag>
                        {teacher.role === 'head_teacher' && (
                          <Tag color="gold">当前班主任</Tag>
                        )}
                      </Space>
                    </div>
                    <div>
                      <Space>
                        {teacher.phone && (
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {teacher.phone}
                          </Text>
                        )}
                        {teacher.role !== 'head_teacher' && (
                          <Button
                            type="primary"
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              Modal.confirm({
                                title: '确认指定班主任',
                                content: `确定要将 ${teacher.name} 指定为班主任吗？`,
                                onOk: async () => {
                                  await assignHeadTeacher(selectedClass.id, teacher.id);
                                  setIsHeadTeacherSelectModalVisible(false);
                                }
                              });
                            }}
                          >
                            指定为班主任
                          </Button>
                        )}
                      </Space>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {teachersData.filter(t => t.role !== 'head_teacher').length === 0 && (
              <div style={{ textAlign: 'center', padding: 20 }}>
                <Text type="secondary">
                  所有教师都已是班主任，或没有可指定的教师
                </Text>
              </div>
            )}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: 40 }}>
            <Empty
              description="暂无教师数据，请先添加教师"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        )}
      </Modal>

      {/* 教师批量导入模态框 */}
      <Modal
        title="批量导入教师信息"
        open={isTeacherBatchImportModalVisible}
        onCancel={() => {
          setIsTeacherBatchImportModalVisible(false);
          setTeacherImportData([]);
        }}
        width={900}
        footer={[
          <Button key="template" onClick={downloadTeacherImportTemplate}>
            下载模板
          </Button>,
          <Button key="cancel" onClick={() => {
            setIsTeacherBatchImportModalVisible(false);
            setTeacherImportData([]);
          }}>
            取消
          </Button>,
          <Button
            key="import"
            type="primary"
            disabled={teacherImportData.length === 0}
            onClick={executeTeacherBatchImport}
          >
            确认导入
          </Button>
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          <Upload
            accept=".xlsx,.xls"
            showUploadList={false}
            beforeUpload={(file) => {
              handleTeacherBatchImport(file).catch(error => {
                message.error('文件解析失败，请检查文件格式');
              });
              return false;
            }}
          >
            <Button icon={<UploadOutlined />}>选择Excel文件</Button>
          </Upload>
          <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
            支持 .xlsx 和 .xls 格式，请先下载模板了解数据格式要求
          </div>
          <div style={{ marginTop: 4, fontSize: '12px', color: '#fa8c16' }}>
            支持科目：语文、数学、英语、物理、化学、生物、历史、地理、政治、体育、音乐、美术、信息技术
          </div>
          <div style={{ marginTop: 4, fontSize: '12px', color: '#52c41a' }}>
            用户名必须在学校内唯一，建议使用教师姓名拼音+数字的组合
          </div>
        </div>

        {teacherImportData.length > 0 && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Text strong>
                预览数据（共 {teacherImportData.length} 条，
                有效 {teacherImportData.filter(item => item.errors.length === 0).length} 条，
                错误 {teacherImportData.filter(item => item.errors.length > 0).length} 条）
              </Text>
            </div>

            <Table
              dataSource={teacherImportData}
              rowKey="index"
              size="small"
              pagination={{ pageSize: 10 }}
              scroll={{ y: 300 }}
              columns={[
                {
                  title: '行号',
                  dataIndex: 'index',
                  width: 60
                },
                {
                  title: '教师姓名',
                  dataIndex: 'teacher_name',
                  width: 100
                },
                {
                  title: '用户名',
                  dataIndex: 'username',
                  width: 120
                },
                {
                  title: '密码',
                  dataIndex: 'password',
                  width: 80,
                  render: (text) => {
                    // 显示实际密码而不是掩码
                    console.log(`🔐 密码显示: "${text}"`);
                    return text || '123456';
                  }
                },
                {
                  title: '任教科目',
                  dataIndex: 'subject',
                  width: 100,
                  render: (subject) => subject ? <Tag color="blue">{subject}</Tag> : '-'
                },
                {
                  title: '教师角色',
                  dataIndex: 'role',
                  width: 100,
                  render: (role) => {
                    const roleMap = {
                      head_teacher: '班主任',
                      subject_teacher: '任课教师'
                    };
                    const color = role === 'head_teacher' ? 'gold' : 'green';
                    return <Tag color={color}>{roleMap[role] || role}</Tag>;
                  }
                },
                {
                  title: '联系电话',
                  dataIndex: 'teacher_phone',
                  width: 120
                },
                {
                  title: '邮箱地址',
                  dataIndex: 'teacher_email',
                  width: 150,
                  ellipsis: true
                },
                {
                  title: '状态',
                  dataIndex: 'errors',
                  width: 80,
                  render: (errors) => {
                    if (errors.length === 0) {
                      return <Tag color="green">有效</Tag>;
                    } else {
                      return <Tag color="red">错误</Tag>;
                    }
                  }
                },
                {
                  title: '错误信息',
                  dataIndex: 'errors',
                  render: (errors) => {
                    if (errors.length > 0) {
                      return (
                        <div style={{ fontSize: '12px', color: '#ff4d4f' }}>
                          {errors.join('；')}
                        </div>
                      );
                    }
                    return '-';
                  }
                }
              ]}
            />
          </div>
        )}

        {teacherImportData.length === 0 && (
          <div style={{ textAlign: 'center', padding: 40 }}>
            <Empty
              description="请选择Excel文件以预览教师数据"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        )}
      </Modal>

      {/* 数据导出模态框 */}
      <Modal
        title="导出班级数据"
        open={isExportModalVisible}
        onCancel={() => setIsExportModalVisible(false)}
        footer={null}
        width={500}
      >
        <div style={{ marginBottom: 16 }}>
          <Text strong>选择要导出的数据类型：</Text>
        </div>

        <div style={{ marginBottom: 24 }}>
          {selectedClass && (
            <div style={{ padding: 12, background: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6, marginBottom: 16 }}>
              <Text>
                当前班级：<Text strong>{selectedClass.name}</Text>
              </Text>
              <br />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                学生 {studentsData.length} 人，教师 {teachersData.length} 人，家长 {parentsData.length} 人
              </Text>
            </div>
          )}

          <Row gutter={[16, 16]}>
            {/* Excel导出选项 */}
            <Col span={12}>
              <Card
                title={
                  <Space>
                    <FileExcelOutlined style={{ color: '#52c41a' }} />
                    <span>Excel格式</span>
                  </Space>
                }
                size="small"
                style={{ height: '100%' }}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button
                    block
                    onClick={() => {
                      exportClassData('excel', 'all');
                      setIsExportModalVisible(false);
                    }}
                  >
                    完整数据
                  </Button>
                  <Button
                    block
                    onClick={() => {
                      exportClassData('excel', 'students');
                      setIsExportModalVisible(false);
                    }}
                  >
                    学生名单
                  </Button>
                  <Button
                    block
                    onClick={() => {
                      exportClassData('excel', 'teachers');
                      setIsExportModalVisible(false);
                    }}
                  >
                    教师名单
                  </Button>
                  <Button
                    block
                    onClick={() => {
                      exportClassData('excel', 'parents');
                      setIsExportModalVisible(false);
                    }}
                  >
                    家长名单
                  </Button>
                  <Button
                    block
                    onClick={() => {
                      exportClassData('excel', 'summary');
                      setIsExportModalVisible(false);
                    }}
                  >
                    统计汇总
                  </Button>
                </Space>
              </Card>
            </Col>

            {/* 报表导出选项 */}
            <Col span={12}>
              <Card
                title={
                  <Space>
                    <FileTextOutlined style={{ color: '#1890ff' }} />
                    <span>报表格式</span>
                  </Space>
                }
                size="small"
                style={{ height: '100%' }}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button
                    block
                    onClick={() => {
                      exportToPDF('all', selectedClass.name, new Date().toISOString().slice(0, 19).replace(/:/g, '-'));
                      setIsExportModalVisible(false);
                    }}
                  >
                    完整报表
                  </Button>
                  <Button
                    block
                    onClick={() => {
                      exportToPDF('students', selectedClass.name, new Date().toISOString().slice(0, 19).replace(/:/g, '-'));
                      setIsExportModalVisible(false);
                    }}
                  >
                    学生报表
                  </Button>
                  <Button
                    block
                    onClick={() => {
                      exportToPDF('teachers', selectedClass.name, new Date().toISOString().slice(0, 19).replace(/:/g, '-'));
                      setIsExportModalVisible(false);
                    }}
                  >
                    教师报表
                  </Button>
                  <Button
                    block
                    onClick={() => {
                      exportToPDF('parents', selectedClass.name, new Date().toISOString().slice(0, 19).replace(/:/g, '-'));
                      setIsExportModalVisible(false);
                    }}
                  >
                    家长报表
                  </Button>
                  <Button
                    block
                    onClick={() => {
                      exportToPDF('summary', selectedClass.name, new Date().toISOString().slice(0, 19).replace(/:/g, '-'));
                      setIsExportModalVisible(false);
                    }}
                  >
                    统计报表
                  </Button>
                </Space>
              </Card>
            </Col>
          </Row>
        </div>

        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Button onClick={() => setIsExportModalVisible(false)}>
            取消
          </Button>
        </div>
      </Modal>

      {/* 学生批量导入模态框 */}
      <Modal
        title={`批量导入学生到 ${selectedClass?.name || '选中班级'}`}
        open={isStudentImportModalVisible}
        onCancel={() => {
          setIsStudentImportModalVisible(false);
          setStudentImportData([]);
        }}
        footer={null}
        width={800}
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 12, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text strong>选择Excel文件：</Text>
            <Button
              icon={<DownloadOutlined />}
              onClick={downloadStudentImportTemplate}
              size="small"
              type="dashed"
            >
              下载导入模板
            </Button>
          </div>

          <Space size="middle">
            <Upload
              accept=".xlsx,.xls"
              beforeUpload={async (file) => {
                try {
                  const data = await handleStudentImport(file);
                  message.success(`成功解析 ${data.length} 条学生记录`);
                } catch (error) {
                  message.error('文件解析失败，请检查文件格式');
                }
                return false; // 阻止自动上传
              }}
              showUploadList={false}
              maxCount={1}
            >
              <Button icon={<UploadOutlined />} size="large" type="primary">
                选择Excel文件
              </Button>
            </Upload>

            <div style={{ color: '#666', fontSize: '12px' }}>
              <div>支持 .xlsx 和 .xls 格式</div>
              <div>建议先下载模板了解格式要求</div>
            </div>
          </Space>

          <div style={{ marginTop: 12, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 6, fontSize: '12px', color: '#666' }}>
            <div style={{ marginBottom: 4 }}>
              <Text strong style={{ color: '#1890ff' }}>当前目标班级：{selectedClass?.name}</Text>
            </div>
            <div>• 表格列名支持：学号/用户名、姓名/学生姓名、邮箱/邮箱地址（邮箱可选）</div>
            <div>• 请确保学号唯一，姓名不能为空</div>
            <div>• 如果没有邮箱，系统会自动生成格式：学号@example.com</div>
          </div>
        </div>

        {studentImportData.length > 0 && (
          <div>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text strong>预览数据（共 {studentImportData.length} 条记录）：</Text>
              <Space>
                <Button
                  onClick={() => {
                    setStudentImportData([]);
                  }}
                >
                  清空数据
                </Button>
                <Button
                  type="primary"
                  icon={<UploadOutlined />}
                  onClick={executeStudentImport}
                  disabled={!selectedClass}
                >
                  确认导入到 {selectedClass?.name}
                </Button>
              </Space>
            </div>

            <Table
              dataSource={studentImportData.slice(0, 10)} // 只显示前10条预览
              pagination={false}
              size="small"
              scroll={{ x: true }}
              columns={[
                {
                  title: '序号',
                  dataIndex: 'index',
                  key: 'index',
                  width: 60
                },
                {
                  title: '学号',
                  dataIndex: 'username',
                  key: 'username',
                  width: 120
                },
                {
                  title: '姓名',
                  dataIndex: 'full_name',
                  key: 'full_name',
                  width: 100
                },
                {
                  title: '邮箱',
                  dataIndex: 'email',
                  key: 'email',
                  ellipsis: true
                }
              ]}
            />

            {studentImportData.length > 10 && (
              <div style={{ textAlign: 'center', marginTop: 8, color: '#666', fontSize: '12px' }}>
                仅显示前10条记录，共 {studentImportData.length} 条记录将被导入
              </div>
            )}
          </div>
        )}

        {studentImportData.length === 0 && (
          <div style={{ textAlign: 'center', padding: 40 }}>
            <Empty
              description="请选择Excel文件以预览学生数据"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ImprovedClassManagement;
