{"ast": null, "code": "import { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genSpaceCompactStyle from './compact';\nconst genSpaceStyle = token => {\n  const {\n    componentCls,\n    antCls\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'inline-flex',\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&-vertical': {\n        flexDirection: 'column'\n      },\n      '&-align': {\n        flexDirection: 'column',\n        '&-center': {\n          alignItems: 'center'\n        },\n        '&-start': {\n          alignItems: 'flex-start'\n        },\n        '&-end': {\n          alignItems: 'flex-end'\n        },\n        '&-baseline': {\n          alignItems: 'baseline'\n        }\n      },\n      [`${componentCls}-item:empty`]: {\n        display: 'none'\n      },\n      // https://github.com/ant-design/ant-design/issues/47875\n      [`${componentCls}-item > ${antCls}-badge-not-a-wrapper:only-child`]: {\n        display: 'block'\n      }\n    }\n  };\n};\nconst genSpaceGapStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      '&-gap-row-small': {\n        rowGap: token.spaceGapSmallSize\n      },\n      '&-gap-row-middle': {\n        rowGap: token.spaceGapMiddleSize\n      },\n      '&-gap-row-large': {\n        rowGap: token.spaceGapLargeSize\n      },\n      '&-gap-col-small': {\n        columnGap: token.spaceGapSmallSize\n      },\n      '&-gap-col-middle': {\n        columnGap: token.spaceGapMiddleSize\n      },\n      '&-gap-col-large': {\n        columnGap: token.spaceGapLargeSize\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = () => ({});\nexport default genStyleHooks('Space', token => {\n  const spaceToken = mergeToken(token, {\n    spaceGapSmallSize: token.paddingXS,\n    spaceGapMiddleSize: token.padding,\n    spaceGapLargeSize: token.paddingLG\n  });\n  return [genSpaceStyle(spaceToken), genSpaceGapStyle(spaceToken), genSpaceCompactStyle(spaceToken)];\n}, () => ({}), {\n  // Space component don't apply extra font style\n  // https://github.com/ant-design/ant-design/issues/40315\n  resetStyle: false\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}