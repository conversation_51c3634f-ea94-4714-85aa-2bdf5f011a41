from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from datetime import datetime
import json
from ..database import Base

class SystemSettings(Base):
    """系统设置表，用于存储各种系统配置
    
    可以存储简单的键值对，也可以通过JSON格式存储复杂的配置结构
    """
    __tablename__ = "system_settings"
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(50), nullable=False, unique=True)  # 设置键名
    value = Column(Text, nullable=True)  # 设置值，可以是简单值或JSON字符串
    description = Column(String(255), nullable=True)  # 设置描述
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<SystemSettings(key='{self.key}', value='{self.value}')>"
    
    def get_json_value(self):
        """如果value是JSON字符串，则解析并返回对应的Python对象"""
        if self.value:
            try:
                return json.loads(self.value)
            except json.JSONDecodeError:
                return self.value
        return None
    
    def set_json_value(self, value_obj):
        """将Python对象转换为JSON字符串并存储"""
        if value_obj is not None:
            self.value = json.dumps(value_obj, ensure_ascii=False)
        else:
            self.value = None
    
    @classmethod
    def get_registration_settings(cls, db):
        """获取注册设置，如果不存在则创建默认设置"""
        # 尝试获取注册设置
        settings = db.query(cls).filter(cls.key == "registration_settings").first()

        # 如果不存在，创建默认设置
        if not settings:
            default_settings = {
                "global_registration_enabled": True,  # 添加总开关
                "roles": {
                    "student": {
                        "enabled": True,
                        "requires_approval": False,
                        "fields": {
                            "school": {"required": True},
                            "class": {"required": True},
                            "subject": {"required": False, "hidden": True}
                        }
                    },
                    "parent": {
                        "enabled": True,
                        "requires_approval": False,
                        "requires_student_binding": True,
                        "fields": {
                            "school": {"required": True},
                            "class": {"required": True},
                            "subject": {"required": False, "hidden": True}
                        }
                    },
                    "teacher": {
                        "enabled": True,
                        "requires_approval": True,
                        "approval_level": "school_admin",
                        "fields": {
                            "school": {"required": True},
                            "class": {"required": False},
                            "subject": {"required": True}
                        }
                    },
                    "class_teacher": {
                        "enabled": True,
                        "requires_approval": True,
                        "approval_level": "school_admin",
                        "fields": {
                            "school": {"required": True},
                            "class": {"required": True, "max": 1},
                            "subject": {"required": True}
                        }
                    },
                    "subject_leader": {
                        "enabled": True,
                        "requires_approval": True,
                        "approval_level": "academic_director",
                        "fields": {
                            "school": {"required": True},
                            "class": {"required": False},
                            "subject": {"required": True}
                        }
                    },
                    "academic_director": {
                        "enabled": True,
                        "requires_approval": True,
                        "approval_level": "principal",
                        "fields": {
                            "school": {"required": True},
                            "class": {"required": False},
                            "subject": {"required": False}
                        }
                    },
                    "vice_principal": {
                        "enabled": True,
                        "requires_approval": True,
                        "approval_level": "principal",
                        "fields": {
                            "school": {"required": True},
                            "class": {"required": False},
                            "subject": {"required": False}
                        }
                    },
                    "principal": {
                        "enabled": True,
                        "requires_approval": True,
                        "approval_level": "system_admin",
                        "fields": {
                            "school": {"required": True},
                            "class": {"required": False},
                            "subject": {"required": False}
                        }
                    },
                    "school_admin": {
                        "enabled": True,
                        "requires_approval": True,
                        "approval_level": "system_admin",
                        "fields": {
                            "school": {"required": True},
                            "class": {"required": False},
                            "subject": {"required": False}
                        }
                    }
                },
                "allow_school_creation": True
            }
            
            # 创建新的设置记录
            settings = cls(
                key="registration_settings",
                description="系统注册设置，包含各种角色的注册配置"
            )
            settings.set_json_value(default_settings)
            db.add(settings)
            db.commit()
            db.refresh(settings)
            
            print(f"已创建默认注册设置: {default_settings}")
            
        # 返回JSON格式的设置
        return settings.get_json_value()
    
    @classmethod
    def get_simple_registration_settings(cls, db):
        """获取简化的注册设置（兼容旧版本）"""
        full_settings = cls.get_registration_settings(db)

        # 检查总开关状态
        global_enabled = full_settings.get("global_registration_enabled", True)

        # 提取学生和教师的启用状态，受总开关控制
        return {
            "global_registration_enabled": global_enabled,
            "allow_student_registration": global_enabled and full_settings["roles"]["student"]["enabled"],
            "allow_teacher_registration": global_enabled and full_settings["roles"]["teacher"]["enabled"]
        }

    @classmethod
    def is_role_registration_enabled(cls, db, role_name):
        """检查指定角色的注册是否启用（考虑总开关和角色开关）"""
        full_settings = cls.get_registration_settings(db)

        # 检查总开关
        global_enabled = full_settings.get("global_registration_enabled", True)
        if not global_enabled:
            return False

        # 检查角色开关
        role_config = full_settings.get("roles", {}).get(role_name, {})
        return role_config.get("enabled", False)

    @classmethod
    def update_global_registration_switch(cls, db, enabled):
        """更新总注册开关"""
        settings = db.query(cls).filter(cls.key == "registration_settings").first()
        if settings:
            current_settings = settings.get_json_value()
            current_settings["global_registration_enabled"] = enabled
            settings.set_json_value(current_settings)
            db.commit()
            return current_settings
        return None