# 📸 拍照解题技术架构文档

## 🏗️ 系统架构概述

拍照解题功能采用前后端分离架构，结合AI视觉模型，为学生提供智能解题服务。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   AI服务        │
│                 │    │                 │    │                 │
│ • 相机组件      │◄──►│ • 图片处理      │◄──►│ • 火山引擎      │
│ • 文件上传      │    │ • API调用       │    │ • GPT-4V模型    │
│ • 结果展示      │    │ • 结果解析      │    │ • OCR识别       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎨 前端架构

### 📱 组件结构
```
PhotoSolvePage (页面容器)
├── PhotoSolve (主组件)
│   ├── CameraCapture (相机组件)
│   ├── FileUpload (文件上传)
│   ├── ImagePreview (图片预览)
│   └── ResultDisplay (结果展示)
└── AppLayout (布局容器)
```

### 🔧 核心组件

#### PhotoSolve 主组件
- **功能**：统筹整个解题流程
- **状态管理**：步骤控制、文件管理、结果存储
- **用户交互**：按钮操作、状态反馈

#### CameraCapture 相机组件
- **技术栈**：WebRTC getUserMedia API
- **功能特性**：
  - 实时相机预览
  - 后置摄像头优先
  - 拍照指导线
  - 照片预览确认
  - 权限管理

#### 技术实现细节
```javascript
// 相机启动
const startCamera = async () => {
  const stream = await navigator.mediaDevices.getUserMedia({
    video: {
      facingMode: 'environment', // 后置摄像头
      width: { ideal: 1280 },
      height: { ideal: 720 }
    }
  });
  videoRef.current.srcObject = stream;
};

// 拍照处理
const takePhoto = () => {
  const canvas = canvasRef.current;
  const context = canvas.getContext('2d');
  context.drawImage(videoRef.current, 0, 0);
  
  canvas.toBlob((blob) => {
    const file = new File([blob], 'photo.jpg', { type: 'image/jpeg' });
    onCapture(file);
  }, 'image/jpeg', 0.8);
};
```

### 📊 状态管理
```javascript
const [loading, setLoading] = useState(false);
const [imageFile, setImageFile] = useState(null);
const [imagePreview, setImagePreview] = useState(null);
const [solveResult, setSolveResult] = useState(null);
const [currentStep, setCurrentStep] = useState(0);
const [showCamera, setShowCamera] = useState(false);
```

### 🎯 用户体验优化
- **步骤指示器**：清晰显示当前进度
- **响应式设计**：适配各种设备尺寸
- **加载状态**：友好的等待提示
- **错误处理**：详细的错误信息展示

## 🔧 后端架构

### 📁 文件结构
```
backend/
├── app/routers/student.py          # 学生路由
├── app/models/ai_config.py         # AI配置模型
├── app/services/ai_service.py      # AI服务（可扩展）
└── correction_demo_routes.py       # 演示路由（可选）
```

### 🔄 处理流程

#### 1. 请求接收
```python
@router.post("/photo-solve")
async def photo_solve_question(
    image: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
```

#### 2. 权限验证
```python
if current_user.role != "student":
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="只有学生用户可以使用拍照解题功能"
    )
```

#### 3. 图片处理
```python
# 读取并转换图片
image_data = await image.read()
pil_image = PILImage.open(io.BytesIO(image_data))

# 格式转换
if pil_image.mode != 'RGB':
    pil_image = pil_image.convert('RGB')

# 尺寸优化
max_size = (1024, 1024)
if pil_image.size[0] > max_size[0] or pil_image.size[1] > max_size[1]:
    pil_image.thumbnail(max_size, PILImage.Resampling.LANCZOS)

# 保存为JPEG
pil_image.save(temp_file, format='JPEG', quality=85)
```

#### 4. AI服务调用
```python
# 获取配置
volcano_config = db.query(AIModelConfig).filter(
    AIModelConfig.provider == "volcano"
).first()

# 构建请求
payload = {
    "model": volcano_config.model_id,
    "messages": [{
        "role": "user",
        "content": [
            {"type": "text", "text": solve_prompt},
            {"type": "image_url", "image_url": {
                "url": f"data:image/jpeg;base64,{base64_image}"
            }}
        ]
    }],
    "max_tokens": 4096,
    "temperature": 0.1
}

# 发送请求
response = requests.post(url, headers=headers, json=payload)
```

#### 5. 结果处理
```python
# 解析响应
response_data = response.json()
ai_content = response_data["choices"][0]["message"]["content"]

# 提取JSON
json_start = ai_content.find("{")
json_end = ai_content.rfind("}") + 1
json_str = ai_content[json_start:json_end]
solve_result = json.loads(json_str)

# 添加用户信息
solve_result["user_id"] = current_user.id
solve_result["username"] = current_user.username
```

### 🔒 安全措施
- **权限控制**：严格的用户角色验证
- **文件清理**：临时文件自动删除
- **输入验证**：图片格式和大小检查
- **错误处理**：完善的异常捕获机制

## 🤖 AI服务集成

### 🔥 火山引擎配置
```python
# 数据库配置
class AIModelConfig:
    provider = "volcano"
    model_id = "ep-20250726110928-qzhhc"  # 实际模型ID
    api_endpoint = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    api_key = "YOUR_API_KEY"
```

### 📝 提示词工程
```python
solve_prompt = """你是一位专业的数学和学科解题助手，具有强大的OCR图像识别能力。请完成以下任务:

1. 识别图片中的题目内容
2. 提供详细的解题步骤和答案
3. 给出相关知识点解释

**重要**: 你必须以下面的JSON格式返回结果...
"""
```

### 🎯 模型参数优化
- **max_tokens**: 4096 - 确保足够的输出长度
- **temperature**: 0.1 - 降低随机性，提高准确性
- **模型选择**: GPT-4V - 视觉理解能力强

## 📊 数据流程

### 🔄 完整数据流
```
用户上传图片 → 前端预处理 → 后端接收 → 图片处理 → AI调用 → 结果解析 → 前端展示
     ↓              ↓            ↓          ↓         ↓         ↓         ↓
  文件选择      格式验证      权限检查    格式转换   API请求   JSON解析   UI渲染
```

### 📈 性能优化
- **图片压缩**：自动调整尺寸和质量
- **异步处理**：非阻塞的文件处理
- **错误重试**：网络异常自动重试
- **缓存机制**：配置信息缓存

## 🔧 部署配置

### 🐳 Docker配置
```dockerfile
# 后端依赖
RUN pip install pillow requests

# 前端构建
RUN npm install
RUN npm run build
```

### 🌐 Nginx配置
```nginx
# 文件上传大小限制
client_max_body_size 10M;

# 超时设置
proxy_read_timeout 60s;
proxy_connect_timeout 60s;
```

### 📱 环境变量
```bash
# AI服务配置
VOLCANO_API_KEY=your_api_key
VOLCANO_ENDPOINT=https://ark.cn-beijing.volces.com/api/v3/chat/completions
VOLCANO_MODEL_ID=ep-20250726110928-qzhhc

# 文件处理
MAX_FILE_SIZE=10485760  # 10MB
TEMP_DIR=/tmp/photo_solve
```

## 🔍 监控与日志

### 📊 关键指标
- **请求量**：每日解题请求数
- **成功率**：AI解题成功率
- **响应时间**：平均处理时间
- **错误率**：各类错误统计

### 📝 日志记录
```python
logger.info(f"学生 {user_id} 开始解题")
logger.info(f"图片处理完成，大小: {file_size}")
logger.info(f"AI响应时间: {response_time}s")
logger.error(f"解题失败: {error_message}")
```

## 🔮 扩展规划

### 📈 功能扩展
- **批量解题**：支持多图片同时处理
- **解题历史**：用户解题记录存储
- **错题本集成**：自动收藏错题
- **语音解答**：TTS语音播报

### 🏗️ 架构优化
- **微服务化**：AI服务独立部署
- **消息队列**：异步处理长时间任务
- **CDN加速**：图片上传和下载优化
- **负载均衡**：多实例部署

---

*最后更新时间：2025年8月1日*
*架构版本：v1.0*
