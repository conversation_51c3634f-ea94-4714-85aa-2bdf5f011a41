#!/usr/bin/env python3
import sqlite3
from datetime import datetime
import os

def add_photo_solve_config():
    try:
        # 使用应用实际的数据库路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(current_dir, "smart_edu.db")
        print(f"数据库路径: {db_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已有拍照解题配置
        cursor.execute('SELECT COUNT(*) FROM ai_model_configs WHERE usage_type = ?', ('photo_solve',))
        photo_solve_count = cursor.fetchone()[0]
        
        if photo_solve_count == 0:
            print("📸 添加专门的拍照解题AI配置...")
            
            # 获取现有的火山引擎配置作为模板
            cursor.execute('SELECT * FROM ai_model_configs WHERE provider = ? AND id = ?', ('volcano', 3))
            template_config = cursor.fetchone()
            
            if template_config:
                print(f"📋 使用ID: {template_config[0]} 的配置作为模板")
                print(f"模板配置: {template_config[2]} - {template_config[3]}")
                
                # 添加新的拍照解题配置
                cursor.execute('''
                    INSERT INTO ai_model_configs 
                    (provider, model_name, model_id, usage_type, api_key, api_endpoint, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    'volcano',  # 提供商
                    'Doubao-Seed-1.6-flash (拍照解题专用)',  # 模型名称
                    template_config[3],  # 使用相同的model_id
                    'photo_solve',  # 专门用于拍照解题
                    template_config[5],  # 使用相同的API密钥
                    template_config[6],  # 使用相同的API端点
                    1,  # 激活状态
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                ))
                
                # 获取新插入的配置ID
                new_config_id = cursor.lastrowid
                print(f"✅ 拍照解题配置添加成功，新配置ID: {new_config_id}")
                
            else:
                print("❌ 未找到火山引擎模板配置")
                return
                
        else:
            print(f"ℹ️ 已存在 {photo_solve_count} 个拍照解题配置")
        
        conn.commit()
        
        # 显示所有配置
        print("\n📋 当前所有AI配置:")
        cursor.execute('SELECT id, provider, model_name, model_id, usage_type, is_active FROM ai_model_configs ORDER BY id')
        results = cursor.fetchall()
        
        for row in results:
            status = "🟢 激活" if row[5] else "🔴 禁用"
            highlight = "🎯 " if row[4] == 'photo_solve' else "   "
            print(f"{highlight}ID: {row[0]}, {row[1]}/{row[2]}, 模型ID: {row[3]}, 用途: {row[4]}, 状态: {status}")
        
        conn.close()
        print("\n🎉 拍照解题专用AI配置创建完成！")
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    add_photo_solve_config()
