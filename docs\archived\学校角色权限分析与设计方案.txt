## 学校角色权限分析与设计方案

### 角色体系设计

基于教育系统的层级结构，将角色划分如下：

1. **校长**：学校最高管理者
2. **副校长**：协助校长管理学校
3. **教务处主任**：负责教学管理和教务安排
4. **年级组长**：负责某个年级的管理
5. **教研组长**：负责某个学科的教研工作
6. **备课组长**：负责某个学科某个年级的备课工作
7. **班主任**：负责某个班级的管理
8. **教师**：进行教学工作

### 权限划分详情

#### 1. 校长
- 查看全校所有统计数据和报表
- 管理学校基本信息
- 管理所有角色用户（添加/删除/修改）
- 查看所有班级、课程和作业
- 创建、编辑、删除、批准全校任何作业
- 查看全校所有作业批改情况和统计数据
- 查看和分析全校教学质量和效果
- 管理AI配置和系统设置
- 管理学科设置
- 批量导入/导出数据

#### 2. 副校长
- 查看全校所有统计数据和报表
- 查看学校基本信息
- 管理除校长外的所有角色用户
- 查看所有班级、课程和作业
- 查看全校所有作业批改情况和统计数据
- 查看和分析全校教学质量和效果
- 批量导入/导出数据

#### 3. 教务处主任
- 查看全校教学统计数据
- 管理年级组长、教研组长和备课组长
- 管理课程安排和教师分配
- 查看所有班级和作业情况
- 查看全校所有作业批改情况和统计数据
- 批量导入/导出教务数据
- 管理学科设置

#### 4. 年级组长
- 查看本年级所有班级数据和统计
- 管理本年级班主任和教师
- 查看本年级所有班级和作业情况
- 查看、批准本年级的作业计划和安排
- 查看本年级所有作业批改情况和统计数据
- 分析本年级教学质量和效果
- 查看年级学生表现和成绩统计

#### 5. 教研组长
- 查看本学科所有教学统计数据
- 管理本学科的备课组长
- 查看本学科所有作业情况
- 创建、编辑、管理本学科的作业模板
- 查看本学科所有作业批改情况和统计数据
- 分析本学科教学质量和效果
- 管理本学科教学资源和教材

#### 6. 备课组长
- 查看本学科本年级的教学统计数据
- 管理本备课组的教师
- 创建和管理本备课组的作业模板
- 查看、批准本备课组教师的作业计划
- 查看本备课组教师的作业批改情况
- 分析本备课组的教学效果

#### 7. 班主任
- 查看本班级所有学生数据和统计
- 管理本班级学生信息
- 查看本班级所有学科作业完成情况
- 查看本班级所有学科作业批改情况和统计数据
- 分析本班级学生表现和成绩
- 与家长和学生进行沟通

#### 8. 教师
- 查看自己教授班级和科目的相关数据
- 创建、编辑、删除自己的作业
- 使用已批准的作业模板创建作业
- 批改自己布置的作业
- 查看自己布置作业的完成情况和统计数据
- 查看自己学生的错题和训练情况
- 个人教学统计分析

### 实现方案

#### 1. 数据库设计

1. **角色表(roles)**:
   - id: 主键
   - name: 角色名称(校长、副校长等)
   - code: 角色代码(principal, vice_principal等)
   - description: 角色描述
   - level: 角色层级(数字，用于权限继承)

2. **权限表(permissions)**:
   - id: 主键
   - name: 权限名称
   - code: 权限代码(view_school_stats, manage_users等)
   - description: 权限描述
   - resource: 权限所属资源(school, class, homework等)

3. **角色权限关联表(role_permissions)**:
   - role_id: 角色ID
   - permission_id: 权限ID

4. **用户角色关联表(user_roles)**:
   - user_id: 用户ID
   - role_id: 角色ID
   - school_id: 学校ID(可选)
   - grade_id: 年级ID(可选，年级组长)
   - subject_id: 学科ID(可选，教研组长、备课组长)
   - class_id: 班级ID(可选，班主任)

5. **扩展现有用户表**:
   - 保留现有的is_admin, is_teacher字段用于兼容
   - 添加primary_role_id字段关联主要角色

#### 2. 后端实现

1. **权限验证中间件**:
   ```python
   async def role_permission_middleware(request: Request, call_next):
       user = request.state.user
       required_permission = get_endpoint_permission(request.url.path, request.method)
       
       if not required_permission or has_permission(user, required_permission):
           return await call_next(request)
       
       raise HTTPException(status_code=403, detail="无权限执行此操作")
   ```

2. **权限检查工具**:
   ```python
   def has_permission(user, permission_code, resource_id=None):
       # 根据用户角色获取权限
       user_permissions = get_user_permissions(user.id)
       
       # 检查是否有全局权限
       if permission_code in user_permissions:
           return True
           
       # 检查是否有特定资源的权限
       if resource_id and f"{permission_code}:{resource_id}" in user_permissions:
           return True
       
       # 检查基于角色范围的资源访问控制
       if permission_code.startswith('view_homework') or permission_code.startswith('manage_homework'):
           # 获取用户的角色和关联资源范围
           user_roles = get_user_roles_with_scope(user.id)
           homework_info = get_homework_info(resource_id) if resource_id else None
           
           for role in user_roles:
               # 校长、副校长、教务主任有全校权限
               if role.code in ['principal', 'vice_principal', 'academic_director']:
                   return True
               
               # 年级组长检查年级范围
               if role.code == 'grade_leader' and homework_info and role.grade_id == homework_info.grade_id:
                   return True
               
               # 教研组长检查学科范围
               if role.code == 'subject_leader' and homework_info and role.subject_id == homework_info.subject_id:
                   return True
               
               # 备课组长检查学科和年级范围
               if role.code == 'lesson_group_leader' and homework_info and role.subject_id == homework_info.subject_id and role.grade_id == homework_info.grade_id:
                   return True
               
               # 班主任检查班级范围
               if role.code == 'class_teacher' and homework_info and role.class_id == homework_info.class_id:
                   return True
               
               # 教师检查是否为自己创建的作业
               if role.code == 'teacher' and homework_info and homework_info.created_by == user.id:
                   return True
       
       return False
   ```

3. **权限装饰器**:
   ```python
   def require_permission(permission_code, resource_param=None):
       def decorator(func):
           @wraps(func)
           async def wrapper(*args, **kwargs):
               request = kwargs.get('request') or args[0]
               user = request.state.user
               
               # 获取资源ID（如果有）
               resource_id = None
               if resource_param:
                   if resource_param in kwargs:
                       resource_id = kwargs[resource_param]
                   else:
                       # 尝试从路径参数获取
                       path_params = request.path_params
                       if resource_param in path_params:
                           resource_id = path_params[resource_param]
               
               if not has_permission(user, permission_code, resource_id):
                   raise HTTPException(status_code=403, detail="无权限执行此操作")
                   
               return await func(*args, **kwargs)
           return wrapper
       return decorator
   ```

4. **作业相关权限控制示例**:
   ```python
   # 作业列表API
   @router.get("/homework/")
   @require_permission("view_homework_all")
   async def list_homework(request: Request, 
                          grade_id: Optional[int] = None,
                          class_id: Optional[int] = None,
                          subject_id: Optional[int] = None):
       user = request.state.user
       user_roles = get_user_roles_with_scope(user.id)
       
       # 根据用户角色和查询参数构建过滤条件
       filters = {}
       
       # 校长、副校长、教务主任可以查看所有作业
       has_full_access = any(role.code in ['principal', 'vice_principal', 'academic_director'] 
                            for role in user_roles)
       
       if not has_full_access:
           # 年级组长只能查看本年级作业
           grade_leader_roles = [role for role in user_roles if role.code == 'grade_leader']
           if grade_leader_roles:
               allowed_grade_ids = [role.grade_id for role in grade_leader_roles]
               if grade_id and grade_id not in allowed_grade_ids:
                   raise HTTPException(status_code=403, detail="无权查看该年级的作业")
               filters['grade_id'] = allowed_grade_ids if not grade_id else grade_id
           
           # 教研组长只能查看本学科作业
           subject_leader_roles = [role for role in user_roles if role.code == 'subject_leader']
           if subject_leader_roles:
               allowed_subject_ids = [role.subject_id for role in subject_leader_roles]
               if subject_id and subject_id not in allowed_subject_ids:
                   raise HTTPException(status_code=403, detail="无权查看该学科的作业")
               filters['subject_id'] = allowed_subject_ids if not subject_id else subject_id
           
           # 班主任只能查看本班级作业
           class_teacher_roles = [role for role in user_roles if role.code == 'class_teacher']
           if class_teacher_roles:
               allowed_class_ids = [role.class_id for role in class_teacher_roles]
               if class_id and class_id not in allowed_class_ids:
                   raise HTTPException(status_code=403, detail="无权查看该班级的作业")
               filters['class_id'] = allowed_class_ids if not class_id else class_id
           
           # 教师只能查看自己创建的作业
           if all(role.code == 'teacher' for role in user_roles):
               filters['created_by'] = user.id
       
       # 应用额外的过滤条件
       if grade_id and 'grade_id' not in filters:
           filters['grade_id'] = grade_id
       if class_id and 'class_id' not in filters:
           filters['class_id'] = class_id
       if subject_id and 'subject_id' not in filters:
           filters['subject_id'] = subject_id
       
       # 查询数据库
       homework_list = await get_homework_by_filters(**filters)
       return homework_list
   
   # 作业详情API
   @router.get("/homework/{homework_id}")
   async def get_homework_detail(request: Request, homework_id: int):
       user = request.state.user
       
       # 获取作业信息
       homework = await get_homework_by_id(homework_id)
       if not homework:
           raise HTTPException(status_code=404, detail="作业不存在")
       
       # 检查权限
       if not has_permission(user, "view_homework_all", homework_id):
           raise HTTPException(status_code=403, detail="无权查看该作业")
       
       return homework
   
   # 创建作业API
   @router.post("/homework/")
   async def create_homework(request: Request, homework_data: HomeworkCreate):
       user = request.state.user
       user_roles = get_user_roles_with_scope(user.id)
       
       # 校长可以创建任何作业
       is_principal = any(role.code == 'principal' for role in user_roles)
       
       if not is_principal:
           # 检查是否有权限在特定班级和学科创建作业
           class_id = homework_data.class_id
           subject_id = homework_data.subject_id
           grade_id = homework_data.grade_id
           
           # 教研组长检查学科权限
           subject_leader_roles = [role for role in user_roles if role.code == 'subject_leader']
           has_subject_permission = any(role.subject_id == subject_id for role in subject_leader_roles)
           
           # 备课组长检查学科和年级权限
           lesson_group_roles = [role for role in user_roles if role.code == 'lesson_group_leader']
           has_lesson_group_permission = any(role.subject_id == subject_id and role.grade_id == grade_id 
                                           for role in lesson_group_roles)
           
           # 普通教师检查是否为自己教授的班级和学科
           teacher_classes = await get_teacher_classes(user.id)
           has_teacher_permission = any(tc['class_id'] == class_id and tc['subject_id'] == subject_id 
                                      for tc in teacher_classes)
           
           if not (has_subject_permission or has_lesson_group_permission or has_teacher_permission):
               raise HTTPException(status_code=403, detail="无权在该班级或学科创建作业")
       
       # 创建作业
       homework_data.created_by = user.id
       new_homework = await create_new_homework(homework_data)
       return new_homework
   ```

#### 3. 前端实现

1. **角色管理界面**:
   - 创建角色列表页面
   - 角色编辑表单
   - 为角色分配权限的界面

2. **权限检查组件**:
   ```jsx
   const PermissionGuard = ({ permission, resource, children }) => {
     const { user } = useAuth();
     const hasPermission = useHasPermission(user, permission, resource);
     
     if (!hasPermission) return null;
     return children;
   };
   ```

3. **导航菜单权限控制**:
   ```jsx
   const menuItems = [
     { key: 'dashboard', label: '首页', icon: <HomeOutlined />, permission: 'view_dashboard' },
     { 
       key: 'school', 
       label: '学校管理', 
       icon: <BankOutlined />, 
       permission: 'manage_school',
       children: [
         { key: 'school_info', label: '基本信息', permission: 'view_school_info' },
         { key: 'school_stats', label: '学校统计', permission: 'view_school_stats' }
       ]
     },
     {
       key: 'homework',
       label: '作业管理',
       icon: <BookOutlined />,
       permission: ['view_homework_all', 'view_homework_by_grade', 'view_homework_by_subject', 'view_homework_by_class', 'view_homework_own'],
       children: [
         { key: 'homework_list', label: '作业列表', permission: ['view_homework_all', 'view_homework_by_grade', 'view_homework_by_subject', 'view_homework_by_class', 'view_homework_own'] },
         { key: 'homework_create', label: '创建作业', permission: ['create_homework_all', 'create_homework_by_subject', 'create_homework_own'] },
         { key: 'homework_template', label: '作业模板', permission: ['manage_homework_template_all', 'manage_homework_template_by_subject', 'manage_homework_template_by_lesson_group', 'use_homework_template'] },
         { key: 'homework_stats', label: '作业统计', permission: ['view_homework_stats_all', 'view_homework_stats_by_grade', 'view_homework_stats_by_subject', 'view_homework_stats_by_class', 'view_homework_stats_own'] }
       ]
     },
     // 更多菜单项...
   ];
   
   // 筛选有权限的菜单项
   const filteredMenuItems = filterMenuItemsByPermission(menuItems, userPermissions);
   ```

4. **前端权限检查工具**:
   ```jsx
   // 检查用户是否有特定权限
   const useHasPermission = (user, permissionCode, resourceId = null) => {
     const { userPermissions } = useContext(AuthContext);
     
     // 检查是否有完全匹配的权限
     if (Array.isArray(permissionCode)) {
       // 如果是权限数组，只要有一个匹配即可
       return permissionCode.some(code => 
         userPermissions.includes(code) || 
         (resourceId && userPermissions.includes(`${code}:${resourceId}`))
       );
     }
     
     // 单个权限检查
     return userPermissions.includes(permissionCode) || 
            (resourceId && userPermissions.includes(`${permissionCode}:${resourceId}`));
   };
   
   // 作业列表组件示例
   const HomeworkList = () => {
     const { user } = useAuth();
     const [homeworkList, setHomeworkList] = useState([]);
     const [filters, setFilters] = useState({});
     
     // 根据用户角色设置过滤器
     useEffect(() => {
       const userRoles = user.roles || [];
       const newFilters = {};
       
       // 非管理员角色需要添加过滤条件
       if (!userRoles.some(r => ['principal', 'vice_principal', 'academic_director'].includes(r.code))) {
         // 年级组长
         const gradeLeaderRoles = userRoles.filter(r => r.code === 'grade_leader');
         if (gradeLeaderRoles.length > 0) {
           newFilters.grade_ids = gradeLeaderRoles.map(r => r.grade_id);
         }
         
         // 教研组长
         const subjectLeaderRoles = userRoles.filter(r => r.code === 'subject_leader');
         if (subjectLeaderRoles.length > 0) {
           newFilters.subject_ids = subjectLeaderRoles.map(r => r.subject_id);
         }
         
         // 班主任
         const classTeacherRoles = userRoles.filter(r => r.code === 'class_teacher');
         if (classTeacherRoles.length > 0) {
           newFilters.class_ids = classTeacherRoles.map(r => r.class_id);
         }
         
         // 普通教师只能查看自己的作业
         if (userRoles.every(r => r.code === 'teacher')) {
           newFilters.created_by = user.id;
         }
       }
       
       setFilters(newFilters);
     }, [user]);
     
     // 获取作业列表
     useEffect(() => {
       fetchHomeworkList(filters);
     }, [filters]);
     
     // 渲染作业操作按钮
     const renderActions = (homework) => {
       return (
         <>
           <PermissionGuard permission="view_homework_all" resource={homework.id}>
             <Button type="link" onClick={() => viewHomework(homework.id)}>查看</Button>
           </PermissionGuard>
           
           {/* 编辑按钮：校长可编辑所有，教研组长可编辑本学科，教师只能编辑自己的 */}
           {(useHasPermission(user, 'edit_homework_all') || 
             (useHasPermission(user, 'edit_homework_by_subject', homework.subject_id)) ||
             (homework.created_by === user.id && useHasPermission(user, 'edit_homework_own'))) && (
             <Button type="link" onClick={() => editHomework(homework.id)}>编辑</Button>
           )}
           
           {/* 删除按钮：校长可删除所有，教研组长可删除本学科，教师只能删除自己的 */}
           {(useHasPermission(user, 'delete_homework_all') || 
             (useHasPermission(user, 'delete_homework_by_subject', homework.subject_id)) ||
             (homework.created_by === user.id && useHasPermission(user, 'delete_homework_own'))) && (
             <Button type="link" danger onClick={() => deleteHomework(homework.id)}>删除</Button>
           )}
         </>
       );
     };
     
     return (
       <div>
         <h1>作业列表</h1>
         <Table
           columns={[
             { title: '作业名称', dataIndex: 'title' },
             { title: '班级', dataIndex: 'class_name' },
             { title: '学科', dataIndex: 'subject_name' },
             { title: '创建时间', dataIndex: 'created_at' },
             { title: '操作', render: (_, record) => renderActions(record) }
           ]}
           dataSource={homeworkList}
         />
       </div>
     );
   };
   ```

### 作业管理相关权限代码示例

以下是作业管理相关的权限代码示例，可用于角色权限配置：

```
// 作业查看权限
view_homework_all                 // 查看所有作业（校长、副校长、教务主任）
view_homework_by_grade:{grade_id} // 按年级查看作业（年级组长）
view_homework_by_subject:{subject_id} // 按学科查看作业（教研组长）
view_homework_by_class:{class_id} // 按班级查看作业（班主任）
view_homework_own                 // 查看自己创建的作业（普通教师）

// 作业创建权限
create_homework_all               // 创建任何作业（校长）
create_homework_by_subject:{subject_id} // 按学科创建作业（教研组长）
create_homework_own               // 创建自己的作业（普通教师）

// 作业编辑权限
edit_homework_all                 // 编辑任何作业（校长）
edit_homework_by_subject:{subject_id} // 按学科编辑作业（教研组长）
edit_homework_own                 // 编辑自己的作业（普通教师）

// 作业删除权限
delete_homework_all               // 删除任何作业（校长）
delete_homework_by_subject:{subject_id} // 按学科删除作业（教研组长）
delete_homework_own               // 删除自己的作业（普通教师）

// 作业批改权限
grade_homework_all                // 批改任何作业（校长）
grade_homework_by_subject:{subject_id} // 按学科批改作业（教研组长）
grade_homework_own                // 批改自己的作业（普通教师）

// 作业模板权限
manage_homework_template_all      // 管理所有作业模板（校长、教务主任）
manage_homework_template_by_subject:{subject_id} // 按学科管理作业模板（教研组长）
manage_homework_template_by_lesson_group:{lesson_group_id} // 按备课组管理作业模板（备课组长）
use_homework_template             // 使用作业模板（所有教师）

// 作业统计查看权限
view_homework_stats_all           // 查看所有作业统计（校长、副校长、教务主任）
view_homework_stats_by_grade:{grade_id} // 按年级查看作业统计（年级组长）
view_homework_stats_by_subject:{subject_id} // 按学科查看作业统计（教研组长）
view_homework_stats_by_class:{class_id} // 按班级查看作业统计（班主任）
view_homework_stats_own           // 查看自己作业的统计（普通教师）
```

### 迁移策略

1. **保持向下兼容**:
   - 保留现有的is_admin和is_teacher字段
   - 为现有管理员分配校长角色
   - 为现有教师分配普通教师角色

2. **数据迁移脚本**:
   ```python
   def migrate_roles():
       # 创建角色和权限
       create_default_roles_and_permissions()
       
       # 迁移现有用户
       users = get_all_users()
       for user in users:
           if user.is_admin:
               assign_role(user.id, 'principal')
           elif user.is_teacher:
               assign_role(user.id, 'teacher')
   ```

3. **作业权限迁移脚本**:
   ```python
   def migrate_homework_permissions():
       # 创建作业相关权限
       homework_permissions = [
           # 作业查看权限
           {"name": "查看所有作业", "code": "view_homework_all", "resource": "homework"},
           {"name": "按年级查看作业", "code": "view_homework_by_grade", "resource": "homework"},
           {"name": "按学科查看作业", "code": "view_homework_by_subject", "resource": "homework"},
           {"name": "按班级查看作业", "code": "view_homework_by_class", "resource": "homework"},
           {"name": "查看自己创建的作业", "code": "view_homework_own", "resource": "homework"},
           
           # 作业创建权限
           {"name": "创建任何作业", "code": "create_homework_all", "resource": "homework"},
           {"name": "按学科创建作业", "code": "create_homework_by_subject", "resource": "homework"},
           {"name": "创建自己的作业", "code": "create_homework_own", "resource": "homework"},
           
           # 作业编辑权限
           {"name": "编辑任何作业", "code": "edit_homework_all", "resource": "homework"},
           {"name": "按学科编辑作业", "code": "edit_homework_by_subject", "resource": "homework"},
           {"name": "编辑自己的作业", "code": "edit_homework_own", "resource": "homework"},
           
           # 作业删除权限
           {"name": "删除任何作业", "code": "delete_homework_all", "resource": "homework"},
           {"name": "按学科删除作业", "code": "delete_homework_by_subject", "resource": "homework"},
           {"name": "删除自己的作业", "code": "delete_homework_own", "resource": "homework"},
           
           # 作业批改权限
           {"name": "批改任何作业", "code": "grade_homework_all", "resource": "homework"},
           {"name": "按学科批改作业", "code": "grade_homework_by_subject", "resource": "homework"},
           {"name": "批改自己的作业", "code": "grade_homework_own", "resource": "homework"},
           
           # 作业模板权限
           {"name": "管理所有作业模板", "code": "manage_homework_template_all", "resource": "homework_template"},
           {"name": "按学科管理作业模板", "code": "manage_homework_template_by_subject", "resource": "homework_template"},
           {"name": "按备课组管理作业模板", "code": "manage_homework_template_by_lesson_group", "resource": "homework_template"},
           {"name": "使用作业模板", "code": "use_homework_template", "resource": "homework_template"},
           
           # 作业统计查看权限
           {"name": "查看所有作业统计", "code": "view_homework_stats_all", "resource": "homework_stats"},
           {"name": "按年级查看作业统计", "code": "view_homework_stats_by_grade", "resource": "homework_stats"},
           {"name": "按学科查看作业统计", "code": "view_homework_stats_by_subject", "resource": "homework_stats"},
           {"name": "按班级查看作业统计", "code": "view_homework_stats_by_class", "resource": "homework_stats"},
           {"name": "查看自己作业的统计", "code": "view_homework_stats_own", "resource": "homework_stats"},
       ]
       
       # 创建权限
       for perm in homework_permissions:
           create_permission(**perm)
       
       # 为角色分配权限
       # 校长权限
       principal_permissions = [
           "view_homework_all", "create_homework_all", "edit_homework_all", 
           "delete_homework_all", "grade_homework_all", "manage_homework_template_all",
           "view_homework_stats_all"
       ]
       assign_permissions_to_role("principal", principal_permissions)
       
       # 副校长权限
       vice_principal_permissions = [
           "view_homework_all", "view_homework_stats_all"
       ]
       assign_permissions_to_role("vice_principal", vice_principal_permissions)
       
       # 教务主任权限
       academic_director_permissions = [
           "view_homework_all", "view_homework_stats_all", "manage_homework_template_all"
       ]
       assign_permissions_to_role("academic_director", academic_director_permissions)
       
       # 年级组长权限
       grade_leader_permissions = [
           "view_homework_by_grade", "view_homework_stats_by_grade"
       ]
       assign_permissions_to_role("grade_leader", grade_leader_permissions)
       
       # 教研组长权限
       subject_leader_permissions = [
           "view_homework_by_subject", "create_homework_by_subject", 
           "edit_homework_by_subject", "delete_homework_by_subject",
           "grade_homework_by_subject", "manage_homework_template_by_subject",
           "view_homework_stats_by_subject"
       ]
       assign_permissions_to_role("subject_leader", subject_leader_permissions)
       
       # 备课组长权限
       lesson_group_leader_permissions = [
           "view_homework_by_subject", "manage_homework_template_by_lesson_group",
           "view_homework_stats_by_subject"
       ]
       assign_permissions_to_role("lesson_group_leader", lesson_group_leader_permissions)
       
       # 班主任权限
       class_teacher_permissions = [
           "view_homework_by_class", "view_homework_stats_by_class"
       ]
       assign_permissions_to_role("class_teacher", class_teacher_permissions)
       
       # 普通教师权限
       teacher_permissions = [
           "view_homework_own", "create_homework_own", "edit_homework_own",
           "delete_homework_own", "grade_homework_own", "use_homework_template",
           "view_homework_stats_own"
       ]
       assign_permissions_to_role("teacher", teacher_permissions)
   ```

3. **分阶段部署**:
   - 第一阶段：添加基础角色和权限结构
   - 第二阶段：实现权限检查和验证
   - 第三阶段：添加前端角色管理界面
   - 第四阶段：细化权限和资源限制

### 总结

通过实现基于角色的权限控制系统，可以细化学校各级管理人员的权限，使系统更加符合实际教育环境的管理结构。这种设计既考虑了层级性（校长→副校长→教务主任→...→教师），又考虑了专业性（教研组长管理学科，年级组长管理年级），能够满足不同角色的管理需求。

是否需要我更详细地说明某个部分，或者修改方案以更好地满足您的需求？

实施步骤建议如下：
首先按照设计方案中的数据库设计创建或修改相关表结构
实现权限验证中间件和权限检查工具
执行迁移脚本，创建角色和权限，并将现有用户迁移到新的角色体系
实现后端API的权限控制
实现前端的权限检查组件和导航菜单权限控制
分阶段部署，先从基础角色权限结构开始，然后逐步实现细化的权限控制
由于方案中已经提供了详细的代码示例和实现思路，开发团队可以直接参考这些代码进行实现，无需从零开始设计。
需要注意的是，在实施过程中应该进行充分的测试，确保不同角色的用户能够正确访问自己有权限的功能，同时无法访问没有权限的功能。