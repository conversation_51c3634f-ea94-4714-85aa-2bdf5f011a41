{"ast": null, "code": "import { codes, values } from 'micromark-util-symbol';\n\n/**\n * Turn the number (in string form as either hexa- or plain decimal) coming from\n * a numeric character reference into a character.\n *\n * Sort of like `String.fromCodePoint(Number.parseInt(value, base))`, but makes\n * non-characters and control characters safe.\n *\n * @param {string} value\n *   Value to decode.\n * @param {number} base\n *   Numeric base.\n * @returns {string}\n *   Character.\n */\nexport function decodeNumericCharacterReference(value, base) {\n  const code = Number.parseInt(value, base);\n  if (\n  // C0 except for HT, LF, FF, CR, space.\n  code < codes.ht || code === codes.vt || code > codes.cr && code < codes.space ||\n  // Control character (DEL) of C0, and C1 controls.\n  code > codes.tilde && code < 160 ||\n  // Lone high surrogates and low surrogates.\n  code > 55_295 && code < 57_344 ||\n  // Noncharacters.\n  code > 64_975 && code < 65_008 || /* eslint-disable no-bitwise */\n  (code & 65_535) === 65_535 || (code & 65_535) === 65_534 || /* eslint-enable no-bitwise */\n  // Out of range\n  code > 1_114_111) {\n    return values.replacementCharacter;\n  }\n  return String.fromCodePoint(code);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}