from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

# 角色-科目权限关联模型
class RoleSubjectPermissionBase(BaseModel):
    role_id: int
    subject_id: int
    can_view: bool = True
    can_edit: bool = False
    can_delete: bool = False
    can_assign: bool = False

class RoleSubjectPermissionCreate(RoleSubjectPermissionBase):
    pass

class RoleSubjectPermissionUpdate(BaseModel):
    can_view: Optional[bool] = None
    can_edit: Optional[bool] = None
    can_delete: Optional[bool] = None
    can_assign: Optional[bool] = None

class RoleSubjectPermission(RoleSubjectPermissionBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

# 批量设置角色-科目权限的请求模型
class BatchSetRoleSubjectPermissions(BaseModel):
    role_id: int
    permissions: List[RoleSubjectPermissionCreate]

# 科目权限检查结果模型
class SubjectPermissionCheck(BaseModel):
    subject_id: int
    can_view: bool
    can_edit: bool
    can_delete: bool
    can_assign: bool 