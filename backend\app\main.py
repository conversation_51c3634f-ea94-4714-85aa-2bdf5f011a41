from fastapi import FastAP<PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session
from .database import engine, Base, get_db
from .routers import auth, admin, school, homework, homework_analysis, public, student, system_analysis, parent, region
import logging
import sys
import os
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建数据库表
Base.metadata.create_all(bind=engine)

# 创建FastAPI应用
app = FastAPI(title="作业批改系统API")

# 配置CORS
origins = [
    "http://localhost",
    "http://localhost:3000",
    "http://localhost:8000",
    "*"
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix="/api", tags=["auth"])
app.include_router(admin.router, prefix="/api/admin", tags=["admin"])
app.include_router(school.router, prefix="/api/schools", tags=["schools"])
app.include_router(homework.router, prefix="/api/homework", tags=["homework"])
app.include_router(homework_analysis.router, prefix="/api/homework-analysis", tags=["homework-analysis"])
app.include_router(public.router, prefix="/api/public", tags=["public"])
app.include_router(student.router, prefix="/api/student", tags=["student"])
app.include_router(system_analysis.router, prefix="/api/system", tags=["system"])
app.include_router(region.router, prefix="/api/regions", tags=["regions"])

# 应用启动日志
logging.info("======== 应用启动 ========")
logging.info(f"启动时间: {datetime.now().isoformat()}")
logging.info(f"Python版本: {sys.version}")
logging.info(f"工作目录: {os.getcwd()}")
logging.info("======== 启动完成 ========")

@app.get("/")
def read_root():
    return {"message": "Welcome to Homework Correction System API"} 