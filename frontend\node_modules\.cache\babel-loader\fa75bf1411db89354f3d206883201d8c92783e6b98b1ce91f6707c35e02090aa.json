{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genDraggerStyle = token => {\n  const {\n    componentCls,\n    iconCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-drag`]: {\n        position: 'relative',\n        width: '100%',\n        height: '100%',\n        textAlign: 'center',\n        background: token.colorFillAlter,\n        border: `${unit(token.lineWidth)} dashed ${token.colorBorder}`,\n        borderRadius: token.borderRadiusLG,\n        cursor: 'pointer',\n        transition: `border-color ${token.motionDurationSlow}`,\n        [componentCls]: {\n          padding: token.padding\n        },\n        [`${componentCls}-btn`]: {\n          display: 'table',\n          width: '100%',\n          height: '100%',\n          outline: 'none',\n          borderRadius: token.borderRadiusLG,\n          '&:focus-visible': {\n            outline: `${unit(token.lineWidthFocus)} solid ${token.colorPrimaryBorder}`\n          }\n        },\n        [`${componentCls}-drag-container`]: {\n          display: 'table-cell',\n          verticalAlign: 'middle'\n        },\n        [`\n          &:not(${componentCls}-disabled):hover,\n          &-hover:not(${componentCls}-disabled)\n        `]: {\n          borderColor: token.colorPrimaryHover\n        },\n        [`p${componentCls}-drag-icon`]: {\n          marginBottom: token.margin,\n          [iconCls]: {\n            color: token.colorPrimary,\n            fontSize: token.uploadThumbnailSize\n          }\n        },\n        [`p${componentCls}-text`]: {\n          margin: `0 0 ${unit(token.marginXXS)}`,\n          color: token.colorTextHeading,\n          fontSize: token.fontSizeLG\n        },\n        [`p${componentCls}-hint`]: {\n          color: token.colorTextDescription,\n          fontSize: token.fontSize\n        },\n        // ===================== Disabled =====================\n        [`&${componentCls}-disabled`]: {\n          [`p${componentCls}-drag-icon ${iconCls},\n            p${componentCls}-text,\n            p${componentCls}-hint\n          `]: {\n            color: token.colorTextDisabled\n          }\n        }\n      }\n    }\n  };\n};\nexport default genDraggerStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}