from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime

from ..database import Base

class School(Base):
    __tablename__ = "schools"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True, nullable=False)
    province = Column(String, index=True, nullable=True)  # 省份
    city = Column(String, index=True, nullable=True)  # 城市
    district = Column(String, index=True, nullable=True)  # 区县
    address = Column(Text, nullable=True)
    contact_info = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    classes = relationship("Class", back_populates="school")
    users = relationship("User", back_populates="school") 