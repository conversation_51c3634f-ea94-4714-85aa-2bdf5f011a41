# -*- coding: utf-8 -*-
"""
作业分析模块路由
提供作业分析相关的API接口
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, text
from typing import List, Optional, Dict, Any
import json
import io
import logging
from datetime import datetime

# PDF生成相关导入 - 临时注释以解决依赖问题
# from reportlab.lib.pagesizes import letter, A4
# from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
# from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
# from reportlab.lib.units import inch
# from reportlab.lib import colors
# from reportlab.pdfbase import pdfmetrics
# from reportlab.pdfbase.ttfonts import TTFont

# Excel生成相关导入 - 临时注释以解决依赖问题
# from openpyxl import Workbook
# from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
# from openpyxl.utils.dataframe import dataframe_to_rows

from ..database import get_db
from ..models.homework import (
    HomeworkAssignment, Homework, HomeworkAnalysis, 
    StudentPerformanceAnalysis, QuestionAnalysis, ExportTemplate
)
from ..models.user import User, Class
from ..models.school import School
from ..models.subject import Subject
from ..services.homework_analysis_service import HomeworkAnalysisService
from ..services.auth_service import get_current_user

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter(tags=["homework-analysis"])

# 功能开关配置
FEATURE_FLAGS = {
    "homework_analysis": True,
    "overview": True,
    "question_analysis": True,
    "student_details": True,
    "smart_suggestions": True,
    "parent_reports": True,
    "data_export": True
}

def check_feature_enabled(feature_name: str):
    """检查功能是否启用"""
    if not FEATURE_FLAGS.get(feature_name, False):
        raise HTTPException(
            status_code=404,
            detail=f"功能 {feature_name} 暂未开放"
        )

def generate_pdf_report(export_data: dict, assignment_id: int) -> io.BytesIO:
    """生成PDF报告 - 临时禁用"""
    buffer = io.BytesIO()
    buffer.write(b"PDF generation temporarily disabled due to missing dependencies")
    buffer.seek(0)
    return buffer

    # 临时注释掉PDF生成代码
    """
    # 创建PDF文档，设置页边距
    doc = SimpleDocTemplate(
        buffer,
        pagesize=A4,
        rightMargin=50,
        leftMargin=50,
        topMargin=50,
        bottomMargin=50
    )
    story = []

    # 获取样式
    styles = getSampleStyleSheet()

    # 自定义样式
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=20,
        spaceAfter=30,
        alignment=1,  # 居中
        textColor=colors.darkblue,
        fontName='Helvetica-Bold'
    )

    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=15,
        spaceBefore=20,
        textColor=colors.darkgreen,
        fontName='Helvetica-Bold'
    )

    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=6,
        fontName='Helvetica'
    )

    # 报告标题和基本信息
    story.append(Paragraph("智教云端智能辅导平台", title_style))
    story.append(Paragraph("作业分析报告", title_style))
    story.append(Spacer(1, 10))

    # 基本信息表格
    info_data = [
        ['报告生成时间', datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')],
        ['作业编号', str(assignment_id)],
        ['报告类型', '综合分析报告']
    ]

    info_table = Table(info_data, colWidths=[2*inch, 3*inch])
    info_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))

    story.append(info_table)
    story.append(Spacer(1, 30))

    # 概览数据
    if 'overview' in export_data:
        overview = export_data['overview']
        story.append(Paragraph("一、作业概览统计", heading_style))

        # 处理overview可能不是字典的情况
        if isinstance(overview, dict):
            total_submissions = overview.get('total_submissions', 0)
            average_score = overview.get('average_score', 0)
            max_score = overview.get('max_score', 0)
            min_score = overview.get('min_score', 0)
            submission_rate = overview.get('submission_rate', 0)
        else:
            # 使用默认值
            total_submissions = 0
            average_score = 0
            max_score = 0
            min_score = 0
            submission_rate = 0

        # 基础统计数据
        basic_stats = [
            ['统计项目', '数值', '说明'],
            ['总提交数', str(total_submissions), '已提交作业的学生人数'],
            ['平均分', f"{average_score:.1f}分", '全班平均成绩'],
            ['最高分', f"{max_score}分", '班级最高成绩'],
            ['最低分', f"{min_score}分", '班级最低成绩'],
            ['提交率', f"{submission_rate:.1f}%", '作业提交完成率'],
        ]

        basic_table = Table(basic_stats, colWidths=[1.5*inch, 1*inch, 2.5*inch])
        basic_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('TOPPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        story.append(basic_table)
        story.append(Spacer(1, 20))

        # 成绩分布
        if 'score_distribution' in overview:
            story.append(Paragraph("成绩分布情况", normal_style))
            dist_data = overview['score_distribution']

            dist_table_data = [['分数段', '人数', '占比']]
            for item in dist_data:
                dist_table_data.append([
                    item.get('range', 'N/A'),
                    str(item.get('count', 0)),
                    f"{item.get('percentage', 0):.1f}%"
                ])

            dist_table = Table(dist_table_data, colWidths=[1.5*inch, 1*inch, 1*inch])
            dist_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))

            story.append(dist_table)
            story.append(Spacer(1, 25))

    # 题目分析
    if 'questions' in export_data:
        questions_data = export_data['questions']
        story.append(Paragraph("二、逐题分析详情", heading_style))

        if 'questions' in questions_data:
            questions = questions_data['questions'][:15]  # 显示前15题

            # 题目分析表格
            question_table_data = [
                ['题号', '题目类型', '正确率', '难度等级', '错误率', '建议']
            ]

            for i, q in enumerate(questions, 1):
                # 处理q可能是字符串或字典的情况
                if isinstance(q, dict):
                    accuracy = q.get('accuracy_rate', 0)
                    question_number = q.get('question_number', i)
                    question_type = q.get('question_type', '选择题')
                else:
                    # 如果是字符串或其他类型，使用默认值
                    accuracy = 0
                    question_number = i
                    question_type = '选择题'

                error_rate = 100 - accuracy

                # 根据正确率判断难度和建议
                if accuracy >= 80:
                    difficulty = "简单"
                    suggestion = "巩固练习"
                elif accuracy >= 60:
                    difficulty = "中等"
                    suggestion = "重点讲解"
                else:
                    difficulty = "困难"
                    suggestion = "专项训练"

                question_table_data.append([
                    f"第{question_number}题",
                    question_type,
                    f"{accuracy:.1f}%",
                    difficulty,
                    f"{error_rate:.1f}%",
                    suggestion
                ])

            question_table = Table(question_table_data, colWidths=[0.8*inch, 1*inch, 0.8*inch, 0.8*inch, 0.8*inch, 1*inch])
            question_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkred),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('TOPPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))

            story.append(question_table)
            story.append(Spacer(1, 25))

    # 学生表现
    if 'students' in export_data:
        students_data = export_data['students']
        story.append(Paragraph("三、学生表现分析", heading_style))

        if 'students' in students_data:
            students = students_data['students'][:20]  # 显示前20名学生

            # 学生表现表格
            student_table_data = [
                ['排名', '学生姓名', '得分', '正确率', '完成时间', '表现等级']
            ]

            for i, student in enumerate(students, 1):
                # 处理student可能是字符串或字典的情况
                if isinstance(student, dict):
                    score = student.get('score', 0)
                    accuracy = student.get('accuracy_rate', 0)
                    student_name = student.get('student_name', f'学生{i}')
                    completion_time = student.get('completion_time', '未知')
                else:
                    # 如果是字符串或其他类型，使用默认值
                    score = 0
                    accuracy = 0
                    student_name = f'学生{i}'
                    completion_time = '未知'

                # 根据得分判断表现等级
                if score >= 90:
                    level = "优秀"
                elif score >= 80:
                    level = "良好"
                elif score >= 70:
                    level = "中等"
                elif score >= 60:
                    level = "及格"
                else:
                    level = "待提高"

                student_table_data.append([
                    str(i),
                    student_name,
                    f"{score:.1f}分",
                    f"{accuracy:.1f}%",
                    completion_time,
                    level
                ])

            student_table = Table(student_table_data, colWidths=[0.6*inch, 1.2*inch, 0.8*inch, 0.8*inch, 1*inch, 0.8*inch])
            student_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkorange),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('TOPPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightyellow),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))

            story.append(student_table)
            story.append(Spacer(1, 25))

    # 智能建议
    if 'suggestions' in export_data:
        suggestions_data = export_data['suggestions']
        story.append(Paragraph("四、智能教学建议", heading_style))

        if 'suggestions' in suggestions_data:
            suggestions = suggestions_data['suggestions'][:8]  # 显示前8条建议

            # 建议分类
            suggestion_categories = {
                '教学重点': [],
                '个别辅导': [],
                '课堂讲解': [],
                '练习建议': []
            }

            for i, suggestion in enumerate(suggestions, 1):
                # 处理suggestion可能是字符串或字典的情况
                if isinstance(suggestion, dict):
                    content = suggestion.get('content', f'建议{i}：根据学生表现调整教学策略')
                    category = suggestion.get('category', '教学重点')
                else:
                    # 如果是字符串，直接使用
                    content = str(suggestion) if suggestion else f'建议{i}：根据学生表现调整教学策略'
                    category = '教学重点'

                if category in suggestion_categories:
                    suggestion_categories[category].append(content)
                else:
                    suggestion_categories['教学重点'].append(content)

            # 按类别显示建议
            for category, items in suggestion_categories.items():
                if items:
                    story.append(Paragraph(f"{category}：", normal_style))
                    for item in items:
                        story.append(Paragraph(f"• {item}", normal_style))
                    story.append(Spacer(1, 10))

    # 报告总结
    story.append(Spacer(1, 20))
    story.append(Paragraph("五、报告总结", heading_style))

    # summary_text = f'''
    # 本次作业分析报告基于作业ID {assignment_id} 的数据生成。通过对学生提交情况、成绩分布、
    # 逐题分析等多个维度的综合分析，为教师提供了详细的教学参考信息。
    #
    # 建议教师根据报告中的数据分析结果，针对性地调整教学策略，关注学习困难的学生，
    # 加强薄弱知识点的讲解，提高整体教学效果。
    # '''

    story.append(Paragraph(summary_text, normal_style))
    story.append(Spacer(1, 20))

    # 页脚信息
    footer_text = f"报告生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')} | 智教云端智能辅导平台"
    footer_style = ParagraphStyle(
        'Footer',
        parent=styles['Normal'],
        fontSize=8,
        textColor=colors.grey,
        alignment=1  # 居中
    )
    story.append(Paragraph(footer_text, footer_style))

    # 生成PDF
    doc.build(story)
    buffer.seek(0)
    return buffer
    """

def generate_simple_pdf_report(export_data: dict, assignment_id: int) -> io.BytesIO:
    """生成简化版PDF报告 - 临时禁用"""
    buffer = io.BytesIO()
    buffer.write(b"PDF generation temporarily disabled due to missing dependencies")
    buffer.seek(0)
    return buffer

    # 临时注释掉PDF生成代码
    """
    # 创建PDF文档
    doc = SimpleDocDocument(buffer, pagesize=A4)
    story = []

    # 获取样式
    styles = getSampleStyleSheet()
    title_style = styles['Title']
    heading_style = styles['Heading2']
    normal_style = styles['Normal']

    # 标题
    story.append(Paragraph(f"作业分析报告", title_style))
    story.append(Paragraph(f"作业ID: {assignment_id}", heading_style))
    story.append(Spacer(1, 20))

    # 概览数据
    if 'overview' in export_data:
        story.append(Paragraph("一、作业概览", heading_style))
        overview = export_data['overview']

        # 简单的文本展示
        if isinstance(overview, dict):
            story.append(Paragraph(f"总提交数: {overview.get('total_submissions', 0)}", normal_style))
            story.append(Paragraph(f"平均分: {overview.get('average_score', 0):.1f}", normal_style))
            story.append(Paragraph(f"最高分: {overview.get('max_score', 0)}", normal_style))
            story.append(Paragraph(f"最低分: {overview.get('min_score', 0)}", normal_style))
        else:
            story.append(Paragraph("概览数据不可用", normal_style))

        story.append(Spacer(1, 20))

    # 题目分析
    if 'questions' in export_data:
        story.append(Paragraph("二、题目分析", heading_style))
        questions_data = export_data['questions']

        if isinstance(questions_data, dict) and 'questions' in questions_data:
            questions = questions_data['questions']
            if isinstance(questions, list):
                for i, q in enumerate(questions[:10], 1):  # 只显示前10题
                    if isinstance(q, dict):
                        accuracy = q.get('accuracy_rate', 0)
                        story.append(Paragraph(f"第{i}题: 正确率 {accuracy:.1f}%", normal_style))
                    else:
                        story.append(Paragraph(f"第{i}题: 数据不可用", normal_style))
            else:
                story.append(Paragraph("题目数据格式错误", normal_style))
        else:
            story.append(Paragraph("题目分析数据不可用", normal_style))

        story.append(Spacer(1, 20))

    # 学生表现
    if 'students' in export_data:
        story.append(Paragraph("三、学生表现", heading_style))
        students_data = export_data['students']

        if isinstance(students_data, dict) and 'students' in students_data:
            students = students_data['students']
            if isinstance(students, list):
                for i, student in enumerate(students[:10], 1):  # 只显示前10名
                    if isinstance(student, dict):
                        name = student.get('student_name', f'学生{i}')
                        score = student.get('score', 0)
                        story.append(Paragraph(f"{i}. {name}: {score:.1f}分", normal_style))
                    else:
                        story.append(Paragraph(f"{i}. 学生数据不可用", normal_style))
            else:
                story.append(Paragraph("学生数据格式错误", normal_style))
        else:
            story.append(Paragraph("学生表现数据不可用", normal_style))

        story.append(Spacer(1, 20))

    # 智能建议
    if 'suggestions' in export_data:
        story.append(Paragraph("四、智能建议", heading_style))
        suggestions_data = export_data['suggestions']

        if isinstance(suggestions_data, dict) and 'suggestions' in suggestions_data:
            suggestions = suggestions_data['suggestions']
            if isinstance(suggestions, list):
                for i, suggestion in enumerate(suggestions[:5], 1):  # 只显示前5条
                    if isinstance(suggestion, dict):
                        content = suggestion.get('content', f'建议{i}')
                    else:
                        content = str(suggestion) if suggestion else f'建议{i}'
                    story.append(Paragraph(f"{i}. {content}", normal_style))
            else:
                story.append(Paragraph("建议数据格式错误", normal_style))
        else:
            story.append(Paragraph("智能建议数据不可用", normal_style))

    # 生成PDF
    doc.build(story)
    buffer.seek(0)
    return buffer
    """

def generate_excel_report(export_data: dict, assignment_id: int) -> io.BytesIO:
    """生成Excel报告 - 临时禁用"""
    buffer = io.BytesIO()
    buffer.write(b"Excel generation temporarily disabled due to missing dependencies")
    buffer.seek(0)
    return buffer

    # 临时注释掉Excel生成代码
    """
    wb = Workbook()

    # 删除默认工作表
    wb.remove(wb.active)

    # 定义样式
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    center_alignment = Alignment(horizontal='center', vertical='center')

    # 1. 概览数据工作表
    if 'overview' in export_data:
        overview_ws = wb.create_sheet("作业概览")
        overview = export_data['overview']

        # 标题
        overview_ws['A1'] = f"作业分析概览 - 作业ID: {assignment_id}"
        overview_ws['A1'].font = Font(bold=True, size=14)
        overview_ws.merge_cells('A1:C1')

        # 基础统计
        overview_ws['A3'] = "统计项目"
        overview_ws['B3'] = "数值"
        overview_ws['C3'] = "说明"

        # 设置标题样式
        for col in ['A3', 'B3', 'C3']:
            overview_ws[col].font = header_font
            overview_ws[col].fill = header_fill
            overview_ws[col].border = border
            overview_ws[col].alignment = center_alignment

        # 数据行
        data_rows = [
            ["总提交数", overview.get('total_submissions', 0), "已提交作业的学生人数"],
            ["平均分", f"{overview.get('average_score', 0):.1f}", "全班平均成绩"],
            ["最高分", overview.get('max_score', 0), "班级最高成绩"],
            ["最低分", overview.get('min_score', 0), "班级最低成绩"],
            ["提交率", f"{overview.get('submission_rate', 0):.1f}%", "作业提交完成率"],
        ]

        for i, row_data in enumerate(data_rows, 4):
            for j, value in enumerate(row_data, 1):
                cell = overview_ws.cell(row=i, column=j, value=value)
                cell.border = border
                if j == 2:  # 数值列居中
                    cell.alignment = center_alignment

        # 调整列宽
        overview_ws.column_dimensions['A'].width = 15
        overview_ws.column_dimensions['B'].width = 12
        overview_ws.column_dimensions['C'].width = 25

    # 2. 逐题分析工作表
    if 'questions' in export_data:
        questions_ws = wb.create_sheet("逐题分析")
        questions_data = export_data['questions']

        if 'questions' in questions_data:
            questions = questions_data['questions']

            # 标题
            questions_ws['A1'] = "逐题分析详情"
            questions_ws['A1'].font = Font(bold=True, size=14)
            questions_ws.merge_cells('A1:F1')

            # 表头
            headers = ["题号", "题目类型", "正确率", "错误率", "难度等级", "教学建议"]
            for j, header in enumerate(headers, 1):
                cell = questions_ws.cell(row=3, column=j, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.border = border
                cell.alignment = center_alignment

            # 数据行
            for i, q in enumerate(questions, 4):
                accuracy = q.get('accuracy_rate', 0)
                error_rate = 100 - accuracy

                # 判断难度和建议
                if accuracy >= 80:
                    difficulty = "简单"
                    suggestion = "巩固练习"
                elif accuracy >= 60:
                    difficulty = "中等"
                    suggestion = "重点讲解"
                else:
                    difficulty = "困难"
                    suggestion = "专项训练"

                row_data = [
                    f"第{q.get('question_number', i-3)}题",
                    q.get('question_type', '选择题'),
                    f"{accuracy:.1f}%",
                    f"{error_rate:.1f}%",
                    difficulty,
                    suggestion
                ]

                for j, value in enumerate(row_data, 1):
                    cell = questions_ws.cell(row=i, column=j, value=value)
                    cell.border = border
                    if j in [3, 4]:  # 百分比列居中
                        cell.alignment = center_alignment

            # 调整列宽
            for col, width in [('A', 12), ('B', 12), ('C', 10), ('D', 10), ('E', 12), ('F', 15)]:
                questions_ws.column_dimensions[col].width = width

    # 3. 学生表现工作表
    if 'students' in export_data:
        students_ws = wb.create_sheet("学生表现")
        students_data = export_data['students']

        if 'students' in students_data:
            students = students_data['students']

            # 标题
            students_ws['A1'] = "学生表现分析"
            students_ws['A1'].font = Font(bold=True, size=14)
            students_ws.merge_cells('A1:F1')

            # 表头
            headers = ["排名", "学生姓名", "得分", "正确率", "完成时间", "表现等级"]
            for j, header in enumerate(headers, 1):
                cell = students_ws.cell(row=3, column=j, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.border = border
                cell.alignment = center_alignment

            # 数据行
            for i, student in enumerate(students, 4):
                score = student.get('score', 0)
                accuracy = student.get('accuracy_rate', 0)

                # 判断表现等级
                if score >= 90:
                    level = "优秀"
                elif score >= 80:
                    level = "良好"
                elif score >= 70:
                    level = "中等"
                elif score >= 60:
                    level = "及格"
                else:
                    level = "待提高"

                row_data = [
                    i - 3,  # 排名
                    student.get('student_name', f'学生{i-3}'),
                    f"{score:.1f}",
                    f"{accuracy:.1f}%",
                    student.get('completion_time', '未知'),
                    level
                ]

                for j, value in enumerate(row_data, 1):
                    cell = students_ws.cell(row=i, column=j, value=value)
                    cell.border = border
                    if j in [1, 3, 4]:  # 数值列居中
                        cell.alignment = center_alignment

            # 调整列宽
            for col, width in [('A', 8), ('B', 15), ('C', 10), ('D', 10), ('E', 15), ('F', 12)]:
                students_ws.column_dimensions[col].width = width

    # 4. 智能建议工作表
    if 'suggestions' in export_data:
        suggestions_ws = wb.create_sheet("智能建议")
        suggestions_data = export_data['suggestions']

        if 'suggestions' in suggestions_data:
            suggestions = suggestions_data['suggestions']

            # 标题
            suggestions_ws['A1'] = "智能教学建议"
            suggestions_ws['A1'].font = Font(bold=True, size=14)
            suggestions_ws.merge_cells('A1:C1')

            # 表头
            headers = ["序号", "建议类型", "具体建议"]
            for j, header in enumerate(headers, 1):
                cell = suggestions_ws.cell(row=3, column=j, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.border = border
                cell.alignment = center_alignment

            # 数据行
            for i, suggestion in enumerate(suggestions, 4):
                row_data = [
                    i - 3,  # 序号
                    suggestion.get('category', '教学建议'),
                    suggestion.get('content', f'建议{i-3}：根据学生表现调整教学策略')
                ]

                for j, value in enumerate(row_data, 1):
                    cell = suggestions_ws.cell(row=i, column=j, value=value)
                    cell.border = border
                    if j == 1:  # 序号居中
                        cell.alignment = center_alignment

            # 调整列宽
            suggestions_ws.column_dimensions['A'].width = 8
            suggestions_ws.column_dimensions['B'].width = 15
            suggestions_ws.column_dimensions['C'].width = 50

    # 保存到内存
    buffer = io.BytesIO()
    wb.save(buffer)
    buffer.seek(0)
    return buffer
    """

def check_permission(user: User, assignment_id: int, db: Session):
    """检查用户权限"""
    # 获取作业信息
    assignment = db.query(HomeworkAssignment).filter(
        HomeworkAssignment.id == assignment_id
    ).first()

    if not assignment:
        # 提供更详细的错误信息，帮助调试数据不一致问题
        max_id_result = db.execute(text("SELECT MAX(id) FROM homework_assignments")).fetchone()
        max_id = max_id_result[0] if max_id_result else 0
        raise HTTPException(
            status_code=404,
            detail=f"作业任务不存在 (ID={assignment_id})。数据库中最大ID为{max_id}，可能存在数据不一致问题。"
        )

    # 权限检查逻辑
    if user.is_admin:
        return True  # 管理员可以查看所有数据

    # 教师只能查看自己任教的班级和学科
    if user.is_teacher:
        # 检查是否是该作业的任课教师
        if assignment.teacher_id == user.id:
            return True

        # 检查是否是该班级的任课教师
        # TODO: 根据实际权限系统完善

    raise HTTPException(status_code=403, detail="无权访问此作业数据")

@router.get("/overview/{assignment_id}")
async def get_homework_overview(
    assignment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取作业概览数据"""
    check_feature_enabled("overview")
    check_permission(current_user, assignment_id, db)
    
    try:
        service = HomeworkAnalysisService(db)
        overview_data = await service.get_homework_overview(assignment_id)
        return {
            "success": True,
            "data": overview_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取概览数据失败: {str(e)}")

@router.get("/questions/{assignment_id}")
async def get_question_analysis(
    assignment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取逐题分析数据"""
    check_feature_enabled("question_analysis")
    check_permission(current_user, assignment_id, db)

    try:
        service = HomeworkAnalysisService(db)
        questions_data = await service.get_question_analysis(assignment_id)
        return {
            "success": True,
            "data": questions_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取题目分析失败: {str(e)}")

@router.get("/question-detail/{assignment_id}/{question_number}")
async def get_question_detail(
    assignment_id: int,
    question_number: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取单题详细分析"""
    check_feature_enabled("question_analysis")
    check_permission(current_user, assignment_id, db)
    
    try:
        service = HomeworkAnalysisService(db)
        question_detail = await service.get_question_detail(assignment_id, question_number)
        return {
            "success": True,
            "data": question_detail
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取题目详情失败: {str(e)}")

@router.get("/students/{assignment_id}")
async def get_student_details(
    assignment_id: int,
    sort_by: str = Query("score", description="排序字段: score, name, submit_time"),
    order: str = Query("desc", description="排序方向: asc, desc"),
    filter_status: str = Query("all", description="筛选状态: all, submitted, not_submitted"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取学生详情列表"""
    check_feature_enabled("student_details")
    check_permission(current_user, assignment_id, db)
    
    try:
        service = HomeworkAnalysisService(db)
        students_data = await service.get_student_details(
            assignment_id, sort_by, order, filter_status
        )
        return {
            "success": True,
            "data": students_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取学生详情失败: {str(e)}")

@router.get("/student-detail/{student_id}/{assignment_id}")
async def get_student_performance(
    student_id: int,
    assignment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取学生个人详细分析"""
    check_feature_enabled("student_details")
    check_permission(current_user, assignment_id, db)
    
    try:
        service = HomeworkAnalysisService(db)
        student_performance = await service.get_student_performance(student_id, assignment_id)
        return {
            "success": True,
            "data": student_performance
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取学生表现失败: {str(e)}")

@router.get("/suggestions/{assignment_id}")
async def get_smart_suggestions(
    assignment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取智能建议"""
    check_feature_enabled("smart_suggestions")
    check_permission(current_user, assignment_id, db)
    
    try:
        service = HomeworkAnalysisService(db)
        suggestions = await service.get_smart_suggestions(assignment_id)
        return {
            "success": True,
            "data": suggestions
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取智能建议失败: {str(e)}")

@router.post("/generate-suggestions/{assignment_id}")
async def generate_suggestions(
    assignment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """生成智能建议"""
    check_feature_enabled("smart_suggestions")
    check_permission(current_user, assignment_id, db)
    
    try:
        service = HomeworkAnalysisService(db)
        suggestions = await service.generate_suggestions(assignment_id)
        return {
            "success": True,
            "data": suggestions,
            "message": "智能建议生成成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成智能建议失败: {str(e)}")

@router.get("/parent-report/{student_id}/{assignment_id}")
async def get_parent_report(
    student_id: int,
    assignment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取家长报告"""
    check_feature_enabled("parent_reports")
    check_permission(current_user, assignment_id, db)
    
    try:
        service = HomeworkAnalysisService(db)
        parent_report = await service.get_parent_report(student_id, assignment_id)
        return {
            "success": True,
            "data": parent_report
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取家长报告失败: {str(e)}")

@router.post("/remind-submission")
async def remind_submission(
    assignment_id: int,
    student_ids: List[int],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """催交作业"""
    check_permission(current_user, assignment_id, db)

    try:
        service = HomeworkAnalysisService(db)
        result = await service.remind_submission(assignment_id, student_ids)
        return {
            "success": True,
            "data": result,
            "message": "催交通知发送成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送催交通知失败: {str(e)}")

@router.post("/remind-all-unsubmitted/{assignment_id}")
async def remind_all_unsubmitted(
    assignment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """一键催交所有未提交学生"""
    check_permission(current_user, assignment_id, db)

    try:
        service = HomeworkAnalysisService(db)

        # 获取所有未提交学生的ID
        unsubmitted_student_ids = await service.get_unsubmitted_student_ids(assignment_id)

        if not unsubmitted_student_ids:
            return {
                "success": True,
                "data": {
                    "assignment_id": assignment_id,
                    "reminded_count": 0,
                    "reminded_students": [],
                    "message": "所有学生都已提交作业"
                },
                "message": "所有学生都已提交作业"
            }

        # 发送催交通知
        result = await service.remind_submission(assignment_id, unsubmitted_student_ids)

        return {
            "success": True,
            "data": result,
            "message": f"已向 {len(unsubmitted_student_ids)} 名未提交学生发送催交通知"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"一键催交失败: {str(e)}")

# 数据导出接口
@router.post("/export")
async def export_homework_analysis(
    request_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """导出作业分析数据"""
    try:
        assignment_id = request_data.get("assignment_id")
        if not assignment_id:
            raise HTTPException(status_code=400, detail="缺少作业ID")

        # 检查权限
        check_permission(current_user, assignment_id, db)

        # 获取导出格式和选项
        export_format = request_data.get("format", "excel")
        export_options = {
            "include_overview": request_data.get("include_overview", True),
            "include_questions": request_data.get("include_questions", True),
            "include_students": request_data.get("include_students", True),
            "include_suggestions": request_data.get("include_suggestions", True)
        }

        # 获取作业分析数据
        service = HomeworkAnalysisService(db)

        # 收集所有需要的数据
        export_data = {}

        if export_options["include_overview"]:
            overview_data = await service.get_homework_overview(assignment_id)
            export_data["overview"] = overview_data

        if export_options["include_questions"]:
            questions_data = await service.get_question_analysis(assignment_id)
            export_data["questions"] = questions_data

        if export_options["include_students"]:
            students_data = await service.get_student_details(assignment_id)
            export_data["students"] = students_data

        if export_options["include_suggestions"]:
            suggestions_data = await service.get_smart_suggestions(assignment_id)
            export_data["suggestions"] = suggestions_data

        # 根据格式生成文件
        if export_format.lower() == "json":
            # JSON格式导出
            json_data = json.dumps(export_data, ensure_ascii=False, indent=2)
            file_content = io.BytesIO(json_data.encode('utf-8'))
            filename = f"homework_analysis_{assignment_id}.json"
            media_type = "application/json"

        elif export_format.lower() == "csv":
            # CSV格式导出（简化版，只导出概览数据）
            csv_lines = []
            csv_lines.append("项目,数值")

            if "overview" in export_data:
                overview = export_data["overview"]
                csv_lines.append(f"总提交数,{overview.get('total_submissions', 0)}")
                csv_lines.append(f"平均分,{overview.get('average_score', 0)}")
                csv_lines.append(f"最高分,{overview.get('max_score', 0)}")
                csv_lines.append(f"最低分,{overview.get('min_score', 0)}")

            csv_content = "\n".join(csv_lines)
            file_content = io.BytesIO(csv_content.encode('utf-8-sig'))  # 使用BOM以支持Excel
            filename = f"homework_analysis_{assignment_id}.csv"
            media_type = "text/csv"

        elif export_format.lower() == "pdf":
            # PDF格式导出（简化版）
            try:
                file_content = generate_simple_pdf_report(export_data, assignment_id)
                filename = f"homework_analysis_{assignment_id}.pdf"
                media_type = "application/pdf"
            except Exception as e:
                # 如果PDF生成失败，返回JSON格式
                json_data = json.dumps(export_data, ensure_ascii=False, indent=2)
                file_content = io.BytesIO(json_data.encode('utf-8'))
                filename = f"homework_analysis_{assignment_id}.json"
                media_type = "application/json"

        elif export_format.lower() == "excel":
            # Excel格式导出
            file_content = generate_excel_report(export_data, assignment_id)
            filename = f"homework_analysis_{assignment_id}.xlsx"
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

        else:
            # 默认JSON格式
            json_data = json.dumps(export_data, ensure_ascii=False, indent=2)
            file_content = io.BytesIO(json_data.encode('utf-8'))
            filename = f"homework_analysis_{assignment_id}.json"
            media_type = "application/json"

        # 返回文件流
        file_content.seek(0)
        return StreamingResponse(
            io.BytesIO(file_content.read()),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

# 生成单个学生作业点评接口
@router.post("/generate-comment/{homework_id}")
async def generate_single_homework_comment(
    homework_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """生成单个学生的作业点评"""
    check_feature_enabled("student_details")

    try:
        service = HomeworkAnalysisService(db)

        # 获取作业信息
        homework = db.query(Homework).filter(Homework.id == homework_id).first()
        if not homework:
            raise HTTPException(status_code=404, detail="作业不存在")

        # 检查权限
        check_permission(current_user, homework.assignment_id, db)

        # 生成并保存作业点评
        comment = await service._generate_and_save_homework_comment(homework_id, homework.student_id)

        return {
            "success": True,
            "data": {
                "homework_id": homework_id,
                "homework_comment": comment
            }
        }
    except Exception as e:
        logger.error(f"生成作业点评失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成作业点评失败: {str(e)}")

# 重新生成作业点评接口
@router.post("/regenerate-comments/{assignment_id}")
async def regenerate_homework_comments(
    assignment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """重新生成指定作业的所有学生作业点评"""
    check_feature_enabled("student_details")
    check_permission(current_user, assignment_id, db)

    try:
        service = HomeworkAnalysisService(db)
        result = await service.regenerate_all_homework_comments(assignment_id)
        return {
            "success": True,
            "message": f"成功重新生成 {result['updated_count']} 名学生的作业点评",
            "data": result
        }
    except Exception as e:
        logger.error(f"重新生成作业点评失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重新生成作业点评失败: {str(e)}")

# 导出学生详情接口
@router.post("/export-students")
async def export_students(
    request: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """导出学生详情列表"""
    check_feature_enabled("student_details")

    try:
        assignment_id = request.get('assignment_id')
        export_format = request.get('export_format', 'excel')
        export_fields = request.get('export_fields', [])
        filters = request.get('filters', {})

        if not assignment_id:
            raise HTTPException(status_code=400, detail="缺少作业ID")

        if not export_fields:
            raise HTTPException(status_code=400, detail="请至少选择一个导出字段")

        # 检查权限
        check_permission(current_user, assignment_id, db)

        service = HomeworkAnalysisService(db)
        file_content, filename, content_type = await service.export_student_details(
            assignment_id, export_format, export_fields, filters
        )

        return Response(
            content=file_content,
            media_type=content_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        logger.error(f"导出学生详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出学生详情失败: {str(e)}")

# 健康检查接口
@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "features": FEATURE_FLAGS,
        "timestamp": datetime.now().isoformat()
    }
