from fastapi import APIRouter, Depends, HTTPException, status, Response, Form
from fastapi.security import <PERSON>Auth2P<PERSON>word<PERSON>earer, OAuth2PasswordRequestForm
from fastapi.security import OAuth2P<PERSON><PERSON><PERSON><PERSON>er, OAuth2AuthorizationCodeBearer
from sqlalchemy.orm import Session
from sqlalchemy import text
from datetime import timedelta
from jose import JWTError, jwt
from typing import Optional, Dict, Any, List
import logging

from ..database import get_db
from ..models.user import User
from ..utils.auth import verify_password, get_password_hash, create_access_token, SECRET_KEY, ALGORITHM
from ..schemas import user as user_schema
from ..models.system_settings import SystemSettings
from ..schemas import registration as registration_schema

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# 安全配置
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login", auto_error=True)
oauth2_scheme_optional = OAuth2PasswordBearer(tokenUrl="login", auto_error=False)

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效凭证",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise credentials_exception
    return user

# 添加一个可选的当前用户依赖函数，用于未登录用户也能访问的API
async def get_current_user_optional(
    token: str = Depends(oauth2_scheme_optional),
    db: Session = Depends(get_db)
):
    if not token:
        return None
    return await get_current_user(token=token, db=db)

@router.post("/user", response_model=user_schema.User)
async def register(user: user_schema.UserCreate, db: Session = Depends(get_db)):
    try:
        logger.info(f"尝试注册用户: {user.username}, email: {user.email}, is_teacher: {user.is_teacher}")
        
        # 检查注册设置
        from ..models.system_settings import SystemSettings

        # 确定用户角色 - 使用英文角色名检查设置
        role_code = "teacher" if user.is_teacher else "student"

        # 检查角色注册是否启用（包括总开关和角色开关）
        if not SystemSettings.is_role_registration_enabled(db, role_code):
            from ..models.user import normalize_role
            role_name = normalize_role(role_code)
            logger.warning(f"{role_name}注册功能已关闭")
            raise HTTPException(status_code=403, detail=f"{role_name}注册功能已关闭，请联系管理员")
        
        # 检查用户是否已存在
        db_user = db.query(User).filter(User.username == user.username).first()
        if db_user:
            logger.warning(f"用户名已存在: {user.username}")
            raise HTTPException(status_code=400, detail="用户名已被注册")
        
        # 检查邮箱是否已存在
        db_email = db.query(User).filter(User.email == user.email).first()
        if db_email:
            logger.warning(f"邮箱已存在: {user.email}")
            raise HTTPException(status_code=400, detail="邮箱已被注册")
        
        # 使用UserService创建新用户（自动同步到user_roles表）
        from ..services.user_service import UserService

        hashed_password = get_password_hash(user.password)

        # 准备额外角色信息
        additional_role_info = {
            'class_id': getattr(user, 'class_id', None)
        }

        # 转换为中文角色名用于数据库存储
        from ..models.user import normalize_role
        role_name = normalize_role(role_code)

        logger.info(f"使用UserService创建用户: {user.username}")
        db_user = UserService.create_user_with_role(
            user_data=user,
            db=db,
            hashed_password=hashed_password,
            role_name=role_name,
            additional_role_info=additional_role_info
        )
        
        # 检查角色是否需要审核
        settings = SystemSettings.get_registration_settings(db)
        role_config = settings["roles"].get(role_code, {})
        if role_config.get("requires_approval", False):
            from ..models.registration import UserRegistrationStatus
            
            # 创建注册状态记录
            reg_status = UserRegistrationStatus(
                user_id=db_user.id,
                role_name=role_name,
                status="pending"
            )
            
            # 添加额外信息
            additional_info = {
                "school_id": user.school_id,
                "class_id": user.class_id,
                "subject": user.subject
            }
            reg_status.set_additional_info(additional_info)
            
            db.add(reg_status)
            logger.info(f"创建了注册审核记录，角色: {role_name}")
        
        db.commit()
        db.refresh(db_user)
        
        return db_user
    except HTTPException:
        logger.exception("注册过程中发生HTTP异常")
        raise
    except Exception as e:
        logger.exception("注册过程中发生错误")
        raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}")

@router.post("/advanced-register", response_model=user_schema.User)
async def advanced_register(
    registration_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """高级注册API，支持多角色和审核流程"""
    try:
        logger.info(f"尝试高级注册用户: {registration_data.get('username')}, role: {registration_data.get('role_name')}")
        
        # 提取基本用户信息
        username = registration_data.get("username")
        email = registration_data.get("email")
        password = registration_data.get("password")
        full_name = registration_data.get("full_name")
        phone = registration_data.get("phone")
        role_name = registration_data.get("role_name")
        school_id = registration_data.get("school_id")
        class_id = registration_data.get("class_id")
        subject = registration_data.get("subject")
        
        # 验证必填字段
        if not username or not email or not password or not full_name or not role_name:
            raise HTTPException(status_code=400, detail="缺少必填字段")
        
        # 获取注册设置并检查角色注册是否启用
        from ..models.system_settings import SystemSettings

        # 将中文角色名转换为英文代码进行检查
        role_code_map = {
            "学生": "student",
            "教师": "teacher",
            "家长": "parent",
            "班主任": "class_teacher",
            "教研组长": "subject_leader",
            "教务处主任": "academic_director",
            "副校长": "vice_principal",
            "校长": "principal",
            "学校管理员": "school_admin"
        }
        role_code = role_code_map.get(role_name, role_name.lower())

        # 检查角色注册是否启用（包括总开关和角色开关）
        if not SystemSettings.is_role_registration_enabled(db, role_code):
            raise HTTPException(status_code=403, detail=f"角色 '{role_name}' 注册功能已关闭")

        # 获取角色配置用于后续验证
        settings = SystemSettings.get_registration_settings(db)
        role_config = settings["roles"].get(role_code, {})
        
        # 验证必填字段
        fields = role_config.get("fields", {})
        if fields.get("school", {}).get("required", False) and not school_id:
            raise HTTPException(status_code=400, detail="学校是必填项")
        
        if fields.get("class", {}).get("required", False) and not class_id:
            raise HTTPException(status_code=400, detail="班级是必填项")
        
        if fields.get("subject", {}).get("required", False) and not subject:
            raise HTTPException(status_code=400, detail="学科是必填项")
        
        # 检查用户是否已存在
        db_user = db.query(User).filter(User.username == username).first()
        if db_user:
            raise HTTPException(status_code=400, detail="用户名已被注册")
        
        db_email = db.query(User).filter(User.email == email).first()
        if db_email:
            raise HTTPException(status_code=400, detail="邮箱已被注册")
        
        # 创建新用户
        hashed_password = get_password_hash(password)
        is_teacher = role_name != "学生" and role_name != "家长"
        
        # 检查角色是否需要审核
        requires_approval = role_config.get("requires_approval", False)
        
        # 处理班级ID（支持多班级）
        primary_class_id = None
        class_ids = []

        if class_id:
            if isinstance(class_id, list):
                # 多班级情况
                class_ids = class_id
                primary_class_id = class_id[0] if class_id else None
            else:
                # 单班级情况
                class_ids = [class_id]
                primary_class_id = class_id

        # 使用UserService创建新用户（自动同步到user_roles表）
        from ..services.user_service import UserService

        # 创建临时用户数据对象
        class TempUserData:
            def __init__(self):
                self.username = username
                self.email = email
                self.password = ""  # 占位符
                self.full_name = full_name
                self.phone = phone
                self.is_teacher = is_teacher
                self.is_admin = False
                self.school_id = school_id
                self.class_id = primary_class_id
                self.subject = subject

        temp_user_data = TempUserData()

        # 准备额外角色信息
        additional_role_info = {
            'class_id': primary_class_id,
            'subject_id': None  # 如果需要，可以根据subject字符串查找subject_id
        }

        logger.info(f"使用UserService创建用户: {username}")
        new_user = UserService.create_user_with_role(
            user_data=temp_user_data,
            db=db,
            hashed_password=hashed_password,
            role_name=role_name,
            additional_role_info=additional_role_info
        )

        # 设置额外属性
        new_user.subject = subject
        new_user.is_active = not requires_approval  # 如果需要审核，则设置为非激活状态
        db.flush()  # 更新数据库

        # 如果是教师且有多个班级，创建班级关联
        if is_teacher and class_ids:
            from ..models.user import ClassTeacher
            for cid in class_ids:
                class_teacher = ClassTeacher(
                    teacher_id=new_user.id,
                    class_id=cid
                )
                db.add(class_teacher)
        
        # 处理特殊角色
        if role_name == "parent":
            # 家长需要绑定学生
            student_id = registration_data.get("student_id")
            relationship = registration_data.get("relationship")
            
            if not student_id or not relationship:
                db.rollback()
                raise HTTPException(status_code=400, detail="家长注册需要绑定学生")
            
            # 检查学生是否存在
            student = db.query(User).filter(User.id == student_id).first()
            if not student:
                db.rollback()
                raise HTTPException(status_code=404, detail="学生不存在")
            
            # 创建家长学生关联
            from ..models.user import ParentStudent
            parent_student = ParentStudent(
                parent_id=new_user.id,
                student_id=student_id,
                relationship=relationship,
                is_primary=registration_data.get("is_primary", False)
            )
            db.add(parent_student)
        
        # 如果角色需要审核，创建注册状态记录
        if requires_approval:
            from ..models.registration import UserRegistrationStatus
            
            # 提取额外信息
            additional_info = {k: v for k, v in registration_data.items() if k not in [
                "username", "email", "password", "full_name", "phone", 
                "role_name", "school_id", "class_id", "subject"
            ]}
            
            reg_status = UserRegistrationStatus(
                user_id=new_user.id,
                role_name=role_name,
                status="pending"
            )
            
            if additional_info:
                reg_status.set_additional_info(additional_info)
            
            db.add(reg_status)
            logger.info(f"创建了注册审核记录，角色: {role_name}")
            
            # 返回给用户的消息
            new_user.registration_message = "您的注册申请已提交，需要管理员审核后才能使用账号"
        else:
            # 不需要审核的角色
            new_user.registration_message = "注册成功"
        
        db.commit()
        db.refresh(new_user)
        
        logger.info(f"用户 {username} 注册成功，角色: {role_name}, 是否需要审核: {requires_approval}")
        return new_user
    except HTTPException as he:
        db.rollback()
        logger.error(f"高级注册过程中发生HTTP异常: {str(he)}")
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"高级注册过程中发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}")

@router.post("/school-application", response_model=registration_schema.SchoolApplication)
async def create_school_application(
    application: registration_schema.SchoolApplicationCreate,
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """
    创建学校申请
    """
    try:
        # 创建新的学校申请
        from ..models.registration import SchoolApplication
        
        applicant_id = current_user.id if current_user else None
        
        new_application = SchoolApplication(
            name=application.name,
            province=application.province,
            city=application.city,
            district=application.district,
            address=application.address,
            description=application.description,
            contact_name=application.contact_name,
            contact_phone=application.contact_phone,
            contact_email=application.contact_email,
            applicant_id=applicant_id,
            status="pending"
        )
        
        db.add(new_application)
        db.commit()
        db.refresh(new_application)
        
        return new_application
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建学校申请失败: {str(e)}")

@router.post("/parent-student-binding", response_model=registration_schema.ParentStudentVerification)
async def create_parent_student_binding(
    binding: registration_schema.ParentStudentVerificationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建家长学生绑定验证"""
    try:
        # 检查是否为家长角色
        if current_user.role != "parent":
            raise HTTPException(status_code=403, detail="只有家长角色可以绑定学生")
        
        # 检查学生是否存在
        student = db.query(User).filter(User.id == binding.student_id).first()
        if not student:
            raise HTTPException(status_code=404, detail="学生不存在")
        
        # 检查是否已绑定
        from ..models.user import ParentStudent
        existing_binding = db.query(ParentStudent).filter(
            ParentStudent.parent_id == current_user.id,
            ParentStudent.student_id == binding.student_id
        ).first()
        
        if existing_binding:
            raise HTTPException(status_code=400, detail="已经绑定了该学生")
        
        # 获取验证设置
        from ..models.system_settings import SystemSettings
        settings = SystemSettings.get_registration_settings(db)
        verification_settings = settings.get("student_binding_verification", {})
        expiry_minutes = verification_settings.get("code_expiry_minutes", 30)
        
        # 创建验证记录
        from ..models.registration import ParentStudentVerification
        verification = ParentStudentVerification.generate_verification(
            parent_id=current_user.id,
            student_id=binding.student_id,
            relationship_type=binding.relationship,
            is_primary=binding.is_primary,
            expiry_minutes=expiry_minutes
        )
        
        db.add(verification)
        db.commit()
        db.refresh(verification)
        
        # TODO: 发送验证码给学生或学校管理员
        
        logger.info(f"创建了家长学生绑定验证: 家长ID={current_user.id}, 学生ID={binding.student_id}")
        return verification
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建家长学生绑定验证失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建家长学生绑定验证失败: {str(e)}")

@router.post("/verify-binding-code", response_model=registration_schema.ParentStudentVerification)
async def verify_binding_code(
    verification_data: registration_schema.ParentStudentVerificationVerify,
    db: Session = Depends(get_db)
):
    """验证家长学生绑定验证码"""
    try:
        from ..models.registration import ParentStudentVerification
        
        # 获取验证记录
        verification = db.query(ParentStudentVerification).filter(
            ParentStudentVerification.id == verification_data.verification_id
        ).first()
        
        if not verification:
            raise HTTPException(status_code=404, detail="验证记录不存在")
        
        # 验证验证码
        success, message = verification.verify(verification_data.code)
        
        if not success:
            raise HTTPException(status_code=400, detail=message)
        
        # 创建家长学生关联
        from ..models.user import ParentStudent
        parent_student = ParentStudent(
            parent_id=verification.parent_id,
            student_id=verification.student_id,
            relationship=verification.relationship,
            is_primary=verification.is_primary
        )

        db.add(parent_student)
        db.commit()
        db.refresh(verification)
        
        logger.info(f"家长学生绑定验证成功: 家长ID={verification.parent_id}, 学生ID={verification.student_id}")
        return verification
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"验证家长学生绑定验证码失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"验证家长学生绑定验证码失败: {str(e)}")

@router.post("/login", response_model=user_schema.Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    try:
        # 查找用户 - 支持用户名、手机号或邮箱登录
        user = db.query(User).filter(
            (User.username == form_data.username) |
            (User.phone == form_data.username) |
            (User.email == form_data.username)
        ).first()

        # 区分用户不存在和密码错误的情况
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在，请检查用户名/手机号/邮箱是否正确",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 检查用户是否被禁用
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="账户已被禁用，请联系管理员",
                headers={"WWW-Authenticate": "Bearer"},
            )

        if not verify_password(form_data.password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="密码错误，请检查密码是否正确",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 生成访问令牌
        access_token_expires = timedelta(minutes=30)
        access_token = create_access_token(
            data={"sub": user.username}, expires_delta=access_token_expires
        )
        
        # 获取学校名称
        school_name = None
        if user.school_id:
            try:
                # 使用原生SQL查询学校名称
                result = db.execute(text("SELECT name FROM schools WHERE id = :id"), {"id": user.school_id})
                row = result.fetchone()
                if row:
                    school_name = row[0]
            except Exception as e:
                logger.error(f"获取学校名称失败: {str(e)}")
        
        # 安全地获取用户属性
        is_teacher = False
        if hasattr(user, 'is_teacher'):
            is_teacher = user.is_teacher

        # 使用角色服务获取正确的中文角色名称
        from ..services.role_service import RoleService
        role = RoleService.get_user_primary_role_name(user, db)
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user_id": user.id,
            "username": user.username,
            "is_teacher": is_teacher,
            "is_admin": user.is_admin,
            "role": role,
            "school_id": user.school_id,
            "school_name": school_name,
            "full_name": user.full_name,
            "email": user.email,
            "phone": user.phone,
            "subject": user.subject,  # 用户科目字段
            "created_at": user.created_at.isoformat() if user.created_at else None
        }
    except HTTPException:
        # 重新抛出HTTP异常，保持原有的错误信息
        raise
    except Exception as e:
        logger.error(f"登录过程中出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/me", response_model=user_schema.User)
async def read_users_me(
    response: Response,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        # 设置缓存控制头，确保数据不被缓存
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"

        # 获取学校名称
        school_name = None
        if current_user.school_id:
            try:
                # 使用原生SQL查询学校名称
                result = db.execute(text("SELECT name FROM schools WHERE id = :id"), {"id": current_user.school_id})
                row = result.fetchone()
                if row:
                    school_name = row[0]
            except Exception as e:
                logger.error(f"获取学校名称失败: {str(e)}")

        # 获取任教科目（除了学生和家长外，其他角色都可能是教师）
        teaching_subjects = []
        # 检查是否为可能具有教师身份的角色
        non_teacher_roles = ['学生', 'student', '家长', 'parent']
        if current_user.is_teacher or (current_user.role and current_user.role not in non_teacher_roles):
            try:
                # 从user_roles表中获取用户的任教科目
                result = db.execute(text("""
                    SELECT DISTINCT s.name
                    FROM user_roles ur
                    JOIN subjects s ON ur.subject_id = s.id
                    WHERE ur.user_id = :user_id AND ur.subject_id > 0
                """), {"user_id": current_user.id})
                subjects = result.fetchall()
                teaching_subjects = [row[0] for row in subjects]

                if teaching_subjects:
                    logger.info(f"用户 {current_user.username} 的任教科目: {teaching_subjects}")
            except Exception as e:
                logger.error(f"获取任教科目失败: {str(e)}")

        # 获取任教科目（如果是教师）
        teaching_subjects = []
        if current_user.is_teacher:
            try:
                # 从user_roles表中获取用户的任教科目
                result = db.execute(text("""
                    SELECT DISTINCT s.name
                    FROM user_roles ur
                    JOIN subjects s ON ur.subject_id = s.id
                    WHERE ur.user_id = :user_id AND ur.subject_id > 0
                """), {"user_id": current_user.id})
                subjects = result.fetchall()
                teaching_subjects = [row[0] for row in subjects]
            except Exception as e:
                logger.error(f"获取任教科目失败: {str(e)}")

        # 使用角色服务获取正确的中文角色名称
        from ..services.role_service import RoleService
        user_role = RoleService.get_user_primary_role_name(current_user, db)

        # 构建用户响应数据
        user_data = {
            "id": current_user.id,
            "username": current_user.username,
            "email": current_user.email,
            "full_name": current_user.full_name,
            "phone": current_user.phone,
            "is_active": current_user.is_active,
            "is_admin": current_user.is_admin,
            "is_teacher": current_user.is_teacher,
            "role": user_role,  # 使用推断后的角色
            "school_id": current_user.school_id,
            "school_name": school_name,
            "teaching_subjects": teaching_subjects,
            "created_at": current_user.created_at
        }

        return user_data
    except Exception as e:
        logger.error(f"获取用户信息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/user/school-applications", response_model=List[registration_schema.SchoolApplication])
async def get_user_school_applications(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取当前用户的学校申请列表
    """
    try:
        from ..models.registration import SchoolApplication
        
        applications = db.query(SchoolApplication).filter(
            SchoolApplication.applicant_id == current_user.id
        ).order_by(SchoolApplication.created_at.desc()).all()
        
        return applications
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取学校申请列表失败: {str(e)}")

@router.post("/login-with-school", response_model=user_schema.Token)
async def login_with_school(
    school_id: int = Form(...),
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    """带学校选择的登录API"""
    try:
        from ..models.school import School

        # 在指定学校范围内查找用户
        user = db.query(User).filter(
            User.school_id == school_id,
            User.username == username,
            User.is_active == True
        ).first()

        # 区分用户不存在和密码错误的情况
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="该学校中不存在此用户名，请检查学校和用户名是否正确",
                headers={"WWW-Authenticate": "Bearer"},
            )

        if not verify_password(password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="密码错误，请检查密码是否正确",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 生成访问令牌
        access_token_expires = timedelta(minutes=30)
        access_token = create_access_token(
            data={"sub": user.username, "school_id": school_id},
            expires_delta=access_token_expires
        )

        # 获取学校信息
        school = db.query(School).filter(School.id == school_id).first()
        school_name = school.name if school else "未知学校"

        # 构建返回数据
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user_id": user.id,
            "username": user.username,
            "full_name": user.full_name,
            "email": user.email,
            "is_admin": user.is_admin,
            "is_teacher": getattr(user, 'is_teacher', False),
            "role": getattr(user, 'role', None),
            "school_id": school_id,
            "school_name": school_name,
            "expires_in": 1800  # 30分钟
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"登录失败: {str(e)}")

