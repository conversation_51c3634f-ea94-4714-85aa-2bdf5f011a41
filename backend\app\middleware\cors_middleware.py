from fastapi import Request
from fastapi.responses import Response
from starlette.middleware.base import BaseHTTPMiddleware
import logging

logger = logging.getLogger(__name__)

class CORSMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 记录请求信息
        logger.info(f"处理请求: {request.method} {request.url.path}")
        
        # 如果是OPTIONS请求，直接返回带有CORS头的响应
        if request.method == "OPTIONS":
            logger.info(f"处理OPTIONS预检请求: {request.url.path}")
            response = Response(status_code=200)
            self._set_cors_headers(response)
            return response
        
        # 处理正常请求
        try:
            response = await call_next(request)
            
            # 为所有响应添加CORS头
            self._set_cors_headers(response)
            
            return response
        except Exception as e:
            logger.error(f"处理请求时出错: {str(e)}")
            response = Response(status_code=500, content=str(e))
            self._set_cors_headers(response)
            return response
    
    def _set_cors_headers(self, response: Response):
        # 设置CORS头，但保留现有的缓存控制头
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD"
        response.headers["Access-Control-Allow-Headers"] = "Authorization, Content-Type, Accept, X-Requested-With, Origin"

        # 保留缓存控制头（如果已设置）
        # 不覆盖Cache-Control, Pragma, Expires等缓存相关头
        logger.info(f"设置CORS头: {response.headers.get('Access-Control-Allow-Origin')}")
        return response