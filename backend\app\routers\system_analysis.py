from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional
import logging

from ..database import get_db
from ..models.user import User
from .auth import get_current_user

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/homework-assignments")
async def get_system_homework_assignments(
    class_id: Optional[int] = None,
    school_id: Optional[int] = None,
    subject_id: Optional[int] = None,
    grade: Optional[str] = None,
    province_id: Optional[int] = None,
    city_id: Optional[int] = None,
    district_id: Optional[int] = None,
    page: int = 1,
    limit: int = 500,
    search: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取系统级作业任务列表，基于管理员角色权限控制"""
    # 应用统一的权限控制（管理员身份）
    if not (current_user.is_admin or current_user.is_teacher):
        raise HTTPException(status_code=403, detail="没有权限查看系统作业任务列表")

    try:
        logger.info(f"系统作业分析API被调用，用户: {current_user.username}, 筛选条件: class_id={class_id}, school_id={school_id}, subject_id={subject_id}")

        # 构建动态WHERE条件 - 移除基础条件，直接构建实际条件
        where_conditions = []
        params = {}

        # 应用筛选条件 - 修改为homeworks表的字段
        if class_id:
            where_conditions.append("h.class_id = :class_id")
            params["class_id"] = class_id

        if school_id:
            where_conditions.append("h.school_id = :school_id")
            params["school_id"] = school_id

        if subject_id:
            where_conditions.append("h.subject_id = :subject_id")
            params["subject_id"] = subject_id

        # 添加年级筛选条件
        if grade:
            where_conditions.append("c.grade = :grade")
            params["grade"] = grade

        # 添加地区筛选条件 - 通过地区表查找对应的名称
        if province_id:
            # 先查找省份名称
            province_result = db.execute(text("SELECT name FROM provinces WHERE id = :province_id"), {"province_id": province_id}).fetchone()
            if province_result:
                province_name = province_result[0]
                where_conditions.append("s.province = :province_name")
                params["province_name"] = province_name

        if city_id:
            # 先查找城市名称
            city_result = db.execute(text("SELECT name FROM cities WHERE id = :city_id"), {"city_id": city_id}).fetchone()
            if city_result:
                city_name = city_result[0]
                where_conditions.append("s.city = :city_name")
                params["city_name"] = city_name

        if district_id:
            # 先查找区县名称
            district_result = db.execute(text("SELECT name FROM districts WHERE id = :district_id"), {"district_id": district_id}).fetchone()
            if district_result:
                district_name = district_result[0]
                where_conditions.append("s.district = :district_name")
                params["district_name"] = district_name

        # 添加搜索过滤
        if search:
            where_conditions.append("h.title LIKE :search")
            params["search"] = f"%{search}%"

        # 添加日期过滤
        if start_date and end_date:
            try:
                from datetime import datetime, timedelta
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
                where_conditions.append("h.created_at >= :start_date AND h.created_at < :end_date")
                params["start_date"] = start_date_obj
                params["end_date"] = end_date_obj
            except ValueError:
                logger.warning(f"日期格式错误: start_date={start_date}, end_date={end_date}")

        # 构建WHERE子句
        if where_conditions:
            where_clause = " AND ".join(where_conditions)
        else:
            where_clause = "1=1"  # 如果没有筛选条件，显示所有记录

        # 构建完整的SQL查询 - 支持地区和年级筛选
        if province_id or city_id or district_id or grade:
            # 需要JOIN学校表和班级表来进行筛选
            sql_query = f"""
            SELECT
                h.id, h.title, h.description, h.class_id, h.student_id,
                h.created_at, h.status, h.school_id, h.subject_id, h.assignment_id,
                h.score, h.accuracy, h.graded_at
            FROM
                homeworks h
            LEFT JOIN schools s ON h.school_id = s.id
            LEFT JOIN classes c ON h.class_id = c.id
            WHERE {where_clause}
            ORDER BY h.created_at DESC
            LIMIT :limit OFFSET :offset
            """
        else:
            # 简化查询，避免JOIN导致数据丢失
            sql_query = f"""
            SELECT
                h.id, h.title, h.description, h.class_id, h.student_id,
                h.created_at, h.status, h.school_id, h.subject_id, h.assignment_id,
                h.score, h.accuracy, h.graded_at
            FROM
                homeworks h
            WHERE {where_clause}
            ORDER BY h.created_at DESC
            LIMIT :limit OFFSET :offset
            """

        params["limit"] = limit
        params["offset"] = (page - 1) * limit

        logger.info(f"执行SQL查询: {sql_query}")
        logger.info(f"查询参数: {params}")

        # 先执行计数查询，检查总数
        if province_id or city_id or district_id or grade:
            # 需要JOIN学校表和班级表来进行筛选
            count_query = f"""
            SELECT COUNT(*) as total
            FROM homeworks h
            LEFT JOIN schools s ON h.school_id = s.id
            LEFT JOIN classes c ON h.class_id = c.id
            WHERE {where_clause}
            """
        else:
            count_query = f"""
            SELECT COUNT(*) as total
            FROM homeworks h
            WHERE {where_clause}
            """
        count_result = db.execute(text(count_query), {k: v for k, v in params.items() if k not in ['limit', 'offset']})
        total_count = count_result.fetchone().total
        logger.info(f"数据库中符合条件的作业总数: {total_count}")

        # 执行查询
        result = db.execute(text(sql_query), params)
        assignments = result.fetchall()

        # 转换为字典列表 - 简化版本，直接使用homeworks表数据
        results = []
        for row in assignments:
            # 根据subject_id获取科目名称
            subject_name = "未指定科目"
            if row.subject_id == 8:
                subject_name = "初中英语"
            elif row.subject_id == 1:
                subject_name = "初中数学"
            elif row.subject_id == 2:
                subject_name = "初中语文"
            # 可以根据需要添加更多科目映射

            # 根据school_id获取学校名称
            school_name = "未知学校"
            if row.school_id == 1:
                school_name = "四川省双流中学"
            elif row.school_id is None:
                school_name = "四川省双流中学"  # 大部分NULL的也是双流中学

            result_dict = {
                "id": row.id,
                "title": row.title,
                "description": row.description,
                "class_id": row.class_id,
                "student_id": row.student_id,
                "assignment_id": row.assignment_id,
                "created_at": str(row.created_at) if row.created_at else None,
                "status": row.status or "submitted",
                "school_id": row.school_id,
                "subject_id": row.subject_id,
                "score": row.score,
                "accuracy": row.accuracy,
                "graded_at": str(row.graded_at) if row.graded_at else None,
                "class_name": f"班级{row.class_id}" if row.class_id else "未分配班级",
                "student_name": f"学生{row.student_id}" if row.student_id else "未知学生",
                "teacher_name": "未知教师",

                "school_name": school_name,
                "assignment_title": row.title or "无作业任务"
            }
            results.append(result_dict)

        logger.info(f"返回 {len(results)} 个作业分配")
        return results
        
    except Exception as e:
        logger.error(f"获取系统作业任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统作业任务列表失败: {str(e)}")

@router.get("/test")
async def test_system_analysis():
    """测试系统分析路由是否正常"""
    return {"message": "系统分析路由正常工作", "timestamp": "2025-01-03"}
