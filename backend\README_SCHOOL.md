# 学校管理功能使用指南

## 概述
学校管理功能是智教云端智能辅导平台的重要扩展，支持多学校部署和管理，满足教育局和集团学校的需求。本文档介绍如何使用和配置学校管理功能。

## 功能特点
- 多学校部署支持
- 基于学校的用户和班级管理
- 学校级统计数据
- 基于角色的权限管理体系
- 与现有系统无缝集成

## 数据库迁移
在使用学校管理功能前，需要先执行数据库迁移脚本，添加学校表和相关字段：

```bash
cd backend
python migrate_db.py
```

迁移脚本将：
1. 创建学校表（schools）
2. 为用户表（users）添加school_id和role字段
3. 为班级表（classes）添加school_id和grade字段
4. 创建默认学校并关联现有数据
5. 基于现有权限分配用户角色

## 权限体系
学校管理功能引入了更细粒度的基于角色的权限体系，同时保持与现有权限系统的兼容性：

### 系统级角色
- **super_admin**：系统超级管理员，可管理所有学校
- **admin**：系统管理员，可管理系统配置

### 学校级角色
- **principal**：校长，可管理单个学校的所有数据
- **academic_director**：教务主任，可管理学校的教学相关数据

### 教学角色
- **grade_director**：年级主任，可管理特定年级的数据
- **subject_leader**：学科组长，可管理特定学科的数据
- **class_teacher**：班主任，可管理特定班级的数据
- **teacher**：教师，可管理自己的教学数据

### 学生角色
- **student**：学生，可访问自己的学习数据
- **parent**：家长，可查看关联学生的学习数据

## API接口

### 学校管理接口
- `GET /api/schools`：获取学校列表
- `GET /api/schools/{school_id}`：获取学校详情
- `POST /api/schools`：创建学校
- `PUT /api/schools/{school_id}`：更新学校
- `DELETE /api/schools/{school_id}`：删除学校

### 用户管理接口扩展
- `GET /api/users?school_id={school_id}`：获取指定学校的用户
- `POST /api/users`：创建用户时可指定school_id和role
- `PUT /api/users/{user_id}`：更新用户时可修改school_id和role

### 班级管理接口扩展
- `GET /api/classes?school_id={school_id}`：获取指定学校的班级
- `POST /api/classes`：创建班级时可指定school_id和grade
- `PUT /api/classes/{class_id}`：更新班级时可修改school_id和grade

## 前端使用
1. 管理员可通过管理员仪表盘访问学校管理功能
2. 点击"学校管理"卡片进入学校管理页面
3. 在学校管理页面可以创建、编辑、删除学校
4. 创建或编辑用户和班级时可以选择关联的学校

## 数据隔离
学校管理功能实现了基于学校的数据隔离：
1. 每个学校的数据相互独立
2. 用户只能访问其所属学校的数据
3. 超级管理员可以访问所有学校的数据

## 注意事项
1. 删除学校前需确保该学校下没有关联的班级和用户
2. 用户的角色和所属学校决定了其可访问的数据范围
3. 现有系统中的管理员默认被分配为超级管理员角色
4. 现有系统中的教师默认被分配为教师角色
5. 现有系统中的学生默认被分配为学生角色

## 故障排除
如果遇到问题，请尝试以下步骤：
1. 检查数据库迁移是否成功完成
2. 确认用户角色和学校关联是否正确
3. 查看应用日志中的错误信息
4. 如果数据库迁移失败，可以使用自动创建的备份文件恢复数据库 