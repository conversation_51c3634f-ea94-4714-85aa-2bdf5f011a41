{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys\\\\frontend\\\\src\\\\components\\\\AppLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation, Link } from 'react-router-dom';\nimport { Layout, Menu, Dropdown, Avatar, Button, Space, Typography, Divider, Drawer } from 'antd';\nimport { UserOutlined, HomeOutlined, BookOutlined, SettingOutlined, LogoutOutlined, BarChartOutlined, TeamOutlined, ExperimentOutlined, MessageOutlined, FileTextOutlined, GlobalOutlined, LineChartOutlined, CameraOutlined, MenuFoldOutlined, MenuUnfoldOutlined, UsergroupAddOutlined, BankOutlined, DatabaseOutlined, DashboardOutlined, CloudUploadOutlined, EnvironmentOutlined } from '@ant-design/icons';\nimport { useAuth } from '../utils/auth';\nimport { getUserRoleTags, isSuperAdmin, hasRoleLevel } from '../utils/roleUtils';\nimport AIAssistant from './AIAssistant';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Content,\n  Sider\n} = Layout;\nconst {\n  Title\n} = Typography;\nconst AppLayout = ({\n  children,\n  user,\n  onLogout,\n  pageTitle\n}) => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const [aiDrawerVisible, setAiDrawerVisible] = useState(false);\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  // 监听窗口大小变化\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth <= 768;\n      setIsMobile(mobile);\n      if (mobile) {\n        setCollapsed(true);\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    handleResize(); // 初始化\n\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  const handleLogout = () => {\n    if (onLogout) {\n      onLogout();\n    }\n    navigate('/login');\n  };\n  const showAIAssistant = () => {\n    setAiDrawerVisible(true);\n  };\n  const closeAIAssistant = () => {\n    setAiDrawerVisible(false);\n  };\n  const items = [{\n    label: '个人中心',\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate('/profile')\n  },\n  // 只有教师和管理员才显示统计数据\n  ...(user && (user.is_teacher || user.is_admin) ? [{\n    label: '统计数据',\n    key: 'statistics',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this),\n    onClick: () => navigate('/profile/statistics')\n  }] : []), {\n    label: '退出登录',\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this),\n    onClick: handleLogout\n  }];\n\n  // 根据用户类型生成不同的菜单\n  const menuItems = (user === null || user === void 0 ? void 0 : user.role) === 'parent' || (user === null || user === void 0 ? void 0 : user.role) === '家长' ? [\n  // 家长专用菜单\n  {\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 17\n    }, this),\n    label: '首页',\n    onClick: () => navigate('/')\n  }, {\n    key: '/parent',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 17\n    }, this),\n    label: '我的孩子',\n    onClick: () => navigate('/parent')\n  }, {\n    key: '/parent/homework',\n    icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 17\n    }, this),\n    label: '作业详情',\n    onClick: async () => {\n      try {\n        // 获取绑定的学生列表\n        const response = await api.get('/parent/bound-students');\n        const students = response.data || [];\n        if (students.length === 1) {\n          // 只有一个孩子，直接跳转到该孩子的作业详情\n          const student = students[0];\n          navigate(`/parent/student/${student.student_id}/homework`, {\n            state: {\n              studentName: student.student_name,\n              studentId: student.student_id\n            }\n          });\n        } else {\n          // 多个孩子或没有孩子，跳转到孩子选择页面\n          navigate('/parent');\n        }\n      } catch (error) {\n        console.error('获取学生列表失败:', error);\n        // 出错时跳转到孩子选择页面\n        navigate('/parent');\n      }\n    }\n  }, {\n    key: '/parent/report',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 17\n    }, this),\n    label: '学习报告',\n    onClick: async () => {\n      try {\n        // 获取绑定的学生列表\n        const response = await api.get('/parent/bound-students');\n        const students = response.data || [];\n        if (students.length === 1) {\n          // 只有一个孩子，直接跳转到该孩子的学习报告\n          const student = students[0];\n          navigate(`/parent/student/${student.student_id}/report`, {\n            state: {\n              studentName: student.student_name,\n              studentId: student.student_id\n            }\n          });\n        } else {\n          // 多个孩子或没有孩子，跳转到孩子选择页面\n          navigate('/parent');\n        }\n      } catch (error) {\n        console.error('获取学生列表失败:', error);\n        // 出错时跳转到孩子选择页面\n        navigate('/parent');\n      }\n    }\n  }] : user && (user.is_teacher || user.is_admin) ? [\n  // 教师和管理员菜单\n  {\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 17\n    }, this),\n    label: '首页',\n    onClick: () => navigate('/')\n  }, {\n    key: '/homework',\n    icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 17\n    }, this),\n    label: '作业管理',\n    onClick: () => navigate('/homework')\n  }, {\n    key: '/training',\n    icon: /*#__PURE__*/_jsxDEV(ExperimentOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 17\n    }, this),\n    label: '错题训练',\n    onClick: () => navigate('/training')\n  }, {\n    key: '/statistics',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 17\n    }, this),\n    label: '统计报表',\n    onClick: () => navigate('/statistics')\n  }] : [\n  // 学生菜单\n  {\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 17\n    }, this),\n    label: '首页',\n    onClick: () => navigate('/')\n  }, {\n    key: '/homework',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 17\n    }, this),\n    label: '作业任务',\n    onClick: () => navigate('/homework')\n  }, {\n    key: '/homework/submit',\n    icon: /*#__PURE__*/_jsxDEV(CloudUploadOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 17\n    }, this),\n    label: '提交作业',\n    onClick: () => navigate('/homework/submit')\n  }, {\n    key: '/homework/review',\n    icon: /*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 17\n    }, this),\n    label: '作业点评',\n    onClick: () => navigate('/homework/review')\n  }, {\n    key: '/homework/history',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 17\n    }, this),\n    label: '往日作业',\n    onClick: () => navigate('/homework/history')\n  }, {\n    key: '/photo-solve',\n    icon: /*#__PURE__*/_jsxDEV(CameraOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 17\n    }, this),\n    label: '拍照解题',\n    onClick: () => navigate('/photo-solve')\n  }, {\n    key: '/training',\n    icon: /*#__PURE__*/_jsxDEV(ExperimentOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 17\n    }, this),\n    label: '错题训练',\n    onClick: () => navigate('/training')\n  }];\n\n  // 如果是教师或管理员，添加班级管理和作业分析菜单\n  if (user && (user.is_teacher || user.is_admin)) {\n    menuItems.push({\n      key: '/class-management',\n      icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 15\n      }, this),\n      label: '班级信息',\n      onClick: () => navigate('/class-management')\n    }, {\n      key: '/homework-analysis',\n      icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 15\n      }, this),\n      label: '作业分析',\n      onClick: () => navigate('/homework-analysis')\n    });\n  }\n\n  // 系统级菜单：班主任及以上角色可以看到（级别40以上）\n  if (user && hasRoleLevel(user, 40)) {\n    // 系统作业管理\n    menuItems.push({\n      key: '/system-homework',\n      icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 13\n      }, this),\n      label: '系统作业管理',\n      onClick: () => navigate('/system-homework')\n    });\n\n    // 系统错题训练\n    menuItems.push({\n      key: '/system-training',\n      icon: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 13\n      }, this),\n      label: '系统错题训练',\n      onClick: () => navigate('/system-training')\n    });\n\n    // 系统统计报表\n    menuItems.push({\n      key: '/system-statistics',\n      icon: /*#__PURE__*/_jsxDEV(LineChartOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 13\n      }, this),\n      label: '系统统计报表',\n      onClick: () => navigate('/system-statistics')\n    });\n\n    // 系统作业分析\n    menuItems.push({\n      key: '/system-homework-analysis',\n      icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 13\n      }, this),\n      label: '系统作业分析',\n      onClick: () => navigate('/system-homework-analysis')\n    });\n\n    // 系统班级管理\n    menuItems.push({\n      key: '/system-class',\n      icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 13\n      }, this),\n      label: '系统班级管理',\n      onClick: () => navigate('/system-class')\n    });\n\n    // 系统用户管理\n    menuItems.push({\n      key: '/system-user',\n      icon: /*#__PURE__*/_jsxDEV(UsergroupAddOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 13\n      }, this),\n      label: '系统用户管理',\n      onClick: () => navigate('/system-user')\n    });\n\n    // 系统学校管理\n    menuItems.push({\n      key: '/schools',\n      icon: /*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 13\n      }, this),\n      label: '系统学校管理',\n      onClick: () => navigate('/schools')\n    });\n\n    // 地区管理（只有超级管理员能看到）\n    if (isSuperAdmin(user)) {\n      menuItems.push({\n        key: '/region-management',\n        icon: /*#__PURE__*/_jsxDEV(EnvironmentOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 15\n        }, this),\n        label: '地区管理',\n        onClick: () => navigate('/region-management')\n      });\n    }\n\n    // 数据库管理（只有超级管理员能看到）\n    if (isSuperAdmin(user)) {\n      menuItems.push({\n        key: '/database-management',\n        icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 15\n        }, this),\n        label: '数据库管理',\n        onClick: () => navigate('/database-management')\n      });\n    }\n  }\n\n  // 超级管理员专有菜单（只有超级管理员能看到的特殊功能）\n  if (user && isSuperAdmin(user)) {\n    // 系统管理（超级管理员专有）\n    menuItems.push({\n      key: '/admin',\n      icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 13\n      }, this),\n      label: '系统管理',\n      onClick: () => navigate('/admin')\n    });\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      className: \"app-header\",\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 24px',\n        background: '#fff',\n        boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo\",\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            textDecoration: 'none'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/logo192.png\",\n            alt: \"\\u667A\\u6559\\u4E91\\u7AEF\",\n            style: {\n              height: '32px',\n              marginRight: '10px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0,\n              color: '#1890ff'\n            },\n            children: \"\\u667A\\u6559\\u4E91\\u7AEF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [isMobile && /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 33\n          }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 58\n          }, this),\n          onClick: () => setCollapsed(!collapsed),\n          style: {\n            marginRight: '16px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 19\n          }, this),\n          onClick: showAIAssistant,\n          style: {\n            marginRight: '16px'\n          },\n          children: isMobile ? '' : 'AI助手'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          menu: {\n            items\n          },\n          placement: \"bottomRight\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              style: {\n                backgroundColor: '#1890ff'\n              },\n              icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'flex-start',\n                lineHeight: '1.2',\n                minWidth: '80px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  marginBottom: '2px',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap',\n                  maxWidth: '120px'\n                },\n                children: (user === null || user === void 0 ? void 0 : user.full_name) || (user === null || user === void 0 ? void 0 : user.username) || '用户'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '11px',\n                  color: '#666',\n                  lineHeight: '1.1'\n                },\n                children: getUserRoleTags(user)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      children: [isMobile ? /*#__PURE__*/_jsxDEV(Drawer, {\n        title: \"\\u83DC\\u5355\",\n        placement: \"left\",\n        onClose: () => setCollapsed(true),\n        open: !collapsed,\n        bodyStyle: {\n          padding: 0\n        },\n        width: 250,\n        children: /*#__PURE__*/_jsxDEV(Menu, {\n          theme: \"dark\",\n          mode: \"inline\",\n          selectedKeys: [location.pathname],\n          items: menuItems,\n          style: {\n            height: '100%',\n            borderRight: 0\n          },\n          onClick: info => {\n            // 关闭抽屉\n            setCollapsed(true);\n\n            // 递归查找菜单项（支持嵌套菜单）\n            const findMenuItem = (items, key) => {\n              for (const item of items) {\n                if (item.key === key) {\n                  return item;\n                }\n                if (item.children) {\n                  const found = findMenuItem(item.children, key);\n                  if (found) return found;\n                }\n              }\n              return null;\n            };\n\n            // 执行菜单项的点击处理\n            const menuItem = findMenuItem(menuItems, info.key);\n            if (menuItem && menuItem.onClick) {\n              menuItem.onClick();\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Sider, {\n        collapsible: true,\n        collapsed: collapsed,\n        onCollapse: value => setCollapsed(value),\n        width: 200,\n        style: {\n          background: '#fff'\n        },\n        children: /*#__PURE__*/_jsxDEV(Menu, {\n          mode: \"inline\",\n          selectedKeys: [location.pathname],\n          style: {\n            height: '100%',\n            borderRight: 0\n          },\n          items: menuItems\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Layout, {\n        style: {\n          padding: '0 24px 24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '16px 0',\n            padding: '16px',\n            background: '#fff'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 3,\n            children: pageTitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            style: {\n              margin: '12px 0'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Content, {\n            style: {\n              padding: '12px 0',\n              minHeight: 'calc(100vh - 200px)'\n            },\n            children: children\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      title: \"AI\\u667A\\u80FD\\u52A9\\u624B\",\n      placement: \"right\",\n      onClose: closeAIAssistant,\n      open: aiDrawerVisible,\n      width: 400,\n      children: /*#__PURE__*/_jsxDEV(AIAssistant, {\n        user: user\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 346,\n    columnNumber: 5\n  }, this);\n};\n_s(AppLayout, \"owQbC90f3WW+rKFjP3zBlBOKcbY=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = AppLayout;\nexport default AppLayout;\nvar _c;\n$RefreshReg$(_c, \"AppLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "Link", "Layout", "<PERSON><PERSON>", "Dropdown", "Avatar", "<PERSON><PERSON>", "Space", "Typography", "Divider", "Drawer", "UserOutlined", "HomeOutlined", "BookOutlined", "SettingOutlined", "LogoutOutlined", "BarChartOutlined", "TeamOutlined", "ExperimentOutlined", "MessageOutlined", "FileTextOutlined", "GlobalOutlined", "LineChartOutlined", "CameraOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "UsergroupAddOutlined", "BankOutlined", "DatabaseOutlined", "DashboardOutlined", "CloudUploadOutlined", "EnvironmentOutlined", "useAuth", "getUserRoleTags", "isSuperAdmin", "hasRoleLevel", "AIAssistant", "jsxDEV", "_jsxDEV", "Header", "Content", "<PERSON><PERSON>", "Title", "AppLayout", "children", "user", "onLogout", "pageTitle", "_s", "collapsed", "setCollapsed", "aiDrawerVisible", "setAiDrawerVisible", "isMobile", "setIsMobile", "window", "innerWidth", "location", "navigate", "handleResize", "mobile", "addEventListener", "removeEventListener", "handleLogout", "showAIAssistant", "closeAIAssistant", "items", "label", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "is_teacher", "is_admin", "menuItems", "role", "response", "api", "get", "students", "data", "length", "student", "student_id", "state", "studentName", "student_name", "studentId", "error", "console", "push", "style", "minHeight", "className", "display", "alignItems", "justifyContent", "padding", "background", "boxShadow", "to", "textDecoration", "src", "alt", "height", "marginRight", "level", "margin", "color", "type", "menu", "placement", "backgroundColor", "flexDirection", "lineHeight", "min<PERSON><PERSON><PERSON>", "fontSize", "fontWeight", "marginBottom", "overflow", "textOverflow", "whiteSpace", "max<PERSON><PERSON><PERSON>", "full_name", "username", "title", "onClose", "open", "bodyStyle", "width", "theme", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "borderRight", "info", "findMenuItem", "item", "found", "menuItem", "collapsible", "onCollapse", "value", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys/frontend/src/components/AppLayout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate, useLocation, Link } from 'react-router-dom';\r\nimport {\r\n  Layout, Menu, Dropdown, Avatar, Button, Space,\r\n  Typography, Divider, Drawer\r\n} from 'antd';\r\nimport {\r\n  UserOutlined, HomeOutlined, BookOutlined, SettingOutlined,\r\n  LogoutOutlined, Bar<PERSON>hartOutlined, TeamOutlined, ExperimentOutlined,\r\n  MessageOutlined, FileTextOutlined, GlobalOutlined, LineChartOutlined,\r\n  CameraOutlined, MenuFoldOutlined, MenuUnfoldOutlined,\r\n  UsergroupAddOutlined, BankOutlined, DatabaseOutlined, DashboardOutlined,\r\n  CloudUploadOutlined, EnvironmentOutlined\r\n} from '@ant-design/icons';\r\nimport { useAuth } from '../utils/auth';\r\nimport { getUserRoleTags, isSuperAdmin, hasRoleLevel } from '../utils/roleUtils';\r\nimport AIAssistant from './AIAssistant';\r\n\r\nconst { Header, Content, Sider } = Layout;\r\nconst { Title } = Typography;\r\n\r\nconst AppLayout = ({ children, user, onLogout, pageTitle }) => {\r\n  const [collapsed, setCollapsed] = useState(false);\r\n  const [aiDrawerVisible, setAiDrawerVisible] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\r\n\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n\r\n\r\n\r\n  // 监听窗口大小变化\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      const mobile = window.innerWidth <= 768;\r\n      setIsMobile(mobile);\r\n      if (mobile) {\r\n        setCollapsed(true);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    handleResize(); // 初始化\r\n\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  const handleLogout = () => {\r\n    if (onLogout) {\r\n      onLogout();\r\n    }\r\n    navigate('/login');\r\n  };\r\n\r\n  const showAIAssistant = () => {\r\n    setAiDrawerVisible(true);\r\n  };\r\n\r\n  const closeAIAssistant = () => {\r\n    setAiDrawerVisible(false);\r\n  };\r\n\r\n  const items = [\r\n    {\r\n      label: '个人中心',\r\n      key: 'profile',\r\n      icon: <UserOutlined />,\r\n      onClick: () => navigate('/profile')\r\n    },\r\n    // 只有教师和管理员才显示统计数据\r\n    ...(user && (user.is_teacher || user.is_admin) ? [{\r\n      label: '统计数据',\r\n      key: 'statistics',\r\n      icon: <BarChartOutlined />,\r\n      onClick: () => navigate('/profile/statistics')\r\n    }] : []),\r\n    {\r\n      label: '退出登录',\r\n      key: 'logout',\r\n      icon: <LogoutOutlined />,\r\n      onClick: handleLogout\r\n    },\r\n  ];\r\n\r\n  // 根据用户类型生成不同的菜单\r\n  const menuItems = (user?.role === 'parent' || user?.role === '家长')\r\n    ? [\r\n        // 家长专用菜单\r\n        {\r\n          key: '/',\r\n          icon: <HomeOutlined />,\r\n          label: '首页',\r\n          onClick: () => navigate('/')\r\n        },\r\n        {\r\n          key: '/parent',\r\n          icon: <UserOutlined />,\r\n          label: '我的孩子',\r\n          onClick: () => navigate('/parent')\r\n        },\r\n        {\r\n          key: '/parent/homework',\r\n          icon: <BookOutlined />,\r\n          label: '作业详情',\r\n          onClick: async () => {\r\n            try {\r\n              // 获取绑定的学生列表\r\n              const response = await api.get('/parent/bound-students');\r\n              const students = response.data || [];\r\n\r\n              if (students.length === 1) {\r\n                // 只有一个孩子，直接跳转到该孩子的作业详情\r\n                const student = students[0];\r\n                navigate(`/parent/student/${student.student_id}/homework`, {\r\n                  state: {\r\n                    studentName: student.student_name,\r\n                    studentId: student.student_id\r\n                  }\r\n                });\r\n              } else {\r\n                // 多个孩子或没有孩子，跳转到孩子选择页面\r\n                navigate('/parent');\r\n              }\r\n            } catch (error) {\r\n              console.error('获取学生列表失败:', error);\r\n              // 出错时跳转到孩子选择页面\r\n              navigate('/parent');\r\n            }\r\n          }\r\n        },\r\n        {\r\n          key: '/parent/report',\r\n          icon: <BarChartOutlined />,\r\n          label: '学习报告',\r\n          onClick: async () => {\r\n            try {\r\n              // 获取绑定的学生列表\r\n              const response = await api.get('/parent/bound-students');\r\n              const students = response.data || [];\r\n\r\n              if (students.length === 1) {\r\n                // 只有一个孩子，直接跳转到该孩子的学习报告\r\n                const student = students[0];\r\n                navigate(`/parent/student/${student.student_id}/report`, {\r\n                  state: {\r\n                    studentName: student.student_name,\r\n                    studentId: student.student_id\r\n                  }\r\n                });\r\n              } else {\r\n                // 多个孩子或没有孩子，跳转到孩子选择页面\r\n                navigate('/parent');\r\n              }\r\n            } catch (error) {\r\n              console.error('获取学生列表失败:', error);\r\n              // 出错时跳转到孩子选择页面\r\n              navigate('/parent');\r\n            }\r\n          }\r\n        }\r\n      ]\r\n    : user && (user.is_teacher || user.is_admin)\r\n    ? [\r\n        // 教师和管理员菜单\r\n        {\r\n          key: '/',\r\n          icon: <HomeOutlined />,\r\n          label: '首页',\r\n          onClick: () => navigate('/')\r\n        },\r\n        {\r\n          key: '/homework',\r\n          icon: <BookOutlined />,\r\n          label: '作业管理',\r\n          onClick: () => navigate('/homework')\r\n        },\r\n        {\r\n          key: '/training',\r\n          icon: <ExperimentOutlined />,\r\n          label: '错题训练',\r\n          onClick: () => navigate('/training')\r\n        },\r\n        {\r\n          key: '/statistics',\r\n          icon: <BarChartOutlined />,\r\n          label: '统计报表',\r\n          onClick: () => navigate('/statistics')\r\n        }\r\n      ]\r\n    : [\r\n        // 学生菜单\r\n        {\r\n          key: '/',\r\n          icon: <HomeOutlined />,\r\n          label: '首页',\r\n          onClick: () => navigate('/')\r\n        },\r\n        {\r\n          key: '/homework',\r\n          icon: <FileTextOutlined />,\r\n          label: '作业任务',\r\n          onClick: () => navigate('/homework')\r\n        },\r\n        {\r\n          key: '/homework/submit',\r\n          icon: <CloudUploadOutlined />,\r\n          label: '提交作业',\r\n          onClick: () => navigate('/homework/submit')\r\n        },\r\n        {\r\n          key: '/homework/review',\r\n          icon: <MessageOutlined />,\r\n          label: '作业点评',\r\n          onClick: () => navigate('/homework/review')\r\n        },\r\n        {\r\n          key: '/homework/history',\r\n          icon: <FileTextOutlined />,\r\n          label: '往日作业',\r\n          onClick: () => navigate('/homework/history')\r\n        },\r\n        {\r\n          key: '/photo-solve',\r\n          icon: <CameraOutlined />,\r\n          label: '拍照解题',\r\n          onClick: () => navigate('/photo-solve')\r\n        },\r\n        {\r\n          key: '/training',\r\n          icon: <ExperimentOutlined />,\r\n          label: '错题训练',\r\n          onClick: () => navigate('/training')\r\n        }\r\n      ];\r\n\r\n  // 如果是教师或管理员，添加班级管理和作业分析菜单\r\n  if (user && (user.is_teacher || user.is_admin)) {\r\n    menuItems.push(\r\n      {\r\n        key: '/class-management',\r\n        icon: <TeamOutlined />,\r\n        label: '班级信息',\r\n        onClick: () => navigate('/class-management')\r\n      },\r\n      {\r\n        key: '/homework-analysis',\r\n        icon: <DashboardOutlined />,\r\n        label: '作业分析',\r\n        onClick: () => navigate('/homework-analysis')\r\n      }\r\n    );\r\n  }\r\n\r\n  // 系统级菜单：班主任及以上角色可以看到（级别40以上）\r\n  if (user && hasRoleLevel(user, 40)) {\r\n\r\n    // 系统作业管理\r\n    menuItems.push({\r\n      key: '/system-homework',\r\n      icon: <FileTextOutlined />,\r\n      label: '系统作业管理',\r\n      onClick: () => navigate('/system-homework')\r\n    });\r\n\r\n    // 系统错题训练\r\n    menuItems.push({\r\n      key: '/system-training',\r\n      icon: <GlobalOutlined />,\r\n      label: '系统错题训练',\r\n      onClick: () => navigate('/system-training')\r\n    });\r\n\r\n    // 系统统计报表\r\n    menuItems.push({\r\n      key: '/system-statistics',\r\n      icon: <LineChartOutlined />,\r\n      label: '系统统计报表',\r\n      onClick: () => navigate('/system-statistics')\r\n    });\r\n    \r\n    // 系统作业分析\r\n    menuItems.push({\r\n      key: '/system-homework-analysis',\r\n      icon: <DashboardOutlined />,\r\n      label: '系统作业分析',\r\n      onClick: () => navigate('/system-homework-analysis')\r\n    });\r\n\r\n    // 系统班级管理\r\n    menuItems.push({\r\n      key: '/system-class',\r\n      icon: <TeamOutlined />,\r\n      label: '系统班级管理',\r\n      onClick: () => navigate('/system-class')\r\n    });\r\n\r\n    // 系统用户管理\r\n    menuItems.push({\r\n      key: '/system-user',\r\n      icon: <UsergroupAddOutlined />,\r\n      label: '系统用户管理',\r\n      onClick: () => navigate('/system-user')\r\n    });\r\n\r\n    // 系统学校管理\r\n    menuItems.push({\r\n      key: '/schools',\r\n      icon: <BankOutlined />,\r\n      label: '系统学校管理',\r\n      onClick: () => navigate('/schools')\r\n    });\r\n\r\n    // 地区管理（只有超级管理员能看到）\r\n    if (isSuperAdmin(user)) {\r\n      menuItems.push({\r\n        key: '/region-management',\r\n        icon: <EnvironmentOutlined />,\r\n        label: '地区管理',\r\n        onClick: () => navigate('/region-management')\r\n      });\r\n    }\r\n\r\n    // 数据库管理（只有超级管理员能看到）\r\n    if (isSuperAdmin(user)) {\r\n      menuItems.push({\r\n        key: '/database-management',\r\n        icon: <DatabaseOutlined />,\r\n        label: '数据库管理',\r\n        onClick: () => navigate('/database-management')\r\n      });\r\n    }\r\n  }\r\n\r\n  // 超级管理员专有菜单（只有超级管理员能看到的特殊功能）\r\n  if (user && isSuperAdmin(user)) {\r\n    // 系统管理（超级管理员专有）\r\n    menuItems.push({\r\n      key: '/admin',\r\n      icon: <SettingOutlined />,\r\n      label: '系统管理',\r\n      onClick: () => navigate('/admin')\r\n    });\r\n  }\r\n\r\n  return (\r\n    <Layout style={{ minHeight: '100vh' }}>\r\n      <Header className=\"app-header\" style={{ \r\n        display: 'flex', \r\n        alignItems: 'center', \r\n        justifyContent: 'space-between',\r\n        padding: '0 24px',\r\n        background: '#fff',\r\n        boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)'\r\n      }}>\r\n        <div className=\"logo\" style={{ display: 'flex', alignItems: 'center' }}>\r\n          <Link to=\"/\" style={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}>\r\n            <img \r\n              src=\"/logo192.png\" \r\n              alt=\"智教云端\" \r\n              style={{ height: '32px', marginRight: '10px' }} \r\n            />\r\n            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>智教云端</Title>\r\n          </Link>\r\n        </div>\r\n        \r\n        <div style={{ display: 'flex', alignItems: 'center' }}>\r\n          {isMobile && (\r\n            <Button\r\n              type=\"text\"\r\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\r\n              onClick={() => setCollapsed(!collapsed)}\r\n              style={{ marginRight: '16px' }}\r\n            />\r\n          )}\r\n\r\n          <Button\r\n            type=\"text\"\r\n            icon={<MessageOutlined />}\r\n            onClick={showAIAssistant}\r\n            style={{ marginRight: '16px' }}\r\n          >\r\n            {isMobile ? '' : 'AI助手'}\r\n          </Button>\r\n          \r\n          <Dropdown menu={{ items }} placement=\"bottomRight\">\r\n            <Space>\r\n              <Avatar\r\n                style={{ backgroundColor: '#1890ff' }}\r\n                icon={<UserOutlined />}\r\n              />\r\n              <div style={{\r\n                display: 'flex',\r\n                flexDirection: 'column',\r\n                alignItems: 'flex-start',\r\n                lineHeight: '1.2',\r\n                minWidth: '80px'\r\n              }}>\r\n                <span style={{\r\n                  fontSize: '14px',\r\n                  fontWeight: '500',\r\n                  marginBottom: '2px',\r\n                  overflow: 'hidden',\r\n                  textOverflow: 'ellipsis',\r\n                  whiteSpace: 'nowrap',\r\n                  maxWidth: '120px'\r\n                }}>\r\n                  {user?.full_name || user?.username || '用户'}\r\n                </span>\r\n                <div style={{\r\n                  fontSize: '11px',\r\n                  color: '#666',\r\n                  lineHeight: '1.1'\r\n                }}>\r\n                  {getUserRoleTags(user)}\r\n                </div>\r\n              </div>\r\n            </Space>\r\n          </Dropdown>\r\n        </div>\r\n      </Header>\r\n      \r\n      <Layout>\r\n        {isMobile ? (\r\n          <Drawer\r\n            title=\"菜单\"\r\n            placement=\"left\"\r\n            onClose={() => setCollapsed(true)}\r\n            open={!collapsed}\r\n            bodyStyle={{ padding: 0 }}\r\n            width={250}\r\n          >\r\n            <Menu\r\n              theme=\"dark\"\r\n              mode=\"inline\"\r\n              selectedKeys={[location.pathname]}\r\n              items={menuItems}\r\n              style={{ height: '100%', borderRight: 0 }}\r\n              onClick={(info) => {\r\n                // 关闭抽屉\r\n                setCollapsed(true);\r\n\r\n                // 递归查找菜单项（支持嵌套菜单）\r\n                const findMenuItem = (items, key) => {\r\n                  for (const item of items) {\r\n                    if (item.key === key) {\r\n                      return item;\r\n                    }\r\n                    if (item.children) {\r\n                      const found = findMenuItem(item.children, key);\r\n                      if (found) return found;\r\n                    }\r\n                  }\r\n                  return null;\r\n                };\r\n\r\n                // 执行菜单项的点击处理\r\n                const menuItem = findMenuItem(menuItems, info.key);\r\n                if (menuItem && menuItem.onClick) {\r\n                  menuItem.onClick();\r\n                }\r\n              }}\r\n            />\r\n          </Drawer>\r\n        ) : (\r\n          <Sider\r\n            collapsible\r\n            collapsed={collapsed}\r\n            onCollapse={value => setCollapsed(value)}\r\n            width={200}\r\n            style={{ background: '#fff' }}\r\n          >\r\n            <Menu\r\n              mode=\"inline\"\r\n              selectedKeys={[location.pathname]}\r\n              style={{ height: '100%', borderRight: 0 }}\r\n              items={menuItems}\r\n            />\r\n          </Sider>\r\n        )}\r\n        \r\n        <Layout style={{ padding: '0 24px 24px' }}>\r\n          <div style={{ margin: '16px 0', padding: '16px', background: '#fff' }}>\r\n            <Title level={3}>{pageTitle}</Title>\r\n            <Divider style={{ margin: '12px 0' }} />\r\n            <Content style={{ padding: '12px 0', minHeight: 'calc(100vh - 200px)' }}>\r\n              {children}\r\n            </Content>\r\n          </div>\r\n        </Layout>\r\n      </Layout>\r\n      \r\n      <Drawer\r\n        title=\"AI智能助手\"\r\n        placement=\"right\"\r\n        onClose={closeAIAssistant}\r\n        open={aiDrawerVisible}\r\n        width={400}\r\n      >\r\n        <AIAssistant user={user} />\r\n      </Drawer>\r\n\r\n\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport default AppLayout;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACjE,SACEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAC7CC,UAAU,EAAEC,OAAO,EAAEC,MAAM,QACtB,MAAM;AACb,SACEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,eAAe,EACzDC,cAAc,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,kBAAkB,EAClEC,eAAe,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EACpEC,cAAc,EAAEC,gBAAgB,EAAEC,kBAAkB,EACpDC,oBAAoB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,iBAAiB,EACvEC,mBAAmB,EAAEC,mBAAmB,QACnC,mBAAmB;AAC1B,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,eAAe,EAAEC,YAAY,EAAEC,YAAY,QAAQ,oBAAoB;AAChF,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAM;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAM,CAAC,GAAGvC,MAAM;AACzC,MAAM;EAAEwC;AAAM,CAAC,GAAGlC,UAAU;AAE5B,MAAMmC,SAAS,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,IAAI;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwD,QAAQ,EAAEC,WAAW,CAAC,GAAGzD,QAAQ,CAAC0D,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAElE,MAAMC,QAAQ,GAAGzD,WAAW,CAAC,CAAC;EAC9B,MAAM0D,QAAQ,GAAG3D,WAAW,CAAC,CAAC;;EAI9B;EACAD,SAAS,CAAC,MAAM;IACd,MAAM6D,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,MAAM,GAAGL,MAAM,CAACC,UAAU,IAAI,GAAG;MACvCF,WAAW,CAACM,MAAM,CAAC;MACnB,IAAIA,MAAM,EAAE;QACVV,YAAY,CAAC,IAAI,CAAC;MACpB;IACF,CAAC;IAEDK,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/CA,YAAY,CAAC,CAAC,CAAC,CAAC;;IAEhB,OAAO,MAAMJ,MAAM,CAACO,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIjB,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ;IACAY,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5BZ,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bb,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMc,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,SAAS;IACdC,IAAI,eAAE/B,OAAA,CAAC3B,YAAY;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,UAAU;EACpC,CAAC;EACD;EACA,IAAIb,IAAI,KAAKA,IAAI,CAAC8B,UAAU,IAAI9B,IAAI,CAAC+B,QAAQ,CAAC,GAAG,CAAC;IAChDT,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,YAAY;IACjBC,IAAI,eAAE/B,OAAA,CAACtB,gBAAgB;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,qBAAqB;EAC/C,CAAC,CAAC,GAAG,EAAE,CAAC,EACR;IACES,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAE/B,OAAA,CAACvB,cAAc;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,OAAO,EAAEX;EACX,CAAC,CACF;;EAED;EACA,MAAMc,SAAS,GAAI,CAAAhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,IAAI,MAAK,QAAQ,IAAI,CAAAjC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,IAAI,MAAK,IAAI,GAC7D;EACE;EACA;IACEV,GAAG,EAAE,GAAG;IACRC,IAAI,eAAE/B,OAAA,CAAC1B,YAAY;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBN,KAAK,EAAE,IAAI;IACXO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,GAAG;EAC7B,CAAC,EACD;IACEU,GAAG,EAAE,SAAS;IACdC,IAAI,eAAE/B,OAAA,CAAC3B,YAAY;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,SAAS;EACnC,CAAC,EACD;IACEU,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAE/B,OAAA,CAACzB,YAAY;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAE,MAAAA,CAAA,KAAY;MACnB,IAAI;QACF;QACA,MAAMK,QAAQ,GAAG,MAAMC,GAAG,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACxD,MAAMC,QAAQ,GAAGH,QAAQ,CAACI,IAAI,IAAI,EAAE;QAEpC,IAAID,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;UACzB;UACA,MAAMC,OAAO,GAAGH,QAAQ,CAAC,CAAC,CAAC;UAC3BxB,QAAQ,CAAC,mBAAmB2B,OAAO,CAACC,UAAU,WAAW,EAAE;YACzDC,KAAK,EAAE;cACLC,WAAW,EAAEH,OAAO,CAACI,YAAY;cACjCC,SAAS,EAAEL,OAAO,CAACC;YACrB;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACA5B,QAAQ,CAAC,SAAS,CAAC;QACrB;MACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;QACAjC,QAAQ,CAAC,SAAS,CAAC;MACrB;IACF;EACF,CAAC,EACD;IACEU,GAAG,EAAE,gBAAgB;IACrBC,IAAI,eAAE/B,OAAA,CAACtB,gBAAgB;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAE,MAAAA,CAAA,KAAY;MACnB,IAAI;QACF;QACA,MAAMK,QAAQ,GAAG,MAAMC,GAAG,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACxD,MAAMC,QAAQ,GAAGH,QAAQ,CAACI,IAAI,IAAI,EAAE;QAEpC,IAAID,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;UACzB;UACA,MAAMC,OAAO,GAAGH,QAAQ,CAAC,CAAC,CAAC;UAC3BxB,QAAQ,CAAC,mBAAmB2B,OAAO,CAACC,UAAU,SAAS,EAAE;YACvDC,KAAK,EAAE;cACLC,WAAW,EAAEH,OAAO,CAACI,YAAY;cACjCC,SAAS,EAAEL,OAAO,CAACC;YACrB;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACA5B,QAAQ,CAAC,SAAS,CAAC;QACrB;MACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;QACAjC,QAAQ,CAAC,SAAS,CAAC;MACrB;IACF;EACF,CAAC,CACF,GACDb,IAAI,KAAKA,IAAI,CAAC8B,UAAU,IAAI9B,IAAI,CAAC+B,QAAQ,CAAC,GAC1C;EACE;EACA;IACER,GAAG,EAAE,GAAG;IACRC,IAAI,eAAE/B,OAAA,CAAC1B,YAAY;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBN,KAAK,EAAE,IAAI;IACXO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,GAAG;EAC7B,CAAC,EACD;IACEU,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAE/B,OAAA,CAACzB,YAAY;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,WAAW;EACrC,CAAC,EACD;IACEU,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAE/B,OAAA,CAACpB,kBAAkB;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,WAAW;EACrC,CAAC,EACD;IACEU,GAAG,EAAE,aAAa;IAClBC,IAAI,eAAE/B,OAAA,CAACtB,gBAAgB;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,aAAa;EACvC,CAAC,CACF,GACD;EACE;EACA;IACEU,GAAG,EAAE,GAAG;IACRC,IAAI,eAAE/B,OAAA,CAAC1B,YAAY;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBN,KAAK,EAAE,IAAI;IACXO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,GAAG;EAC7B,CAAC,EACD;IACEU,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAE/B,OAAA,CAAClB,gBAAgB;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,WAAW;EACrC,CAAC,EACD;IACEU,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAE/B,OAAA,CAACR,mBAAmB;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,kBAAkB;EAC5C,CAAC,EACD;IACEU,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAE/B,OAAA,CAACnB,eAAe;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,kBAAkB;EAC5C,CAAC,EACD;IACEU,GAAG,EAAE,mBAAmB;IACxBC,IAAI,eAAE/B,OAAA,CAAClB,gBAAgB;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,mBAAmB;EAC7C,CAAC,EACD;IACEU,GAAG,EAAE,cAAc;IACnBC,IAAI,eAAE/B,OAAA,CAACf,cAAc;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,cAAc;EACxC,CAAC,EACD;IACEU,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAE/B,OAAA,CAACpB,kBAAkB;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BN,KAAK,EAAE,MAAM;IACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,WAAW;EACrC,CAAC,CACF;;EAEL;EACA,IAAIb,IAAI,KAAKA,IAAI,CAAC8B,UAAU,IAAI9B,IAAI,CAAC+B,QAAQ,CAAC,EAAE;IAC9CC,SAAS,CAACgB,IAAI,CACZ;MACEzB,GAAG,EAAE,mBAAmB;MACxBC,IAAI,eAAE/B,OAAA,CAACrB,YAAY;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBN,KAAK,EAAE,MAAM;MACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,mBAAmB;IAC7C,CAAC,EACD;MACEU,GAAG,EAAE,oBAAoB;MACzBC,IAAI,eAAE/B,OAAA,CAACT,iBAAiB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC3BN,KAAK,EAAE,MAAM;MACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,oBAAoB;IAC9C,CACF,CAAC;EACH;;EAEA;EACA,IAAIb,IAAI,IAAIV,YAAY,CAACU,IAAI,EAAE,EAAE,CAAC,EAAE;IAElC;IACAgC,SAAS,CAACgB,IAAI,CAAC;MACbzB,GAAG,EAAE,kBAAkB;MACvBC,IAAI,eAAE/B,OAAA,CAAClB,gBAAgB;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC1BN,KAAK,EAAE,QAAQ;MACfO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,kBAAkB;IAC5C,CAAC,CAAC;;IAEF;IACAmB,SAAS,CAACgB,IAAI,CAAC;MACbzB,GAAG,EAAE,kBAAkB;MACvBC,IAAI,eAAE/B,OAAA,CAACjB,cAAc;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxBN,KAAK,EAAE,QAAQ;MACfO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,kBAAkB;IAC5C,CAAC,CAAC;;IAEF;IACAmB,SAAS,CAACgB,IAAI,CAAC;MACbzB,GAAG,EAAE,oBAAoB;MACzBC,IAAI,eAAE/B,OAAA,CAAChB,iBAAiB;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC3BN,KAAK,EAAE,QAAQ;MACfO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,oBAAoB;IAC9C,CAAC,CAAC;;IAEF;IACAmB,SAAS,CAACgB,IAAI,CAAC;MACbzB,GAAG,EAAE,2BAA2B;MAChCC,IAAI,eAAE/B,OAAA,CAACT,iBAAiB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC3BN,KAAK,EAAE,QAAQ;MACfO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,2BAA2B;IACrD,CAAC,CAAC;;IAEF;IACAmB,SAAS,CAACgB,IAAI,CAAC;MACbzB,GAAG,EAAE,eAAe;MACpBC,IAAI,eAAE/B,OAAA,CAACrB,YAAY;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBN,KAAK,EAAE,QAAQ;MACfO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,eAAe;IACzC,CAAC,CAAC;;IAEF;IACAmB,SAAS,CAACgB,IAAI,CAAC;MACbzB,GAAG,EAAE,cAAc;MACnBC,IAAI,eAAE/B,OAAA,CAACZ,oBAAoB;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC9BN,KAAK,EAAE,QAAQ;MACfO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,cAAc;IACxC,CAAC,CAAC;;IAEF;IACAmB,SAAS,CAACgB,IAAI,CAAC;MACbzB,GAAG,EAAE,UAAU;MACfC,IAAI,eAAE/B,OAAA,CAACX,YAAY;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBN,KAAK,EAAE,QAAQ;MACfO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,UAAU;IACpC,CAAC,CAAC;;IAEF;IACA,IAAIxB,YAAY,CAACW,IAAI,CAAC,EAAE;MACtBgC,SAAS,CAACgB,IAAI,CAAC;QACbzB,GAAG,EAAE,oBAAoB;QACzBC,IAAI,eAAE/B,OAAA,CAACP,mBAAmB;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC7BN,KAAK,EAAE,MAAM;QACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,oBAAoB;MAC9C,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIxB,YAAY,CAACW,IAAI,CAAC,EAAE;MACtBgC,SAAS,CAACgB,IAAI,CAAC;QACbzB,GAAG,EAAE,sBAAsB;QAC3BC,IAAI,eAAE/B,OAAA,CAACV,gBAAgB;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC1BN,KAAK,EAAE,OAAO;QACdO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,sBAAsB;MAChD,CAAC,CAAC;IACJ;EACF;;EAEA;EACA,IAAIb,IAAI,IAAIX,YAAY,CAACW,IAAI,CAAC,EAAE;IAC9B;IACAgC,SAAS,CAACgB,IAAI,CAAC;MACbzB,GAAG,EAAE,QAAQ;MACbC,IAAI,eAAE/B,OAAA,CAACxB,eAAe;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzBN,KAAK,EAAE,MAAM;MACbO,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,QAAQ;IAClC,CAAC,CAAC;EACJ;EAEA,oBACEpB,OAAA,CAACpC,MAAM;IAAC4F,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAnD,QAAA,gBACpCN,OAAA,CAACC,MAAM;MAACyD,SAAS,EAAC,YAAY;MAACF,KAAK,EAAE;QACpCG,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BC,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE;MACb,CAAE;MAAA1D,QAAA,gBACAN,OAAA;QAAK0D,SAAS,EAAC,MAAM;QAACF,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAtD,QAAA,eACrEN,OAAA,CAACrC,IAAI;UAACsG,EAAE,EAAC,GAAG;UAACT,KAAK,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEM,cAAc,EAAE;UAAO,CAAE;UAAA5D,QAAA,gBACpFN,OAAA;YACEmE,GAAG,EAAC,cAAc;YAClBC,GAAG,EAAC,0BAAM;YACVZ,KAAK,EAAE;cAAEa,MAAM,EAAE,MAAM;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACFnC,OAAA,CAACI,KAAK;YAACmE,KAAK,EAAE,CAAE;YAACf,KAAK,EAAE;cAAEgB,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAnE,QAAA,EAAC;UAAI;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENnC,OAAA;QAAKwD,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAtD,QAAA,GACnDS,QAAQ,iBACPf,OAAA,CAAChC,MAAM;UACL0G,IAAI,EAAC,MAAM;UACX3C,IAAI,EAAEpB,SAAS,gBAAGX,OAAA,CAACb,kBAAkB;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGnC,OAAA,CAACd,gBAAgB;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChEC,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC,CAACD,SAAS,CAAE;UACxC6C,KAAK,EAAE;YAAEc,WAAW,EAAE;UAAO;QAAE;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CACF,eAEDnC,OAAA,CAAChC,MAAM;UACL0G,IAAI,EAAC,MAAM;UACX3C,IAAI,eAAE/B,OAAA,CAACnB,eAAe;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BC,OAAO,EAAEV,eAAgB;UACzB8B,KAAK,EAAE;YAAEc,WAAW,EAAE;UAAO,CAAE;UAAAhE,QAAA,EAE9BS,QAAQ,GAAG,EAAE,GAAG;QAAM;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eAETnC,OAAA,CAAClC,QAAQ;UAAC6G,IAAI,EAAE;YAAE/C;UAAM,CAAE;UAACgD,SAAS,EAAC,aAAa;UAAAtE,QAAA,eAChDN,OAAA,CAAC/B,KAAK;YAAAqC,QAAA,gBACJN,OAAA,CAACjC,MAAM;cACLyF,KAAK,EAAE;gBAAEqB,eAAe,EAAE;cAAU,CAAE;cACtC9C,IAAI,eAAE/B,OAAA,CAAC3B,YAAY;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACFnC,OAAA;cAAKwD,KAAK,EAAE;gBACVG,OAAO,EAAE,MAAM;gBACfmB,aAAa,EAAE,QAAQ;gBACvBlB,UAAU,EAAE,YAAY;gBACxBmB,UAAU,EAAE,KAAK;gBACjBC,QAAQ,EAAE;cACZ,CAAE;cAAA1E,QAAA,gBACAN,OAAA;gBAAMwD,KAAK,EAAE;kBACXyB,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,KAAK;kBACjBC,YAAY,EAAE,KAAK;kBACnBC,QAAQ,EAAE,QAAQ;kBAClBC,YAAY,EAAE,UAAU;kBACxBC,UAAU,EAAE,QAAQ;kBACpBC,QAAQ,EAAE;gBACZ,CAAE;gBAAAjF,QAAA,EACC,CAAAC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiF,SAAS,MAAIjF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkF,QAAQ,KAAI;cAAI;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACPnC,OAAA;gBAAKwD,KAAK,EAAE;kBACVyB,QAAQ,EAAE,MAAM;kBAChBR,KAAK,EAAE,MAAM;kBACbM,UAAU,EAAE;gBACd,CAAE;gBAAAzE,QAAA,EACCX,eAAe,CAACY,IAAI;cAAC;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETnC,OAAA,CAACpC,MAAM;MAAA0C,QAAA,GACJS,QAAQ,gBACPf,OAAA,CAAC5B,MAAM;QACLsH,KAAK,EAAC,cAAI;QACVd,SAAS,EAAC,MAAM;QAChBe,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,IAAI,CAAE;QAClCgF,IAAI,EAAE,CAACjF,SAAU;QACjBkF,SAAS,EAAE;UAAE/B,OAAO,EAAE;QAAE,CAAE;QAC1BgC,KAAK,EAAE,GAAI;QAAAxF,QAAA,eAEXN,OAAA,CAACnC,IAAI;UACHkI,KAAK,EAAC,MAAM;UACZC,IAAI,EAAC,QAAQ;UACbC,YAAY,EAAE,CAAC9E,QAAQ,CAAC+E,QAAQ,CAAE;UAClCtE,KAAK,EAAEW,SAAU;UACjBiB,KAAK,EAAE;YAAEa,MAAM,EAAE,MAAM;YAAE8B,WAAW,EAAE;UAAE,CAAE;UAC1C/D,OAAO,EAAGgE,IAAI,IAAK;YACjB;YACAxF,YAAY,CAAC,IAAI,CAAC;;YAElB;YACA,MAAMyF,YAAY,GAAGA,CAACzE,KAAK,EAAEE,GAAG,KAAK;cACnC,KAAK,MAAMwE,IAAI,IAAI1E,KAAK,EAAE;gBACxB,IAAI0E,IAAI,CAACxE,GAAG,KAAKA,GAAG,EAAE;kBACpB,OAAOwE,IAAI;gBACb;gBACA,IAAIA,IAAI,CAAChG,QAAQ,EAAE;kBACjB,MAAMiG,KAAK,GAAGF,YAAY,CAACC,IAAI,CAAChG,QAAQ,EAAEwB,GAAG,CAAC;kBAC9C,IAAIyE,KAAK,EAAE,OAAOA,KAAK;gBACzB;cACF;cACA,OAAO,IAAI;YACb,CAAC;;YAED;YACA,MAAMC,QAAQ,GAAGH,YAAY,CAAC9D,SAAS,EAAE6D,IAAI,CAACtE,GAAG,CAAC;YAClD,IAAI0E,QAAQ,IAAIA,QAAQ,CAACpE,OAAO,EAAE;cAChCoE,QAAQ,CAACpE,OAAO,CAAC,CAAC;YACpB;UACF;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,gBAETnC,OAAA,CAACG,KAAK;QACJsG,WAAW;QACX9F,SAAS,EAAEA,SAAU;QACrB+F,UAAU,EAAEC,KAAK,IAAI/F,YAAY,CAAC+F,KAAK,CAAE;QACzCb,KAAK,EAAE,GAAI;QACXtC,KAAK,EAAE;UAAEO,UAAU,EAAE;QAAO,CAAE;QAAAzD,QAAA,eAE9BN,OAAA,CAACnC,IAAI;UACHmI,IAAI,EAAC,QAAQ;UACbC,YAAY,EAAE,CAAC9E,QAAQ,CAAC+E,QAAQ,CAAE;UAClC1C,KAAK,EAAE;YAAEa,MAAM,EAAE,MAAM;YAAE8B,WAAW,EAAE;UAAE,CAAE;UAC1CvE,KAAK,EAAEW;QAAU;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACR,eAEDnC,OAAA,CAACpC,MAAM;QAAC4F,KAAK,EAAE;UAAEM,OAAO,EAAE;QAAc,CAAE;QAAAxD,QAAA,eACxCN,OAAA;UAAKwD,KAAK,EAAE;YAAEgB,MAAM,EAAE,QAAQ;YAAEV,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAO,CAAE;UAAAzD,QAAA,gBACpEN,OAAA,CAACI,KAAK;YAACmE,KAAK,EAAE,CAAE;YAAAjE,QAAA,EAAEG;UAAS;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpCnC,OAAA,CAAC7B,OAAO;YAACqF,KAAK,EAAE;cAAEgB,MAAM,EAAE;YAAS;UAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxCnC,OAAA,CAACE,OAAO;YAACsD,KAAK,EAAE;cAAEM,OAAO,EAAE,QAAQ;cAAEL,SAAS,EAAE;YAAsB,CAAE;YAAAnD,QAAA,EACrEA;UAAQ;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAETnC,OAAA,CAAC5B,MAAM;MACLsH,KAAK,EAAC,4BAAQ;MACdd,SAAS,EAAC,OAAO;MACjBe,OAAO,EAAEhE,gBAAiB;MAC1BiE,IAAI,EAAE/E,eAAgB;MACtBiF,KAAK,EAAE,GAAI;MAAAxF,QAAA,eAEXN,OAAA,CAACF,WAAW;QAACS,IAAI,EAAEA;MAAK;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEb,CAAC;AAACzB,EAAA,CAneIL,SAAS;EAAA,QAKI3C,WAAW,EACXD,WAAW;AAAA;AAAAmJ,EAAA,GANxBvG,SAAS;AAqef,eAAeA,SAAS;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}