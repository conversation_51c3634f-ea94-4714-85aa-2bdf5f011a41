from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Table
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

# 科目分类表
class SubjectCategory(Base):
    """科目分类表"""
    __tablename__ = "subject_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False)  # 分类名称，如小学、初中、高中
    description = Column(String(200), nullable=True)  # 分类描述
    order = Column(Integer, default=0)  # 排序顺序
    is_active = Column(Boolean, default=True)  # 是否激活
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联该分类下的所有科目
    subjects = relationship("Subject", back_populates="category")

    def __repr__(self):
        return f"<SubjectCategory(name='{self.name}')>"

# 年级-科目关联表
class GradeSubject(Base):
    """年级-科目关联表"""
    __tablename__ = "grade_subjects"
    
    id = Column(Integer, primary_key=True, index=True)
    grade = Column(String(20), nullable=False)  # 年级，如一年级、二年级
    subject_id = Column(Integer, ForeignKey("subjects.id", ondelete="CASCADE"), nullable=False)
    is_required = Column(Boolean, default=True)  # 是否必修
    order = Column(Integer, default=0)  # 排序顺序
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联的科目
    subject = relationship("Subject", back_populates="grade_subjects")
    
    def __repr__(self):
        return f"<GradeSubject(grade='{self.grade}', subject_id={self.subject_id})>"

class Subject(Base):
    """学科表"""
    __tablename__ = "subjects"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, unique=True)  # 学科名称，如小学语文、初中数学
    pattern = Column(String(100), nullable=True)  # 学科类型，如文科、理科、艺体
    category_id = Column(Integer, ForeignKey("subject_categories.id"), nullable=True)  # 分类ID
    is_active = Column(Boolean, default=True)  # 是否激活
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联的分类
    category = relationship("SubjectCategory", back_populates="subjects")
    
    # 关联的年级
    grade_subjects = relationship("GradeSubject", back_populates="subject", cascade="all, delete-orphan")
    
    # 关联的角色权限
    role_permissions = relationship("RoleSubjectPermission", back_populates="subject", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Subject(name='{self.name}')>" 