#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
将所有"系统管理员"改回"超级管理员"
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.database import SessionLocal
from app.models.user import User

def change_back_to_super_admin():
    """将所有系统管理员改回超级管理员"""
    
    print("🔄 将所有'系统管理员'改回'超级管理员'")
    print("=" * 50)
    
    db = SessionLocal()
    
    try:
        # 1. 修改数据库中的角色
        print("1. 修改数据库中的角色...")
        
        # 查找所有系统管理员
        system_admins = db.query(User).filter(User.role == '系统管理员').all()
        print(f"找到 {len(system_admins)} 个系统管理员用户")
        
        for user in system_admins:
            print(f"  - 修改用户 {user.username}: '系统管理员' -> '超级管理员'")
            user.role = '超级管理员'
        
        db.commit()
        print(f"✅ 数据库修改完成")
        
        # 2. 验证修改结果
        print(f"\n2. 验证修改结果...")
        super_admins = db.query(User).filter(User.role == '超级管理员').all()
        system_admins_remaining = db.query(User).filter(User.role == '系统管理员').all()
        
        print(f"  超级管理员数量: {len(super_admins)}")
        print(f"  系统管理员数量: {len(system_admins_remaining)}")
        
        for user in super_admins:
            print(f"    - {user.username}: {user.role}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修改失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def update_code_files():
    """更新代码文件中的角色名称"""
    
    print(f"\n3. 需要手动更新的代码文件...")
    
    # 需要检查的文件列表
    files_to_check = [
        "app/routers/admin.py",
        "app/routers/auth.py", 
        "app/utils/auth.py",
        "../frontend/src/utils/auth.js",
        "../frontend/src/utils/roleUtils.js",
        "../frontend/src/pages/ImprovedSystemClassManagement.js"
    ]
    
    print("建议检查以下文件，将'系统管理员'改为'超级管理员':")
    for file_path in files_to_check:
        full_path = Path(__file__).parent / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❓ {file_path} (文件不存在)")
    
    print(f"\n💡 搜索建议:")
    print("  - 搜索: '系统管理员'")
    print("  - 替换为: '超级管理员'")
    print("  - 注意: 菜单名称中的'系统'可以保留，如'系统班级管理'")

def main():
    """主函数"""
    print("🚀 开始将系统管理员改回超级管理员")
    
    success = change_back_to_super_admin()
    
    if success:
        update_code_files()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 数据库修改完成！")
        print("📝 接下来需要:")
        print("  1. 手动更新代码文件中的角色名称")
        print("  2. 重启后端服务器")
        print("  3. 刷新前端页面")
        print("  4. 测试登录功能")
        print(f"\n✨ 现在所有地方都将显示'超级管理员'了！")
    else:
        print("💥 修改失败")

if __name__ == '__main__':
    main()
