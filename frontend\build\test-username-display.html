<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户名显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .username {
            background-color: #e6f7ff;
            font-weight: bold;
        }
        .error {
            color: red;
            background-color: #fff2f0;
        }
        .success {
            color: green;
            background-color: #f6ffed;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>教师和家长用户名显示测试</h1>
        
        <div class="section">
            <button onclick="testAPI()">测试API数据</button>
            <button onclick="clearCache()">清除缓存</button>
            <button onclick="location.reload()">刷新页面</button>
        </div>

        <div id="status" class="status" style="display: none;"></div>

        <div class="section">
            <h2>教师列表</h2>
            <div id="teachers-loading" class="loading">点击"测试API数据"按钮开始测试</div>
            <table id="teachers-table" style="display: none;">
                <thead>
                    <tr>
                        <th>用户名</th>
                        <th>姓名</th>
                        <th>科目</th>
                        <th>角色</th>
                        <th>电话</th>
                        <th>邮箱</th>
                    </tr>
                </thead>
                <tbody id="teachers-tbody">
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>家长列表</h2>
            <div id="parents-loading" class="loading">点击"测试API数据"按钮开始测试</div>
            <table id="parents-table" style="display: none;">
                <thead>
                    <tr>
                        <th>用户名</th>
                        <th>姓名</th>
                        <th>关系</th>
                        <th>学生</th>
                        <th>电话</th>
                        <th>邮箱</th>
                    </tr>
                </thead>
                <tbody id="parents-tbody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        let token = null;
        let classId = null;

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.style.display = 'block';
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function clearCache() {
            // 清除localStorage
            localStorage.clear();
            // 清除sessionStorage
            sessionStorage.clear();
            showStatus('缓存已清除', 'success');
        }

        async function login() {
            try {
                const response = await fetch('http://127.0.0.1:8083/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'username=admin&password=admin123'
                });

                if (response.ok) {
                    const data = await response.json();
                    token = data.access_token;
                    showStatus('登录成功', 'success');
                    return true;
                } else {
                    showStatus('登录失败: ' + response.statusText, 'error');
                    return false;
                }
            } catch (error) {
                showStatus('登录错误: ' + error.message, 'error');
                return false;
            }
        }

        async function getClasses() {
            try {
                const response = await fetch('http://127.0.0.1:8083/api/admin/classes', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const classes = await response.json();
                    if (classes && classes.length > 0) {
                        classId = classes[0].id;
                        showStatus(`找到班级: ${classes[0].name} (ID: ${classId})`, 'success');
                        return true;
                    } else {
                        showStatus('没有找到班级', 'error');
                        return false;
                    }
                } else {
                    showStatus('获取班级失败: ' + response.statusText, 'error');
                    return false;
                }
            } catch (error) {
                showStatus('获取班级错误: ' + error.message, 'error');
                return false;
            }
        }

        async function getTeachers() {
            try {
                const response = await fetch(`http://127.0.0.1:8083/api/admin/classes/${classId}/teachers?t=${Date.now()}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const teachers = await response.json();
                    displayTeachers(teachers);
                    return teachers;
                } else {
                    showStatus('获取教师失败: ' + response.statusText, 'error');
                    return [];
                }
            } catch (error) {
                showStatus('获取教师错误: ' + error.message, 'error');
                return [];
            }
        }

        async function getParents() {
            try {
                const response = await fetch(`http://127.0.0.1:8083/api/admin/classes/${classId}/parents?t=${Date.now()}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const parents = await response.json();
                    displayParents(parents);
                    return parents;
                } else {
                    showStatus('获取家长失败: ' + response.statusText, 'error');
                    return [];
                }
            } catch (error) {
                showStatus('获取家长错误: ' + error.message, 'error');
                return [];
            }
        }

        function displayTeachers(teachers) {
            const loadingDiv = document.getElementById('teachers-loading');
            const table = document.getElementById('teachers-table');
            const tbody = document.getElementById('teachers-tbody');

            loadingDiv.style.display = 'none';
            table.style.display = 'table';

            tbody.innerHTML = '';

            teachers.forEach(teacher => {
                const row = tbody.insertRow();
                const usernameCell = row.insertCell(0);
                const nameCell = row.insertCell(1);
                const subjectCell = row.insertCell(2);
                const roleCell = row.insertCell(3);
                const phoneCell = row.insertCell(4);
                const emailCell = row.insertCell(5);

                usernameCell.textContent = teacher.username || '❌ 空';
                usernameCell.className = teacher.username ? 'username' : 'error';
                
                nameCell.textContent = teacher.name || '-';
                subjectCell.textContent = teacher.subject || '-';
                roleCell.textContent = teacher.role || '-';
                phoneCell.textContent = teacher.phone || '-';
                emailCell.textContent = teacher.email || '-';
            });

            showStatus(`显示了 ${teachers.length} 个教师`, 'success');
        }

        function displayParents(parents) {
            const loadingDiv = document.getElementById('parents-loading');
            const table = document.getElementById('parents-table');
            const tbody = document.getElementById('parents-tbody');

            loadingDiv.style.display = 'none';
            table.style.display = 'table';

            tbody.innerHTML = '';

            parents.forEach(parent => {
                const row = tbody.insertRow();
                const usernameCell = row.insertCell(0);
                const nameCell = row.insertCell(1);
                const relationshipCell = row.insertCell(2);
                const studentCell = row.insertCell(3);
                const phoneCell = row.insertCell(4);
                const emailCell = row.insertCell(5);

                usernameCell.textContent = parent.username || '❌ 空';
                usernameCell.className = parent.username ? 'username' : 'error';
                
                nameCell.textContent = parent.name || '-';
                relationshipCell.textContent = parent.relationship || '-';
                studentCell.textContent = parent.student_name || '-';
                phoneCell.textContent = parent.phone || '-';
                emailCell.textContent = parent.email || '-';
            });

            showStatus(`显示了 ${parents.length} 个家长`, 'success');
        }

        async function testAPI() {
            showStatus('开始测试...', 'info');

            // 1. 登录
            const loginSuccess = await login();
            if (!loginSuccess) return;

            // 2. 获取班级
            const classSuccess = await getClasses();
            if (!classSuccess) return;

            // 3. 获取教师和家长数据
            const [teachers, parents] = await Promise.all([
                getTeachers(),
                getParents()
            ]);

            // 4. 检查数据完整性
            const teachersWithUsername = teachers.filter(t => t.username);
            const parentsWithUsername = parents.filter(p => p.username);

            showStatus(
                `测试完成 - 教师: ${teachersWithUsername.length}/${teachers.length} 有用户名, ` +
                `家长: ${parentsWithUsername.length}/${parents.length} 有用户名`,
                teachersWithUsername.length === teachers.length && parentsWithUsername.length === parents.length ? 'success' : 'error'
            );
        }
    </script>
</body>
</html>
