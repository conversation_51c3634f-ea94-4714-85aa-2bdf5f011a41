{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys\\\\frontend\\\\src\\\\components\\\\SystemHomeworkAnalysis\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Routes, Route, useNavigate, useLocation } from 'react-router-dom';\nimport { Layout, Menu, Spin, Button } from 'antd';\nimport { DashboardOutlined, BarChartOutlined, TeamOutlined, BulbOutlined, FileTextOutlined, DownloadOutlined, HomeOutlined, BookOutlined, ExperimentOutlined, LineChartOutlined, GlobalOutlined, UsergroupAddOutlined, DatabaseOutlined, ClearOutlined, SettingOutlined } from '@ant-design/icons';\n\n// 复用HomeworkAnalysis的子组件\nimport Overview from '../HomeworkAnalysis/Overview';\nimport QuestionAnalysis from '../HomeworkAnalysis/QuestionAnalysis';\nimport StudentDetails from '../HomeworkAnalysis/StudentDetails';\nimport SmartSuggestions from '../HomeworkAnalysis/SmartSuggestions';\nimport ParentReport from '../HomeworkAnalysis/ParentReport';\nimport DataExport from '../HomeworkAnalysis/DataExport';\nimport SystemAssignmentSelector from './SystemAssignmentSelector';\nimport { isSuperAdmin } from '../../utils/roleUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Sider,\n  Content\n} = Layout;\nconst SystemHomeworkAnalysis = ({\n  user\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n  const [featureFlags, setFeatureFlags] = useState({});\n\n  // 移动端检测\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // 从URL参数获取作业ID\n  const getAssignmentIdFromUrl = () => {\n    const params = new URLSearchParams(location.search);\n    // 支持两种参数名：assignmentId 和 assignment_id\n    const id = params.get('assignmentId') || params.get('assignment_id');\n    console.log('🔍 getAssignmentIdFromUrl - search params:', location.search);\n    console.log('🔍 getAssignmentIdFromUrl - extracted assignmentId:', id);\n\n    // 如果URL参数中没有，尝试从localStorage获取最后使用的系统assignmentId\n    if (!id) {\n      const lastAssignmentId = localStorage.getItem('lastSystemAssignmentId');\n      console.log('🔍 Fallback to localStorage system assignmentId:', lastAssignmentId);\n      return lastAssignmentId;\n    }\n\n    // 保存到localStorage以备后用\n    if (id) {\n      localStorage.setItem('lastSystemAssignmentId', id);\n    }\n    return id;\n  };\n\n  // 清除筛选状态的函数\n  const clearFilterState = () => {\n    try {\n      localStorage.removeItem('systemHomeworkAnalysisFilters');\n      localStorage.removeItem('lastSystemAssignmentId');\n      console.log('🗑️ 已清除系统作业分析筛选状态');\n    } catch (error) {\n      console.error('清除筛选状态失败:', error);\n    }\n  };\n\n  // 菜单项配置\n  const menuItems = [{\n    key: 'overview',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 13\n    }, this),\n    label: '作业概览',\n    path: '/system-homework-analysis/overview'\n  }, {\n    key: 'questions',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 13\n    }, this),\n    label: '逐题分析',\n    path: '/system-homework-analysis/questions'\n  }, {\n    key: 'students',\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 13\n    }, this),\n    label: '学生详情',\n    path: '/system-homework-analysis/students'\n  }, {\n    key: 'suggestions',\n    icon: /*#__PURE__*/_jsxDEV(BulbOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 13\n    }, this),\n    label: '智能建议',\n    path: '/system-homework-analysis/suggestions'\n  }, {\n    key: 'parent-report',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this),\n    label: '家长报告',\n    path: '/system-homework-analysis/parent-report'\n  }, {\n    key: 'export',\n    icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 13\n    }, this),\n    label: '数据导出',\n    path: '/system-homework-analysis/export'\n  }];\n\n  // 获取当前菜单选中项\n  const getCurrentMenuKey = () => {\n    const path = location.pathname;\n    const menuItem = menuItems.find(item => path.includes(item.path));\n    return menuItem ? menuItem.key : 'overview';\n  };\n\n  // 检查功能标志\n  useEffect(() => {\n    const checkFeatures = async () => {\n      try {\n        // 这里可以添加从后端获取功能标志的逻辑\n        setFeatureFlags({\n          overview: true,\n          questions: true,\n          students: true,\n          suggestions: true,\n          'parent-report': true,\n          export: true\n        });\n      } catch (error) {\n        console.error('获取功能标志失败:', error);\n      }\n    };\n    checkFeatures();\n  }, []);\n\n  // 处理菜单点击\n  const handleMenuClick = ({\n    key\n  }) => {\n    console.log('🔍 handleMenuClick called with key:', key);\n    const menuItem = menuItems.find(item => item.key === key);\n    console.log('🔍 Found menu item:', menuItem);\n    if (menuItem) {\n      const assignmentId = getAssignmentIdFromUrl();\n      console.log('🔍 assignmentId for navigation:', assignmentId);\n      const newPath = assignmentId ? `${menuItem.path}?assignmentId=${assignmentId}` : menuItem.path;\n      console.log('🔍 Navigating to:', newPath);\n      navigate(newPath);\n    }\n  };\n\n  // 检查功能是否启用\n  const isFeatureEnabled = featureName => {\n    return featureFlags[featureName] !== false;\n  };\n\n  // 获取作业ID\n  const assignmentId = getAssignmentIdFromUrl();\n  console.log('🔍 Current assignmentId:', assignmentId);\n  console.log('🔍 Current location:', location.pathname, location.search);\n\n  // 构建完整的主菜单项（复制HomeworkAnalysis的逻辑）\n  const buildMainMenuItems = () => {\n    const items = [{\n      key: '/',\n      icon: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 15\n      }, this),\n      label: '首页'\n    }, {\n      key: '/homework',\n      icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 15\n      }, this),\n      label: '作业管理'\n    }, {\n      key: '/training',\n      icon: /*#__PURE__*/_jsxDEV(ExperimentOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 15\n      }, this),\n      label: '错题训练'\n    }, {\n      key: '/statistics',\n      icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 15\n      }, this),\n      label: '统计报表'\n    }];\n\n    // 如果是教师或管理员，添加班级管理和作业分析菜单\n    if (user && (user.is_teacher || user.is_admin)) {\n      items.push({\n        key: '/class-management',\n        icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 17\n        }, this),\n        label: '班级管理'\n      }, {\n        key: '/homework-analysis',\n        icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 17\n        }, this),\n        label: '作业分析'\n      });\n    }\n\n    // 如果是超级管理员，添加系统级菜单\n    if (user && user.is_admin && user.role === '超级管理员') {\n      items.push({\n        key: '/system-homework',\n        icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 17\n        }, this),\n        label: '系统作业管理'\n      }, {\n        key: '/system-training',\n        icon: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 17\n        }, this),\n        label: '系统错题训练'\n      }, {\n        key: '/system-statistics',\n        icon: /*#__PURE__*/_jsxDEV(LineChartOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 17\n        }, this),\n        label: '系统统计报表'\n      }, {\n        key: '/system-class',\n        icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 17\n        }, this),\n        label: '系统班级管理'\n      }, {\n        key: '/system-user',\n        icon: /*#__PURE__*/_jsxDEV(UsergroupAddOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 17\n        }, this),\n        label: '系统用户管理'\n      }, {\n        key: '/database-management',\n        icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 17\n        }, this),\n        label: '数据库管理'\n      }, {\n        key: '/system-homework-analysis',\n        icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 17\n        }, this),\n        label: '系统作业分析',\n        children: menuItems.map(item => ({\n          ...item,\n          disabled: !assignmentId\n        }))\n      });\n    }\n\n    // 超级管理员专有菜单（系统管理）\n    if (user && isSuperAdmin(user)) {\n      items.push({\n        key: '/admin',\n        icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 15\n        }, this),\n        label: '系统管理'\n      });\n    }\n    return items;\n  };\n  const mainMenuItems = buildMainMenuItems();\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    className: \"system-homework-analysis-page\",\n    style: {\n      height: '100vh',\n      overflow: 'hidden'\n    },\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      children: [/*#__PURE__*/_jsxDEV(Sider, {\n        width: 200,\n        theme: \"light\",\n        style: {\n          overflow: 'auto',\n          height: '100vh'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u7CFB\\u7EDF\\u4F5C\\u4E1A\\u5206\\u6790\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), assignmentId && /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ClearOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 23\n            }, this),\n            size: \"small\",\n            onClick: () => {\n              clearFilterState();\n              navigate('/system-homework-analysis');\n            },\n            style: {\n              marginTop: '8px'\n            },\n            children: \"\\u6E05\\u9664\\u7B5B\\u9009\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          mode: \"inline\",\n          selectedKeys: assignmentId ? [getCurrentMenuKey()] : ['/system-homework-analysis'],\n          defaultOpenKeys: ['/system-homework-analysis'],\n          style: {\n            height: '100%',\n            borderRight: 0\n          },\n          onClick: info => {\n            if (info.key.startsWith('/system-homework-analysis/')) {\n              handleMenuClick(info);\n            } else {\n              // 主菜单点击 - 直接导航\n              navigate(info.key);\n            }\n          },\n          items: mainMenuItems\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Layout, {\n        style: {\n          padding: '0 24px 24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Content, {\n          style: {\n            padding: 24,\n            margin: 0,\n            minHeight: 280,\n            background: '#fff',\n            borderRadius: '4px',\n            overflow: 'auto'\n          },\n          children: !assignmentId ? /*#__PURE__*/_jsxDEV(SystemAssignmentSelector, {\n            user: user\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: loading,\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Overview, {\n                  assignmentId: assignmentId,\n                  user: user,\n                  isSystemLevel: true,\n                  onLoading: setLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 44\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/overview\",\n                element: /*#__PURE__*/_jsxDEV(Overview, {\n                  assignmentId: assignmentId,\n                  user: user,\n                  isSystemLevel: true,\n                  onLoading: setLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 52\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/questions\",\n                element: /*#__PURE__*/_jsxDEV(QuestionAnalysis, {\n                  assignmentId: assignmentId,\n                  user: user,\n                  isSystemLevel: true,\n                  onLoading: setLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 53\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/students\",\n                element: /*#__PURE__*/_jsxDEV(StudentDetails, {\n                  assignmentId: assignmentId,\n                  user: user,\n                  isSystemLevel: true,\n                  onLoading: setLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 52\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/suggestions\",\n                element: /*#__PURE__*/_jsxDEV(SmartSuggestions, {\n                  assignmentId: assignmentId,\n                  user: user,\n                  isSystemLevel: true,\n                  onLoading: setLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 55\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/parent-report\",\n                element: /*#__PURE__*/_jsxDEV(ParentReport, {\n                  assignmentId: assignmentId,\n                  user: user,\n                  isSystemLevel: true,\n                  onLoading: setLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 57\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/export\",\n                element: /*#__PURE__*/_jsxDEV(DataExport, {\n                  assignmentId: assignmentId,\n                  user: user,\n                  isSystemLevel: true,\n                  onLoading: setLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 283,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemHomeworkAnalysis, \"xJbDkbL6Bsam3I8aU+oBKdYov3g=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = SystemHomeworkAnalysis;\nexport default SystemHomeworkAnalysis;\nvar _c;\n$RefreshReg$(_c, \"SystemHomeworkAnalysis\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Routes", "Route", "useNavigate", "useLocation", "Layout", "<PERSON><PERSON>", "Spin", "<PERSON><PERSON>", "DashboardOutlined", "BarChartOutlined", "TeamOutlined", "BulbOutlined", "FileTextOutlined", "DownloadOutlined", "HomeOutlined", "BookOutlined", "ExperimentOutlined", "LineChartOutlined", "GlobalOutlined", "UsergroupAddOutlined", "DatabaseOutlined", "ClearOutlined", "SettingOutlined", "Overview", "QuestionAnalysis", "StudentDetails", "SmartSuggestions", "ParentReport", "DataExport", "SystemAssignmentSelector", "isSuperAdmin", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "Content", "SystemHomeworkAnalysis", "user", "_s", "navigate", "location", "loading", "setLoading", "featureFlags", "setFeatureFlags", "isMobile", "setIsMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "getAssignmentIdFromUrl", "params", "URLSearchParams", "search", "id", "get", "console", "log", "lastAssignmentId", "localStorage", "getItem", "setItem", "clearFilterState", "removeItem", "error", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "path", "getCurrentMenuKey", "pathname", "menuItem", "find", "item", "includes", "checkFeatures", "overview", "questions", "students", "suggestions", "export", "handleMenuClick", "assignmentId", "newPath", "isFeatureEnabled", "featureName", "buildMainMenuItems", "items", "is_teacher", "is_admin", "push", "role", "children", "map", "disabled", "mainMenuItems", "className", "style", "height", "overflow", "width", "theme", "padding", "textAlign", "size", "onClick", "marginTop", "mode", "<PERSON><PERSON><PERSON><PERSON>", "defaultOpenKeys", "borderRight", "info", "startsWith", "margin", "minHeight", "background", "borderRadius", "spinning", "element", "isSystemLevel", "onLoading", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys/frontend/src/components/SystemHomeworkAnalysis/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Routes, Route, useNavigate, useLocation } from 'react-router-dom';\r\nimport { Layout, Menu, Spin, Button } from 'antd';\r\nimport {\r\n  DashboardOutlined,\r\n  Bar<PERSON>hartOutlined,\r\n  TeamOutlined,\r\n  BulbOutlined,\r\n  FileTextOutlined,\r\n  DownloadOutlined,\r\n  HomeOutlined,\r\n  BookOutlined,\r\n  ExperimentOutlined,\r\n  LineChartOutlined,\r\n  GlobalOutlined,\r\n  UsergroupAddOutlined,\r\n  DatabaseOutlined,\r\n  ClearOutlined,\r\n  SettingOutlined\r\n} from '@ant-design/icons';\r\n\r\n// 复用HomeworkAnalysis的子组件\r\nimport Overview from '../HomeworkAnalysis/Overview';\r\nimport QuestionAnalysis from '../HomeworkAnalysis/QuestionAnalysis';\r\nimport StudentDetails from '../HomeworkAnalysis/StudentDetails';\r\nimport SmartSuggestions from '../HomeworkAnalysis/SmartSuggestions';\r\nimport ParentReport from '../HomeworkAnalysis/ParentReport';\r\nimport DataExport from '../HomeworkAnalysis/DataExport';\r\nimport SystemAssignmentSelector from './SystemAssignmentSelector';\r\nimport { isSuperAdmin } from '../../utils/roleUtils';\r\n\r\nconst { Sider, Content } = Layout;\r\n\r\nconst SystemHomeworkAnalysis = ({ user }) => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [loading, setLoading] = useState(false);\r\n  const [featureFlags, setFeatureFlags] = useState({});\r\n\r\n  // 移动端检测\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth <= 768);\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  // 从URL参数获取作业ID\r\n  const getAssignmentIdFromUrl = () => {\r\n    const params = new URLSearchParams(location.search);\r\n    // 支持两种参数名：assignmentId 和 assignment_id\r\n    const id = params.get('assignmentId') || params.get('assignment_id');\r\n    console.log('🔍 getAssignmentIdFromUrl - search params:', location.search);\r\n    console.log('🔍 getAssignmentIdFromUrl - extracted assignmentId:', id);\r\n\r\n    // 如果URL参数中没有，尝试从localStorage获取最后使用的系统assignmentId\r\n    if (!id) {\r\n      const lastAssignmentId = localStorage.getItem('lastSystemAssignmentId');\r\n      console.log('🔍 Fallback to localStorage system assignmentId:', lastAssignmentId);\r\n      return lastAssignmentId;\r\n    }\r\n\r\n    // 保存到localStorage以备后用\r\n    if (id) {\r\n      localStorage.setItem('lastSystemAssignmentId', id);\r\n    }\r\n\r\n    return id;\r\n  };\r\n\r\n  // 清除筛选状态的函数\r\n  const clearFilterState = () => {\r\n    try {\r\n      localStorage.removeItem('systemHomeworkAnalysisFilters');\r\n      localStorage.removeItem('lastSystemAssignmentId');\r\n      console.log('🗑️ 已清除系统作业分析筛选状态');\r\n    } catch (error) {\r\n      console.error('清除筛选状态失败:', error);\r\n    }\r\n  };\r\n\r\n  // 菜单项配置\r\n  const menuItems = [\r\n    {\r\n      key: 'overview',\r\n      icon: <DashboardOutlined />,\r\n      label: '作业概览',\r\n      path: '/system-homework-analysis/overview'\r\n    },\r\n    {\r\n      key: 'questions',\r\n      icon: <BarChartOutlined />,\r\n      label: '逐题分析',\r\n      path: '/system-homework-analysis/questions'\r\n    },\r\n    {\r\n      key: 'students',\r\n      icon: <TeamOutlined />,\r\n      label: '学生详情',\r\n      path: '/system-homework-analysis/students'\r\n    },\r\n    {\r\n      key: 'suggestions',\r\n      icon: <BulbOutlined />,\r\n      label: '智能建议',\r\n      path: '/system-homework-analysis/suggestions'\r\n    },\r\n    {\r\n      key: 'parent-report',\r\n      icon: <FileTextOutlined />,\r\n      label: '家长报告',\r\n      path: '/system-homework-analysis/parent-report'\r\n    },\r\n    {\r\n      key: 'export',\r\n      icon: <DownloadOutlined />,\r\n      label: '数据导出',\r\n      path: '/system-homework-analysis/export'\r\n    }\r\n  ];\r\n\r\n  // 获取当前菜单选中项\r\n  const getCurrentMenuKey = () => {\r\n    const path = location.pathname;\r\n    const menuItem = menuItems.find(item => path.includes(item.path));\r\n    return menuItem ? menuItem.key : 'overview';\r\n  };\r\n\r\n  // 检查功能标志\r\n  useEffect(() => {\r\n    const checkFeatures = async () => {\r\n      try {\r\n        // 这里可以添加从后端获取功能标志的逻辑\r\n        setFeatureFlags({\r\n          overview: true,\r\n          questions: true,\r\n          students: true,\r\n          suggestions: true,\r\n          'parent-report': true,\r\n          export: true\r\n        });\r\n      } catch (error) {\r\n        console.error('获取功能标志失败:', error);\r\n      }\r\n    };\r\n\r\n    checkFeatures();\r\n  }, []);\r\n\r\n  // 处理菜单点击\r\n  const handleMenuClick = ({ key }) => {\r\n    console.log('🔍 handleMenuClick called with key:', key);\r\n    const menuItem = menuItems.find(item => item.key === key);\r\n    console.log('🔍 Found menu item:', menuItem);\r\n\r\n    if (menuItem) {\r\n      const assignmentId = getAssignmentIdFromUrl();\r\n      console.log('🔍 assignmentId for navigation:', assignmentId);\r\n\r\n      const newPath = assignmentId\r\n        ? `${menuItem.path}?assignmentId=${assignmentId}`\r\n        : menuItem.path;\r\n      console.log('🔍 Navigating to:', newPath);\r\n      navigate(newPath);\r\n    }\r\n  };\r\n\r\n  // 检查功能是否启用\r\n  const isFeatureEnabled = (featureName) => {\r\n    return featureFlags[featureName] !== false;\r\n  };\r\n\r\n  // 获取作业ID\r\n  const assignmentId = getAssignmentIdFromUrl();\r\n  console.log('🔍 Current assignmentId:', assignmentId);\r\n  console.log('🔍 Current location:', location.pathname, location.search);\r\n\r\n  // 构建完整的主菜单项（复制HomeworkAnalysis的逻辑）\r\n  const buildMainMenuItems = () => {\r\n    const items = [\r\n      {\r\n        key: '/',\r\n        icon: <HomeOutlined />,\r\n        label: '首页'\r\n      },\r\n      {\r\n        key: '/homework',\r\n        icon: <BookOutlined />,\r\n        label: '作业管理'\r\n      },\r\n      {\r\n        key: '/training',\r\n        icon: <ExperimentOutlined />,\r\n        label: '错题训练'\r\n      },\r\n      {\r\n        key: '/statistics',\r\n        icon: <BarChartOutlined />,\r\n        label: '统计报表'\r\n      }\r\n    ];\r\n\r\n    // 如果是教师或管理员，添加班级管理和作业分析菜单\r\n    if (user && (user.is_teacher || user.is_admin)) {\r\n      items.push(\r\n        {\r\n          key: '/class-management',\r\n          icon: <TeamOutlined />,\r\n          label: '班级管理'\r\n        },\r\n        {\r\n          key: '/homework-analysis',\r\n          icon: <DashboardOutlined />,\r\n          label: '作业分析'\r\n        }\r\n      );\r\n    }\r\n\r\n    // 如果是超级管理员，添加系统级菜单\r\n    if (user && user.is_admin && user.role === '超级管理员') {\r\n      items.push(\r\n        {\r\n          key: '/system-homework',\r\n          icon: <FileTextOutlined />,\r\n          label: '系统作业管理'\r\n        },\r\n        {\r\n          key: '/system-training',\r\n          icon: <GlobalOutlined />,\r\n          label: '系统错题训练'\r\n        },\r\n        {\r\n          key: '/system-statistics',\r\n          icon: <LineChartOutlined />,\r\n          label: '系统统计报表'\r\n        },\r\n        {\r\n          key: '/system-class',\r\n          icon: <TeamOutlined />,\r\n          label: '系统班级管理'\r\n        },\r\n        {\r\n          key: '/system-user',\r\n          icon: <UsergroupAddOutlined />,\r\n          label: '系统用户管理'\r\n        },\r\n        {\r\n          key: '/database-management',\r\n          icon: <DatabaseOutlined />,\r\n          label: '数据库管理'\r\n        },\r\n        {\r\n          key: '/system-homework-analysis',\r\n          icon: <DashboardOutlined />,\r\n          label: '系统作业分析',\r\n          children: menuItems.map(item => ({\r\n            ...item,\r\n            disabled: !assignmentId\r\n          }))\r\n        }\r\n      );\r\n    }\r\n\r\n    // 超级管理员专有菜单（系统管理）\r\n    if (user && isSuperAdmin(user)) {\r\n      items.push({\r\n        key: '/admin',\r\n        icon: <SettingOutlined />,\r\n        label: '系统管理'\r\n      });\r\n    }\r\n\r\n    return items;\r\n  };\r\n\r\n  const mainMenuItems = buildMainMenuItems();\r\n\r\n  return (\r\n    <Layout className=\"system-homework-analysis-page\" style={{ height: '100vh', overflow: 'hidden' }}>\r\n      <Layout>\r\n        <Sider width={200} theme=\"light\" style={{ overflow: 'auto', height: '100vh' }}>\r\n          <div style={{ padding: '16px', textAlign: 'center' }}>\r\n            <h3>系统作业分析</h3>\r\n            {assignmentId && (\r\n              <Button\r\n                icon={<ClearOutlined />}\r\n                size=\"small\"\r\n                onClick={() => {\r\n                  clearFilterState();\r\n                  navigate('/system-homework-analysis');\r\n                }}\r\n                style={{ marginTop: '8px' }}\r\n              >\r\n                清除筛选\r\n              </Button>\r\n            )}\r\n          </div>\r\n          <Menu\r\n            mode=\"inline\"\r\n            selectedKeys={assignmentId ? [getCurrentMenuKey()] : ['/system-homework-analysis']}\r\n            defaultOpenKeys={['/system-homework-analysis']}\r\n            style={{ height: '100%', borderRight: 0 }}\r\n            onClick={(info) => {\r\n              if (info.key.startsWith('/system-homework-analysis/')) {\r\n                handleMenuClick(info);\r\n              } else {\r\n                // 主菜单点击 - 直接导航\r\n                navigate(info.key);\r\n              }\r\n            }}\r\n            items={mainMenuItems}\r\n          />\r\n        </Sider>\r\n        <Layout style={{ padding: '0 24px 24px' }}>\r\n          <Content\r\n            style={{\r\n              padding: 24,\r\n              margin: 0,\r\n              minHeight: 280,\r\n              background: '#fff',\r\n              borderRadius: '4px',\r\n              overflow: 'auto'\r\n            }}\r\n          >\r\n            {!assignmentId ? (\r\n              <SystemAssignmentSelector\r\n                user={user}\r\n              />\r\n            ) : (\r\n              <Spin spinning={loading}>\r\n                <Routes>\r\n                  <Route path=\"/\" element={<Overview assignmentId={assignmentId} user={user} isSystemLevel={true} onLoading={setLoading} />} />\r\n                  <Route path=\"/overview\" element={<Overview assignmentId={assignmentId} user={user} isSystemLevel={true} onLoading={setLoading} />} />\r\n                  <Route path=\"/questions\" element={<QuestionAnalysis assignmentId={assignmentId} user={user} isSystemLevel={true} onLoading={setLoading} />} />\r\n                  <Route path=\"/students\" element={<StudentDetails assignmentId={assignmentId} user={user} isSystemLevel={true} onLoading={setLoading} />} />\r\n                  <Route path=\"/suggestions\" element={<SmartSuggestions assignmentId={assignmentId} user={user} isSystemLevel={true} onLoading={setLoading} />} />\r\n                  <Route path=\"/parent-report\" element={<ParentReport assignmentId={assignmentId} user={user} isSystemLevel={true} onLoading={setLoading} />} />\r\n                  <Route path=\"/export\" element={<DataExport assignmentId={assignmentId} user={user} isSystemLevel={true} onLoading={setLoading} />} />\r\n                </Routes>\r\n              </Spin>\r\n            )}\r\n          </Content>\r\n        </Layout>\r\n      </Layout>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport default SystemHomeworkAnalysis; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC1E,SAASC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,QAAQ,MAAM;AACjD,SACEC,iBAAiB,EACjBC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,kBAAkB,EAClBC,iBAAiB,EACjBC,cAAc,EACdC,oBAAoB,EACpBC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,QACV,mBAAmB;;AAE1B;AACA,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,SAASC,YAAY,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAG9B,MAAM;AAEjC,MAAM+B,sBAAsB,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC3C,MAAMC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAMqC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAACgD,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAElEhD,SAAS,CAAC,MAAM;IACd,MAAMiD,YAAY,GAAGA,CAAA,KAAM;MACzBH,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAEDD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAACd,QAAQ,CAACe,MAAM,CAAC;IACnD;IACA,MAAMC,EAAE,GAAGH,MAAM,CAACI,GAAG,CAAC,cAAc,CAAC,IAAIJ,MAAM,CAACI,GAAG,CAAC,eAAe,CAAC;IACpEC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEnB,QAAQ,CAACe,MAAM,CAAC;IAC1EG,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEH,EAAE,CAAC;;IAEtE;IACA,IAAI,CAACA,EAAE,EAAE;MACP,MAAMI,gBAAgB,GAAGC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;MACvEJ,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEC,gBAAgB,CAAC;MACjF,OAAOA,gBAAgB;IACzB;;IAEA;IACA,IAAIJ,EAAE,EAAE;MACNK,YAAY,CAACE,OAAO,CAAC,wBAAwB,EAAEP,EAAE,CAAC;IACpD;IAEA,OAAOA,EAAE;EACX,CAAC;;EAED;EACA,MAAMQ,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI;MACFH,YAAY,CAACI,UAAU,CAAC,+BAA+B,CAAC;MACxDJ,YAAY,CAACI,UAAU,CAAC,wBAAwB,CAAC;MACjDP,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAClC,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEpC,OAAA,CAACxB,iBAAiB;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEpC,OAAA,CAACvB,gBAAgB;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEpC,OAAA,CAACtB,YAAY;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,aAAa;IAClBC,IAAI,eAAEpC,OAAA,CAACrB,YAAY;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,eAAe;IACpBC,IAAI,eAAEpC,OAAA,CAACpB,gBAAgB;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEpC,OAAA,CAACnB,gBAAgB;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC,CACF;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMD,IAAI,GAAGnC,QAAQ,CAACqC,QAAQ;IAC9B,MAAMC,QAAQ,GAAGX,SAAS,CAACY,IAAI,CAACC,IAAI,IAAIL,IAAI,CAACM,QAAQ,CAACD,IAAI,CAACL,IAAI,CAAC,CAAC;IACjE,OAAOG,QAAQ,GAAGA,QAAQ,CAACV,GAAG,GAAG,UAAU;EAC7C,CAAC;;EAED;EACApE,SAAS,CAAC,MAAM;IACd,MAAMkF,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF;QACAtC,eAAe,CAAC;UACduC,QAAQ,EAAE,IAAI;UACdC,SAAS,EAAE,IAAI;UACfC,QAAQ,EAAE,IAAI;UACdC,WAAW,EAAE,IAAI;UACjB,eAAe,EAAE,IAAI;UACrBC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOrB,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC;IAEDgB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,eAAe,GAAGA,CAAC;IAAEpB;EAAI,CAAC,KAAK;IACnCV,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAES,GAAG,CAAC;IACvD,MAAMU,QAAQ,GAAGX,SAAS,CAACY,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACZ,GAAG,KAAKA,GAAG,CAAC;IACzDV,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEmB,QAAQ,CAAC;IAE5C,IAAIA,QAAQ,EAAE;MACZ,MAAMW,YAAY,GAAGrC,sBAAsB,CAAC,CAAC;MAC7CM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE8B,YAAY,CAAC;MAE5D,MAAMC,OAAO,GAAGD,YAAY,GACxB,GAAGX,QAAQ,CAACH,IAAI,iBAAiBc,YAAY,EAAE,GAC/CX,QAAQ,CAACH,IAAI;MACjBjB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE+B,OAAO,CAAC;MACzCnD,QAAQ,CAACmD,OAAO,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,WAAW,IAAK;IACxC,OAAOjD,YAAY,CAACiD,WAAW,CAAC,KAAK,KAAK;EAC5C,CAAC;;EAED;EACA,MAAMH,YAAY,GAAGrC,sBAAsB,CAAC,CAAC;EAC7CM,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE8B,YAAY,CAAC;EACrD/B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEnB,QAAQ,CAACqC,QAAQ,EAAErC,QAAQ,CAACe,MAAM,CAAC;;EAEvE;EACA,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,KAAK,GAAG,CACZ;MACE1B,GAAG,EAAE,GAAG;MACRC,IAAI,eAAEpC,OAAA,CAAClB,YAAY;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,WAAW;MAChBC,IAAI,eAAEpC,OAAA,CAACjB,YAAY;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,WAAW;MAChBC,IAAI,eAAEpC,OAAA,CAAChB,kBAAkB;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC5BC,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,aAAa;MAClBC,IAAI,eAAEpC,OAAA,CAACvB,gBAAgB;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC1BC,KAAK,EAAE;IACT,CAAC,CACF;;IAED;IACA,IAAIrC,IAAI,KAAKA,IAAI,CAAC0D,UAAU,IAAI1D,IAAI,CAAC2D,QAAQ,CAAC,EAAE;MAC9CF,KAAK,CAACG,IAAI,CACR;QACE7B,GAAG,EAAE,mBAAmB;QACxBC,IAAI,eAAEpC,OAAA,CAACtB,YAAY;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACtBC,KAAK,EAAE;MACT,CAAC,EACD;QACEN,GAAG,EAAE,oBAAoB;QACzBC,IAAI,eAAEpC,OAAA,CAACxB,iBAAiB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC3BC,KAAK,EAAE;MACT,CACF,CAAC;IACH;;IAEA;IACA,IAAIrC,IAAI,IAAIA,IAAI,CAAC2D,QAAQ,IAAI3D,IAAI,CAAC6D,IAAI,KAAK,OAAO,EAAE;MAClDJ,KAAK,CAACG,IAAI,CACR;QACE7B,GAAG,EAAE,kBAAkB;QACvBC,IAAI,eAAEpC,OAAA,CAACpB,gBAAgB;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC1BC,KAAK,EAAE;MACT,CAAC,EACD;QACEN,GAAG,EAAE,kBAAkB;QACvBC,IAAI,eAAEpC,OAAA,CAACd,cAAc;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACxBC,KAAK,EAAE;MACT,CAAC,EACD;QACEN,GAAG,EAAE,oBAAoB;QACzBC,IAAI,eAAEpC,OAAA,CAACf,iBAAiB;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC3BC,KAAK,EAAE;MACT,CAAC,EACD;QACEN,GAAG,EAAE,eAAe;QACpBC,IAAI,eAAEpC,OAAA,CAACtB,YAAY;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACtBC,KAAK,EAAE;MACT,CAAC,EACD;QACEN,GAAG,EAAE,cAAc;QACnBC,IAAI,eAAEpC,OAAA,CAACb,oBAAoB;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC9BC,KAAK,EAAE;MACT,CAAC,EACD;QACEN,GAAG,EAAE,sBAAsB;QAC3BC,IAAI,eAAEpC,OAAA,CAACZ,gBAAgB;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC1BC,KAAK,EAAE;MACT,CAAC,EACD;QACEN,GAAG,EAAE,2BAA2B;QAChCC,IAAI,eAAEpC,OAAA,CAACxB,iBAAiB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC3BC,KAAK,EAAE,QAAQ;QACfyB,QAAQ,EAAEhC,SAAS,CAACiC,GAAG,CAACpB,IAAI,KAAK;UAC/B,GAAGA,IAAI;UACPqB,QAAQ,EAAE,CAACZ;QACb,CAAC,CAAC;MACJ,CACF,CAAC;IACH;;IAEA;IACA,IAAIpD,IAAI,IAAIN,YAAY,CAACM,IAAI,CAAC,EAAE;MAC9ByD,KAAK,CAACG,IAAI,CAAC;QACT7B,GAAG,EAAE,QAAQ;QACbC,IAAI,eAAEpC,OAAA,CAACV,eAAe;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACzBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;IAEA,OAAOoB,KAAK;EACd,CAAC;EAED,MAAMQ,aAAa,GAAGT,kBAAkB,CAAC,CAAC;EAE1C,oBACE5D,OAAA,CAAC5B,MAAM;IAACkG,SAAS,EAAC,+BAA+B;IAACC,KAAK,EAAE;MAAEC,MAAM,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAP,QAAA,eAC/FlE,OAAA,CAAC5B,MAAM;MAAA8F,QAAA,gBACLlE,OAAA,CAACC,KAAK;QAACyE,KAAK,EAAE,GAAI;QAACC,KAAK,EAAC,OAAO;QAACJ,KAAK,EAAE;UAAEE,QAAQ,EAAE,MAAM;UAAED,MAAM,EAAE;QAAQ,CAAE;QAAAN,QAAA,gBAC5ElE,OAAA;UAAKuE,KAAK,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAX,QAAA,gBACnDlE,OAAA;YAAAkE,QAAA,EAAI;UAAM;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACdgB,YAAY,iBACXxD,OAAA,CAACzB,MAAM;YACL6D,IAAI,eAAEpC,OAAA,CAACX,aAAa;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxBsC,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAM;cACbhD,gBAAgB,CAAC,CAAC;cAClBzB,QAAQ,CAAC,2BAA2B,CAAC;YACvC,CAAE;YACFiE,KAAK,EAAE;cAAES,SAAS,EAAE;YAAM,CAAE;YAAAd,QAAA,EAC7B;UAED;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNxC,OAAA,CAAC3B,IAAI;UACH4G,IAAI,EAAC,QAAQ;UACbC,YAAY,EAAE1B,YAAY,GAAG,CAACb,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,2BAA2B,CAAE;UACnFwC,eAAe,EAAE,CAAC,2BAA2B,CAAE;UAC/CZ,KAAK,EAAE;YAAEC,MAAM,EAAE,MAAM;YAAEY,WAAW,EAAE;UAAE,CAAE;UAC1CL,OAAO,EAAGM,IAAI,IAAK;YACjB,IAAIA,IAAI,CAAClD,GAAG,CAACmD,UAAU,CAAC,4BAA4B,CAAC,EAAE;cACrD/B,eAAe,CAAC8B,IAAI,CAAC;YACvB,CAAC,MAAM;cACL;cACA/E,QAAQ,CAAC+E,IAAI,CAAClD,GAAG,CAAC;YACpB;UACF,CAAE;UACF0B,KAAK,EAAEQ;QAAc;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRxC,OAAA,CAAC5B,MAAM;QAACmG,KAAK,EAAE;UAAEK,OAAO,EAAE;QAAc,CAAE;QAAAV,QAAA,eACxClE,OAAA,CAACE,OAAO;UACNqE,KAAK,EAAE;YACLK,OAAO,EAAE,EAAE;YACXW,MAAM,EAAE,CAAC;YACTC,SAAS,EAAE,GAAG;YACdC,UAAU,EAAE,MAAM;YAClBC,YAAY,EAAE,KAAK;YACnBjB,QAAQ,EAAE;UACZ,CAAE;UAAAP,QAAA,EAED,CAACV,YAAY,gBACZxD,OAAA,CAACH,wBAAwB;YACvBO,IAAI,EAAEA;UAAK;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,gBAEFxC,OAAA,CAAC1B,IAAI;YAACqH,QAAQ,EAAEnF,OAAQ;YAAA0D,QAAA,eACtBlE,OAAA,CAAChC,MAAM;cAAAkG,QAAA,gBACLlE,OAAA,CAAC/B,KAAK;gBAACyE,IAAI,EAAC,GAAG;gBAACkD,OAAO,eAAE5F,OAAA,CAACT,QAAQ;kBAACiE,YAAY,EAAEA,YAAa;kBAACpD,IAAI,EAAEA,IAAK;kBAACyF,aAAa,EAAE,IAAK;kBAACC,SAAS,EAAErF;gBAAW;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7HxC,OAAA,CAAC/B,KAAK;gBAACyE,IAAI,EAAC,WAAW;gBAACkD,OAAO,eAAE5F,OAAA,CAACT,QAAQ;kBAACiE,YAAY,EAAEA,YAAa;kBAACpD,IAAI,EAAEA,IAAK;kBAACyF,aAAa,EAAE,IAAK;kBAACC,SAAS,EAAErF;gBAAW;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrIxC,OAAA,CAAC/B,KAAK;gBAACyE,IAAI,EAAC,YAAY;gBAACkD,OAAO,eAAE5F,OAAA,CAACR,gBAAgB;kBAACgE,YAAY,EAAEA,YAAa;kBAACpD,IAAI,EAAEA,IAAK;kBAACyF,aAAa,EAAE,IAAK;kBAACC,SAAS,EAAErF;gBAAW;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9IxC,OAAA,CAAC/B,KAAK;gBAACyE,IAAI,EAAC,WAAW;gBAACkD,OAAO,eAAE5F,OAAA,CAACP,cAAc;kBAAC+D,YAAY,EAAEA,YAAa;kBAACpD,IAAI,EAAEA,IAAK;kBAACyF,aAAa,EAAE,IAAK;kBAACC,SAAS,EAAErF;gBAAW;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3IxC,OAAA,CAAC/B,KAAK;gBAACyE,IAAI,EAAC,cAAc;gBAACkD,OAAO,eAAE5F,OAAA,CAACN,gBAAgB;kBAAC8D,YAAY,EAAEA,YAAa;kBAACpD,IAAI,EAAEA,IAAK;kBAACyF,aAAa,EAAE,IAAK;kBAACC,SAAS,EAAErF;gBAAW;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChJxC,OAAA,CAAC/B,KAAK;gBAACyE,IAAI,EAAC,gBAAgB;gBAACkD,OAAO,eAAE5F,OAAA,CAACL,YAAY;kBAAC6D,YAAY,EAAEA,YAAa;kBAACpD,IAAI,EAAEA,IAAK;kBAACyF,aAAa,EAAE,IAAK;kBAACC,SAAS,EAAErF;gBAAW;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9IxC,OAAA,CAAC/B,KAAK;gBAACyE,IAAI,EAAC,SAAS;gBAACkD,OAAO,eAAE5F,OAAA,CAACJ,UAAU;kBAAC4D,YAAY,EAAEA,YAAa;kBAACpD,IAAI,EAAEA,IAAK;kBAACyF,aAAa,EAAE,IAAK;kBAACC,SAAS,EAAErF;gBAAW;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACnC,EAAA,CA7TIF,sBAAsB;EAAA,QACTjC,WAAW,EACXC,WAAW;AAAA;AAAA4H,EAAA,GAFxB5F,sBAAsB;AA+T5B,eAAeA,sBAAsB;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}