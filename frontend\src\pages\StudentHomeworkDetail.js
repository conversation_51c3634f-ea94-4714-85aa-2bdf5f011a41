import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Spin,
  message,
  Button,
  Tag,
  Statistic,
  Progress,
  Empty,
  Timeline,
  Descriptions,
  Badge,
  Space,
  Divider,
  Avatar,
  List,
  Pagination
} from 'antd';
import {
  ArrowLeftOutlined,
  BookOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  StarOutlined,
  CalendarOutlined,
  UserOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import api from '../utils/api';

const { Title, Text, Paragraph } = Typography;

// 时间格式化函数 - 移动端友好
const formatTime = (timeString) => {
  const date = new Date(timeString);
  const now = new Date();
  const diffTime = now - date;
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays === 1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
  }
};

const StudentHomeworkDetail = () => {
  const navigate = useNavigate();
  const { studentId } = useParams();
  const location = useLocation();
  const { studentName } = location.state || {};
  
  const [loading, setLoading] = useState(true);
  const [homeworkList, setHomeworkList] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [statistics, setStatistics] = useState(null);

  // 获取作业列表
  const fetchHomework = async (page = 1, limit = 10) => {
    try {
      setLoading(true);
      const response = await api.get(`/parent/student/${studentId}/homework`, {
        params: { page, limit }
      });
      
      if (response && response.success && response.data) {
        const homeworkData = response.data.homework || [];
        setHomeworkList(homeworkData);
        setPagination({
          current: page,
          pageSize: limit,
          total: response.data.pagination?.total || homeworkData.length
        });
      }
    } catch (error) {
      console.error('获取作业列表失败:', error);
      message.error('获取作业列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const response = await api.get(`/parent/student/${studentId}/statistics`);
      if (response && response.success && response.data) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  };

  useEffect(() => {
    if (studentId) {
      fetchHomework();
      fetchStatistics();
    }
  }, [studentId]);

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusMap = {
      'graded': { color: 'success', text: '已批改', icon: <CheckCircleOutlined /> },
      'submitted': { color: 'processing', text: '已提交', icon: <ClockCircleOutlined /> },
      'pending': { color: 'warning', text: '待提交', icon: <ExclamationCircleOutlined /> }
    };
    const config = statusMap[status] || statusMap['pending'];
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 获取分数颜色
  const getScoreColor = (score) => {
    if (score >= 90) return '#52c41a';
    if (score >= 80) return '#1890ff';
    if (score >= 60) return '#faad14';
    return '#ff4d4f';
  };

  // 分页处理
  const handlePageChange = (page, pageSize) => {
    fetchHomework(page, pageSize);
  };

  if (loading && homeworkList.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载中...</div>
      </div>
    );
  }

  return (
    <div className="parent-homework-detail-page" style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 头部导航 */}
      <Card className="homework-header" style={{ marginBottom: 24 }}>
        <Row align="middle" justify="space-between">
          <Col>
            <Space size="large">
              <Button 
                type="text" 
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate('/parent')}
                style={{ fontSize: '16px' }}
              >
                返回
              </Button>
              <div>
                <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
                  <BookOutlined style={{ marginRight: 8 }} />
                  {studentName || '学生'} - 作业详情
                </Title>
                <Text type="secondary">查看学生的所有作业记录和学习情况</Text>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 统计概览 */}
      {statistics && (
        <Row className="stats-row" gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总作业数"
                value={statistics.homework_stats?.total_homework || 0}
                prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="已批改"
                value={statistics.homework_stats?.graded_homework || 0}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="平均分"
                value={Math.round(statistics.homework_stats?.avg_score || 0)}
                suffix="分"
                prefix={<TrophyOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: getScoreColor(statistics.homework_stats?.avg_score || 0) }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="平均正确率"
                value={Math.round((statistics.homework_stats?.avg_accuracy || 0) * 100)}
                suffix="%"
                prefix={<StarOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 作业列表 */}
      <Card 
        title={
          <Space>
            <BookOutlined />
            作业列表
            <Badge count={pagination.total} style={{ backgroundColor: '#1890ff' }} />
          </Space>
        }
        extra={
          <Text type="secondary">
            共 {pagination.total} 份作业
          </Text>
        }
      >
        {homeworkList.length > 0 ? (
          <>
            <List
              className="homework-list"
              dataSource={homeworkList}
              renderItem={(homework, index) => (
                <List.Item
                  style={{
                    padding: '16px 0',
                    borderBottom: index === homeworkList.length - 1 ? 'none' : '1px solid #f0f0f0'
                  }}
                >
                  <Card 
                    style={{ width: '100%' }}
                    bodyStyle={{ padding: '16px' }}
                    hoverable
                  >
                    <Row gutter={[16, 16]}>
                      <Col xs={24} md={16}>
                        <Space direction="vertical" size="small" style={{ width: '100%' }}>
                          <div>
                            <Title level={4} className="homework-item-title" style={{ margin: 0, color: '#1890ff' }}>
                              {homework.assignment_title || homework.title}
                            </Title>
                          </div>

                          {/* 状态标签 - 横向排列 */}
                          <div className="homework-status-tags">
                            {getStatusTag(homework.status)}
                            <Tag color="blue">{homework.subject_name || '未知'}</Tag>
                            <Tag color="green">{homework.class_name || '未知'}</Tag>
                          </div>

                          {/* 时间信息 - 简化显示 */}
                          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                            <div className="homework-time">
                              <CalendarOutlined />
                              <span>提交: {formatTime(homework.created_at)}</span>
                            </div>
                            {homework.graded_at && (
                              <div className="homework-time">
                                <CalendarOutlined />
                                <span>批改: {formatTime(homework.graded_at)}</span>
                              </div>
                            )}
                          </div>
                        </Space>
                      </Col>
                      
                      <Col xs={24} md={8}>
                        <div className="homework-scores">
                          <Row gutter={[8, 8]}>
                            <Col span={12}>
                              <Card size="small" style={{ textAlign: 'center' }}>
                                <Statistic
                                  title="分数"
                                  value={homework.score || 0}
                                  suffix="分"
                                  valueStyle={{
                                    color: getScoreColor(homework.score || 0),
                                    fontSize: '18px'
                                  }}
                                />
                              </Card>
                            </Col>
                            <Col span={12}>
                              <Card size="small" style={{ textAlign: 'center' }}>
                                <Statistic
                                  title="正确率"
                                  value={Math.round((homework.accuracy || 0) * 100)}
                                  suffix="%"
                                  valueStyle={{
                                    color: '#722ed1',
                                    fontSize: '18px'
                                  }}
                                />
                              </Card>
                            </Col>
                          </Row>
                        </div>

                        {homework.homework_comment && (
                          <Card
                            className="homework-comment"
                            size="small"
                            style={{ marginTop: 8 }}
                            title={
                              <Space>
                                <UserOutlined />
                                <Text strong>老师评语</Text>
                              </Space>
                            }
                          >
                            <Paragraph
                              ellipsis={{ rows: 3, expandable: true }}
                              style={{ margin: 0, fontSize: '12px' }}
                            >
                              {homework.homework_comment}
                            </Paragraph>
                          </Card>
                        )}
                      </Col>
                    </Row>
                  </Card>
                </List.Item>
              )}
            />
            
            {pagination.total > pagination.pageSize && (
              <div className="homework-pagination" style={{ textAlign: 'center', marginTop: 24 }}>
                <Pagination
                  current={pagination.current}
                  pageSize={pagination.pageSize}
                  total={pagination.total}
                  onChange={handlePageChange}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) => 
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }
                />
              </div>
            )}
          </>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无作业记录"
          />
        )}
      </Card>
    </div>
  );
};

export default StudentHomeworkDetail;
