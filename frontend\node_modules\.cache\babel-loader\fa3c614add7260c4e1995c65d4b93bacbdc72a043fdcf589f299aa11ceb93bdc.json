{"ast": null, "code": "import React from 'react';\nimport SizeContext from '../SizeContext';\nconst useSize = customSize => {\n  const size = React.useContext(SizeContext);\n  const mergedSize = React.useMemo(() => {\n    if (!customSize) {\n      return size;\n    }\n    if (typeof customSize === 'string') {\n      return customSize !== null && customSize !== void 0 ? customSize : size;\n    }\n    if (typeof customSize === 'function') {\n      return customSize(size);\n    }\n    return size;\n  }, [customSize, size]);\n  return mergedSize;\n};\nexport default useSize;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}