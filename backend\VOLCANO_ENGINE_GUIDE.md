# 火山引擎API配置指南

## 概述

本指南将帮助您正确配置火山引擎API，以便在智教云端智能辅导平台中使用AI助手功能。

## 火山引擎模型信息

火山引擎提供多种大语言模型，常见的有：

1. **豆包系列模型**:
   - Doubao-1.5-vision-pro
   - Doubao-1.5-thinking-vision-pro
   - Doubao-2.0-vision-pro

2. **GLM系列模型**:
   - glm-4
   - glm-3-turbo

每个模型都有特定的模型ID格式，通常为：
- 豆包系列：`ep-xxxxxxxxxx`（如：`ep-20250609181935-f8v88`）
- GLM系列：直接使用模型名称（如：`glm-4`）

## 配置步骤

### 1. 获取火山引擎API密钥和模型ID

1. 注册并登录[火山引擎开放平台](https://www.volcengine.com/)
2. 创建应用并开通AI服务
3. 获取API密钥(Access Key)
4. 获取您所需模型的模型ID

### 2. 配置API端点

火山引擎有多个可用的API端点，根据您的账户类型和地区选择合适的端点：

- `https://api.volcengine.com/v1/chat/completions`（推荐）
- `https://open.volcengineapi.com/v1/chat/completions`
- `https://api.volc.wastai.com/v1/chat/completions`

### 3. 使用配置脚本添加配置

在项目根目录下运行：

```bash
cd backend
python configure_ai.py
```

选择"添加新配置"，然后按照提示输入：
1. 提供商选择"volcano"
2. 输入模型名称（如：Doubao-1.5-vision-pro）
3. 输入模型ID（如：ep-20250609181935-f8v88）
4. 输入API密钥
5. 选择API端点
6. 确认激活此配置

### 4. 测试API连接

您可以使用我们提供的测试脚本直接测试API连接：

```bash
cd backend
python test_volcano_direct.py --api_key 您的API密钥 --model_id 您的模型ID --api_endpoint https://api.volcengine.com/v1/chat/completions
```

## 常见问题排查

### 1. 域名解析错误

如果遇到`getaddrinfo failed`错误，可能是DNS解析问题，尝试：
- 检查网络连接
- 尝试不同的API端点
- 确认防火墙设置

### 2. 认证错误

如果遇到401或403错误，可能是API密钥问题：
- 确认API密钥正确无误
- 检查账户余额和权限
- 确认API密钥未过期

### 3. 模型ID错误

如果遇到模型相关错误：
- 确认模型ID格式正确
- 确认您的账户有权限使用该模型
- 对于豆包系列模型，确保使用`ep-`开头的完整ID

### 4. 请求格式错误

不同模型可能需要不同的请求格式：
- 豆包系列模型可能需要在`parameters`字段中设置温度等参数
- GLM系列模型直接在根级别设置参数

## 示例配置

### 豆包系列模型

```json
{
  "provider": "volcano",
  "model_name": "Doubao-1.5-vision-pro",
  "model_id": "ep-20250609181935-f8v88",
  "api_key": "您的API密钥",
  "api_endpoint": "https://api.volcengine.com/v1/chat/completions"
}
```

### GLM系列模型

```json
{
  "provider": "volcano",
  "model_name": "GLM-4",
  "model_id": "glm-4",
  "api_key": "您的API密钥",
  "api_endpoint": "https://api.volcengine.com/v1/chat/completions"
}
```

## 其他说明

1. 火山引擎API调用是付费服务，请关注您的账户余额
2. 不同模型的API格式可能略有差异，请参考火山引擎官方文档
3. 如果您的网络环境无法访问火山引擎API，可以考虑使用本地Ollama模型 