{"ast": null, "code": "import { decodeNamedCharacterReference } from 'decode-named-character-reference';\nimport { decodeNumericCharacterReference } from 'micromark-util-decode-numeric-character-reference';\nimport { codes, constants } from 'micromark-util-symbol';\nconst characterEscapeOrReference = /\\\\([!-/:-@[-`{-~])|&(#(?:\\d{1,7}|x[\\da-f]{1,6})|[\\da-z]{1,31});/gi;\n\n/**\n * Decode markdown strings (which occur in places such as fenced code info\n * strings, destinations, labels, and titles).\n *\n * The “string” content type allows character escapes and -references.\n * This decodes those.\n *\n * @param {string} value\n *   Value to decode.\n * @returns {string}\n *   Decoded value.\n */\nexport function decodeString(value) {\n  return value.replace(characterEscapeOrReference, decode);\n}\n\n/**\n * @param {string} $0\n *   Match.\n * @param {string} $1\n *   Character escape.\n * @param {string} $2\n *   Character reference.\n * @returns {string}\n *   Decoded value\n */\nfunction decode($0, $1, $2) {\n  if ($1) {\n    // Escape.\n    return $1;\n  }\n\n  // Reference.\n  const head = $2.charCodeAt(0);\n  if (head === codes.numberSign) {\n    const head = $2.charCodeAt(1);\n    const hex = head === codes.lowercaseX || head === codes.uppercaseX;\n    return decodeNumericCharacterReference($2.slice(hex ? 2 : 1), hex ? constants.numericBaseHexadecimal : constants.numericBaseDecimal);\n  }\n  return decodeNamedCharacterReference($2) || $0;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}