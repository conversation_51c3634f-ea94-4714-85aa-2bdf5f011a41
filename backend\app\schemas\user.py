from typing import Optional, List, Any
from pydantic import BaseModel, EmailStr, validator, Field
from datetime import datetime
import re
from fastapi import Form, Body
from enum import Enum

# 定义用户角色枚举
class UserRole(str, Enum):
    # 现有角色
    ADMIN = "admin"
    TEACHER = "teacher"
    STUDENT = "student"
    
    # 新增角色
    SUPER_ADMIN = "super_admin"
    PRINCIPAL = "principal"
    ACADEMIC_DIRECTOR = "academic_director"
    GRADE_DIRECTOR = "grade_director"
    SUBJECT_LEADER = "subject_leader"
    CLASS_TEACHER = "class_teacher"
    TEACHING_ASSISTANT = "teaching_assistant"
    PARENT = "parent"

class UserBase(BaseModel):
    username: str
    email: str  # 改为普通字符串，自己验证
    full_name: Optional[str] = None
    phone: Optional[str] = None
    is_teacher: bool = False
    is_admin: bool = False
    role: Optional[str] = None  # 添加角色字段
    school_id: Optional[int] = None  # 添加学校ID字段
    
    # 自定义邮箱验证器，比pydantic内置的EmailStr更宽松
    @validator('email')
    def validate_email(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('邮箱不能为空')
        
        # 使用简单的正则表达式验证邮箱格式
        email_pattern = re.compile(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$')
        if not email_pattern.match(v):
            raise ValueError('邮箱格式不正确')
        
        return v

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    password: Optional[str] = None
    role: Optional[str] = None  # 添加角色字段
    school_id: Optional[int] = None  # 添加学校ID字段
    
    # 自定义邮箱验证器
    @validator('email')
    def validate_email(cls, v):
        if v is None:
            return v
            
        if not isinstance(v, str):
            raise ValueError('邮箱必须是字符串')
        
        # 使用简单的正则表达式验证邮箱格式
        email_pattern = re.compile(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$')
        if not email_pattern.match(v):
            raise ValueError('邮箱格式不正确')
        
        return v

class User(UserBase):
    id: int
    is_active: bool
    created_at: datetime
    school_name: Optional[str] = None  # 添加学校名称字段
    teaching_subjects: Optional[List[str]] = []  # 添加任教科目字段
    registration_message: Optional[str] = None  # 添加注册消息字段

    model_config = {
        "from_attributes": True
    }

class Token(BaseModel):
    access_token: str
    token_type: str
    user_id: int
    username: str
    is_teacher: bool
    is_admin: bool
    role: Optional[str] = None  # 添加角色字段
    school_id: Optional[int] = None  # 添加学校ID字段
    school_name: Optional[str] = None  # 添加学校名称字段
    full_name: Optional[str] = None  # 添加姓名字段
    email: Optional[str] = None  # 添加邮箱字段
    phone: Optional[str] = None  # 添加电话字段
    created_at: Optional[str] = None  # 添加创建时间字段