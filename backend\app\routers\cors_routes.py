from fastapi import APIRouter, Response, Request
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

# 处理OPTIONS预检请求的通用路由
@router.options("/{path:path}")
async def options_route(path: str, request: Request):
    logger.info(f"处理OPTIONS预检请求: /{path}")
    logger.info(f"请求头: {request.headers}")
    
    # 返回带有CORS头的响应
    response = Response(status_code=200)
    
    # 添加CORS头
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Credentials"] = "true"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD"
    response.headers["Access-Control-Allow-Headers"] = "Authorization, Content-Type, Accept, X-Requested-With, Origin"
    
    logger.info(f"OPTIONS响应头: {response.headers}")
    return response

# 添加一个测试路由
@router.get("/cors-test")
async def cors_test():
    """测试CORS配置是否正确"""
    logger.info("CORS测试路由被调用")
    response = {"message": "CORS测试成功"}
    return response
