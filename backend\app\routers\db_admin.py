from fastapi import APIRouter, Depends, HTTPException, status, Form
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, Any, Optional, List
import logging
import os
import datetime
import sqlite3
from pathlib import Path

from ..database import get_db
from ..models.user import User
from ..routers.auth import get_current_user

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# 验证超级管理员权限
def verify_super_admin(current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        logger.warning(f"非管理员用户 {current_user.username} 尝试访问数据库管理功能")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user

@router.get("/tables")
async def get_all_tables(
    admin: User = Depends(verify_super_admin),
    db: Session = Depends(get_db)
):
    """获取所有数据库表"""
    try:
        # SQLite专用查询获取表列表
        result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"))
        tables = [row[0] for row in result]
        logger.info(f"管理员 {admin.username} 获取了数据库表列表，共 {len(tables)} 个表")
        return {"tables": tables}
    except Exception as e:
        logger.error(f"获取数据库表列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取表列表失败: {str(e)}"
        )

@router.get("/schema/{table}")
async def get_table_schema(
    table: str,
    admin: User = Depends(verify_super_admin),
    db: Session = Depends(get_db)
):
    """获取指定表的结构"""
    try:
        # 检查表是否存在
        table_check = db.execute(
            text("SELECT name FROM sqlite_master WHERE type='table' AND name=:table"),
            {"table": table}
        ).first()
        
        if not table_check:
            logger.warning(f"表 {table} 不存在")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"表 {table} 不存在"
            )
        
        # 获取表结构
        result = db.execute(text(f"PRAGMA table_info({table})"))
        columns = []
        for row in result:
            columns.append({
                "cid": row[0],
                "name": row[1],
                "type": row[2],
                "not_null": row[3],
                "default_value": row[4],
                "pk": row[5]
            })
        
        logger.info(f"管理员 {admin.username} 获取了表 {table} 的结构")
        return {"table": table, "columns": columns}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取表 {table} 结构失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取表结构失败: {str(e)}"
        )

@router.get("/data/{table}")
async def get_table_data(
    table: str,
    limit: int = 100,
    offset: int = 0,
    admin: User = Depends(verify_super_admin),
    db: Session = Depends(get_db)
):
    """获取表数据"""
    try:
        # 检查表是否存在
        table_check = db.execute(
            text("SELECT name FROM sqlite_master WHERE type='table' AND name=:table"),
            {"table": table}
        ).first()
        
        if not table_check:
            logger.warning(f"表 {table} 不存在")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"表 {table} 不存在"
            )
            
        # 获取表结构以确定列名
        columns_result = db.execute(text(f"PRAGMA table_info({table})"))
        columns = [row[1] for row in columns_result]
        
        # 获取表数据
        result = db.execute(text(f"SELECT * FROM {table} LIMIT :limit OFFSET :offset"), {"limit": limit, "offset": offset})
        
        rows = []
        for row in result:
            row_data = {}
            for i, col in enumerate(columns):
                # 处理日期时间类型
                if isinstance(row[i], datetime.datetime):
                    row_data[col] = row[i].isoformat()
                else:
                    row_data[col] = row[i]
            rows.append(row_data)
        
        # 获取表总记录数
        count_result = db.execute(text(f"SELECT COUNT(*) FROM {table}"))
        total_count = count_result.scalar()
        
        logger.info(f"管理员 {admin.username} 获取了表 {table} 的数据，获取 {len(rows)} 条记录")
        return {
            "table": table,
            "columns": columns,
            "rows": rows,
            "total": total_count,
            "limit": limit,
            "offset": offset
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取表 {table} 数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取表数据失败: {str(e)}"
        )

@router.post("/execute")
async def execute_sql(
    sql: str = Form(...),
    admin: User = Depends(verify_super_admin),
    db: Session = Depends(get_db)
):
    """执行SQL查询"""
    try:
        # 检查SQL是否是查询语句
        normalized_sql = sql.strip().lower()
        is_select = normalized_sql.startswith('select') or normalized_sql.startswith('pragma')
        
        # 记录SQL执行
        logger.info(f"管理员 {admin.username} 执行SQL: {sql}")
        
        if is_select:
            # 执行查询
            result = db.execute(text(sql))
            
            if result.returns_rows:
                columns = list(result.keys())
                rows = []
                for row in result:
                    # 兼容Row/元组/对象
                    try:
                        row_dict = {col: getattr(row, col, row[i]) for i, col in enumerate(columns)}
                    except Exception:
                        row_dict = {col: row[i] for i, col in enumerate(columns)}
                    # 处理日期
                    for k, v in row_dict.items():
                        if isinstance(v, datetime.datetime):
                            row_dict[k] = v.isoformat()
                    rows.append(row_dict)
                return {"success": True, "columns": columns, "rows": rows}
            else:
                return {"success": True, "message": "查询执行成功，但没有返回数据"}
        else:
            # 非查询语句，需要额外确认是否为超级管理员
            if not admin.role == "super_admin" and not admin.username == "admin":
                logger.warning(f"用户 {admin.username} 尝试执行非查询SQL但权限不足")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有超级管理员可以执行非查询SQL"
                )
            
            # 执行修改语句
            result = db.execute(text(sql))
            db.commit()
            
            return {"success": True, "affected_rows": result.rowcount, "message": f"执行成功，影响 {result.rowcount} 行"}
    except Exception as e:
        db.rollback()
        logger.error(f"执行SQL失败: {str(e)}")
        return {"success": False, "error": str(e)}

@router.get("/stats")
async def get_database_stats(
    admin: User = Depends(verify_super_admin),
    db: Session = Depends(get_db)
):
    """获取数据库统计信息"""
    try:
        # 获取所有表
        tables_result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"))
        tables = [row[0] for row in tables_result]
        
        stats = []
        for table in tables:
            # 获取表记录数
            count_result = db.execute(text(f"SELECT COUNT(*) FROM {table}"))
            record_count = count_result.scalar()
            
            # 获取表结构
            columns_result = db.execute(text(f"PRAGMA table_info({table})"))
            column_count = len([row for row in columns_result])
            
            stats.append({
                "table_name": table,
                "record_count": record_count,
                "column_count": column_count
            })
        
        # 获取数据库文件大小
        db_path = db.bind.engine.url.database
        if db_path and os.path.exists(db_path):
            file_size = os.path.getsize(db_path)
            size_mb = round(file_size / (1024 * 1024), 2)
        else:
            size_mb = None
            
        logger.info(f"管理员 {admin.username} 获取了数据库统计信息")
        return {
            "table_count": len(tables),
            "tables": stats,
            "db_size_mb": size_mb,
            "db_path": db_path
        }
    except Exception as e:
        logger.error(f"获取数据库统计信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取数据库统计信息失败: {str(e)}"
        )

@router.post("/backup")
async def create_database_backup(
    admin: User = Depends(verify_super_admin),
    db: Session = Depends(get_db)
):
    """创建数据库备份"""
    try:
        # 获取数据库文件路径
        db_path = db.bind.engine.url.database
        if not db_path or not os.path.exists(db_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="数据库文件不存在"
            )
        
        # 创建备份文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        db_filename = os.path.basename(db_path)
        backup_filename = f"{os.path.splitext(db_filename)[0]}_backup_{timestamp}.db"
        backup_dir = os.path.dirname(db_path)
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # 创建备份
        db.commit()  # 确保所有事务完成
        conn = sqlite3.connect(db_path)
        backup_conn = sqlite3.connect(backup_path)
        conn.backup(backup_conn)
        backup_conn.close()
        conn.close()
        
        # 获取备份文件信息
        backup_size = os.path.getsize(backup_path)
        backup_size_mb = round(backup_size / (1024 * 1024), 2)
        
        logger.info(f"管理员 {admin.username} 创建了数据库备份: {backup_filename}")
        return {
            "success": True,
            "backup_filename": backup_filename,
            "backup_path": backup_path,
            "backup_size_mb": backup_size_mb,
            "timestamp": timestamp
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建数据库备份失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建数据库备份失败: {str(e)}"
        )

@router.get("/backups")
async def list_database_backups(
    admin: User = Depends(verify_super_admin),
    db: Session = Depends(get_db)
):
    """列出数据库备份"""
    try:
        # 获取数据库文件路径
        db_path = db.bind.engine.url.database
        if not db_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="数据库文件不存在"
            )
        
        # 获取备份目录
        backup_dir = os.path.dirname(db_path)
        db_name = os.path.splitext(os.path.basename(db_path))[0]
        
        # 查找备份文件
        backups = []
        for file in os.listdir(backup_dir):
            if file.startswith(f"{db_name}_backup_") and file.endswith(".db"):
                file_path = os.path.join(backup_dir, file)
                file_size = os.path.getsize(file_path)
                file_date = datetime.datetime.fromtimestamp(os.path.getctime(file_path))
                
                backups.append({
                    "filename": file,
                    "path": file_path,
                    "size_bytes": file_size,
                    "size_mb": round(file_size / (1024 * 1024), 2),
                    "created_at": file_date.isoformat()
                })
        
        # 按创建时间排序
        backups.sort(key=lambda x: x["created_at"], reverse=True)
        
        logger.info(f"管理员 {admin.username} 获取了数据库备份列表，共 {len(backups)} 个备份")
        return {"backups": backups}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据库备份列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取数据库备份列表失败: {str(e)}"
        ) 