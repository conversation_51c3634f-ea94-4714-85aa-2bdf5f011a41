{"ast": null, "code": "import ResizeObserver from 'resize-observer-polyfill';\n// =============================== Const ===============================\nvar elementListeners = new Map();\nfunction onResize(entities) {\n  entities.forEach(function (entity) {\n    var _elementListeners$get;\n    var target = entity.target;\n    (_elementListeners$get = elementListeners.get(target)) === null || _elementListeners$get === void 0 || _elementListeners$get.forEach(function (listener) {\n      return listener(target);\n    });\n  });\n}\n\n// Note: ResizeObserver polyfill not support option to measure border-box resize\nvar resizeObserver = new ResizeObserver(onResize);\n\n// Dev env only\nexport var _el = process.env.NODE_ENV !== 'production' ? elementListeners : null; // eslint-disable-line\nexport var _rs = process.env.NODE_ENV !== 'production' ? onResize : null; // eslint-disable-line\n\n// ============================== Observe ==============================\nexport function observe(element, callback) {\n  if (!elementListeners.has(element)) {\n    elementListeners.set(element, new Set());\n    resizeObserver.observe(element);\n  }\n  elementListeners.get(element).add(callback);\n}\nexport function unobserve(element, callback) {\n  if (elementListeners.has(element)) {\n    elementListeners.get(element).delete(callback);\n    if (!elementListeners.get(element).size) {\n      resizeObserver.unobserve(element);\n      elementListeners.delete(element);\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}