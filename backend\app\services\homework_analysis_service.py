# -*- coding: utf-8 -*-
"""
作业分析服务类
提供作业分析的核心业务逻辑
"""

from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc, asc
from typing import List, Dict, Any, Optional
import json
from datetime import datetime
import logging

from ..models.homework import (
    HomeworkAssignment, Homework, HomeworkCorrection, WrongQuestion,
    HomeworkAnalysis, StudentPerformanceAnalysis, QuestionAnalysis
)
from ..models.user import User, Class, ClassStudent
from ..models.school import School

logger = logging.getLogger(__name__)

class HomeworkAnalysisService:
    """作业分析服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_homework_overview(self, assignment_id: int) -> Dict[str, Any]:
        """获取作业概览数据"""
        try:
            # 获取作业基本信息
            assignment = self.db.query(HomeworkAssignment).filter(
                HomeworkAssignment.id == assignment_id
            ).first()
            
            if not assignment:
                raise ValueError("作业不存在")
            
            # 获取班级信息
            class_info = self.db.query(Class).filter(
                Class.id == assignment.class_id
            ).first()

            # 获取班级总学生数（正确的统计方式）
            from ..models.user import ClassStudent
            total_students = self.db.query(ClassStudent).filter(
                ClassStudent.class_id == assignment.class_id
            ).count() if assignment.class_id else 0

            # 获取所有学生作业
            homeworks = self.db.query(Homework).filter(
                Homework.assignment_id == assignment_id
            ).all()

            # 计算已提交作业的学生数量
            submitted_homeworks = [hw for hw in homeworks if hw.status == "graded"]
            submitted_count = len(submitted_homeworks)
            
            # 计算分数统计
            scores = [hw.score for hw in submitted_homeworks if hw.score is not None]
            
            if scores:
                average_score = sum(scores) / len(scores)
                max_score = max(scores)
                min_score = min(scores)
            else:
                average_score = max_score = min_score = 0
            
            # 计算分数段分布
            excellent_count = len([s for s in scores if s >= 90])
            good_count = len([s for s in scores if 80 <= s < 90])
            pass_count = len([s for s in scores if 60 <= s < 80])
            fail_count = len([s for s in scores if s < 60])
            
            # 获取优秀学生排行榜（前10名）
            top_students = self.db.query(
                Homework, User.full_name
            ).join(User, Homework.student_id == User.id).filter(
                Homework.assignment_id == assignment_id,
                Homework.status == "graded",
                Homework.score.isnot(None)
            ).order_by(desc(Homework.score)).limit(10).all()
            
            top_students_list = [
                {
                    "student_id": hw.student_id,
                    "student_name": name or f"学生{hw.student_id}",
                    "score": hw.score,
                    "rank": idx + 1
                }
                for idx, (hw, name) in enumerate(top_students)
            ]
            
            # 获取未提交学生名单
            submitted_student_ids = [hw.student_id for hw in submitted_homeworks]

            # 根据班级学生关系表获取未提交学生
            unsubmitted_students = []
            if assignment.class_id:
                # 获取班级所有学生
                class_students = self.db.query(ClassStudent, User).join(
                    User, ClassStudent.student_id == User.id
                ).filter(
                    ClassStudent.class_id == assignment.class_id
                ).all()

                # 筛选出未提交作业的学生
                for class_student, user in class_students:
                    if user.id not in submitted_student_ids:
                        unsubmitted_students.append({
                            "student_id": user.id,
                            "student_name": user.full_name or user.username,
                            "username": user.username
                        })
            
            # 构建返回数据
            overview_data = {
                "assignment_info": {
                    "id": assignment.id,
                    "title": assignment.title,
                    "class_name": class_info.name if class_info else "未知班级",
                    "created_at": assignment.created_at.isoformat() if assignment.created_at else None
                },
                "key_metrics": {
                    "average_score": round(average_score, 1),
                    "max_score": max_score,
                    "min_score": min_score,
                    "submitted_count": submitted_count,
                    "total_students": total_students,
                    "unsubmitted_count": total_students - submitted_count,
                    "submission_rate": round((submitted_count / total_students * 100), 1) if total_students > 0 else 0
                },
                "score_distribution": {
                    "excellent": excellent_count,  # 90-100分
                    "good": good_count,           # 80-89分
                    "pass": pass_count,           # 60-79分
                    "fail": fail_count            # 0-59分
                },
                "top_students": top_students_list,
                "unsubmitted_students": unsubmitted_students,
                "quick_actions": {
                    "excellent_count": excellent_count,
                    "typical_answers": min(15, submitted_count),
                    "wrong_analysis": min(8, submitted_count),
                    "unsubmitted_count": total_students - submitted_count
                }
            }
            
            return overview_data
            
        except Exception as e:
            logger.error(f"获取作业概览失败: {str(e)}")
            raise
    
    async def get_question_analysis(self, assignment_id: int) -> Dict[str, Any]:
        """获取逐题分析数据"""

        try:
            # 获取该作业的所有批改数据，同时获取学生信息
            corrections = self.db.query(HomeworkCorrection, User.full_name, User.username).join(
                Homework, HomeworkCorrection.homework_id == Homework.id
            ).join(
                User, Homework.student_id == User.id
            ).filter(
                Homework.assignment_id == assignment_id
            ).all()

            # 解析批改数据，统计每题情况
            question_stats = {}

            for correction, full_name, username in corrections:
                if correction.correction_data:
                    try:
                        correction_json = json.loads(correction.correction_data)
                        questions = correction_json.get("questions", [])

                        # 确定学生姓名
                        student_name = full_name or username or "未知学生"

                        for question in questions:
                            q_num = question.get("question_number")
                            if q_num:
                                if q_num not in question_stats:
                                    question_stats[q_num] = {
                                        "question_number": q_num,
                                        "question_type": question.get("question_type", ""),
                                        "question_content": question.get("question_content", ""),
                                        "correct_answer": question.get("correct_answer", ""),
                                        "total_answers": 0,
                                        "correct_count": 0,
                                        "student_answers": []
                                    }

                                question_stats[q_num]["total_answers"] += 1
                                if question.get("is_correct"):
                                    question_stats[q_num]["correct_count"] += 1

                                answer_data = {
                                    "student_name": student_name,
                                    "student_answer": question.get("student_answer", ""),
                                    "is_correct": question.get("is_correct", False)
                                }

                                question_stats[q_num]["student_answers"].append(answer_data)
                    except json.JSONDecodeError:
                        continue
            
            # 计算正确率并分类
            questions_list = []
            for q_num, stats in question_stats.items():
                if stats["total_answers"] > 0:
                    accuracy_rate = stats["correct_count"] / stats["total_answers"]
                    
                    # 确定状态
                    if accuracy_rate >= 0.8:
                        status = "excellent"  # 绿色
                    elif accuracy_rate >= 0.6:
                        status = "warning"    # 黄色
                    else:
                        status = "error"      # 红色
                    
                    questions_list.append({
                        "question_number": q_num,
                        "question_type": stats["question_type"],
                        "question_content": stats["question_content"],
                        "correct_answer": stats["correct_answer"],
                        "accuracy_rate": round(accuracy_rate * 100, 1),
                        "status": status,
                        "total_answers": stats["total_answers"],
                        "correct_count": stats["correct_count"],
                        "student_answers": stats["student_answers"]
                    })
            
            # 按题号排序
            questions_list.sort(key=lambda x: int(x["question_number"]) if x["question_number"].isdigit() else 999)
            
            return {
                "questions": questions_list,
                "summary": {
                    "total_questions": len(questions_list),
                    "excellent_questions": len([q for q in questions_list if q["status"] == "excellent"]),
                    "warning_questions": len([q for q in questions_list if q["status"] == "warning"]),
                    "error_questions": len([q for q in questions_list if q["status"] == "error"])
                }
            }
            
        except Exception as e:
            logger.error(f"获取题目分析失败: {str(e)}")
            raise
    
    async def get_question_detail(self, assignment_id: int, question_number: int) -> Dict[str, Any]:
        """获取单题详细分析"""
        try:
            # 获取该题的详细数据
            questions_data = await self.get_question_analysis(assignment_id)
            
            target_question = None
            for question in questions_data["questions"]:
                if str(question["question_number"]) == str(question_number):
                    target_question = question
                    break
            
            if not target_question:
                raise ValueError(f"题目 {question_number} 不存在")
            
            # 统计选项分布（如果是选择题）
            option_distribution = {}
            for answer in target_question["student_answers"]:
                student_answer = answer["student_answer"].strip().upper()
                if student_answer:
                    option_distribution[student_answer] = option_distribution.get(student_answer, 0) + 1
            
            # 构建详细数据
            detail_data = {
                **target_question,
                "option_distribution": option_distribution,
                "analysis": {
                    "difficulty_assessment": self._assess_difficulty(target_question["accuracy_rate"]),
                    "common_errors": self._analyze_common_errors(target_question["student_answers"]),
                    "teaching_suggestions": self._generate_teaching_suggestions(target_question)
                }
            }
            
            return detail_data
            
        except Exception as e:
            logger.error(f"获取题目详情失败: {str(e)}")
            raise
    
    def _assess_difficulty(self, accuracy_rate: float) -> str:
        """评估题目难度"""
        if accuracy_rate >= 80:
            return "简单"
        elif accuracy_rate >= 60:
            return "中等"
        else:
            return "困难"
    
    def _analyze_common_errors(self, student_answers: List[Dict]) -> List[str]:
        """分析常见错误"""
        errors = []
        wrong_answers = [ans for ans in student_answers if not ans["is_correct"]]
        
        if len(wrong_answers) > 0:
            errors.append(f"共有 {len(wrong_answers)} 名学生答错")
            
            # 统计错误答案分布
            error_distribution = {}
            for ans in wrong_answers:
                answer = ans["student_answer"].strip()
                if answer:
                    error_distribution[answer] = error_distribution.get(answer, 0) + 1
            
            # 找出最常见的错误答案
            if error_distribution:
                most_common_error = max(error_distribution.items(), key=lambda x: x[1])
                errors.append(f"最常见错误答案: {most_common_error[0]} ({most_common_error[1]}人)")
        
        return errors
    
    def _generate_teaching_suggestions(self, question_data: Dict) -> List[str]:
        """生成教学建议"""
        suggestions = []
        accuracy_rate = question_data["accuracy_rate"]
        
        if accuracy_rate < 60:
            suggestions.append("建议重点讲解此题，分配15-20分钟时间")
            suggestions.append("需要重新讲解相关概念和知识点")
            suggestions.append("建议课后个别辅导错误较多的学生")
        elif accuracy_rate < 80:
            suggestions.append("建议适当讲解此题，分配8-10分钟时间")
            suggestions.append("重点强调易错点和解题技巧")
        else:
            suggestions.append("此题掌握较好，可简单回顾")
            suggestions.append("可作为示例题展示给其他班级")
        
        return suggestions

    async def get_student_details(self, assignment_id: int, sort_by: str = "score",
                                order: str = "desc", filter_status: str = "all") -> Dict[str, Any]:
        """获取学生详情列表"""
        try:
            # 基础查询 - 获取每个学生最新的作业记录
            # 首先找到每个学生最新的作业ID
            latest_homework_subquery = self.db.query(
                Homework.student_id,
                func.max(Homework.created_at).label('latest_created_at')
            ).filter(
                Homework.assignment_id == assignment_id
            ).group_by(Homework.student_id).subquery()

            # 然后根据学生ID和最新提交时间获取完整的作业记录
            query = self.db.query(
                Homework, User.full_name, User.id
            ).join(User, Homework.student_id == User.id).join(
                latest_homework_subquery,
                and_(
                    Homework.student_id == latest_homework_subquery.c.student_id,
                    Homework.created_at == latest_homework_subquery.c.latest_created_at
                )
            ).filter(
                Homework.assignment_id == assignment_id
            )

            # 状态筛选
            if filter_status == "submitted":
                query = query.filter(Homework.status == "graded")
            elif filter_status == "not_submitted":
                query = query.filter(Homework.status != "graded")

            # 排序
            if sort_by == "score":
                if order == "desc":
                    query = query.order_by(desc(Homework.score))
                else:
                    query = query.order_by(asc(Homework.score))
            elif sort_by == "name":
                if order == "desc":
                    query = query.order_by(desc(User.full_name))
                else:
                    query = query.order_by(asc(User.full_name))
            elif sort_by == "submit_time":
                if order == "desc":
                    query = query.order_by(desc(Homework.created_at))
                else:
                    query = query.order_by(asc(Homework.created_at))

            results = query.all()

            # 构建学生列表
            students_list = []
            for idx, (homework, student_name, student_id) in enumerate(results):
                # 计算主观题和客观题分数
                objective_score, subjective_score = self._calculate_question_scores(homework.id)

                # 获取错误题号和错误分析（快速）
                wrong_questions = self._get_wrong_question_numbers(homework.id)
                error_analysis = self._get_error_analysis(homework.id)

                # 智能获取作业点评：优先从数据库读取，没有则显示生成中
                homework_comment = self._get_or_set_homework_comment(homework)

                # 确保所有分数都是整数，不是None
                total_score = round(homework.score) if homework.score else 0
                objective_score = int(objective_score) if objective_score is not None else 0
                subjective_score = int(subjective_score) if subjective_score is not None else 0

                students_list.append({
                    "rank": idx + 1,
                    "homework_id": homework.id,  # 添加homework_id字段
                    "student_id": student_id,
                    "student_name": student_name or f"学生{student_id}",
                    "total_score": total_score,
                    "objective_score": objective_score,
                    "subjective_score": subjective_score,
                    "submit_status": "已提交" if homework.status == "graded" else "未提交",
                    "submit_time": homework.created_at.strftime("%m月%d日%H:%M") if homework.created_at else None,
                    "accuracy_rate": homework.accuracy or 0,
                    "wrong_questions": wrong_questions,  # 错误题号列表
                    "error_analysis": error_analysis,   # 错误分析
                    "homework_comment": homework_comment  # 作业点评
                })

            # 获取正确的班级统计数据
            # 首先获取作业任务信息
            assignment = self.db.query(HomeworkAssignment).filter(
                HomeworkAssignment.id == assignment_id
            ).first()

            # 获取班级总学生数
            total_students = 0
            if assignment and assignment.class_id:
                total_students = self.db.query(ClassStudent).filter(
                    ClassStudent.class_id == assignment.class_id
                ).count()

            submitted_students = len([s for s in students_list if s["submit_status"] == "已提交"])
            scores = [s["total_score"] for s in students_list if s["total_score"] > 0]

            class_statistics = {
                "total_students": total_students,
                "submitted_count": submitted_students,
                "unsubmitted_count": total_students - submitted_students,
                "submission_rate": round((submitted_students / total_students * 100), 1) if total_students > 0 else 0,
                "average_score": round(sum(scores) / len(scores), 1) if scores else 0,
                "max_score": max(scores) if scores else 0
            }

            return {
                "students": students_list,
                "statistics": class_statistics,
                "filters": {
                    "sort_by": sort_by,
                    "order": order,
                    "filter_status": filter_status
                }
            }

        except Exception as e:
            logger.error(f"获取学生详情失败: {str(e)}")
            raise

    def _calculate_question_scores(self, homework_id: int) -> tuple:
        """
        根据批改数据计算主观题和客观题分数
        系统按正确率计算分数：比如10题对8题，总分就是80分
        如果作业中没有某类型题目，该类型分数记为0
        返回: (objective_score, subjective_score)
        """
        try:
            # 获取批改数据
            correction = self.db.query(HomeworkCorrection).filter(
                HomeworkCorrection.homework_id == homework_id
            ).first()

            if not correction or not correction.correction_data:
                return (0, 0)

            # 解析批改数据
            correction_data = json.loads(correction.correction_data)
            questions = correction_data.get('questions', [])

            if not questions:
                return (0, 0)

            # 定义题目类型分类
            # 客观题：有固定选项，学生选择答案
            objective_types = ['选择题', '单选题', '多选题', '判断题']
            # 主观题：需要学生自己组织答案，包括填空题
            subjective_types = ['填空题', '解答题', '计算题', '应用题', '证明题', '作文题', '简答题', '论述题']

            # 统计各类型题目的正确数和总数
            objective_correct = 0
            objective_total = 0
            subjective_correct = 0
            subjective_total = 0

            for question in questions:
                question_type = question.get('question_type', '')
                is_correct = question.get('is_correct', False)

                if question_type in objective_types:
                    objective_total += 1
                    if is_correct:
                        objective_correct += 1
                elif question_type in subjective_types:
                    subjective_total += 1
                    if is_correct:
                        subjective_correct += 1
                else:
                    # 未知类型，默认归类为主观题（因为大多数未知类型都是主观性质）
                    subjective_total += 1
                    if is_correct:
                        subjective_correct += 1

            # 按正确率计算分数（满分100分），四舍五入为整数
            # 如果没有该类型题目，分数记为0而不是None
            objective_score = round((objective_correct / objective_total * 100)) if objective_total > 0 else 0
            subjective_score = round((subjective_correct / subjective_total * 100)) if subjective_total > 0 else 0

            # 确保返回的分数都是整数，不是None
            objective_score = int(objective_score) if objective_score is not None else 0
            subjective_score = int(subjective_score) if subjective_score is not None else 0

            return (objective_score, subjective_score)

        except Exception as e:
            logger.error(f"计算题目分数失败: {str(e)}")
            return (0, 0)

    async def _get_student_detailed_info(self, homework_id: int, student_id: int) -> tuple:
        """
        获取学生详细信息：错误题号、错误分析、作业点评
        返回: (wrong_questions, error_analysis, homework_comment)
        """
        try:
            # 1. 获取错误题号
            wrong_questions = self._get_wrong_question_numbers(homework_id)

            # 2. 获取错误分析（从批改结果数据中提取）
            error_analysis = self._get_error_analysis(homework_id)

            # 3. 生成作业点评（调用AI模型）
            homework_comment = await self._generate_homework_comment(homework_id, student_id)

            return (wrong_questions, error_analysis, homework_comment)

        except Exception as e:
            logger.error(f"获取学生详细信息失败: {str(e)}")
            return ([], "暂无错误分析", "暂无作业点评")

    def _get_wrong_question_numbers(self, homework_id: int) -> List[str]:
        """获取错误题号列表"""
        try:
            # 获取批改数据
            correction = self.db.query(HomeworkCorrection).filter(
                HomeworkCorrection.homework_id == homework_id
            ).first()

            if not correction or not correction.correction_data:
                return []

            # 解析批改数据
            correction_data = json.loads(correction.correction_data)
            questions = correction_data.get('questions', [])

            wrong_questions = []
            for question in questions:
                is_correct = question.get('is_correct', True)
                if not is_correct:
                    question_number = question.get('question_number', '')
                    if question_number:
                        wrong_questions.append(str(question_number))

            return wrong_questions

        except Exception as e:
            logger.error(f"获取错误题号失败: {str(e)}")
            return []

    def _get_error_analysis(self, homework_id: int) -> str:
        """获取错误分析（从批改结果数据中提取）"""
        try:
            # 获取批改数据
            correction = self.db.query(HomeworkCorrection).filter(
                HomeworkCorrection.homework_id == homework_id
            ).first()

            if not correction or not correction.correction_data:
                return "暂无错误分析"

            # 解析批改数据
            correction_data = json.loads(correction.correction_data)
            questions = correction_data.get('questions', [])

            # 收集所有错题的分析
            error_analyses = []
            for question in questions:
                is_correct = question.get('is_correct', True)
                if not is_correct:
                    question_number = question.get('question_number', '')
                    analysis = question.get('analysis', '')
                    if analysis:
                        error_analyses.append(f"第{question_number}题：{analysis}")

            if error_analyses:
                return "; ".join(error_analyses)
            else:
                return "暂无错误分析"

        except Exception as e:
            logger.error(f"获取错误分析失败: {str(e)}")
            return "暂无错误分析"

    async def _generate_homework_comment(self, homework_id: int, student_id: int) -> str:
        """生成作业点评（调用本地默认大模型）"""
        try:
            # 获取学生信息
            student = self.db.query(User).filter(User.id == student_id).first()
            student_name = student.full_name if student else f"学生{student_id}"

            # 获取作业信息
            homework = self.db.query(Homework).filter(Homework.id == homework_id).first()
            if not homework:
                return "暂无作业点评"

            # 获取作业任务信息
            assignment = self.db.query(HomeworkAssignment).filter(
                HomeworkAssignment.id == homework.assignment_id
            ).first()
            assignment_title = assignment.title if assignment else "未知作业"

            # 获取批改数据
            correction = self.db.query(HomeworkCorrection).filter(
                HomeworkCorrection.homework_id == homework_id
            ).first()

            if not correction or not correction.correction_data:
                return "暂无作业点评"

            # 解析批改数据
            correction_data = json.loads(correction.correction_data)
            questions = correction_data.get('questions', [])

            # 统计作业表现
            total_questions = len(questions)
            correct_count = sum(1 for q in questions if q.get('is_correct', False))
            accuracy_rate = (correct_count / total_questions * 100) if total_questions > 0 else 0

            # 收集错题信息
            wrong_questions_info = []
            for question in questions:
                if not question.get('is_correct', True):
                    wrong_info = {
                        'number': question.get('question_number', ''),
                        'type': question.get('question_type', ''),
                        'content': question.get('question_content', '')[:50] + '...' if len(question.get('question_content', '')) > 50 else question.get('question_content', ''),
                        'analysis': question.get('analysis', '')
                    }
                    wrong_questions_info.append(wrong_info)

            # 构建AI提示词
            prompt = self._build_comment_prompt(
                student_name, assignment_title, total_questions,
                correct_count, accuracy_rate, wrong_questions_info
            )

            # 调用AI生成点评 - 使用配置的作业点评AI模型
            comment = await self._call_ai_with_config(prompt, "homework_comment")

            if comment and comment != "暂无作业点评":
                return comment
            else:
                return self._generate_default_comment(student_name, accuracy_rate, total_questions, correct_count)

        except Exception as e:
            logger.error(f"生成作业点评失败: {str(e)}")
            return "暂无作业点评"

    async def _call_ai_with_config(self, prompt: str, usage_type: str) -> str:
        """使用AI配置调用模型生成内容"""
        try:
            # 导入AI服务
            from ..services.ai_service import get_ai_config_by_usage

            # 获取指定用途的AI配置
            config = get_ai_config_by_usage(self.db, usage_type)

            if not config:
                logger.warning(f"未找到{usage_type}的AI配置，使用默认方式")
                return await self._call_deepseek_direct(prompt)

            # 根据提供商调用不同的API
            if config.provider == "ollama":
                return await self._call_ollama_api(prompt, config)
            else:
                logger.warning(f"不支持的AI提供商: {config.provider}，使用默认方式")
                return await self._call_deepseek_direct(prompt)

        except Exception as e:
            logger.error(f"使用AI配置调用失败: {str(e)}，使用默认方式")
            return await self._call_deepseek_direct(prompt)

    async def _call_ollama_api(self, prompt: str, config) -> str:
        """调用Ollama API"""
        try:
            import httpx

            payload = {
                "model": config.model_id or "deepseek-r1:8b",
                "prompt": prompt,
                "stream": False
            }

            api_endpoint = config.api_endpoint or "http://localhost:11434"
            url = f"{api_endpoint}/api/generate"

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=60
                )

                if response.status_code == 200:
                    result = response.json()
                    comment = result.get('response', '')

                    # 清理deepseek的思考过程标记
                    if '<think>' in comment and '</think>' in comment:
                        # 提取</think>之后的内容
                        parts = comment.split('</think>')
                        if len(parts) > 1:
                            comment = parts[-1].strip()

                    return comment if comment else "暂无作业点评"
                else:
                    logger.error(f"调用Ollama API失败: {response.status_code}")
                    return "暂无作业点评"

        except Exception as e:
            logger.error(f"调用Ollama API异常: {str(e)}")
            return "暂无作业点评"

    async def _call_deepseek_direct(self, prompt: str) -> str:
        """直接调用deepseek模型生成作业点评"""
        try:
            import httpx

            payload = {
                "model": "deepseek-r1:8b",
                "prompt": prompt,
                "stream": False
            }

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "http://localhost:11434/api/generate",
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=60
                )

                if response.status_code == 200:
                    result = response.json()
                    comment = result.get('response', '')

                    # 清理deepseek的思考过程标记
                    if '<think>' in comment and '</think>' in comment:
                        # 提取</think>之后的内容
                        parts = comment.split('</think>')
                        if len(parts) > 1:
                            comment = parts[-1].strip()

                    return comment if comment else "暂无作业点评"
                else:
                    logger.error(f"直接调用deepseek失败: {response.status_code}")
                    return "暂无作业点评"

        except Exception as e:
            logger.error(f"直接调用deepseek异常: {str(e)}")
            return "暂无作业点评"

    def _get_or_set_homework_comment(self, homework: Homework) -> str:
        """智能获取作业点评：优先从数据库读取，没有则返回生成中状态"""
        try:
            # 1. 如果数据库中已有点评，直接返回
            if homework.homework_comment:
                logger.info(f"从数据库读取作业{homework.id}的点评")
                return homework.homework_comment

            # 2. 如果作业已批改但没有点评，返回生成中状态
            if homework.status == "graded":
                logger.info(f"作业{homework.id}已批改但无点评，返回生成中状态")
                return "AI作业点评生成中..."

            # 3. 如果作业未批改，返回暂无点评
            return "暂无作业点评"

        except Exception as e:
            logger.error(f"获取作业点评失败: {str(e)}")
            return "暂无作业点评"

    async def _generate_and_save_homework_comment(self, homework_id: int, student_id: int) -> str:
        """生成并保存作业点评到数据库"""
        try:
            # 生成作业点评
            comment = await self._generate_homework_comment(homework_id, student_id)

            if comment and comment != "暂无作业点评":
                # 保存到数据库
                homework = self.db.query(Homework).filter(Homework.id == homework_id).first()
                if homework:
                    homework.homework_comment = comment
                    self.db.commit()
                    logger.info(f"成功保存作业{homework_id}的点评到数据库")
                    return comment

            return comment

        except Exception as e:
            logger.error(f"生成并保存作业点评失败: {str(e)}")
            return "暂无作业点评"

    async def auto_generate_comment_after_grading(self, homework_id: int) -> bool:
        """批改完成后自动生成作业点评"""
        try:
            homework = self.db.query(Homework).filter(Homework.id == homework_id).first()
            if not homework:
                logger.warning(f"作业{homework_id}不存在")
                return False

            # 只有已批改且没有点评的作业才自动生成
            if homework.status == "graded" and not homework.homework_comment:
                logger.info(f"自动为作业{homework_id}生成点评")
                comment = await self._generate_and_save_homework_comment(homework_id, homework.student_id)
                return comment and comment != "暂无作业点评"

            return True

        except Exception as e:
            logger.error(f"自动生成作业点评失败: {str(e)}")
            return False

    async def export_student_details(self, assignment_id: int, export_format: str, export_fields: list, filters: dict = None):
        """导出学生详情列表"""
        try:
            # 获取学生详情数据
            result = await self.get_student_details(assignment_id)
            students_data = result['students']

            # 应用筛选条件
            if filters:
                students_data = self._apply_export_filters(students_data, filters)

            # 根据格式导出
            if export_format == 'excel':
                return await self._export_to_excel(students_data, export_fields, assignment_id)
            elif export_format == 'pdf':
                return await self._export_to_pdf(students_data, export_fields, assignment_id)
            else:
                raise Exception(f"不支持的导出格式: {export_format}")

        except Exception as e:
            logger.error(f"导出学生详情失败: {str(e)}")
            raise

    def _apply_export_filters(self, students_data: list, filters: dict) -> list:
        """应用导出筛选条件"""
        filtered_data = students_data

        # 根据提交状态筛选
        if filters.get('submit_status'):
            status_filter = filters['submit_status']
            if status_filter == 'submitted':
                filtered_data = [s for s in filtered_data if s['submit_status'] == '已提交']
            elif status_filter == 'unsubmitted':
                filtered_data = [s for s in filtered_data if s['submit_status'] == '未提交']

        # 根据分数范围筛选
        if filters.get('score_range'):
            min_score = filters['score_range'].get('min', 0)
            max_score = filters['score_range'].get('max', 100)
            filtered_data = [s for s in filtered_data if min_score <= s['total_score'] <= max_score]

        return filtered_data

    async def _export_to_excel(self, students_data: list, export_fields: list, assignment_id: int):
        """导出为Excel格式"""
        try:
            from openpyxl import Workbook
            from io import BytesIO

            # 字段映射
            field_mapping = {
                'student_name': '学生姓名',
                'total_score': '总分',
                'objective_score': '客观题分数',
                'subjective_score': '主观题分数',
                'accuracy_rate': '准确率(%)',
                'submit_status': '提交状态',
                'submit_time': '提交时间',
                'wrong_questions': '错误题号',
                'error_analysis': '错误分析',
                'homework_comment': '作业点评'
            }

            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "学生详情"

            # 写入表头
            headers = [field_mapping[field] for field in export_fields if field in field_mapping]
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # 写入数据
            for row_idx, student in enumerate(students_data, 2):
                for col_idx, field in enumerate(export_fields, 1):
                    if field in field_mapping:
                        value = student.get(field, '')

                        # 特殊处理某些字段
                        if field == 'accuracy_rate':
                            if isinstance(value, (int, float)):
                                value = f"{value * 100:.1f}%"
                            else:
                                value = str(value)
                        elif field == 'wrong_questions' and isinstance(value, list):
                            value = ', '.join(map(str, value)) if value else '无'
                        elif field in ['error_analysis', 'homework_comment']:
                            # 清理文本内容
                            if isinstance(value, str):
                                # 强力清理字符串
                                import re
                                # 只保留安全字符
                                value = re.sub(r'[^\u4e00-\u9fff\u0020-\u007e\u3000-\u303f\uff00-\uffef]', '', value)
                                value = value.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                                value = ' '.join(value.split())  # 移除多余空格
                                if len(value) > 500:
                                    value = value[:500] + '...'

                        # 确保值是安全的字符串
                        if value is None:
                            value = ''
                        else:
                            value = str(value)
                            # 最终安全检查
                            try:
                                value.encode('utf-8')
                            except:
                                value = '数据包含特殊字符'

                        ws.cell(row=row_idx, column=col_idx, value=value)

            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if cell.value and len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # 保存到BytesIO
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            filename = f"student_details_{assignment_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

            return output.getvalue(), filename, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

        except Exception as e:
            logger.error(f"Excel导出失败: {str(e)}")
            raise

    async def _export_to_pdf(self, students_data: list, export_fields: list, assignment_id: int):
        """导出为PDF格式"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, HRFlowable
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            from io import BytesIO

            # 注册中文字体
            try:
                pdfmetrics.registerFont(TTFont('SimSun', 'C:/Windows/Fonts/simsun.ttc'))
                font_name = 'SimSun'
            except:
                font_name = 'Helvetica'  # 备用字体

            # 字段映射
            field_mapping = {
                'student_name': '学生姓名',
                'total_score': '总分',
                'objective_score': '客观题分数',
                'subjective_score': '主观题分数',
                'accuracy_rate': '准确率(%)',
                'submit_status': '提交状态',
                'submit_time': '提交时间',
                'wrong_questions': '错误题号',
                'error_analysis': '错误分析',
                'homework_comment': '作业点评'
            }

            # 创建PDF
            output = BytesIO()
            doc = SimpleDocTemplate(output, pagesize=A4, topMargin=0.5*inch)

            # 样式
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontName=font_name,
                fontSize=16,
                alignment=1,  # 居中
                spaceAfter=20
            )

            # 内容
            story = []

            # 标题
            title = Paragraph(f"学生详情报告 - 作业{assignment_id}", title_style)
            story.append(title)
            story.append(Spacer(1, 20))

            # 创建文本样式
            basic_info_style = ParagraphStyle(
                'BasicInfo',
                parent=styles['Normal'],
                fontName=font_name,
                fontSize=11,
                leading=14,
                spaceAfter=8,
                textColor=colors.black
            )

            error_analysis_style = ParagraphStyle(
                'ErrorAnalysis',
                parent=styles['Normal'],
                fontName=font_name,
                fontSize=10,
                leading=12,
                spaceAfter=8,
                leftIndent=20,
                textColor=colors.darkred
            )

            homework_comment_style = ParagraphStyle(
                'HomeworkComment',
                parent=styles['Normal'],
                fontName=font_name,
                fontSize=10,
                leading=12,
                spaceAfter=20,
                leftIndent=20,
                textColor=colors.darkgreen
            )

            # 为每个学生创建垂直布局
            for i, student in enumerate(students_data):
                if i > 0:
                    # 学生之间添加分隔线
                    story.append(Spacer(1, 10))
                    story.append(HRFlowable(width="100%", thickness=1, color=colors.grey))
                    story.append(Spacer(1, 10))

                # 第一行：基本信息
                basic_info_parts = []
                for field in export_fields:
                    if field in field_mapping and field not in ['error_analysis', 'homework_comment']:
                        value = student.get(field, '')

                        # 特殊处理某些字段
                        if field == 'accuracy_rate':
                            value = f"{value * 100:.1f}%" if isinstance(value, (int, float)) else str(value)
                        elif field == 'wrong_questions' and isinstance(value, list):
                            value = ', '.join(map(str, value)) if value else '无'

                        if value:
                            basic_info_parts.append(f"{field_mapping[field]}: {value}")

                if basic_info_parts:
                    basic_info_text = " | ".join(basic_info_parts)
                    story.append(Paragraph(basic_info_text, basic_info_style))

                # 第二行：错误分析（如果包含在导出字段中）
                if 'error_analysis' in export_fields:
                    error_analysis = student.get('error_analysis', '')
                    if error_analysis:
                        # 清理文本但不截断
                        import re
                        error_analysis = re.sub(r'[^\u4e00-\u9fff\u0020-\u007e\u3000-\u303f\uff00-\uffef]', '', str(error_analysis))
                        error_analysis = error_analysis.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                        error_analysis = ' '.join(error_analysis.split())

                        error_text = f"<b>错误分析：</b>{error_analysis}"
                        story.append(Paragraph(error_text, error_analysis_style))

                # 第三行：作业点评（如果包含在导出字段中）
                if 'homework_comment' in export_fields:
                    homework_comment = student.get('homework_comment', '')
                    if homework_comment:
                        # 清理文本但不截断
                        import re
                        homework_comment = re.sub(r'[^\u4e00-\u9fff\u0020-\u007e\u3000-\u303f\uff00-\uffef]', '', str(homework_comment))
                        homework_comment = homework_comment.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                        homework_comment = ' '.join(homework_comment.split())

                        comment_text = f"<b>作业点评：</b>{homework_comment}"
                        story.append(Paragraph(comment_text, homework_comment_style))

            # 生成PDF
            doc.build(story)
            output.seek(0)

            filename = f"student_details_{assignment_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

            return output.getvalue(), filename, "application/pdf"

        except Exception as e:
            logger.error(f"PDF导出失败: {str(e)}")
            raise

    def _build_comment_prompt(self, student_name: str, assignment_title: str,
                             total_questions: int, correct_count: int,
                             accuracy_rate: float, wrong_questions_info: List[Dict]) -> str:
        """构建作业点评的AI提示词"""

        wrong_questions_text = ""
        if wrong_questions_info:
            wrong_questions_text = "\n错题详情：\n"
            for i, wrong in enumerate(wrong_questions_info, 1):
                wrong_questions_text += f"{i}. 第{wrong['number']}题（{wrong['type']}）：{wrong['content']}\n"
                if wrong['analysis']:
                    wrong_questions_text += f"   错误分析：{wrong['analysis']}\n"

        # 构建优化的作业点评提示词，以鼓励为主，综合评价学生作业质量
        prompt = f"""你是一位温暖而专业的教师，正在为学生撰写作业点评。请基于以下信息生成一份充满温度和鼓励的综合评价。

学生信息：
- 姓名：{student_name}
- 作业：{assignment_title}

作业表现：
- 总题数：{total_questions}题
- 正确题数：{correct_count}题
- 正确率：{accuracy_rate:.1f}%

{wrong_questions_text}

请直接输出一段温暖的作业点评，用中文撰写。点评要求：

【核心原则】
- 以鼓励和肯定为主基调，即使有不足也要温和地提出
- 先找出学生的亮点和进步，再指出需要改进的地方
- 语气要像慈祥的老师对待自己的孩子一样温暖

【内容结构】
1. 开头：温暖的称呼和整体肯定
2. 亮点发现：找出学生作业中的优秀表现（如认真态度、正确率、解题思路等）
3. 温和建议：对错题或不足之处给出具体而温和的改进建议
4. 结尾：给予鼓励和对未来的期待

【语言风格】
- 使用"你"来称呼学生，语气亲切自然
- 多用积极正面的词汇：优秀、认真、进步、努力、棒、很好等
- 避免严厉批评，用"可以尝试"、"建议"、"如果能..."等温和表达
- 体现个性化，不要千篇一律

重要提示：直接输出点评内容，不要有任何思考过程或标签。控制在120-150字，简洁而富有温度。"""

        return prompt

    def _generate_default_comment(self, student_name: str, accuracy_rate: float,
                                 total_questions: int, correct_count: int) -> str:
        """生成默认的作业点评（当AI调用失败时使用）"""

        if accuracy_rate >= 90:
            performance = "优秀"
            encouragement = "继续保持这种认真的学习态度！"
            suggestion = "可以尝试挑战更有难度的题目。"
        elif accuracy_rate >= 80:
            performance = "良好"
            encouragement = "你的基础很扎实！"
            suggestion = "注意细心检查，避免粗心错误。"
        elif accuracy_rate >= 60:
            performance = "一般"
            encouragement = "还有进步的空间，加油！"
            suggestion = "建议多练习基础题目，巩固知识点。"
        else:
            performance = "需要努力"
            encouragement = "不要气馁，每一次练习都是进步！"
            suggestion = "建议重新复习相关知识点，多做类似练习。"

        comment = f"{student_name}同学，你本次作业完成了{total_questions}题，正确{correct_count}题，正确率{accuracy_rate:.1f}%，表现{performance}。{encouragement}{suggestion}期待你下次作业有更好的表现！"

        return comment

    async def regenerate_all_homework_comments(self, assignment_id: int) -> Dict[str, Any]:
        """重新生成指定作业的所有学生作业点评"""
        try:
            # 获取该作业的所有学生作业记录
            latest_homework_subquery = self.db.query(
                Homework.student_id,
                func.max(Homework.created_at).label('latest_created_at')
            ).filter(
                Homework.assignment_id == assignment_id
            ).group_by(Homework.student_id).subquery()

            # 获取每个学生最新的作业记录
            results = self.db.query(
                Homework,
                User.full_name.label('student_name'),
                Homework.student_id
            ).join(
                latest_homework_subquery,
                and_(
                    Homework.student_id == latest_homework_subquery.c.student_id,
                    Homework.created_at == latest_homework_subquery.c.latest_created_at
                )
            ).join(
                User, User.id == Homework.student_id
            ).filter(
                Homework.assignment_id == assignment_id
            ).all()

            updated_count = 0
            failed_count = 0

            logger.info(f"开始重新生成作业{assignment_id}的所有学生作业点评，共{len(results)}名学生")

            for homework, student_name, student_id in results:
                try:
                    # 重新生成并保存作业点评
                    homework_comment = await self._generate_and_save_homework_comment(homework.id, student_id)

                    if homework_comment and homework_comment != "暂无作业点评":
                        updated_count += 1
                        logger.info(f"成功重新生成并保存学生{student_name}的作业点评")
                    else:
                        failed_count += 1
                        logger.warning(f"学生{student_name}的作业点评生成失败")

                except Exception as e:
                    failed_count += 1
                    logger.error(f"重新生成学生{student_name}作业点评时出错: {str(e)}")

            result = {
                "assignment_id": assignment_id,
                "total_students": len(results),
                "updated_count": updated_count,
                "failed_count": failed_count,
                "success_rate": f"{(updated_count / len(results) * 100):.1f}%" if len(results) > 0 else "0%"
            }

            logger.info(f"作业点评重新生成完成: {result}")
            return result

        except Exception as e:
            logger.error(f"重新生成作业点评失败: {str(e)}")
            raise

    async def get_student_performance(self, student_id: int, assignment_id: int) -> Dict[str, Any]:
        """获取学生个人详细分析"""
        try:
            # 获取学生作业
            homework = self.db.query(Homework).filter(
                and_(Homework.student_id == student_id, Homework.assignment_id == assignment_id)
            ).first()

            if not homework:
                raise ValueError("学生作业不存在")

            # 获取学生信息
            student = self.db.query(User).filter(User.id == student_id).first()

            # 获取批改数据
            correction = self.db.query(HomeworkCorrection).filter(
                HomeworkCorrection.homework_id == homework.id
            ).first()

            # 解析答题详情
            answer_details = []
            if correction and correction.correction_data:
                try:
                    correction_json = json.loads(correction.correction_data)
                    questions = correction_json.get("questions", [])

                    for question in questions:
                        answer_details.append({
                            "question_number": question.get("question_number"),
                            "student_answer": question.get("student_answer"),
                            "is_correct": question.get("is_correct"),
                            "correct_answer": question.get("correct_answer"),
                            "analysis": question.get("analysis", "")
                        })
                except json.JSONDecodeError:
                    pass

            # 分析优势和问题
            correct_count = len([a for a in answer_details if a["is_correct"]])
            wrong_count = len(answer_details) - correct_count

            strengths = []
            problems = []
            suggestions = []

            if correct_count > wrong_count:
                strengths.append("基础知识掌握较好，大部分题目能够正确解答")
            if homework.accuracy and homework.accuracy > 0.8:
                strengths.append("答题准确性较高，学习态度认真")

            if wrong_count > 0:
                wrong_questions = [a["question_number"] for a in answer_details if not a["is_correct"]]
                problems.append(f"第{', '.join(wrong_questions)}题存在错误，需要重点关注")
                suggestions.append("针对错题进行专项复习和练习")
                suggestions.append("加强相关知识点的理解和掌握")

            return {
                "basic_info": {
                    "student_id": student_id,
                    "student_name": student.full_name if student else f"学生{student_id}",
                    "total_score": homework.score or 0,
                    "accuracy_rate": homework.accuracy or 0,
                    "submit_status": "已提交" if homework.status == "graded" else "未提交",
                    "submit_time": homework.created_at.strftime("%m月%d日%H:%M") if homework.created_at else None
                },
                "answer_analysis": answer_details,
                "performance_analysis": {
                    "strengths": strengths,
                    "problems": problems,
                    "suggestions": suggestions
                },
                "statistics": {
                    "total_questions": len(answer_details),
                    "correct_count": correct_count,
                    "wrong_count": wrong_count,
                    "accuracy_rate": round((correct_count / len(answer_details) * 100), 1) if answer_details else 0
                }
            }

        except Exception as e:
            logger.error(f"获取学生表现失败: {str(e)}")
            raise

    async def get_smart_suggestions(self, assignment_id: int) -> Dict[str, Any]:
        """获取智能建议"""
        try:
            # 获取题目分析数据
            questions_data = await self.get_question_analysis(assignment_id)
            questions = questions_data["questions"]

            # 生成评讲建议
            teaching_suggestions = []
            for question in questions:
                if question["accuracy_rate"] < 80:  # 正确率低于80%的题目需要重点讲解
                    priority = "高" if question["accuracy_rate"] < 60 else "中"
                    time_needed = "15分钟" if question["accuracy_rate"] < 60 else "8分钟"

                    teaching_suggestions.append({
                        "question_number": question["question_number"],
                        "priority": priority,
                        "accuracy_rate": question["accuracy_rate"],
                        "main_problem": self._identify_main_problem(question),
                        "suggested_time": time_needed,
                        "teaching_method": self._suggest_teaching_method(question)
                    })

            # 按优先级排序
            teaching_suggestions.sort(key=lambda x: (x["priority"] == "高", -x["accuracy_rate"]))

            # 获取学生详情用于个别辅导建议
            students_data = await self.get_student_details(assignment_id)
            students = students_data["students"]

            # 生成个别辅导建议
            individual_guidance = {
                "need_attention": [],  # 需要重点关注
                "need_care": [],       # 需要适当关注
                "not_submitted": []    # 未提交作业
            }

            for student in students:
                if student["submit_status"] == "未提交":
                    individual_guidance["not_submitted"].append({
                        "student_name": student["student_name"],
                        "suggestion": "需要催交作业并了解原因"
                    })
                elif student["total_score"] < 60:
                    individual_guidance["need_attention"].append({
                        "student_name": student["student_name"],
                        "score": student["total_score"],
                        "suggestion": self._generate_student_suggestion(student)
                    })
                elif student["total_score"] < 80:
                    individual_guidance["need_care"].append({
                        "student_name": student["student_name"],
                        "score": student["total_score"],
                        "suggestion": self._generate_student_suggestion(student)
                    })

            # 生成班级总结
            overview_data = await self.get_homework_overview(assignment_id)
            class_summary = self._generate_class_summary(overview_data, questions_data)

            # 生成下次作业建议
            next_assignment_suggestions = self._generate_next_assignment_suggestions(questions_data)

            return {
                "teaching_suggestions": teaching_suggestions,
                "individual_guidance": individual_guidance,
                "class_summary": class_summary,
                "next_assignment_suggestions": next_assignment_suggestions
            }

        except Exception as e:
            logger.error(f"获取智能建议失败: {str(e)}")
            raise

    async def generate_suggestions(self, assignment_id: int) -> Dict[str, Any]:
        """生成并保存智能建议"""
        try:
            # 获取智能建议
            suggestions = await self.get_smart_suggestions(assignment_id)

            # 这里可以将建议保存到数据库中
            # TODO: 实现建议的持久化存储

            return suggestions

        except Exception as e:
            logger.error(f"生成智能建议失败: {str(e)}")
            raise

    async def get_parent_report(self, student_id: int, assignment_id: int) -> Dict[str, Any]:
        """获取家长报告"""
        try:
            # 获取学生详细表现
            student_performance = await self.get_student_performance(student_id, assignment_id)

            # 获取班级概览用于对比
            overview_data = await self.get_homework_overview(assignment_id)

            # 生成家长报告
            basic_info = student_performance["basic_info"]
            class_average = overview_data["key_metrics"]["average_score"]

            # 与班级平均对比
            comparison = {
                "student_score": basic_info["total_score"],
                "class_average": class_average,
                "above_average": basic_info["total_score"] > class_average
            }

            # 提取优势和问题
            analysis = student_performance["performance_analysis"]

            # 生成家庭辅导建议
            home_guidance = self._generate_home_guidance(student_performance)

            # 生成进步趋势（暂时模拟数据）
            progress_trend = self._generate_progress_trend(student_id)

            # 生成老师寄语
            teacher_message = self._generate_teacher_message(student_performance)

            return {
                "assignment_performance": {
                    **basic_info,
                    "class_comparison": comparison
                },
                "strengths_and_problems": {
                    "strengths": analysis["strengths"],
                    "problems": analysis["problems"]
                },
                "home_guidance": home_guidance,
                "progress_trend": progress_trend,
                "teacher_message": teacher_message
            }

        except Exception as e:
            logger.error(f"获取家长报告失败: {str(e)}")
            raise

    async def get_unsubmitted_student_ids(self, assignment_id: int) -> List[int]:
        """获取未提交作业的学生ID列表"""
        try:
            # 获取作业任务信息
            assignment = self.db.query(HomeworkAssignment).filter(
                HomeworkAssignment.id == assignment_id
            ).first()

            if not assignment or not assignment.class_id:
                return []

            # 获取已提交作业的学生ID
            submitted_student_ids = self.db.query(Homework.student_id).filter(
                Homework.assignment_id == assignment_id,
                Homework.student_id.isnot(None)
            ).distinct().all()

            submitted_ids = [row[0] for row in submitted_student_ids]

            # 获取班级所有学生ID
            all_student_ids = self.db.query(ClassStudent.student_id).filter(
                ClassStudent.class_id == assignment.class_id
            ).all()

            all_ids = [row[0] for row in all_student_ids]

            # 计算未提交的学生ID
            unsubmitted_ids = [student_id for student_id in all_ids if student_id not in submitted_ids]

            return unsubmitted_ids

        except Exception as e:
            logger.error(f"获取未提交学生ID失败: {str(e)}")
            return []

    async def remind_submission(self, assignment_id: int, student_ids: List[int]) -> Dict[str, Any]:
        """催交作业"""
        try:
            # 获取作业信息
            assignment = self.db.query(HomeworkAssignment).filter(
                HomeworkAssignment.id == assignment_id
            ).first()

            if not assignment:
                raise ValueError("作业不存在")

            # 获取学生信息
            students = self.db.query(User).filter(
                User.id.in_(student_ids)
            ).all()

            # 这里可以实现实际的通知发送逻辑
            # 比如发送短信、邮件或系统通知
            # 目前先模拟发送成功

            reminded_students = []
            for student in students:
                reminded_students.append({
                    "student_id": student.id,
                    "student_name": student.full_name or student.username or f"学生{student.id}",
                    "status": "已发送催交通知"
                })

            return {
                "assignment_id": assignment_id,
                "assignment_title": assignment.title,
                "reminded_count": len(reminded_students),
                "reminded_students": reminded_students,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"催交作业失败: {str(e)}")
            raise

    # 辅助方法
    def _identify_main_problem(self, question: Dict) -> str:
        """识别题目主要问题"""
        accuracy_rate = question["accuracy_rate"]
        if accuracy_rate < 40:
            return "概念理解错误"
        elif accuracy_rate < 60:
            return "计算错误或理解偏差"
        else:
            return "细节错误"

    def _suggest_teaching_method(self, question: Dict) -> str:
        """建议教学方法"""
        accuracy_rate = question["accuracy_rate"]
        if accuracy_rate < 40:
            return "重新讲解概念，板书演示，增加练习"
        elif accuracy_rate < 60:
            return "强调解题步骤，提醒注意事项"
        else:
            return "简单回顾，重点强调易错点"

    def _generate_student_suggestion(self, student: Dict) -> str:
        """生成学生建议"""
        score = student["total_score"]
        if score < 40:
            return "基础薄弱，需要课后个别辅导"
        elif score < 60:
            return "部分知识点掌握不牢，需要加强练习"
        elif score < 80:
            return "整体表现良好，个别题目需要注意"
        else:
            return "表现优秀，可以挑战更难的题目"

    def _generate_class_summary(self, overview_data: Dict, questions_data: Dict) -> Dict[str, Any]:
        """生成班级总结"""
        key_metrics = overview_data["key_metrics"]
        average_score = key_metrics["average_score"]

        # 整体表现评价
        if average_score >= 85:
            overall_performance = "优秀"
        elif average_score >= 75:
            overall_performance = "良好"
        elif average_score >= 60:
            overall_performance = "一般"
        else:
            overall_performance = "需要改进"

        # 主要问题
        error_questions = [q for q in questions_data["questions"] if q["accuracy_rate"] < 60]
        main_problems = [f"第{q['question_number']}题{self._identify_main_problem(q)}" for q in error_questions[:3]]

        # 改进建议
        improvement_suggestions = []
        if error_questions:
            improvement_suggestions.append("加强薄弱知识点的讲解和练习")
            improvement_suggestions.append("增加类似题型的训练")
        else:
            improvement_suggestions.append("保持当前教学水平")
            improvement_suggestions.append("可以适当增加题目难度")

        return {
            "overall_performance": overall_performance,
            "average_score": average_score,
            "main_problems": main_problems,
            "improvement_suggestions": improvement_suggestions
        }

    def _generate_next_assignment_suggestions(self, questions_data: Dict) -> List[str]:
        """生成下次作业建议"""
        suggestions = []
        error_questions = [q for q in questions_data["questions"] if q["accuracy_rate"] < 60]

        if error_questions:
            suggestions.append(f"增加{len(error_questions)}道类似题目，巩固薄弱知识点")
            suggestions.append("适当降低题目难度，重点关注基础概念")
        else:
            suggestions.append("可以适当增加题目难度")
            suggestions.append("增加综合性题目，提高学生能力")

        suggestions.append("控制题目数量，确保学生有足够时间思考")

        return suggestions

    def _generate_home_guidance(self, student_performance: Dict) -> Dict[str, List[str]]:
        """生成家庭辅导建议"""
        analysis = student_performance["performance_analysis"]

        knowledge_guidance = []
        habit_guidance = []

        if analysis["problems"]:
            knowledge_guidance.append("重点复习错题相关知识点")
            knowledge_guidance.append("建议购买相关练习册进行专项训练")
        else:
            knowledge_guidance.append("保持当前学习状态")
            knowledge_guidance.append("可以预习下一章节内容")

        habit_guidance.append("做题前仔细审题，理解题意后再作答")
        habit_guidance.append("完成后鼓励孩子自己检查，养成良好习惯")

        return {
            "knowledge_guidance": knowledge_guidance,
            "habit_guidance": habit_guidance
        }

    def _generate_progress_trend(self, student_id: int) -> Dict[str, Any]:
        """生成进步趋势（暂时模拟数据）"""
        # TODO: 实现真实的历史数据查询
        return {
            "recent_scores": [85, 88, 92, 89, 94],  # 最近5次作业分数
            "trend": "稳步提升",
            "trend_description": "最近5次作业表现稳定，总体呈上升趋势"
        }

    def _generate_teacher_message(self, student_performance: Dict) -> str:
        """生成老师寄语"""
        basic_info = student_performance["basic_info"]
        analysis = student_performance["performance_analysis"]

        student_name = basic_info["student_name"]
        score = basic_info["total_score"]

        if score >= 90:
            message = f"{student_name}同学表现优秀，基础扎实，继续保持！"
        elif score >= 80:
            message = f"{student_name}同学学习态度认真，成绩良好。"
        elif score >= 60:
            message = f"{student_name}同学基础尚可，需要加强薄弱环节。"
        else:
            message = f"{student_name}同学需要重点关注，建议加强基础训练。"

        if analysis["problems"]:
            message += "建议家长配合督促相关知识点的复习。"

        return message
