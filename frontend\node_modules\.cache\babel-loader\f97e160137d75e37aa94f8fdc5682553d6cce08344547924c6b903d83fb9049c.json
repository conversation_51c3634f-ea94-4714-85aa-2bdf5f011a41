{"ast": null, "code": "// Countdown\nconst timeUnits = [['Y', 1000 * 60 * 60 * 24 * 365],\n// years\n['M', 1000 * 60 * 60 * 24 * 30],\n// months\n['D', 1000 * 60 * 60 * 24],\n// days\n['H', 1000 * 60 * 60],\n// hours\n['m', 1000 * 60],\n// minutes\n['s', 1000],\n// seconds\n['S', 1] // million seconds\n];\nexport function formatTimeStr(duration, format) {\n  let leftDuration = duration;\n  const escapeRegex = /\\[[^\\]]*]/g;\n  const keepList = (format.match(escapeRegex) || []).map(str => str.slice(1, -1));\n  const templateText = format.replace(escapeRegex, '[]');\n  const replacedText = timeUnits.reduce((current, [name, unit]) => {\n    if (current.includes(name)) {\n      const value = Math.floor(leftDuration / unit);\n      leftDuration -= value * unit;\n      return current.replace(new RegExp(`${name}+`, 'g'), match => {\n        const len = match.length;\n        return value.toString().padStart(len, '0');\n      });\n    }\n    return current;\n  }, templateText);\n  let index = 0;\n  return replacedText.replace(escapeRegex, () => {\n    const match = keepList[index];\n    index += 1;\n    return match;\n  });\n}\nexport function formatCounter(value, config, down) {\n  const {\n    format = ''\n  } = config;\n  const target = new Date(value).getTime();\n  const current = Date.now();\n  const diff = down ? Math.max(target - current, 0) : Math.max(current - target, 0);\n  return formatTimeStr(diff, format);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}