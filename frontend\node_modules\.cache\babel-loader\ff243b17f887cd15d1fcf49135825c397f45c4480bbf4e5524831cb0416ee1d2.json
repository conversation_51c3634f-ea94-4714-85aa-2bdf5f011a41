{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport classNames from 'classnames';\nimport RcSlider from 'rc-slider';\nimport raf from \"rc-util/es/raf\";\nimport { devUseWarning } from '../_util/warning';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport SliderInternalContext from './Context';\nimport SliderTooltip from './SliderTooltip';\nimport useStyle from './style';\nimport useRafLock from './useRafLock';\nimport { useComponentConfig } from '../config-provider/context';\nfunction getTipFormatter(tipFormatter, legacyTipFormatter) {\n  if (tipFormatter || tipFormatter === null) {\n    return tipFormatter;\n  }\n  if (legacyTipFormatter || legacyTipFormatter === null) {\n    return legacyTipFormatter;\n  }\n  return val => typeof val === 'number' ? val.toString() : '';\n}\nconst Slider = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      range,\n      className,\n      rootClassName,\n      style,\n      disabled,\n      // Deprecated Props\n      tooltipPrefixCls: legacyTooltipPrefixCls,\n      tipFormatter: legacyTipFormatter,\n      tooltipVisible: legacyTooltipVisible,\n      getTooltipPopupContainer: legacyGetTooltipPopupContainer,\n      tooltipPlacement: legacyTooltipPlacement,\n      tooltip = {},\n      onChangeComplete,\n      classNames: sliderClassNames,\n      styles\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"range\", \"className\", \"rootClassName\", \"style\", \"disabled\", \"tooltipPrefixCls\", \"tipFormatter\", \"tooltipVisible\", \"getTooltipPopupContainer\", \"tooltipPlacement\", \"tooltip\", \"onChangeComplete\", \"classNames\", \"styles\"]);\n  const {\n    vertical\n  } = props;\n  const {\n    getPrefixCls,\n    direction: contextDirection,\n    className: contextClassName,\n    style: contextStyle,\n    classNames: contextClassNames,\n    styles: contextStyles,\n    getPopupContainer\n  } = useComponentConfig('slider');\n  const contextDisabled = React.useContext(DisabledContext);\n  const mergedDisabled = disabled !== null && disabled !== void 0 ? disabled : contextDisabled;\n  // ============================= Context ==============================\n  const {\n    handleRender: contextHandleRender,\n    direction: internalContextDirection\n  } = React.useContext(SliderInternalContext);\n  const mergedDirection = internalContextDirection || contextDirection;\n  const isRTL = mergedDirection === 'rtl';\n  // =============================== Open ===============================\n  const [hoverOpen, setHoverOpen] = useRafLock();\n  const [focusOpen, setFocusOpen] = useRafLock();\n  const tooltipProps = Object.assign({}, tooltip);\n  const {\n    open: tooltipOpen,\n    placement: tooltipPlacement,\n    getPopupContainer: getTooltipPopupContainer,\n    prefixCls: customizeTooltipPrefixCls,\n    formatter: tipFormatter\n  } = tooltipProps;\n  const lockOpen = tooltipOpen !== null && tooltipOpen !== void 0 ? tooltipOpen : legacyTooltipVisible;\n  const activeOpen = (hoverOpen || focusOpen) && lockOpen !== false;\n  const mergedTipFormatter = getTipFormatter(tipFormatter, legacyTipFormatter);\n  // ============================= Change ==============================\n  const [dragging, setDragging] = useRafLock();\n  const onInternalChangeComplete = nextValues => {\n    onChangeComplete === null || onChangeComplete === void 0 ? void 0 : onChangeComplete(nextValues);\n    setDragging(false);\n  };\n  // ============================ Placement ============================\n  const getTooltipPlacement = (placement, vert) => {\n    if (placement) {\n      return placement;\n    }\n    if (!vert) {\n      return 'top';\n    }\n    return isRTL ? 'left' : 'right';\n  };\n  // ============================== Style ===============================\n  const prefixCls = getPrefixCls('slider', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const rootClassNames = classNames(className, contextClassName, contextClassNames.root, sliderClassNames === null || sliderClassNames === void 0 ? void 0 : sliderClassNames.root, rootClassName, {\n    [`${prefixCls}-rtl`]: isRTL,\n    [`${prefixCls}-lock`]: dragging\n  }, hashId, cssVarCls);\n  // make reverse default on rtl direction\n  if (isRTL && !restProps.vertical) {\n    restProps.reverse = !restProps.reverse;\n  }\n  // ============================= Warning ==============================\n  // Warning for deprecated usage\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Slider');\n    [['tooltipPrefixCls', 'prefixCls'], ['getTooltipPopupContainer', 'getPopupContainer'], ['tipFormatter', 'formatter'], ['tooltipPlacement', 'placement'], ['tooltipVisible', 'open']].forEach(([deprecatedName, newName]) => {\n      warning.deprecated(!(deprecatedName in props), deprecatedName, `tooltip.${newName}`);\n    });\n  }\n  // ============================== Handle ==============================\n  React.useEffect(() => {\n    const onMouseUp = () => {\n      // Delay for 1 frame to make the click to enable hide tooltip\n      // even when the handle is focused\n      raf(() => {\n        setFocusOpen(false);\n      }, 1);\n    };\n    document.addEventListener('mouseup', onMouseUp);\n    return () => {\n      document.removeEventListener('mouseup', onMouseUp);\n    };\n  }, []);\n  const useActiveTooltipHandle = range && !lockOpen;\n  const handleRender = contextHandleRender || ((node, info) => {\n    const {\n      index\n    } = info;\n    const nodeProps = node.props;\n    function proxyEvent(eventName, event, triggerRestPropsEvent) {\n      var _a, _b, _c, _d;\n      if (triggerRestPropsEvent) {\n        (_b = (_a = restProps)[eventName]) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      }\n      (_d = (_c = nodeProps)[eventName]) === null || _d === void 0 ? void 0 : _d.call(_c, event);\n    }\n    const passedProps = Object.assign(Object.assign({}, nodeProps), {\n      onMouseEnter: e => {\n        setHoverOpen(true);\n        proxyEvent('onMouseEnter', e);\n      },\n      onMouseLeave: e => {\n        setHoverOpen(false);\n        proxyEvent('onMouseLeave', e);\n      },\n      onMouseDown: e => {\n        setFocusOpen(true);\n        setDragging(true);\n        proxyEvent('onMouseDown', e);\n      },\n      onFocus: e => {\n        var _a;\n        setFocusOpen(true);\n        (_a = restProps.onFocus) === null || _a === void 0 ? void 0 : _a.call(restProps, e);\n        proxyEvent('onFocus', e, true);\n      },\n      onBlur: e => {\n        var _a;\n        setFocusOpen(false);\n        (_a = restProps.onBlur) === null || _a === void 0 ? void 0 : _a.call(restProps, e);\n        proxyEvent('onBlur', e, true);\n      }\n    });\n    const cloneNode = /*#__PURE__*/React.cloneElement(node, passedProps);\n    const open = (!!lockOpen || activeOpen) && mergedTipFormatter !== null;\n    // Wrap on handle with Tooltip when is single mode or multiple with all show tooltip\n    if (!useActiveTooltipHandle) {\n      return /*#__PURE__*/React.createElement(SliderTooltip, Object.assign({}, tooltipProps, {\n        prefixCls: getPrefixCls('tooltip', customizeTooltipPrefixCls !== null && customizeTooltipPrefixCls !== void 0 ? customizeTooltipPrefixCls : legacyTooltipPrefixCls),\n        title: mergedTipFormatter ? mergedTipFormatter(info.value) : '',\n        value: info.value,\n        open: open,\n        placement: getTooltipPlacement(tooltipPlacement !== null && tooltipPlacement !== void 0 ? tooltipPlacement : legacyTooltipPlacement, vertical),\n        key: index,\n        classNames: {\n          root: `${prefixCls}-tooltip`\n        },\n        getPopupContainer: getTooltipPopupContainer || legacyGetTooltipPopupContainer || getPopupContainer\n      }), cloneNode);\n    }\n    return cloneNode;\n  });\n  // ========================== Active Handle ===========================\n  const activeHandleRender = useActiveTooltipHandle ? (handle, info) => {\n    const cloneNode = /*#__PURE__*/React.cloneElement(handle, {\n      style: Object.assign(Object.assign({}, handle.props.style), {\n        visibility: 'hidden'\n      })\n    });\n    return /*#__PURE__*/React.createElement(SliderTooltip, Object.assign({}, tooltipProps, {\n      prefixCls: getPrefixCls('tooltip', customizeTooltipPrefixCls !== null && customizeTooltipPrefixCls !== void 0 ? customizeTooltipPrefixCls : legacyTooltipPrefixCls),\n      title: mergedTipFormatter ? mergedTipFormatter(info.value) : '',\n      open: mergedTipFormatter !== null && activeOpen,\n      placement: getTooltipPlacement(tooltipPlacement !== null && tooltipPlacement !== void 0 ? tooltipPlacement : legacyTooltipPlacement, vertical),\n      key: \"tooltip\",\n      classNames: {\n        root: `${prefixCls}-tooltip`\n      },\n      getPopupContainer: getTooltipPopupContainer || legacyGetTooltipPopupContainer || getPopupContainer,\n      draggingDelete: info.draggingDelete\n    }), cloneNode);\n  } : undefined;\n  // ============================== Render ==============================\n  const rootStyle = Object.assign(Object.assign(Object.assign(Object.assign({}, contextStyles.root), contextStyle), styles === null || styles === void 0 ? void 0 : styles.root), style);\n  const mergedTracks = Object.assign(Object.assign({}, contextStyles.tracks), styles === null || styles === void 0 ? void 0 : styles.tracks);\n  const mergedTracksClassNames = classNames(contextClassNames.tracks, sliderClassNames === null || sliderClassNames === void 0 ? void 0 : sliderClassNames.tracks);\n  return wrapCSSVar(/*#__PURE__*/\n  // @ts-ignore\n  React.createElement(RcSlider, Object.assign({}, restProps, {\n    classNames: Object.assign({\n      handle: classNames(contextClassNames.handle, sliderClassNames === null || sliderClassNames === void 0 ? void 0 : sliderClassNames.handle),\n      rail: classNames(contextClassNames.rail, sliderClassNames === null || sliderClassNames === void 0 ? void 0 : sliderClassNames.rail),\n      track: classNames(contextClassNames.track, sliderClassNames === null || sliderClassNames === void 0 ? void 0 : sliderClassNames.track)\n    }, mergedTracksClassNames ? {\n      tracks: mergedTracksClassNames\n    } : {}),\n    styles: Object.assign({\n      handle: Object.assign(Object.assign({}, contextStyles.handle), styles === null || styles === void 0 ? void 0 : styles.handle),\n      rail: Object.assign(Object.assign({}, contextStyles.rail), styles === null || styles === void 0 ? void 0 : styles.rail),\n      track: Object.assign(Object.assign({}, contextStyles.track), styles === null || styles === void 0 ? void 0 : styles.track)\n    }, Object.keys(mergedTracks).length ? {\n      tracks: mergedTracks\n    } : {}),\n    step: restProps.step,\n    range: range,\n    className: rootClassNames,\n    style: rootStyle,\n    disabled: mergedDisabled,\n    ref: ref,\n    prefixCls: prefixCls,\n    handleRender: handleRender,\n    activeHandleRender: activeHandleRender,\n    onChangeComplete: onInternalChangeComplete\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Slider.displayName = 'Slider';\n}\nexport default Slider;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}