#!/usr/bin/env python3
"""
导出服务 - 支持分层权限导出功能
"""

import os
import zipfile
import shutil
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path
import logging
from sqlalchemy.orm import Session

from ..models.user import User, Class
from ..models.school import School
from ..models.subject import Subject
from ..models.homework import Homework, HomeworkAssignment, HomeworkImage, HomeworkAnnotatedImage
from ..models.file_metadata import FileMetadata, FileType
from .file_manager import file_manager
from .permission_service import PermissionService

logger = logging.getLogger(__name__)

class ExportService:
    """导出服务类"""
    
    def __init__(self):
        self.file_manager = file_manager
    
    async def export_homework_by_assignment(
        self,
        current_user: User,
        db: Session,
        assignment_id: int,
        include_annotated: bool = True,
        student_ids: List[int] = None,
        page_numbers: List[int] = None
    ) -> Optional[str]:
        """
        按作业任务导出（基于角色权限）

        Args:
            current_user: 当前用户
            db: 数据库会话
            assignment_id: 作业任务ID
            include_annotated: 是否包含批注图片
            student_ids: 学生ID列表（可选）
            page_numbers: 页面号列表（可选）

        Returns:
            导出文件的URL路径
        """
        try:
            logger.info(f"用户 {current_user.username} 导出作业任务 {assignment_id}")

            # 获取作业任务
            assignment = db.query(HomeworkAssignment).filter(
                HomeworkAssignment.id == assignment_id
            ).first()

            if not assignment:
                logger.error(f"作业任务不存在: {assignment_id}")
                return None

            # 获取用户权限范围
            permission_scope = await PermissionService.get_user_permission_scope(
                current_user, db, "homework_management"
            )

            logger.info(f"用户权限范围: {permission_scope['description']}")

            # 检查用户是否有权限访问该作业任务
            if not await self._check_assignment_permission(assignment, permission_scope, current_user):
                logger.error(f"用户无权限访问作业任务: {assignment_id}")
                return None

            # 根据权限范围获取可导出的作业
            homeworks = await self._get_accessible_homeworks(
                assignment_id, permission_scope, current_user, db, student_ids
            )

            if not homeworks:
                logger.warning(f"作业任务 {assignment_id} 没有可导出的作业")
                return None

            # 确定用户角色类型
            user_role_type = self._get_user_role_type(permission_scope)

            # 创建导出目录
            export_dir = self.file_manager.create_export_directory(
                user_role_type,
                current_user.school_id if user_role_type != "super_admin" else None
            )

            # 生成导出文件名
            export_filename = self.file_manager.generate_export_filename(
                "作业导出",
                assignment.school.name if assignment.school else "学校",
                f"{assignment.class_.grade}{assignment.class_.name}" if assignment.class_ else "班级",
                assignment.subject.name if assignment.subject else "科目",
                assignment.title
            )

            export_path = os.path.join(export_dir, export_filename)

            # 组织数据并创建导出文件
            organized_data = self._organize_homework_data_simple(homeworks, db, page_numbers)

            return await self._create_export_archive(organized_data, export_path, db, include_annotated)

        except Exception as e:
            logger.error(f"按作业任务导出失败: {e}")
            return None
    
    async def _export_system_level(
        self, 
        current_user: User, 
        db: Session, 
        filters: Dict[str, Any] = None
    ) -> Optional[str]:
        """超级管理员级别导出"""
        logger.info("执行超级管理员级别导出")
        
        # 创建导出目录
        export_dir = self.file_manager.create_export_directory("super_admin")
        
        # 获取所有学校的作业数据
        query = db.query(Homework).join(HomeworkAssignment)
        
        if filters:
            query = self._apply_filters(query, filters)
        
        homeworks = query.all()
        
        if not homeworks:
            logger.warning("没有找到符合条件的作业")
            return None
        
        # 按学校-年级-班级-学生组织文件
        organized_data = self._organize_homework_data(homeworks, db, "system")
        
        # 创建导出文件
        export_filename = self.file_manager.generate_export_filename(
            "系统导出", "全系统", "全部年级", "全部科目", "全部作业"
        )
        
        export_path = os.path.join(export_dir, export_filename)
        
        return await self._create_export_archive(organized_data, export_path, db)
    
    async def _export_school_level(
        self, 
        current_user: User, 
        db: Session, 
        filters: Dict[str, Any] = None,
        permission_scope: Dict[str, Any] = None
    ) -> Optional[str]:
        """学校管理员级别导出"""
        logger.info("执行学校管理员级别导出")
        
        school_ids = permission_scope.get("school_ids", [current_user.school_id])
        if not school_ids:
            logger.error("学校管理员没有关联的学校")
            return None
        
        # 创建导出目录
        export_dir = self.file_manager.create_export_directory("school_admin", school_ids[0])
        
        # 获取学校的作业数据
        query = db.query(Homework).join(HomeworkAssignment).filter(
            HomeworkAssignment.school_id.in_(school_ids)
        )
        
        if filters:
            query = self._apply_filters(query, filters)
        
        homeworks = query.all()
        
        if not homeworks:
            logger.warning("没有找到符合条件的作业")
            return None
        
        # 获取学校信息
        school = db.query(School).filter(School.id == school_ids[0]).first()
        school_name = school.name if school else f"学校{school_ids[0]}"
        
        # 按年级-班级-学生组织文件
        organized_data = self._organize_homework_data(homeworks, db, "school")
        
        # 创建导出文件
        export_filename = self.file_manager.generate_export_filename(
            "学校导出", school_name, "全部年级", "全部科目", "全部作业"
        )
        
        export_path = os.path.join(export_dir, export_filename)
        
        return await self._create_export_archive(organized_data, export_path, db)
    
    async def _export_teacher_level(
        self, 
        current_user: User, 
        db: Session, 
        filters: Dict[str, Any] = None,
        permission_scope: Dict[str, Any] = None
    ) -> Optional[str]:
        """教师级别导出"""
        logger.info("执行教师级别导出")
        
        class_ids = permission_scope.get("class_ids", [])
        if not class_ids:
            logger.error("教师没有关联的班级")
            return None
        
        # 创建导出目录
        export_dir = self.file_manager.create_export_directory("teacher", current_user.school_id)
        
        # 获取教师班级的作业数据
        query = db.query(Homework).join(HomeworkAssignment).filter(
            HomeworkAssignment.class_id.in_(class_ids)
        )
        
        if filters:
            query = self._apply_filters(query, filters)
        
        homeworks = query.all()
        
        if not homeworks:
            logger.warning("没有找到符合条件的作业")
            return None
        
        # 获取班级信息
        class_info = db.query(Class).filter(Class.id == class_ids[0]).first()
        class_name = f"{class_info.grade}{class_info.name}" if class_info else f"班级{class_ids[0]}"
        
        # 按学生组织文件
        organized_data = self._organize_homework_data(homeworks, db, "teacher")
        
        # 创建导出文件
        export_filename = self.file_manager.generate_export_filename(
            "教师导出", current_user.school_name or "学校", class_name, current_user.subject or "科目", "作业"
        )
        
        export_path = os.path.join(export_dir, export_filename)
        
        return await self._create_export_archive(organized_data, export_path, db)
    
    def _apply_filters(self, query, filters: Dict[str, Any]):
        """应用过滤条件"""
        if not filters:
            return query
        
        if "assignment_id" in filters:
            query = query.filter(Homework.assignment_id == filters["assignment_id"])
        
        if "subject_id" in filters:
            query = query.filter(HomeworkAssignment.subject_id == filters["subject_id"])
        
        if "start_date" in filters:
            query = query.filter(Homework.created_at >= filters["start_date"])
        
        if "end_date" in filters:
            query = query.filter(Homework.created_at <= filters["end_date"])
        
        return query
    
    def _organize_homework_data(
        self, 
        homeworks: List[Homework], 
        db: Session, 
        level: str
    ) -> Dict[str, Any]:
        """组织作业数据"""
        organized = {}
        
        for homework in homeworks:
            # 获取学生信息
            student = db.query(User).filter(User.id == homework.student_id).first()
            student_name = student.full_name or student.username if student else f"学生{homework.student_id}"
            
            # 获取作业任务信息
            assignment = homework.assignment
            if not assignment:
                continue
            
            # 获取学校信息
            school = db.query(School).filter(School.id == assignment.school_id).first()
            school_name = school.name if school else f"学校{assignment.school_id}"
            
            # 获取班级信息
            class_info = db.query(Class).filter(Class.id == assignment.class_id).first()
            class_name = f"{class_info.grade}{class_info.name}" if class_info else f"班级{assignment.class_id}"
            
            # 获取科目信息
            subject = assignment.subject
            subject_name = subject.name if subject else "未知科目"
            
            # 根据导出级别组织目录结构
            if level == "system":
                path_key = f"{school_name}/{class_name}/{subject_name}/{assignment.title}"
            elif level == "school":
                path_key = f"{class_name}/{subject_name}/{assignment.title}"
            else:  # teacher level
                path_key = f"{subject_name}/{assignment.title}"
            
            if path_key not in organized:
                organized[path_key] = {}
            
            if student_name not in organized[path_key]:
                organized[path_key][student_name] = {
                    "homework": homework,
                    "original_images": [],
                    "annotated_images": []
                }
            
            # 获取原始图片
            original_images = db.query(HomeworkImage).filter(
                HomeworkImage.homework_id == homework.id
            ).all()
            organized[path_key][student_name]["original_images"] = original_images
            
            # 获取批注图片
            annotated_images = db.query(HomeworkAnnotatedImage).filter(
                HomeworkAnnotatedImage.homework_id == homework.id
            ).all()
            organized[path_key][student_name]["annotated_images"] = annotated_images
        
        return organized

    def _organize_homework_data_simple(
        self,
        homeworks: List[Homework],
        db: Session,
        page_numbers: List[int] = None
    ) -> Dict[str, Any]:
        """简化的作业数据组织"""
        organized = {}

        for homework in homeworks:
            # 获取学生信息
            student = db.query(User).filter(User.id == homework.student_id).first()
            student_name = student.full_name or student.username if student else f"学生{homework.student_id}"

            # 获取作业任务信息
            assignment = homework.assignment
            if not assignment:
                continue

            # 简化的路径结构
            path_key = f"{assignment.title}"

            if path_key not in organized:
                organized[path_key] = {}

            if student_name not in organized[path_key]:
                organized[path_key][student_name] = {
                    "homework": homework,
                    "original_images": [],
                    "annotated_images": []
                }

            # 获取原始图片
            original_query = db.query(HomeworkImage).filter(
                HomeworkImage.homework_id == homework.id
            )

            # 如果指定了页面号，则过滤
            if page_numbers:
                original_query = original_query.filter(HomeworkImage.page_number.in_(page_numbers))

            original_images = original_query.all()
            organized[path_key][student_name]["original_images"] = original_images

            # 获取批注图片
            annotated_query = db.query(HomeworkAnnotatedImage).filter(
                HomeworkAnnotatedImage.homework_id == homework.id
            )

            # 如果指定了页面号，则过滤
            if page_numbers:
                annotated_query = annotated_query.filter(HomeworkAnnotatedImage.page_number.in_(page_numbers))

            annotated_images = annotated_query.all()
            organized[path_key][student_name]["annotated_images"] = annotated_images

        return organized

    async def _create_export_archive(
        self,
        organized_data: Dict[str, Any],
        export_path: str,
        db: Session,
        include_annotated: bool = True
    ) -> Optional[str]:
        """创建导出归档文件"""
        try:
            with zipfile.ZipFile(export_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                file_count = 0

                for path_key, students in organized_data.items():
                    for student_name, data in students.items():
                        homework = data["homework"]

                        # 添加原始图片
                        for image in data["original_images"]:
                            image_path = self._resolve_image_path(image.image_path)
                            if image_path and os.path.exists(image_path):
                                # 生成导出文件名（使用学生姓名）
                                page_labels = self.file_manager.get_page_labels("math")  # 默认使用数学标签
                                page_label = page_labels[image.page_number - 1] if image.page_number <= len(page_labels) else f"第{image.page_number}页"

                                file_extension = os.path.splitext(image_path)[1]
                                export_filename = f"{student_name}_{page_label}{file_extension}"
                                archive_path = f"{path_key}/原始作业/{export_filename}"

                                zipf.write(image_path, archive_path)
                                file_count += 1

                        # 添加批注图片（如果需要）
                        if include_annotated:
                            for annotated_image in data["annotated_images"]:
                                image_path = self._resolve_image_path(annotated_image.image_path)
                                if image_path and os.path.exists(image_path):
                                    # 生成导出文件名（使用学生姓名）
                                    page_labels = self.file_manager.get_page_labels("math")
                                    page_label = page_labels[annotated_image.page_number - 1] if annotated_image.page_number <= len(page_labels) else f"第{annotated_image.page_number}页"

                                    file_extension = os.path.splitext(image_path)[1]
                                    export_filename = f"{student_name}_{page_label}_批注{file_extension}"
                                    archive_path = f"{path_key}/批注作业/{export_filename}"

                                    zipf.write(image_path, archive_path)
                                    file_count += 1

                logger.info(f"导出归档创建完成，包含 {file_count} 个文件")

            # 返回相对于uploads目录的URL路径
            relative_path = os.path.relpath(export_path, self.file_manager.base_upload_dir)
            return f"/uploads/{relative_path.replace(os.sep, '/')}"

        except Exception as e:
            logger.error(f"创建导出归档失败: {e}")
            return None

    def _resolve_image_path(self, image_path: str) -> Optional[str]:
        """解析图片路径"""
        if not image_path:
            return None

        # 移除URL前缀
        if image_path.startswith("/uploads/"):
            relative_path = image_path[9:]
        elif image_path.startswith("uploads/"):
            relative_path = image_path[8:]
        else:
            relative_path = image_path

        # 尝试多个可能的路径
        possible_paths = [
            os.path.join(str(self.file_manager.base_upload_dir), relative_path),
            os.path.join("backend/uploads", relative_path),
            os.path.join("uploads", relative_path),
            os.path.join("backend/uploads", os.path.basename(relative_path))
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        logger.warning(f"无法找到图片文件: {image_path}")
        return None

    def _organize_homework_data_simple(
        self,
        homeworks: List[Homework],
        db: Session,
        page_numbers: List[int] = None
    ) -> Dict[str, Any]:
        """简化的作业数据组织"""
        organized = {}

        for homework in homeworks:
            # 获取学生信息
            student = db.query(User).filter(User.id == homework.student_id).first()
            student_name = student.full_name or student.username if student else f"学生{homework.student_id}"

            # 获取作业任务信息
            assignment = homework.assignment
            if not assignment:
                continue

            # 简化的路径结构
            path_key = f"{assignment.title}"

            if path_key not in organized:
                organized[path_key] = {}

            if student_name not in organized[path_key]:
                organized[path_key][student_name] = {
                    "homework": homework,
                    "original_images": [],
                    "annotated_images": []
                }

            # 获取原始图片
            original_query = db.query(HomeworkImage).filter(
                HomeworkImage.homework_id == homework.id
            )

            # 如果指定了页面号，则过滤
            if page_numbers:
                original_query = original_query.filter(HomeworkImage.page_number.in_(page_numbers))

            original_images = original_query.all()
            organized[path_key][student_name]["original_images"] = original_images

            # 获取批注图片
            annotated_query = db.query(HomeworkAnnotatedImage).filter(
                HomeworkAnnotatedImage.homework_id == homework.id
            )

            # 如果指定了页面号，则过滤
            if page_numbers:
                annotated_query = annotated_query.filter(HomeworkAnnotatedImage.page_number.in_(page_numbers))

            annotated_images = annotated_query.all()
            organized[path_key][student_name]["annotated_images"] = annotated_images

        return organized

    async def _check_assignment_permission(
        self,
        assignment: HomeworkAssignment,
        permission_scope: Dict[str, Any],
        current_user: User
    ) -> bool:
        """检查用户是否有权限访问作业任务"""
        try:
            # 系统级权限（超级管理员）
            if permission_scope["scope"] == "system":
                return True

            # 学校级权限
            if permission_scope["scope"] == "school":
                school_ids = permission_scope.get("school_ids", [])
                if assignment.school_id in school_ids:
                    return True
                # 如果没有指定学校ID，检查用户的学校ID
                if not school_ids and current_user.school_id == assignment.school_id:
                    return True

            # 班级级权限
            if permission_scope["scope"] == "class":
                class_ids = permission_scope.get("class_ids", [])
                if assignment.class_id in class_ids:
                    return True

            # 科目级权限
            if permission_scope["scope"] == "subject":
                subject_ids = permission_scope.get("subject_ids", [])
                if assignment.subject_id in subject_ids:
                    return True

            return False

        except Exception as e:
            logger.error(f"检查作业任务权限失败: {e}")
            return False

    async def _get_accessible_homeworks(
        self,
        assignment_id: int,
        permission_scope: Dict[str, Any],
        current_user: User,
        db: Session,
        student_ids: List[int] = None
    ) -> List[Homework]:
        """根据权限范围获取可访问的作业"""
        try:
            # 基础查询
            query = db.query(Homework).filter(Homework.assignment_id == assignment_id)

            # 根据权限范围过滤
            if permission_scope["scope"] == "system":
                # 系统级权限：可以访问所有作业
                pass
            elif permission_scope["scope"] == "school":
                # 学校级权限：只能访问本校的作业
                query = query.join(HomeworkAssignment).filter(
                    HomeworkAssignment.school_id.in_(
                        permission_scope.get("school_ids", [current_user.school_id])
                    )
                )
            elif permission_scope["scope"] == "class":
                # 班级级权限：只能访问指定班级的作业
                class_ids = permission_scope.get("class_ids", [])
                if class_ids:
                    query = query.join(HomeworkAssignment).filter(
                        HomeworkAssignment.class_id.in_(class_ids)
                    )
            elif permission_scope["scope"] == "subject":
                # 科目级权限：只能访问指定科目的作业
                subject_ids = permission_scope.get("subject_ids", [])
                if subject_ids:
                    query = query.join(HomeworkAssignment).filter(
                        HomeworkAssignment.subject_id.in_(subject_ids)
                    )

            # 如果指定了学生ID，则进一步过滤
            if student_ids:
                query = query.filter(Homework.student_id.in_(student_ids))

            return query.all()

        except Exception as e:
            logger.error(f"获取可访问作业失败: {e}")
            return []

    def _get_user_role_type(self, permission_scope: Dict[str, Any]) -> str:
        """根据权限范围确定用户角色类型"""
        scope = permission_scope.get("scope", "teacher")

        if scope == "system":
            return "super_admin"
        elif scope == "school":
            return "school_admin"
        else:
            return "teacher"

# 全局导出服务实例
export_service = ExportService()
