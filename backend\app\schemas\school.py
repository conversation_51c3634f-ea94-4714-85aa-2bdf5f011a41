from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

# 基础模型
class SchoolBase(BaseModel):
    name: str
    province: Optional[str] = None
    city: Optional[str] = None
    district: Optional[str] = None
    address: Optional[str] = None
    contact_info: Optional[str] = None

# 用于公开API的简化学校模型
class SchoolPublic(BaseModel):
    id: int
    school_name: str
    province: Optional[str] = None
    city: Optional[str] = None
    district: Optional[str] = None

    model_config = {
        "from_attributes": True
    }

# 用于公开API的简化班级模型
class ClassPublic(BaseModel):
    id: int
    name: str
    grade: str
    school_id: int

    model_config = {
        "from_attributes": True
    }

# 用于公开API的简化科目模型
class SubjectPublic(BaseModel):
    id: int
    name: str

    model_config = {
        "from_attributes": True
    }

class SchoolCreate(SchoolBase):
    pass

class SchoolUpdate(SchoolBase):
    name: Optional[str] = None

class SchoolDetail(SchoolBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = {
        "from_attributes": True
    }

# 学校响应模型 (包含统计信息)
class SchoolResponse(SchoolDetail):
    address: Optional[str] = None
    contact_info: Optional[str] = None
    class_count: int = 0
    teacher_count: int = 0
    student_count: int = 0

    model_config = {
        "from_attributes": True
    }

# 班级模型
class ClassBase(BaseModel):
    name: str
    grade: str
    school_id: int

class ClassCreate(ClassBase):
    pass

class ClassUpdate(ClassBase):
    name: Optional[str] = None
    grade: Optional[str] = None
    school_id: Optional[int] = None

class ClassDetail(ClassBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = {
        "from_attributes": True
    }

class ClassWithStudentCount(ClassDetail):
    student_count: int

# 角色模型
class RoleBase(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    level: int

class RoleCreate(RoleBase):
    pass

class RoleUpdate(RoleBase):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    level: Optional[int] = None

class Role(RoleBase):
    id: int

    model_config = {
        "from_attributes": True
    }

# 权限模型
class PermissionBase(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    resource: str

class PermissionCreate(PermissionBase):
    pass

class Permission(PermissionBase):
    id: int

    model_config = {
        "from_attributes": True
    }

# 用户角色模型
class UserRoleBase(BaseModel):
    user_id: int
    role_id: int
    school_id: Optional[int] = None
    grade_id: Optional[int] = None
    subject_id: Optional[int] = None
    class_id: Optional[int] = None

class UserRoleCreate(UserRoleBase):
    pass

class UserRoleDelete(BaseModel):
    user_id: int
    role_id: int
    school_id: Optional[int] = None

class UserRole(UserRoleBase):
    model_config = {
        "from_attributes": True
    }

# 用户模型（带角色信息）
class UserWithRoles(BaseModel):
    id: int
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    is_active: bool
    is_admin: bool
    is_teacher: bool
    school_id: Optional[int] = None
    class_id: Optional[int] = None
    primary_role: Optional[Role] = None
    roles: List[Role] = []

    model_config = {
        "from_attributes": True
    }

# 通用消息响应
class Message(BaseModel):
    message: str