from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Query, Body, Request
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import logging
import json
from datetime import datetime

from ..database import get_db
from ..models.user import User
from ..models.homework import WrongQuestion, ReinforcementExercise, ExerciseAnswerRecord
from ..services.ai_service import call_ai_api, analyze_error, generate_reinforcement_exercises, evaluate_exercise_answer
from ..routers.auth import get_current_user
# 不再需要AIResponse导入

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ai", tags=["AI助手"])

class ChatMessage(BaseModel):
    role: str  # 'user' 或 'assistant'
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    stream: Optional[bool] = False
    
class ChatResponse(BaseModel):
    response: str

class AnalysisRequest(BaseModel):
    question_content: str
    student_answer: str
    correct_answer: str
    question_type: Optional[str] = "未知类型"

class ErrorAnalysisRequest(BaseModel):
    student_answer: str
    correct_answer: str
    question_content: str

class ReinforcementExerciseRequest(BaseModel):
    wrong_question_id: int
    count: Optional[int] = 1

class ExerciseAnswerRequest(BaseModel):
    exercise_id: int
    student_answer: str

class AnswerRecordResponse(BaseModel):
    id: int
    exercise_id: int
    student_answer: str
    is_correct: bool
    ai_explanation: str
    confidence_score: Optional[float]
    submitted_at: str

@router.post("/chat", response_model=ChatResponse)
async def chat_with_ai(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """与AI助手聊天"""
    try:
        # 记录请求
        logger.info(f"用户 {current_user.username} 发送AI助手请求")
        logger.info(f"消息内容: {request.messages[-1].content if request.messages else '空'}")
        
        # 获取AI助手专用配置
        from ..models.ai_config import AIModelConfig
        from ..services.ai_service import get_ai_config_by_usage

        try:
            config = get_ai_config_by_usage(db, "ai_assistant")
            logger.info(f"使用AI助手配置: 提供商={config.provider}, 模型={config.model_name}")
            ai_config_id = config.id
        except Exception as e:
            logger.warning(f"未找到AI助手专用配置: {str(e)}，将使用默认配置")
            ai_config_id = None
        
        # 构建提示
        prompt = "你是一个教育领域的智能助手，擅长回答教育相关问题。请用简洁、专业的语言回答以下问题：\n\n"
        
        # 添加历史消息
        for msg in request.messages[-5:]:  # 只保留最近5条消息，避免提示过长
            prompt += f"{msg.role}: {msg.content}\n"
        
        # 调用AI API获取回复，传递数据库会话和配置ID
        logger.info("开始调用AI API...")
        try:
            response = await call_ai_api(prompt, db, ai_config_id=ai_config_id)
            if response:
                logger.info("AI助手回应成功")
                return {"response": response}
        except Exception as inner_e:
            logger.error(f"调用AI API时出错: {str(inner_e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            # 返回模拟响应
            user_message = request.messages[-1].content if request.messages else ""
            mock_response = generate_mock_response(user_message)
            logger.info(f"返回模拟响应: {mock_response[:50]}...")
            return {"response": mock_response}
        
        # 如果没有回应，使用模拟响应
        logger.warning("AI API没有返回内容，使用模拟响应")
        user_message = request.messages[-1].content if request.messages else ""
        mock_response = generate_mock_response(user_message)
        logger.info(f"返回模拟响应: {mock_response[:50]}...")
        return {"response": mock_response}
        
    except Exception as e:
        logger.error(f"AI助手处理请求出错: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        
        # 出错时返回固定响应
        return {"response": "抱歉，AI助手暂时遇到技术问题。请稍后再试，或联系管理员检查系统配置。"}

def generate_mock_response(message: str) -> str:
    """生成模拟响应，添加更多系统相关的实际回答"""
    message = message.lower()
    
    # 基本问候
    if any(word in message for word in ["你好", "hello", "hi", "嗨", "您好"]):
        return "你好！我是智教云端平台的AI助手，很高兴为您服务。我可以回答关于平台使用的问题，例如如何上传作业、查看批改结果等。请问有什么我可以帮助您的吗？"
    
    # 身份相关问题
    elif any(word in message for word in ["你是谁", "你叫什么", "你的名字"]):
        return "我是智教云端平台的智能助手，专门为教师和学生提供学习辅导和平台使用帮助。我可以回答关于作业管理、系统使用等方面的问题。"
    
    # 上传作业相关
    elif "上传作业" in message:
        return "在智教云端平台上传作业的步骤如下：\n\n1. 登录您的账号后，点击主页面上的\"作业管理\"菜单\n2. 点击右上角的\"上传作业\"按钮\n3. 选择相应的班级和作业类型\n4. 点击\"选择文件\"上传您的作业图片或PDF文件\n5. 上传完成后点击\"提交\"按钮\n\n系统会自动将您的作业提交给AI进行批改，您稍后可以在\"我的作业\"页面查看批改结果。"
    
    # 查看批改结果
    elif any(phrase in message for phrase in ["查看批改", "批改结果", "作业结果"]):
        return "查看作业批改结果的步骤：\n\n1. 登录系统后，点击\"我的作业\"菜单\n2. 在列表中找到您想查看的作业，状态显示为\"已批改\"\n3. 点击该作业的\"查看详情\"按钮\n4. 在详情页面，您可以看到每道题的批改结果、正确答案和详细解析\n5. 系统还会为您错误的题目生成相应的强化训练内容\n\n如果您有任何疑问，可以点击批改结果下方的\"提问\"按钮向教师提问。"
    
    # 错题训练
    elif any(phrase in message for phrase in ["错题", "训练", "强化"]):
        return "智教云端平台提供的错题强化训练功能使用方法：\n\n1. 在主菜单中点击\"错题训练\"选项\n2. 系统会自动收集您之前作业中的错题，并根据题型和知识点进行分类\n3. 您可以选择特定知识点进行针对性训练\n4. 完成训练后，系统会评估您的掌握情况，并调整后续训练内容\n\n坚持练习错题是提高学习效果的有效方法，建议您定期使用此功能。"
    
    # 班级管理
    elif any(phrase in message for phrase in ["班级管理", "加入班级", "班级"]):
        return "关于班级管理的说明：\n\n学生：\n1. 点击\"班级\"菜单，然后点击\"加入班级\"\n2. 输入教师提供的班级码，点击\"加入\"\n\n教师：\n1. 点击\"班级管理\"，然后点击\"创建班级\"\n2. 填写班级名称和描述，点击\"创建\"\n3. 在班级详情页面，可以获取班级码并分享给学生\n4. 您可以在班级管理页面查看学生名单、发布作业等\n\n管理员可以管理所有班级和用户。"
    
    # 系统介绍
    elif any(phrase in message for phrase in ["系统", "平台", "智教云端", "功能"]):
        return "智教云端智能辅导平台是一个面向教师和学生的智能教育平台，主要功能包括：\n\n1. 作业管理：上传、提交和批改作业\n2. 智能批改：利用AI技术自动批改作业\n3. 错题强化：基于学生错题自动生成个性化训练内容\n4. 数据分析：提供学习情况的统计和分析\n5. 班级管理：便捷的班级创建和管理功能\n\n我们的目标是通过AI技术提高教学效率和学习效果，为师生提供更智能的教育体验。"
    
    # 帮助和文档
    elif any(phrase in message for phrase in ["帮助", "文档", "说明", "指南"]):
        return "如需获取详细的使用帮助，您可以：\n\n1. 点击系统右上角的\"帮助\"按钮，查看在线帮助文档\n2. 在各功能页面中点击问号图标，获取即时提示\n3. 通过\"联系我们\"页面向管理员提交问题\n\n对于常见问题，您也可以直接向我提问，我会尽力提供帮助。"
    
    # 教师相关
    elif any(phrase in message for phrase in ["教师", "老师", "批量上传"]):
        return "教师功能指南：\n\n1. 批量上传作业：在作业管理页面，点击\"批量上传\"，您可以一次上传多个学生的作业\n2. 作业分析：系统会自动分析学生作业情况，生成数据报表\n3. 班级管理：创建班级、添加学生、发布作业等\n4. 数据导出：可以导出学生成绩和分析报告\n\n如有更具体的问题，请告诉我您需要了解哪方面的详细信息。"
    
    # 学生相关
    elif any(phrase in message for phrase in ["学生", "提交作业", "我的成绩"]):
        return "学生功能指南：\n\n1. 提交作业：在作业列表中选择未完成的作业进行提交\n2. 查看批改：在\"我的作业\"中查看已批改的作业和详细反馈\n3. 错题训练：系统会根据您的错题自动生成训练内容\n4. 学习分析：查看自己的学习情况统计和进步曲线\n\n请在规定时间内完成作业，系统会自动为您进行智能批改并提供详细的学习反馈。"
    
    # 通用问题解答
    elif any(word in message for word in ["什么是", "解释", "定义"]):
        return "这是一个很好的问题。作为智教云端平台的AI助手，我可以解释平台相关的概念和功能。如果您想了解更多关于特定功能的详细信息，请直接询问我相关的具体问题，例如\"如何上传作业\"或\"如何查看批改结果\"等。"
    
    elif any(word in message for word in ["如何", "怎么", "方法"]):
        return "您想了解的是平台的具体操作方法。智教云端平台设计简洁直观，大多数功能都可以通过点击相应的菜单按钮完成。如果您能告诉我您具体想了解哪个功能的使用方法，我可以提供更详细的步骤指导。例如，您可以问我\"如何上传作业\"或\"如何加入班级\"等具体问题。"
    
    elif any(word in message for word in ["作业", "题目", "问题"]):
        return "智教云端平台的作业管理功能非常完善：\n\n1. 学生可以方便地上传作业并查看批改结果\n2. 教师可以批量发布和管理作业\n3. AI系统会自动批改作业并提供详细的错误分析\n4. 系统会自动收集错题并生成个性化的强化训练\n\n如果您有关于作业管理的具体问题，请直接询问我，例如\"如何上传作业\"或\"如何查看批改结果\"。"
    
    # 默认回复
    else:
        return "感谢您的提问。我是智教云端平台的AI助手，可以回答关于平台使用的各种问题。我可以帮您了解如何上传作业、查看批改结果、使用错题训练等功能。请问有什么具体的问题需要帮助吗？"

@router.post("/analyze-error")
async def analyze_error_route(
    request: AnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """分析错误原因 - 仅返回错误分析"""
    try:
        # 获取错误分析
        analysis_result = analyze_error(
            request.student_answer,
            request.correct_answer,
            request.question_content,
            db
        )
        
        # 确保错误分析不为空
        if not analysis_result or analysis_result.strip() == "":
            analysis_result = "学生答案与标准答案不一致。可能是理解概念有误或计算过程出错。"
        
        logger.info(f"生成错误分析: {analysis_result[:50]}...")
        
        # 只返回错误分析结果
        return {
            "analysis": analysis_result
        }
    except Exception as e:
        logging.error(f"Error analyzing error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析错误失败: {str(e)}")

@router.post("/generate-reinforcement")
async def generate_reinforcement_route(
    request: AnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """生成强化建议 - 仅返回强化建议"""
    try:
        # 首先获取错误分析（用于生成更好的强化建议）
        analysis_result = analyze_error(
            request.student_answer,
            request.correct_answer,
            request.question_content,
            db
        )

        # 生成强化建议
        reinforcement = generate_reinforcement_exercises(
            request.question_content,
            request.correct_answer,
            request.student_answer,
            analysis_result,
            count=1,
            db=db
        )
        
        # 提取强化建议文本
        reinforcement_text = ""
        if reinforcement and len(reinforcement) > 0:
            # 首先尝试获取reinforcement_suggestion字段
            if "reinforcement_suggestion" in reinforcement[0]:
                reinforcement_text = reinforcement[0].get("reinforcement_suggestion", "")
                logger.info(f"使用reinforcement_suggestion字段: {reinforcement_text[:50]}...")
            
            # 如果没有reinforcement_suggestion字段，使用analysis字段
            if not reinforcement_text or reinforcement_text.strip() == "":
                reinforcement_text = reinforcement[0].get("analysis", "")
                logger.info(f"使用analysis字段: {reinforcement_text[:50]}...")
            
            # 确保强化建议不为空
            if not reinforcement_text or reinforcement_text.strip() == "" or reinforcement_text == "请参考原题的解析。":
                reinforcement_text = "建议针对该题型进行专项练习，重点复习相关概念和解题方法，注意解题步骤的完整性和准确性。可以通过对比正确答案与错误答案的差异，理解解题思路中的关键环节。"
        else:
            reinforcement_text = "建议针对该题型进行专项练习，重点复习相关概念和解题方法，注意解题步骤的完整性和准确性。可以通过对比正确答案与错误答案的差异，理解解题思路中的关键环节。"
        
        logger.info(f"生成强化建议: {reinforcement_text[:50]}...")
        
        # 只返回强化建议
        return {
            "reinforcement": reinforcement_text
        }
    except Exception as e:
        logging.error(f"Error generating reinforcement: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成强化建议失败: {str(e)}")

@router.post("/generate-exercises")
async def generate_exercises_route(
    request: ReinforcementExerciseRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """生成强化训练题目"""
    logging.info(f"🎯 收到生成练习请求: wrong_question_id={request.wrong_question_id}, count={request.count}")

    # 获取错题信息
    wrong_question = db.query(WrongQuestion).filter(WrongQuestion.id == request.wrong_question_id).first()
    if not wrong_question:
        logging.error(f"❌ 错题不存在: {request.wrong_question_id}")
        raise HTTPException(status_code=404, detail="错题不存在")

    logging.info(f"📝 找到错题: {wrong_question.question_content[:50]}...")
    
    # 验证权限
    if not current_user.is_teacher and wrong_question.student_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此错题")
    
    try:
        # 生成强化训练题目
        logging.info(f"🤖 开始调用AI生成练习...")
        exercises = generate_reinforcement_exercises(
            wrong_question.question_content,
            wrong_question.correct_answer,
            wrong_question.wrong_answer,
            wrong_question.analysis,
            count=request.count,
            db=db
        )
        logging.info(f"✅ AI生成完成，获得 {len(exercises)} 个练习")
        
        # 保存到数据库
        logging.info(f"💾 开始保存 {len(exercises)} 个练习到数据库...")
        saved_exercises = []
        for i, exercise in enumerate(exercises, 1):
            logging.info(f"📝 保存练习 {i}: {exercise['content'][:50]}...")
            db_exercise = ReinforcementExercise(
                wrong_question_id=wrong_question.id,
                student_id=wrong_question.student_id,
                exercise_content=exercise["content"],
                answer=exercise["answer"],
                analysis=exercise["analysis"],
                is_completed=False
            )
            db.add(db_exercise)
            db.commit()
            db.refresh(db_exercise)
            saved_exercises.append(db_exercise)
            logging.info(f"✅ 练习 {i} 保存成功，ID: {db_exercise.id}")
        
        # 返回结果
        return {"exercises": saved_exercises}
    except Exception as e:
        logging.error(f"Error generating exercises: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成强化训练题目失败: {str(e)}")

@router.post("/evaluate-answer")
async def evaluate_answer_route(
    request: ExerciseAnswerRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """评估学生答案"""
    # 获取练习信息
    exercise = db.query(ReinforcementExercise).filter(ReinforcementExercise.id == request.exercise_id).first()
    if not exercise:
        raise HTTPException(status_code=404, detail="练习不存在")

    # 验证权限
    if exercise.student_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限提交此练习答案")

    try:
        # 添加调试日志
        logging.info(f"评估练习 {exercise.id} 的答案")
        logging.info(f"练习内容: {exercise.exercise_content}")
        logging.info(f"正确答案: {exercise.answer}")
        logging.info(f"学生答案: {request.student_answer}")

        # 评估答案
        evaluation = evaluate_exercise_answer(
            exercise.exercise_content,
            exercise.answer,
            request.student_answer,
            db
        )

        # 保存答题记录到数据库
        answer_record = ExerciseAnswerRecord(
            exercise_id=exercise.id,
            student_id=current_user.id,
            student_answer=request.student_answer,
            is_correct=evaluation["is_correct"],
            ai_explanation=evaluation.get("explanation", ""),
            confidence_score=evaluation.get("confidence", None),
            submitted_at=datetime.utcnow()
        )
        db.add(answer_record)

        # 如果答案正确，更新练习状态为已完成
        if evaluation["is_correct"]:
            exercise.is_completed = True
            exercise.completed_at = datetime.utcnow()

        # 提交数据库更改
        db.commit()

        logging.info(f"答题记录已保存: 练习ID={exercise.id}, 学生ID={current_user.id}, 正确={evaluation['is_correct']}")

        # 返回评估结果
        return evaluation
    except Exception as e:
        logging.error(f"Error evaluating answer: {str(e)}")
        db.rollback()  # 回滚事务
        raise HTTPException(status_code=500, detail=f"评估答案失败: {str(e)}")

@router.get("/exercise-answer-records/{exercise_id}")
async def get_exercise_answer_records(
    exercise_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定练习的答题记录"""
    # 获取练习信息
    exercise = db.query(ReinforcementExercise).filter(ReinforcementExercise.id == exercise_id).first()
    if not exercise:
        raise HTTPException(status_code=404, detail="练习不存在")

    # 验证权限 - 只能查看自己的答题记录
    if exercise.student_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此练习的答题记录")

    try:
        # 查询答题记录，按提交时间倒序排列
        records = db.query(ExerciseAnswerRecord).filter(
            ExerciseAnswerRecord.exercise_id == exercise_id,
            ExerciseAnswerRecord.student_id == current_user.id
        ).order_by(ExerciseAnswerRecord.submitted_at.desc()).all()

        # 转换为响应格式
        record_list = []
        for record in records:
            record_list.append({
                "id": record.id,
                "exercise_id": record.exercise_id,
                "student_answer": record.student_answer,
                "is_correct": record.is_correct,
                "ai_explanation": record.ai_explanation or "",
                "confidence_score": record.confidence_score,
                "submitted_at": record.submitted_at.isoformat() if record.submitted_at else ""
            })

        logging.info(f"获取练习 {exercise_id} 的答题记录: 共 {len(record_list)} 条")
        return {"records": record_list}

    except Exception as e:
        logging.error(f"Error getting answer records: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取答题记录失败: {str(e)}")

@router.get("/student-answer-records")
async def get_student_answer_records(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    limit: int = Query(50, description="返回记录数量限制"),
    offset: int = Query(0, description="偏移量")
):
    """获取当前学生的所有答题记录"""
    try:
        # 查询学生的所有答题记录，按提交时间倒序排列
        records = db.query(ExerciseAnswerRecord).filter(
            ExerciseAnswerRecord.student_id == current_user.id
        ).order_by(ExerciseAnswerRecord.submitted_at.desc()).offset(offset).limit(limit).all()

        # 转换为响应格式，包含练习信息
        record_list = []
        for record in records:
            exercise = db.query(ReinforcementExercise).filter(ReinforcementExercise.id == record.exercise_id).first()
            record_list.append({
                "id": record.id,
                "exercise_id": record.exercise_id,
                "exercise_content": exercise.exercise_content if exercise else "练习已删除",
                "student_answer": record.student_answer,
                "is_correct": record.is_correct,
                "ai_explanation": record.ai_explanation or "",
                "confidence_score": record.confidence_score,
                "submitted_at": record.submitted_at.isoformat() if record.submitted_at else ""
            })

        # 获取总记录数
        total_count = db.query(ExerciseAnswerRecord).filter(
            ExerciseAnswerRecord.student_id == current_user.id
        ).count()

        logging.info(f"获取学生 {current_user.id} 的答题记录: 共 {total_count} 条，返回 {len(record_list)} 条")
        return {
            "records": record_list,
            "total": total_count,
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        logging.error(f"Error getting student answer records: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取答题记录失败: {str(e)}")