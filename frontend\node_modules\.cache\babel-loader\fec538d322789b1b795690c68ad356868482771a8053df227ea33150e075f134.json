{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { textEllipsis } from '../../style';\nconst genStepsNavStyle = token => {\n  const {\n    componentCls,\n    navContentMaxWidth,\n    navArrowColor,\n    stepsNavActiveColor,\n    motionDurationSlow\n  } = token;\n  return {\n    [`&${componentCls}-navigation`]: {\n      paddingTop: token.paddingSM,\n      [`&${componentCls}-small`]: {\n        [`${componentCls}-item`]: {\n          '&-container': {\n            marginInlineStart: token.calc(token.marginSM).mul(-1).equal()\n          }\n        }\n      },\n      [`${componentCls}-item`]: {\n        overflow: 'visible',\n        textAlign: 'center',\n        '&-container': {\n          display: 'inline-block',\n          height: '100%',\n          marginInlineStart: token.calc(token.margin).mul(-1).equal(),\n          paddingBottom: token.paddingSM,\n          textAlign: 'start',\n          transition: `opacity ${motionDurationSlow}`,\n          [`${componentCls}-item-content`]: {\n            maxWidth: navContentMaxWidth\n          },\n          [`${componentCls}-item-title`]: Object.assign(Object.assign({\n            maxWidth: '100%',\n            paddingInlineEnd: 0\n          }, textEllipsis), {\n            '&::after': {\n              display: 'none'\n            }\n          })\n        },\n        [`&:not(${componentCls}-item-active)`]: {\n          [`${componentCls}-item-container[role='button']`]: {\n            cursor: 'pointer',\n            '&:hover': {\n              opacity: 0.85\n            }\n          }\n        },\n        '&:last-child': {\n          flex: 1,\n          '&::after': {\n            display: 'none'\n          }\n        },\n        '&::after': {\n          position: 'absolute',\n          top: `calc(50% - ${unit(token.calc(token.paddingSM).div(2).equal())})`,\n          insetInlineStart: '100%',\n          display: 'inline-block',\n          width: token.fontSizeIcon,\n          height: token.fontSizeIcon,\n          borderTop: `${unit(token.lineWidth)} ${token.lineType} ${navArrowColor}`,\n          borderBottom: 'none',\n          borderInlineStart: 'none',\n          borderInlineEnd: `${unit(token.lineWidth)} ${token.lineType} ${navArrowColor}`,\n          transform: 'translateY(-50%) translateX(-50%) rotate(45deg)',\n          content: '\"\"'\n        },\n        '&::before': {\n          position: 'absolute',\n          bottom: 0,\n          insetInlineStart: '50%',\n          display: 'inline-block',\n          width: 0,\n          height: token.lineWidthBold,\n          backgroundColor: stepsNavActiveColor,\n          transition: `width ${motionDurationSlow}, inset-inline-start ${motionDurationSlow}`,\n          transitionTimingFunction: 'ease-out',\n          content: '\"\"'\n        }\n      },\n      [`${componentCls}-item${componentCls}-item-active::before`]: {\n        insetInlineStart: 0,\n        width: '100%'\n      }\n    },\n    [`&${componentCls}-navigation${componentCls}-vertical`]: {\n      [`> ${componentCls}-item`]: {\n        marginInlineEnd: 0,\n        '&::before': {\n          display: 'none'\n        },\n        [`&${componentCls}-item-active::before`]: {\n          top: 0,\n          insetInlineEnd: 0,\n          insetInlineStart: 'unset',\n          display: 'block',\n          width: token.calc(token.lineWidth).mul(3).equal(),\n          height: `calc(100% - ${unit(token.marginLG)})`\n        },\n        '&::after': {\n          position: 'relative',\n          insetInlineStart: '50%',\n          display: 'block',\n          width: token.calc(token.controlHeight).mul(0.25).equal(),\n          height: token.calc(token.controlHeight).mul(0.25).equal(),\n          marginBottom: token.marginXS,\n          textAlign: 'center',\n          transform: 'translateY(-50%) translateX(-50%) rotate(135deg)'\n        },\n        '&:last-child': {\n          '&::after': {\n            display: 'none'\n          }\n        },\n        [`> ${componentCls}-item-container > ${componentCls}-item-tail`]: {\n          visibility: 'hidden'\n        }\n      }\n    },\n    [`&${componentCls}-navigation${componentCls}-horizontal`]: {\n      [`> ${componentCls}-item > ${componentCls}-item-container > ${componentCls}-item-tail`]: {\n        visibility: 'hidden'\n      }\n    }\n  };\n};\nexport default genStepsNavStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}