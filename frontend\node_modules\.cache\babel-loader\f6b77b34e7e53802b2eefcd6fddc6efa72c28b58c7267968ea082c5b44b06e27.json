{"ast": null, "code": "'use strict';\n\nvar undefined;\nvar $Object = require('es-object-atoms');\nvar $Error = require('es-errors');\nvar $EvalError = require('es-errors/eval');\nvar $RangeError = require('es-errors/range');\nvar $ReferenceError = require('es-errors/ref');\nvar $SyntaxError = require('es-errors/syntax');\nvar $TypeError = require('es-errors/type');\nvar $URIError = require('es-errors/uri');\nvar abs = require('math-intrinsics/abs');\nvar floor = require('math-intrinsics/floor');\nvar max = require('math-intrinsics/max');\nvar min = require('math-intrinsics/min');\nvar pow = require('math-intrinsics/pow');\nvar round = require('math-intrinsics/round');\nvar sign = require('math-intrinsics/sign');\nvar $Function = Function;\n\n// eslint-disable-next-line consistent-return\nvar getEvalledConstructor = function (expressionSyntax) {\n  try {\n    return $Function('\"use strict\"; return (' + expressionSyntax + ').constructor;')();\n  } catch (e) {}\n};\nvar $gOPD = require('gopd');\nvar $defineProperty = require('es-define-property');\nvar throwTypeError = function () {\n  throw new $TypeError();\n};\nvar ThrowTypeError = $gOPD ? function () {\n  try {\n    // eslint-disable-next-line no-unused-expressions, no-caller, no-restricted-properties\n    arguments.callee; // IE 8 does not throw here\n    return throwTypeError;\n  } catch (calleeThrows) {\n    try {\n      // IE 8 throws on Object.getOwnPropertyDescriptor(arguments, '')\n      return $gOPD(arguments, 'callee').get;\n    } catch (gOPDthrows) {\n      return throwTypeError;\n    }\n  }\n}() : throwTypeError;\nvar hasSymbols = require('has-symbols')();\nvar getProto = require('get-proto');\nvar $ObjectGPO = require('get-proto/Object.getPrototypeOf');\nvar $ReflectGPO = require('get-proto/Reflect.getPrototypeOf');\nvar $apply = require('call-bind-apply-helpers/functionApply');\nvar $call = require('call-bind-apply-helpers/functionCall');\nvar needsEval = {};\nvar TypedArray = typeof Uint8Array === 'undefined' || !getProto ? undefined : getProto(Uint8Array);\nvar INTRINSICS = {\n  __proto__: null,\n  '%AggregateError%': typeof AggregateError === 'undefined' ? undefined : AggregateError,\n  '%Array%': Array,\n  '%ArrayBuffer%': typeof ArrayBuffer === 'undefined' ? undefined : ArrayBuffer,\n  '%ArrayIteratorPrototype%': hasSymbols && getProto ? getProto([][Symbol.iterator]()) : undefined,\n  '%AsyncFromSyncIteratorPrototype%': undefined,\n  '%AsyncFunction%': needsEval,\n  '%AsyncGenerator%': needsEval,\n  '%AsyncGeneratorFunction%': needsEval,\n  '%AsyncIteratorPrototype%': needsEval,\n  '%Atomics%': typeof Atomics === 'undefined' ? undefined : Atomics,\n  '%BigInt%': typeof BigInt === 'undefined' ? undefined : BigInt,\n  '%BigInt64Array%': typeof BigInt64Array === 'undefined' ? undefined : BigInt64Array,\n  '%BigUint64Array%': typeof BigUint64Array === 'undefined' ? undefined : BigUint64Array,\n  '%Boolean%': Boolean,\n  '%DataView%': typeof DataView === 'undefined' ? undefined : DataView,\n  '%Date%': Date,\n  '%decodeURI%': decodeURI,\n  '%decodeURIComponent%': decodeURIComponent,\n  '%encodeURI%': encodeURI,\n  '%encodeURIComponent%': encodeURIComponent,\n  '%Error%': $Error,\n  '%eval%': eval,\n  // eslint-disable-line no-eval\n  '%EvalError%': $EvalError,\n  '%Float16Array%': typeof Float16Array === 'undefined' ? undefined : Float16Array,\n  '%Float32Array%': typeof Float32Array === 'undefined' ? undefined : Float32Array,\n  '%Float64Array%': typeof Float64Array === 'undefined' ? undefined : Float64Array,\n  '%FinalizationRegistry%': typeof FinalizationRegistry === 'undefined' ? undefined : FinalizationRegistry,\n  '%Function%': $Function,\n  '%GeneratorFunction%': needsEval,\n  '%Int8Array%': typeof Int8Array === 'undefined' ? undefined : Int8Array,\n  '%Int16Array%': typeof Int16Array === 'undefined' ? undefined : Int16Array,\n  '%Int32Array%': typeof Int32Array === 'undefined' ? undefined : Int32Array,\n  '%isFinite%': isFinite,\n  '%isNaN%': isNaN,\n  '%IteratorPrototype%': hasSymbols && getProto ? getProto(getProto([][Symbol.iterator]())) : undefined,\n  '%JSON%': typeof JSON === 'object' ? JSON : undefined,\n  '%Map%': typeof Map === 'undefined' ? undefined : Map,\n  '%MapIteratorPrototype%': typeof Map === 'undefined' || !hasSymbols || !getProto ? undefined : getProto(new Map()[Symbol.iterator]()),\n  '%Math%': Math,\n  '%Number%': Number,\n  '%Object%': $Object,\n  '%Object.getOwnPropertyDescriptor%': $gOPD,\n  '%parseFloat%': parseFloat,\n  '%parseInt%': parseInt,\n  '%Promise%': typeof Promise === 'undefined' ? undefined : Promise,\n  '%Proxy%': typeof Proxy === 'undefined' ? undefined : Proxy,\n  '%RangeError%': $RangeError,\n  '%ReferenceError%': $ReferenceError,\n  '%Reflect%': typeof Reflect === 'undefined' ? undefined : Reflect,\n  '%RegExp%': RegExp,\n  '%Set%': typeof Set === 'undefined' ? undefined : Set,\n  '%SetIteratorPrototype%': typeof Set === 'undefined' || !hasSymbols || !getProto ? undefined : getProto(new Set()[Symbol.iterator]()),\n  '%SharedArrayBuffer%': typeof SharedArrayBuffer === 'undefined' ? undefined : SharedArrayBuffer,\n  '%String%': String,\n  '%StringIteratorPrototype%': hasSymbols && getProto ? getProto(''[Symbol.iterator]()) : undefined,\n  '%Symbol%': hasSymbols ? Symbol : undefined,\n  '%SyntaxError%': $SyntaxError,\n  '%ThrowTypeError%': ThrowTypeError,\n  '%TypedArray%': TypedArray,\n  '%TypeError%': $TypeError,\n  '%Uint8Array%': typeof Uint8Array === 'undefined' ? undefined : Uint8Array,\n  '%Uint8ClampedArray%': typeof Uint8ClampedArray === 'undefined' ? undefined : Uint8ClampedArray,\n  '%Uint16Array%': typeof Uint16Array === 'undefined' ? undefined : Uint16Array,\n  '%Uint32Array%': typeof Uint32Array === 'undefined' ? undefined : Uint32Array,\n  '%URIError%': $URIError,\n  '%WeakMap%': typeof WeakMap === 'undefined' ? undefined : WeakMap,\n  '%WeakRef%': typeof WeakRef === 'undefined' ? undefined : WeakRef,\n  '%WeakSet%': typeof WeakSet === 'undefined' ? undefined : WeakSet,\n  '%Function.prototype.call%': $call,\n  '%Function.prototype.apply%': $apply,\n  '%Object.defineProperty%': $defineProperty,\n  '%Object.getPrototypeOf%': $ObjectGPO,\n  '%Math.abs%': abs,\n  '%Math.floor%': floor,\n  '%Math.max%': max,\n  '%Math.min%': min,\n  '%Math.pow%': pow,\n  '%Math.round%': round,\n  '%Math.sign%': sign,\n  '%Reflect.getPrototypeOf%': $ReflectGPO\n};\nif (getProto) {\n  try {\n    null.error; // eslint-disable-line no-unused-expressions\n  } catch (e) {\n    // https://github.com/tc39/proposal-shadowrealm/pull/384#issuecomment-1364264229\n    var errorProto = getProto(getProto(e));\n    INTRINSICS['%Error.prototype%'] = errorProto;\n  }\n}\nvar doEval = function doEval(name) {\n  var value;\n  if (name === '%AsyncFunction%') {\n    value = getEvalledConstructor('async function () {}');\n  } else if (name === '%GeneratorFunction%') {\n    value = getEvalledConstructor('function* () {}');\n  } else if (name === '%AsyncGeneratorFunction%') {\n    value = getEvalledConstructor('async function* () {}');\n  } else if (name === '%AsyncGenerator%') {\n    var fn = doEval('%AsyncGeneratorFunction%');\n    if (fn) {\n      value = fn.prototype;\n    }\n  } else if (name === '%AsyncIteratorPrototype%') {\n    var gen = doEval('%AsyncGenerator%');\n    if (gen && getProto) {\n      value = getProto(gen.prototype);\n    }\n  }\n  INTRINSICS[name] = value;\n  return value;\n};\nvar LEGACY_ALIASES = {\n  __proto__: null,\n  '%ArrayBufferPrototype%': ['ArrayBuffer', 'prototype'],\n  '%ArrayPrototype%': ['Array', 'prototype'],\n  '%ArrayProto_entries%': ['Array', 'prototype', 'entries'],\n  '%ArrayProto_forEach%': ['Array', 'prototype', 'forEach'],\n  '%ArrayProto_keys%': ['Array', 'prototype', 'keys'],\n  '%ArrayProto_values%': ['Array', 'prototype', 'values'],\n  '%AsyncFunctionPrototype%': ['AsyncFunction', 'prototype'],\n  '%AsyncGenerator%': ['AsyncGeneratorFunction', 'prototype'],\n  '%AsyncGeneratorPrototype%': ['AsyncGeneratorFunction', 'prototype', 'prototype'],\n  '%BooleanPrototype%': ['Boolean', 'prototype'],\n  '%DataViewPrototype%': ['DataView', 'prototype'],\n  '%DatePrototype%': ['Date', 'prototype'],\n  '%ErrorPrototype%': ['Error', 'prototype'],\n  '%EvalErrorPrototype%': ['EvalError', 'prototype'],\n  '%Float32ArrayPrototype%': ['Float32Array', 'prototype'],\n  '%Float64ArrayPrototype%': ['Float64Array', 'prototype'],\n  '%FunctionPrototype%': ['Function', 'prototype'],\n  '%Generator%': ['GeneratorFunction', 'prototype'],\n  '%GeneratorPrototype%': ['GeneratorFunction', 'prototype', 'prototype'],\n  '%Int8ArrayPrototype%': ['Int8Array', 'prototype'],\n  '%Int16ArrayPrototype%': ['Int16Array', 'prototype'],\n  '%Int32ArrayPrototype%': ['Int32Array', 'prototype'],\n  '%JSONParse%': ['JSON', 'parse'],\n  '%JSONStringify%': ['JSON', 'stringify'],\n  '%MapPrototype%': ['Map', 'prototype'],\n  '%NumberPrototype%': ['Number', 'prototype'],\n  '%ObjectPrototype%': ['Object', 'prototype'],\n  '%ObjProto_toString%': ['Object', 'prototype', 'toString'],\n  '%ObjProto_valueOf%': ['Object', 'prototype', 'valueOf'],\n  '%PromisePrototype%': ['Promise', 'prototype'],\n  '%PromiseProto_then%': ['Promise', 'prototype', 'then'],\n  '%Promise_all%': ['Promise', 'all'],\n  '%Promise_reject%': ['Promise', 'reject'],\n  '%Promise_resolve%': ['Promise', 'resolve'],\n  '%RangeErrorPrototype%': ['RangeError', 'prototype'],\n  '%ReferenceErrorPrototype%': ['ReferenceError', 'prototype'],\n  '%RegExpPrototype%': ['RegExp', 'prototype'],\n  '%SetPrototype%': ['Set', 'prototype'],\n  '%SharedArrayBufferPrototype%': ['SharedArrayBuffer', 'prototype'],\n  '%StringPrototype%': ['String', 'prototype'],\n  '%SymbolPrototype%': ['Symbol', 'prototype'],\n  '%SyntaxErrorPrototype%': ['SyntaxError', 'prototype'],\n  '%TypedArrayPrototype%': ['TypedArray', 'prototype'],\n  '%TypeErrorPrototype%': ['TypeError', 'prototype'],\n  '%Uint8ArrayPrototype%': ['Uint8Array', 'prototype'],\n  '%Uint8ClampedArrayPrototype%': ['Uint8ClampedArray', 'prototype'],\n  '%Uint16ArrayPrototype%': ['Uint16Array', 'prototype'],\n  '%Uint32ArrayPrototype%': ['Uint32Array', 'prototype'],\n  '%URIErrorPrototype%': ['URIError', 'prototype'],\n  '%WeakMapPrototype%': ['WeakMap', 'prototype'],\n  '%WeakSetPrototype%': ['WeakSet', 'prototype']\n};\nvar bind = require('function-bind');\nvar hasOwn = require('hasown');\nvar $concat = bind.call($call, Array.prototype.concat);\nvar $spliceApply = bind.call($apply, Array.prototype.splice);\nvar $replace = bind.call($call, String.prototype.replace);\nvar $strSlice = bind.call($call, String.prototype.slice);\nvar $exec = bind.call($call, RegExp.prototype.exec);\n\n/* adapted from https://github.com/lodash/lodash/blob/4.17.15/dist/lodash.js#L6735-L6744 */\nvar rePropName = /[^%.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|%$))/g;\nvar reEscapeChar = /\\\\(\\\\)?/g; /** Used to match backslashes in property paths. */\nvar stringToPath = function stringToPath(string) {\n  var first = $strSlice(string, 0, 1);\n  var last = $strSlice(string, -1);\n  if (first === '%' && last !== '%') {\n    throw new $SyntaxError('invalid intrinsic syntax, expected closing `%`');\n  } else if (last === '%' && first !== '%') {\n    throw new $SyntaxError('invalid intrinsic syntax, expected opening `%`');\n  }\n  var result = [];\n  $replace(string, rePropName, function (match, number, quote, subString) {\n    result[result.length] = quote ? $replace(subString, reEscapeChar, '$1') : number || match;\n  });\n  return result;\n};\n/* end adaptation */\n\nvar getBaseIntrinsic = function getBaseIntrinsic(name, allowMissing) {\n  var intrinsicName = name;\n  var alias;\n  if (hasOwn(LEGACY_ALIASES, intrinsicName)) {\n    alias = LEGACY_ALIASES[intrinsicName];\n    intrinsicName = '%' + alias[0] + '%';\n  }\n  if (hasOwn(INTRINSICS, intrinsicName)) {\n    var value = INTRINSICS[intrinsicName];\n    if (value === needsEval) {\n      value = doEval(intrinsicName);\n    }\n    if (typeof value === 'undefined' && !allowMissing) {\n      throw new $TypeError('intrinsic ' + name + ' exists, but is not available. Please file an issue!');\n    }\n    return {\n      alias: alias,\n      name: intrinsicName,\n      value: value\n    };\n  }\n  throw new $SyntaxError('intrinsic ' + name + ' does not exist!');\n};\nmodule.exports = function GetIntrinsic(name, allowMissing) {\n  if (typeof name !== 'string' || name.length === 0) {\n    throw new $TypeError('intrinsic name must be a non-empty string');\n  }\n  if (arguments.length > 1 && typeof allowMissing !== 'boolean') {\n    throw new $TypeError('\"allowMissing\" argument must be a boolean');\n  }\n  if ($exec(/^%?[^%]*%?$/, name) === null) {\n    throw new $SyntaxError('`%` may not be present anywhere but at the beginning and end of the intrinsic name');\n  }\n  var parts = stringToPath(name);\n  var intrinsicBaseName = parts.length > 0 ? parts[0] : '';\n  var intrinsic = getBaseIntrinsic('%' + intrinsicBaseName + '%', allowMissing);\n  var intrinsicRealName = intrinsic.name;\n  var value = intrinsic.value;\n  var skipFurtherCaching = false;\n  var alias = intrinsic.alias;\n  if (alias) {\n    intrinsicBaseName = alias[0];\n    $spliceApply(parts, $concat([0, 1], alias));\n  }\n  for (var i = 1, isOwn = true; i < parts.length; i += 1) {\n    var part = parts[i];\n    var first = $strSlice(part, 0, 1);\n    var last = $strSlice(part, -1);\n    if ((first === '\"' || first === \"'\" || first === '`' || last === '\"' || last === \"'\" || last === '`') && first !== last) {\n      throw new $SyntaxError('property names with quotes must have matching quotes');\n    }\n    if (part === 'constructor' || !isOwn) {\n      skipFurtherCaching = true;\n    }\n    intrinsicBaseName += '.' + part;\n    intrinsicRealName = '%' + intrinsicBaseName + '%';\n    if (hasOwn(INTRINSICS, intrinsicRealName)) {\n      value = INTRINSICS[intrinsicRealName];\n    } else if (value != null) {\n      if (!(part in value)) {\n        if (!allowMissing) {\n          throw new $TypeError('base intrinsic for ' + name + ' exists, but the property is not available.');\n        }\n        return void undefined;\n      }\n      if ($gOPD && i + 1 >= parts.length) {\n        var desc = $gOPD(value, part);\n        isOwn = !!desc;\n\n        // By convention, when a data property is converted to an accessor\n        // property to emulate a data property that does not suffer from\n        // the override mistake, that accessor's getter is marked with\n        // an `originalValue` property. Here, when we detect this, we\n        // uphold the illusion by pretending to see that original data\n        // property, i.e., returning the value rather than the getter\n        // itself.\n        if (isOwn && 'get' in desc && !('originalValue' in desc.get)) {\n          value = desc.get;\n        } else {\n          value = value[part];\n        }\n      } else {\n        isOwn = hasOwn(value, part);\n        value = value[part];\n      }\n      if (isOwn && !skipFurtherCaching) {\n        INTRINSICS[intrinsicRealName] = value;\n      }\n    }\n  }\n  return value;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}