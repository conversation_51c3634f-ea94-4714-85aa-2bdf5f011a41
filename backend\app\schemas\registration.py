from pydantic import BaseModel, Field, EmailStr, field_validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum
import json


class RegistrationStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_VERIFICATION = "needs_verification"


class RelationshipType(str, Enum):
    FATHER = "father"
    MOTHER = "mother"
    GRANDFATHER = "grandfather"
    GRANDMOTHER = "grandmother"
    OTHER = "other"


class RoleConfig(BaseModel):
    """角色配置模型"""
    enabled: bool = True
    requires_approval: bool = False
    approval_level: Optional[str] = None
    fields: Dict[str, Dict[str, Any]] = {}


class RegistrationSettings(BaseModel):
    """注册设置模型"""
    roles: Dict[str, RoleConfig]
    allow_school_creation: bool = True
    student_binding_verification: Dict[str, Any] = {
        "methods": ["code", "admin_approval"],
        "code_expiry_minutes": 30
    }


class SimpleRegistrationSettings(BaseModel):
    """简化的注册设置模型（兼容旧版本）"""
    allow_student_registration: bool = True
    allow_teacher_registration: bool = True


class UserRegistrationStatusBase(BaseModel):
    """用户注册状态基础模型"""
    user_id: int
    role_name: str
    status: RegistrationStatus = RegistrationStatus.PENDING
    additional_info: Optional[Dict[str, Any]] = None


class UserRegistrationStatusCreate(UserRegistrationStatusBase):
    """创建用户注册状态请求模型"""
    pass


class UserRegistrationStatusUpdate(BaseModel):
    """更新用户注册状态请求模型"""
    status: Optional[RegistrationStatus] = None
    rejection_reason: Optional[str] = None
    additional_info: Optional[Dict[str, Any]] = None


class UserRegistrationStatus(UserRegistrationStatusBase):
    """用户注册状态响应模型"""
    id: int
    role_id: Optional[int] = None
    first_reviewer_id: Optional[int] = None
    final_reviewer_id: Optional[int] = None
    rejection_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    # 添加用户详细信息字段
    user_details: Optional[Dict[str, Any]] = None

    @field_validator('additional_info', mode='before')
    @classmethod
    def parse_additional_info(cls, v):
        """解析additional_info字段，如果是字符串则转换为字典"""
        if isinstance(v, str):
            try:
                return json.loads(v) if v else None
            except json.JSONDecodeError:
                return None
        return v

    class Config:
        from_attributes = True


class ParentStudentVerificationBase(BaseModel):
    """家长学生绑定验证基础模型"""
    parent_id: int
    student_id: int
    relationship: RelationshipType
    is_primary: bool = False


class ParentStudentVerificationCreate(ParentStudentVerificationBase):
    """创建家长学生绑定验证请求模型"""
    pass


class TempParentStudentVerificationCreate(BaseModel):
    """创建临时家长学生绑定验证请求模型（用于注册流程）"""
    student_id: int
    relationship: RelationshipType
    is_primary: bool = False


class ParentStudentVerificationVerify(BaseModel):
    """验证家长学生绑定验证码请求模型"""
    verification_id: int
    code: str


class ParentStudentVerification(ParentStudentVerificationBase):
    """家长学生绑定验证响应模型"""
    id: int
    verification_code: Optional[str] = None
    status: str
    expires_at: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TempParentStudentVerification(BaseModel):
    """临时家长学生绑定验证响应模型（用于注册流程）"""
    id: int
    parent_id: int
    student_id: int
    relationship_type: RelationshipType
    is_primary: bool
    verification_code: Optional[str] = None
    status: str
    expires_at: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SchoolApplicationBase(BaseModel):
    """学校申请基础模型"""
    name: str
    province: str
    city: str
    district: str
    address: str
    contact_name: str
    contact_phone: str
    contact_email: EmailStr
    description: Optional[str] = None


class SchoolApplicationCreate(SchoolApplicationBase):
    """创建学校申请请求模型"""
    pass


class SchoolApplicationUpdate(BaseModel):
    """更新学校申请请求模型"""
    status: Optional[str] = None
    rejection_reason: Optional[str] = None


class SchoolApplication(SchoolApplicationBase):
    """学校申请响应模型"""
    id: int
    applicant_id: int
    status: str
    reviewer_id: Optional[int] = None
    rejection_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class StudentSearchResult(BaseModel):
    """学生搜索结果模型"""
    id: int
    username: str
    full_name: str
    class_id: Optional[int] = None
    class_name: Optional[str] = None
    school_id: Optional[int] = None
    school_name: Optional[str] = None


class AvailableRolesResponse(BaseModel):
    """可用角色响应模型"""
    roles: List[Dict[str, Any]]
    allow_school_creation: bool 