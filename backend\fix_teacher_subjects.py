#!/usr/bin/env python3
"""
修复教师任教科目数据脚本
将user.subject字段的科目信息同步到user_roles表中
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.database import SessionLocal
from app.models.user import User
from app.models.subject import Subject
from app.models.user_role import UserRole
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_teacher_subjects():
    """修复教师任教科目数据"""
    db = SessionLocal()
    try:
        # 查找所有有科目信息但user_roles表中没有科目记录的教师
        teachers = db.query(User).filter(
            User.is_teacher == True,
            User.subject.isnot(None),
            User.subject != ""
        ).all()
        
        logger.info(f"找到 {len(teachers)} 个有科目信息的教师")
        
        updated_count = 0
        for teacher in teachers:
            try:
                # 检查是否已有科目记录
                existing_subject_role = db.execute(text("""
                    SELECT ur.id FROM user_roles ur
                    JOIN subjects s ON ur.subject_id = s.id
                    WHERE ur.user_id = :user_id AND ur.subject_id > 0
                """), {"user_id": teacher.id}).fetchone()
                
                if existing_subject_role:
                    logger.info(f"教师 {teacher.username} 已有科目记录，跳过")
                    continue
                
                # 查找科目
                subject = db.query(Subject).filter(Subject.name == teacher.subject).first()
                if not subject:
                    logger.warning(f"未找到科目: {teacher.subject}，为教师 {teacher.username}")
                    continue
                
                # 查找教师的user_roles记录
                user_role = db.query(UserRole).filter(UserRole.user_id == teacher.id).first()
                if user_role:
                    # 更新现有记录
                    user_role.subject_id = subject.id
                    logger.info(f"更新教师 {teacher.username} 的科目为: {teacher.subject}")
                else:
                    logger.warning(f"教师 {teacher.username} 没有user_roles记录，跳过")
                    continue
                
                updated_count += 1
                
            except Exception as e:
                logger.error(f"处理教师 {teacher.username} 时出错: {str(e)}")
                continue
        
        # 提交所有更改
        db.commit()
        logger.info(f"成功更新 {updated_count} 个教师的科目信息")
        
    except Exception as e:
        logger.error(f"修复过程中出错: {str(e)}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    logger.info("开始修复教师任教科目数据...")
    fix_teacher_subjects()
    logger.info("修复完成")
