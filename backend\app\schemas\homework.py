from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from datetime import datetime

class HomeworkImageBase(BaseModel):
    page_number: int

class HomeworkImageCreate(HomeworkImageBase):
    pass

class HomeworkImage(HomeworkImageBase):
    id: int
    homework_id: int
    image_path: str
    created_at: datetime

    model_config = {
        "from_attributes": True
    }

class HomeworkAnnotatedImageBase(BaseModel):
    page_number: int

class HomeworkAnnotatedImageCreate(HomeworkAnnotatedImageBase):
    pass

class HomeworkAnnotatedImage(HomeworkAnnotatedImageBase):
    id: int
    homework_id: int
    original_image_id: int
    image_path: str
    created_at: datetime

    model_config = {
        "from_attributes": True
    }

class HomeworkCorrectionBase(BaseModel):
    page_number: int
    correction_data: str  # JSON格式存储批改数据

class HomeworkCorrectionCreate(HomeworkCorrectionBase):
    pass

class HomeworkCorrection(HomeworkCorrectionBase):
    id: int
    homework_id: int
    created_at: datetime

    model_config = {
        "from_attributes": True
    }

class WrongQuestionBase(BaseModel):
    question_type: str
    question_content: str
    correct_answer: str
    wrong_answer: Optional[str] = None
    analysis: str

class WrongQuestionCreate(WrongQuestionBase):
    pass

class WrongQuestion(WrongQuestionBase):
    id: int
    homework_id: int
    student_id: int
    created_at: datetime

    model_config = {
        "from_attributes": True
    }

class HomeworkBase(BaseModel):
    title: str
    description: Optional[str] = None
    class_id: Optional[int] = None
    school_id: Optional[int] = None
    subject_id: Optional[int] = None

class HomeworkCreate(HomeworkBase):
    student_id: int
    assignment_id: Optional[int] = None

class HomeworkUpdate(BaseModel):
    status: Optional[str] = None
    score: Optional[float] = None
    accuracy: Optional[float] = None

class Homework(HomeworkBase):
    id: int
    student_id: int
    assignment_id: Optional[int] = None
    status: str
    score: Optional[float] = None
    accuracy: Optional[float] = None
    homework_comment: Optional[str] = None  # 作业点评字段
    created_at: datetime
    graded_at: Optional[datetime] = None
    images: List[HomeworkImage] = []
    corrections: List[HomeworkCorrection] = []
    wrong_questions: List[WrongQuestion] = []
    annotated_images: List[HomeworkAnnotatedImage] = []

    model_config = {
        "from_attributes": True
    }

class HomeworkWithDetails(BaseModel):
    # 基础字段
    id: int
    title: str
    description: Optional[str] = None
    class_id: Optional[int] = None
    school_id: Optional[int] = None
    subject_id: Optional[int] = None
    student_id: int
    assignment_id: Optional[int] = None
    status: str
    score: Optional[float] = None
    accuracy: Optional[float] = None
    homework_comment: Optional[str] = None
    created_at: datetime
    graded_at: Optional[datetime] = None
    images: List[HomeworkImage] = []
    corrections: List[HomeworkCorrection] = []
    wrong_questions: List[WrongQuestion] = []
    annotated_images: List[HomeworkAnnotatedImage] = []

    # 扩展字段
    student_name: str
    assignment_title: Optional[str] = None
    class_name: Optional[str] = None
    corrected_at: Optional[datetime] = None
    subject_name: Optional[str] = None
    version_count: Optional[int] = 1  # 记录同一作业任务的提交次数

    model_config = {
        "from_attributes": True
    }

class HomeworkAssignmentBase(BaseModel):
    title: str
    description: Optional[str] = None
    class_id: Optional[int] = None
    due_date: Optional[datetime] = None

class HomeworkAssignmentCreate(BaseModel):
    title: str
    description: Optional[str] = None
    subject_id: Optional[int] = None
    class_id: int
    deadline: Optional[datetime] = None
    teacher_id: Optional[int] = None
    school_id: Optional[int] = None
    status: Optional[str] = "active"
    correction_mode: Optional[str] = "auto"
    pattern_provider: Optional[str] = "volcano"
    ai_config_id: Optional[int] = None
    auto_correct_description: Optional[str] = None
    reference_description: Optional[str] = None
    due_date: Optional[datetime] = None

class HomeworkAssignmentUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    subject_id: Optional[int] = None
    class_id: Optional[int] = None
    deadline: Optional[datetime] = None
    teacher_id: Optional[int] = None
    school_id: Optional[int] = None
    status: Optional[str] = None
    correction_mode: Optional[str] = None
    pattern_provider: Optional[str] = "volcano"
    ai_config_id: Optional[int] = None

class HomeworkAssignment(BaseModel):
    id: int
    title: str
    description: Optional[str] = None
    subject_id: Optional[int] = None
    class_id: int
    due_date: Optional[datetime] = None
    teacher_id: int
    school_id: Optional[int] = None
    created_at: datetime
    status: Optional[str] = "active"
    correction_mode: str
    pattern_provider: Optional[str] = None
    ai_config_id: Optional[int] = None
    class_name: Optional[str] = None
    teacher_name: Optional[str] = None

    model_config = {
        "from_attributes": True
    }

class HomeworkAssignmentWithDetails(HomeworkAssignment):
    submission_count: int
    graded_count: int

class HomeworkAssignmentStatus(BaseModel):
    status: str

class ReinforcementExerciseBase(BaseModel):
    exercise_content: str
    answer: str
    analysis: str

class ReinforcementExerciseCreate(ReinforcementExerciseBase):
    wrong_question_id: int
    student_id: int

class ReinforcementExercise(ReinforcementExerciseBase):
    id: int
    wrong_question_id: int
    student_id: int
    is_completed: bool
    created_at: datetime
    completed_at: Optional[datetime] = None

    model_config = {
        "from_attributes": True
    }

class StatisticsBase(BaseModel):
    pending_homework_count: int
    average_accuracy: float
    highest_correct_count: Optional[int] = 0
    average_correct_count: Optional[float] = 0.0
    lowest_correct_count: Optional[int] = 0

class TeacherStatistics(StatisticsBase):
    class_count: int
    student_count: int
    total_homework_count: int
    graded_homework_count: int
    class_statistics: Dict[str, Any]
    recent_homeworks: Optional[List[Any]] = []

class StudentStatistics(StatisticsBase):
    total_homework_count: int
    completed_homework_count: int
    wrong_question_count: int
    reinforcement_exercise_count: Optional[int] = 0
    completed_exercise_count: Optional[int] = 0
    accuracy_trend: Optional[List[Any]] = [] 