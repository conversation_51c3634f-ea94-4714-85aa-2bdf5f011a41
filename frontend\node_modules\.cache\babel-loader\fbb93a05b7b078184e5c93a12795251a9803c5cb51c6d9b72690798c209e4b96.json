{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext } from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport convertToTooltipProps from '../_util/convertToTooltipProps';\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { devUseWarning } from '../_util/warning';\nimport Badge from '../badge';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport Tooltip from '../tooltip';\nimport FloatButtonGroupContext from './context';\nimport Content from './FloatButtonContent';\nimport useStyle from './style';\nexport const floatButtonPrefixCls = 'float-btn';\nconst InternalFloatButton = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      style,\n      type = 'default',\n      shape = 'circle',\n      icon,\n      description,\n      tooltip,\n      htmlType = 'button',\n      badge = {}\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"rootClassName\", \"style\", \"type\", \"shape\", \"icon\", \"description\", \"tooltip\", \"htmlType\", \"badge\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = useContext(ConfigContext);\n  const groupShape = useContext(FloatButtonGroupContext);\n  const prefixCls = getPrefixCls(floatButtonPrefixCls, customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const mergedShape = groupShape || shape;\n  const classString = classNames(hashId, cssVarCls, rootCls, prefixCls, className, rootClassName, `${prefixCls}-${type}`, `${prefixCls}-${mergedShape}`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  });\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('FloatButton', style === null || style === void 0 ? void 0 : style.zIndex);\n  const mergedStyle = Object.assign(Object.assign({}, style), {\n    zIndex\n  });\n  // 虽然在 ts 中已经 omit 过了，但是为了防止多余的属性被透传进来，这里再 omit 一遍，以防万一\n  const badgeProps = omit(badge, ['title', 'children', 'status', 'text']);\n  let buttonNode = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-body`\n  }, /*#__PURE__*/React.createElement(Content, {\n    prefixCls: prefixCls,\n    description: description,\n    icon: icon\n  }));\n  if ('badge' in props) {\n    buttonNode = /*#__PURE__*/React.createElement(Badge, Object.assign({}, badgeProps), buttonNode);\n  }\n  // ============================ Tooltip ============================\n  const tooltipProps = convertToTooltipProps(tooltip);\n  if (tooltipProps) {\n    buttonNode = /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, tooltipProps), buttonNode);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('FloatButton');\n    process.env.NODE_ENV !== \"production\" ? warning(!(shape === 'circle' && description), 'usage', 'supported only when `shape` is `square`. Due to narrow space for text, short sentence is recommended.') : void 0;\n  }\n  return wrapCSSVar(props.href ? (/*#__PURE__*/React.createElement(\"a\", Object.assign({\n    ref: ref\n  }, restProps, {\n    className: classString,\n    style: mergedStyle\n  }), buttonNode)) : (/*#__PURE__*/React.createElement(\"button\", Object.assign({\n    ref: ref\n  }, restProps, {\n    className: classString,\n    style: mergedStyle,\n    type: htmlType\n  }), buttonNode)));\n});\nconst FloatButton = InternalFloatButton;\nif (process.env.NODE_ENV !== 'production') {\n  FloatButton.displayName = 'FloatButton';\n}\nexport default FloatButton;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}