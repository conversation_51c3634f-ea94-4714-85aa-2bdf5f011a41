from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum
from datetime import datetime
from ..database import Base
import enum

class AIUsageType(enum.Enum):
    """AI使用类型枚举"""
    HOMEWORK_GRADING = "homework_grading"  # 作业批改
    ERROR_ANALYSIS = "error_analysis"      # 错题分析
    REINFORCEMENT_EXERCISE = "reinforcement_exercise"  # 强化练习生成
    AI_ASSISTANT = "ai_assistant"          # AI助手问答
    HOMEWORK_COMMENT = "homework_comment"  # 作业点评

class AIModelConfig(Base):
    """AI模型配置表"""
    __tablename__ = "ai_model_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    provider = Column(String(50), nullable=False)  # 提供商名称，如volcano、ollama
    model_name = Column(String(100), nullable=False)  # 模型名称，用于显示
    model_id = Column(String(100), nullable=True)  # 模型ID，用于API调用
    usage_type = Column(String(50), nullable=False)  # 使用类型
    api_key = Column(String(255), nullable=True)  # API密钥
    api_endpoint = Column(String(255), nullable=True)  # API端点URL
    is_active = Column(Boolean, default=True)  # 是否可用（设置为True表示该配置可用）
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<AIModelConfig(model_name='{self.model_name}', provider='{self.provider}')>" 