#!/usr/bin/env python3
"""
导出功能路由
"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging

from ..database import get_db
from ..models.user import User
from ..routers.auth import get_current_user
from ..services.export_service import export_service
from ..services.permission_service import PermissionService

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/homework")
async def export_homework(
    background_tasks: BackgroundTasks,
    assignment_id: Optional[int] = Query(None, description="作业任务ID"),
    subject_id: Optional[int] = Query(None, description="科目ID"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    include_annotated: bool = Query(True, description="是否包含批注图片"),
    student_ids: Optional[str] = Query(None, description="学生ID列表，逗号分隔"),
    page_numbers: Optional[str] = Query(None, description="页面号列表，逗号分隔"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    导出作业
    
    根据用户权限和过滤条件导出作业文件
    """
    try:
        logger.info(f"用户 {current_user.username} 请求导出作业")
        
        # 检查是否指定了作业任务ID
        if not assignment_id:
            raise HTTPException(
                status_code=400,
                detail="请指定要导出的作业任务ID"
            )

        # 解析学生ID列表
        parsed_student_ids = None
        if student_ids:
            try:
                parsed_student_ids = [int(id.strip()) for id in student_ids.split(',') if id.strip()]
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail="学生ID格式错误，请使用逗号分隔的数字"
                )

        # 解析页面号列表
        parsed_page_numbers = None
        if page_numbers:
            try:
                parsed_page_numbers = [int(num.strip()) for num in page_numbers.split(',') if num.strip()]
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail="页面号格式错误，请使用逗号分隔的数字"
                )

        # 导出作业图片
        export_url = await export_service.export_homework_by_assignment(
            current_user=current_user,
            db=db,
            assignment_id=assignment_id,
            include_annotated=include_annotated,
            student_ids=parsed_student_ids,
            page_numbers=parsed_page_numbers
        )
        
        if not export_url:
            raise HTTPException(
                status_code=404,
                detail="没有找到符合条件的作业或导出失败"
            )
        
        logger.info(f"作业导出成功: {export_url}")
        
        return {
            "success": True,
            "message": "作业导出成功",
            "export_url": export_url,
            "download_url": export_url  # 提供下载链接
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出作业失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"导出作业失败: {str(e)}"
        )

@router.get("/permissions")
async def get_export_permissions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户的导出权限信息
    """
    try:
        # 获取用户权限范围
        permission_scope = await PermissionService.get_user_permission_scope(
            current_user, db, "homework_management"
        )

        # 获取可访问的作业任务ID列表（简化实现）
        accessible_assignment_ids = []
        if permission_scope["scope"] == "system":
            # 系统级权限：获取所有作业任务ID
            assignments = db.query(HomeworkAssignment.id).all()
            accessible_assignment_ids = [a.id for a in assignments]
        elif permission_scope["scope"] == "school":
            # 学校级权限：获取本校作业任务ID
            school_ids = permission_scope.get("school_ids", [current_user.school_id])
            if school_ids:
                assignments = db.query(HomeworkAssignment.id).filter(
                    HomeworkAssignment.school_id.in_(school_ids)
                ).all()
                accessible_assignment_ids = [a.id for a in assignments]
        elif permission_scope["scope"] in ["class", "subject"]:
            # 班级或科目级权限：获取相关作业任务ID
            class_ids = permission_scope.get("class_ids", [])
            subject_ids = permission_scope.get("subject_ids", [])

            query = db.query(HomeworkAssignment.id)
            if class_ids:
                query = query.filter(HomeworkAssignment.class_id.in_(class_ids))
            if subject_ids:
                query = query.filter(HomeworkAssignment.subject_id.in_(subject_ids))

            assignments = query.all()
            accessible_assignment_ids = [a.id for a in assignments]

        return {
            "success": True,
            "permissions": {
                "scope": permission_scope["scope"],
                "description": permission_scope["description"],
                "school_ids": permission_scope.get("school_ids"),
                "class_ids": permission_scope.get("class_ids"),
                "subject_ids": permission_scope.get("subject_ids"),
                "accessible_assignment_ids": accessible_assignment_ids[:100]  # 限制返回数量
            }
        }

    except Exception as e:
        logger.error(f"获取导出权限失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取导出权限失败: {str(e)}"
        )

@router.get("/history")
async def get_export_history(
    limit: int = Query(10, description="返回记录数量"),
    offset: int = Query(0, description="偏移量"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户的导出历史记录
    """
    try:
        # 这里可以实现导出历史记录的查询
        # 暂时返回空列表，后续可以添加导出历史表
        
        return {
            "success": True,
            "total": 0,
            "exports": [],
            "message": "导出历史功能待实现"
        }
        
    except Exception as e:
        logger.error(f"获取导出历史失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取导出历史失败: {str(e)}"
        )

@router.post("/cleanup")
async def cleanup_export_files(
    older_than_hours: int = Query(24, description="清理多少小时前的导出文件"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    清理过期的导出文件
    
    只有管理员可以执行此操作
    """
    try:
        # 检查权限
        if not current_user.is_admin:
            raise HTTPException(
                status_code=403,
                detail="只有管理员可以清理导出文件"
            )
        
        # 清理导出文件
        from ..services.file_manager import file_manager
        file_manager.cleanup_temp_files(older_than_hours)
        
        logger.info(f"管理员 {current_user.username} 清理了 {older_than_hours} 小时前的导出文件")
        
        return {
            "success": True,
            "message": f"已清理 {older_than_hours} 小时前的导出文件"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理导出文件失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"清理导出文件失败: {str(e)}"
        )

@router.get("/stats")
async def get_export_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取导出统计信息（简化版本）
    """
    try:
        # 简化的统计信息
        if current_user.is_admin:
            user_scope = "system"
        elif current_user.is_teacher:
            user_scope = "teacher"
        else:
            user_scope = "none"

        stats = {
            "user_scope": user_scope,
            "accessible_schools": 1 if current_user.school_id else 0,
            "accessible_classes": 0,  # 暂时返回0
            "accessible_subjects": 0,  # 暂时返回0
        }

        # 如果有学校ID，获取学校存储信息
        if current_user.school_id:
            try:
                from ..services.file_manager import file_manager
                storage_info = file_manager.get_school_storage_info(current_user.school_id)
                stats.update({
                    "storage_info": storage_info
                })
            except Exception as e:
                logger.warning(f"获取学校存储信息失败: {e}")

        return {
            "success": True,
            "stats": stats
        }

    except Exception as e:
        logger.error(f"获取导出统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取导出统计失败: {str(e)}"
        )
