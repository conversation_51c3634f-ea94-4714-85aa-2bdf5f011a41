# -*- coding: utf-8 -*-
from fastapi import FastAPI, Depends, HTTPException, status, Request
from fastapi.middleware.cors import CORSMiddleware as FastAPICORSMiddleware
from fastapi.security import OAuth2<PERSON><PERSON><PERSON><PERSON>earer
from typing import Optional, List, Dict
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
import uvicorn
import logging
import os
import sys
import time
from datetime import datetime
from sqlalchemy.orm import Session

# 在导入任何其他模块前先配置日志级别

# 导入路由模块
from app.routers import auth, homework, feedback, statistics, ai_assistant, admin, school, cors_routes, school_statistics, homework_analysis, public, student, system_analysis, export, region
from app.services.auth_service import get_current_user

# 禁用watchfiles的所有日志
logging.getLogger("watchfiles").setLevel(logging.ERROR)
logging.getLogger("uvicorn").setLevel(logging.WARNING)
logging.getLogger("uvicorn.error").setLevel(logging.WARNING)
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)

# 导入路由器初始化模块
from app.routers import router as init_router
from app.database import engine, Base, init_db, get_db
# import correction_demo_routes  # 注释掉不存在的模块
from app.middleware.cors_middleware import CORSMiddleware

# 添加当前目录到模块搜索路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入应用模块 - 仅导入需要创建表的模型
from app.models import user, homework as homework_model, school as school_model, ai_config
from app.models import region as region_model
from app.models.user import User

# 导入数据库事件处理器（确保用户创建时自动同步到user_roles表）
from app.database.events import setup_database_events

# 修复模块已移除，不再需要

# 创建数据库表
Base.metadata.create_all(bind=engine)

# 初始化数据库
init_db()

# 配置日志
logging.basicConfig(
    level=logging.INFO,  # 从DEBUG改为INFO，减少日志输出
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("app.log", mode="w", encoding="utf-8"),  # 使用覆盖模式，不是追加模式，添加utf-8编码
        logging.FileHandler("debug.log", mode="w", encoding="utf-8")  # 使用覆盖模式，不是追加模式，添加utf-8编码
    ]
)

# 创建应用级别的根logger
logger = logging.getLogger()
logger.setLevel(logging.INFO)  # 恢复为INFO

# 再次确保禁用一些不必要的模块日志
logging.getLogger("watchfiles").setLevel(logging.ERROR)  # 完全禁用watchfiles日志
logging.getLogger("uvicorn").setLevel(logging.WARNING)  # 禁用uvicorn的DEBUG日志
logging.getLogger("uvicorn.error").setLevel(logging.WARNING)  # 禁用uvicorn.error的DEBUG日志
logging.getLogger("httpx").setLevel(logging.WARNING)  # 禁用httpx的DEBUG日志
logging.getLogger("httpcore").setLevel(logging.WARNING)  # 禁用httpcore的DEBUG日志

# 为特定模块设置更详细的日志级别
homework_logger = logging.getLogger("app.routers.homework")
homework_logger.setLevel(logging.INFO)  # 从DEBUG改为INFO

# 定义应用生命周期事件处理函数
async def lifespan(app: FastAPI):
    # 应用启动时的操作
    logger.info("======== 应用启动 ========")
    logger.info(f"启动时间: {datetime.now().isoformat()}")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"工作目录: {os.getcwd()}")
    logger.info("======== 启动完成 ========")
    
    yield  # 这里是应用运行的部分
    
    # 应用关闭时的操作
    logger.info("======== 应用关闭 ========")
    logger.info(f"关闭时间: {datetime.now().isoformat()}")
    logger.info("======== 关闭完成 ========")

# 创建FastAPI应用
app = FastAPI(
    title="智教云端智能辅导平台",
    description="提供作业管理、智能批改和错题强化训练等功能的API",
    version="1.0.0",
    lifespan=lifespan
)

# 添加请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()

    # 记录请求信息
    method = request.method
    url = str(request.url)
    path = request.url.path

    # 记录所有API请求（排除静态文件和健康检查）
    if path.startswith("/api") or "/homework" in path or "/batch" in path:
        logger.info(f"🌐 API请求: {method} {path}")

        # 如果是POST请求，尝试记录请求体信息
        if method == "POST":
            try:
                # 获取Content-Type
                content_type = request.headers.get("content-type", "")
                if "multipart/form-data" in content_type:
                    logger.info(f"📤 POST请求包含文件上传")
                elif "application/json" in content_type:
                    logger.info(f"📤 POST请求包含JSON数据")
            except:
                pass

    # 处理请求
    response = await call_next(request)

    # 记录响应信息
    if path.startswith("/api") or "/homework" in path or "/batch" in path:
        process_time = time.time() - start_time
        logger.info(f"🔄 API响应: {response.status_code} - 耗时: {process_time:.2f}s")

    return response

# 添加自定义CORS中间件
app.add_middleware(CORSMiddleware)

# 配置FastAPI内置的CORS
app.add_middleware(
    FastAPICORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH", "HEAD"],
    allow_headers=["*"],
)

# 创建上传目录
UPLOAD_DIR = "backend/uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)

# 挂载静态文件目录
app.mount("/uploads", StaticFiles(directory=UPLOAD_DIR), name="uploads")

# 注册路由
app.include_router(auth.router, prefix="/api", tags=["认证"])
app.include_router(homework.router, prefix="/api", tags=["作业管理"])
app.include_router(feedback.router, prefix="/api", tags=["批改反馈"])
app.include_router(statistics.router, prefix="/api", tags=["统计分析"])
app.include_router(school_statistics.router, prefix="/api", tags=["学校统计"])
app.include_router(ai_assistant.router, prefix="/api", tags=["AI助手"])
app.include_router(admin.router, prefix="/api/admin", tags=["管理员"])
# 注册作业分析路由
app.include_router(homework_analysis.router, prefix="/api/homework-analysis", tags=["作业分析"])
# 注册学校管理路由，同时添加公开端点
app.include_router(school.router, prefix="/api/schools", tags=["学校管理"])

# 注册学生路由
app.include_router(student.router, prefix="/api/students", tags=["学生功能"])

# 注册家长端路由
from app.routers import parent
app.include_router(parent.router, tags=["家长端"])

# 注册数据库管理模块路由
from app.routers import db_admin
app.include_router(db_admin.router, prefix="/api/admin/db", tags=["数据库管理"])

# 注册CORS预检请求处理路由
app.include_router(cors_routes.router, prefix="/api")

# 注册公开路由 - 不需要认证
app.include_router(public.router, prefix="/api/public", tags=["公开接口"])

# 注册系统分析路由
app.include_router(system_analysis.router, prefix="/api/system", tags=["系统分析"])

# 注册地区管理路由
app.include_router(region.router, prefix="/api/regions", tags=["地区管理"])

# 注册导出功能路由
app.include_router(export.router, prefix="/api/export", tags=["导出功能"])

# 注册初始化路由器（包含重定向路由）
app.include_router(init_router)

# 添加作业批改演示路由
# app.include_router(correction_demo_routes.router, prefix="/api/test", tags=["作业批改演示"])  # 注释掉不存在的模块

# 健康检查端点
@app.get("/api/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "services": {
            "database": "connected",
            "ai_service": "available"
        }
    }

# 添加角色管理的兼容性路由
@app.get("/api/roles")
async def get_roles_compatibility():
    """兼容性端点：直接返回角色数据"""
    # 直接返回预定义的角色数据，避免数据库模型映射问题
    return [
        {"id": 1, "name": "超级管理员", "code": "super_admin", "description": "系统超级管理员", "level": 100},
        {"id": 2, "name": "学校管理员", "code": "school_admin", "description": "学校管理员", "level": 90},
        {"id": 3, "name": "教师", "code": "teacher", "description": "教师用户", "level": 50},
        {"id": 4, "name": "学生", "code": "student", "description": "学生用户", "level": 10}
    ]

# 添加 /api/classes 兼容性路由
@app.get("/api/classes")
async def get_classes_compatibility(
    school_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """兼容性端点：/api/classes 返回班级列表，根据用户权限返回相应数据"""
    try:
        from app.models.user import Class, User
        from app.services.auth_service import get_current_user

        # 构建查询
        query = db.query(Class)

        # 根据用户权限和参数筛选班级
        if current_user.is_admin and current_user.role == '超级管理员':
            # 超级管理员：如果指定了school_id则按学校筛选，否则返回所属学校的班级
            if school_id:
                query = query.filter(Class.school_id == school_id)
            elif current_user.school_id:
                query = query.filter(Class.school_id == current_user.school_id)
        elif current_user.is_admin or (hasattr(current_user, 'role') and current_user.role in ['学校管理员', 'school_admin', 'principal']):
            # 其他管理员：只能看到所属学校的班级
            if current_user.school_id:
                query = query.filter(Class.school_id == current_user.school_id)
            else:
                # 如果没有学校ID，返回空列表
                return []
        else:
            # 普通用户：返回空列表或根据具体需求处理
            return []

        # 执行查询
        classes = query.all()

        # 构建响应数据
        result = []
        for class_ in classes:
            class_dict = {
                "id": class_.id,
                "name": class_.name,
                "description": class_.description or "",
                "grade": class_.grade or "",
                "school_id": class_.school_id,
                "student_count": len(class_.students) if class_.students else 0,
                "created_at": class_.created_at.isoformat() if class_.created_at else datetime.now().isoformat()
            }
            result.append(class_dict)

        return result

    except Exception as e:
        logger.error(f"获取班级列表失败: {str(e)}")
        # 如果出错，返回空列表而不是硬编码数据
        return []

# 根路由
@app.get("/", response_class=HTMLResponse)
async def root():
    return """
    <html>
        <head>
            <title>智教云端智能辅导平台 API</title>
            <style>
                body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
                h1 { color: #0066cc; }
                a { color: #0066cc; text-decoration: none; }
                a:hover { text-decoration: underline; }
            </style>
        </head>
        <body>
            <h1>智教云端智能辅导平台 API</h1>
            <p>欢迎使用智教云端智能辅导平台 API！</p>
            <p>请访问 <a href="/docs">/docs</a> 查看API文档。</p>
        </body>
    </html>
    """

# 添加一个简单的健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

# 临时调试端点 - 查看当前用户信息
@app.get("/api/debug/current-user")
async def debug_current_user(current_user: User = Depends(get_current_user)):
    return {
        "user_id": current_user.id,
        "username": current_user.username,
        "full_name": current_user.full_name,
        "school_id": current_user.school_id,
        "is_teacher": current_user.is_teacher,
        "is_admin": current_user.is_admin,
        "timestamp": datetime.now().isoformat()
    }

# 不需要认证的测试端点
@app.get("/api/debug/test")
async def debug_test():
    return {
        "message": "API连接正常",
        "timestamp": datetime.now().isoformat(),
        "status": "success"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8083, reload=False)