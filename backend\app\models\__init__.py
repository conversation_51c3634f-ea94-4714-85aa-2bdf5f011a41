# 导入所有模型以确保SQLAlchemy关系正确初始化
from .user import User, Class, ClassStudent, ClassTeacher, ParentStudent
from .user_role import UserRole
from .homework import Homework, HomeworkAssignment, Comment
from .ai_config import AIModelConfig, AIUsageType
from .subject import Subject, SubjectCategory, GradeSubject
from .school import School
from .role import Role, Permission
from .role_subject_permission import RoleSubjectPermission
from .system_settings import SystemSettings
from .registration import UserRegistrationStatus, ParentStudentVerification, SchoolApplication
from .file_metadata import FileMetadata, FileAccessLog, FileType

# 确保所有模型都被导入，这样SQLAlchemy的关系才能正确工作
__all__ = [
    "User",
    "UserRole",
    "Class",
    "ClassStudent",
    "ClassTeacher",
    "ParentStudent",
    "Homework",
    "HomeworkAssignment",
    "Comment",
    "AIModelConfig",
    "AIUsageType",
    "Subject",
    "SubjectCategory",
    "GradeSubject",
    "School",
    "Role",
    "Permission",
    "RoleSubjectPermission",
    "SystemSettings",
    "UserRegistrationStatus",
    "ParentStudentVerification",
    "SchoolApplication",
    "FileMetadata",
    "FileAccessLog",
    "FileType"
]